version: "2"
services:
  bun-service:
    build:
      context: ./bun-app
      args:
        NEXT_PUBLIC_AXIOM_TOKEN: ${NEXT_PUBLIC_AXIOM_TOKEN}
        NEXT_PUBLIC_AXIOM_DATASET: ${NEXT_PUBLIC_AXIOM_DATASET}
        NEXT_PUBLIC_APP_ID: ${NEXT_PUBLIC_APP_ID}
        NEXT_PUBLIC_PB_URL: ${NEXT_PUBLIC_PB_URL}
        NEXT_PUBLIC_APP_URL: ${NEXT_PUBLIC_APP_URL}
        NEXT_PUBLIC_QNAME: ${NEXT_PUBLIC_QNAME}
        FIREBASE_PRIVATE_KEY: ${FIREBASE_PRIVATE_KEY}
        STRIPE_SECRET_KEY: ${STRIPE_SECRET_KEY}
        NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY: ${NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY}
    ports:
      - "${HOST_PORT_BUN}:3006"
    environment:
      HOST_PORT_BUN: ${HOST_PORT_BUN}
      OPENAI_API_KEY: ${OPENAI_API_KEY}
      INNGEST_SIGNING_KEY: ${INNGEST_SIGNING_KEY}
      INNGEST_EVENT_KEY: ${INNGEST_EVENT_KEY}
      INNGEST_SERVE_PATH: ${INNGEST_SERVE_PATH}
      NODE_ENV: "production"
      WA_PHONE_NUMBER_ID: ${WA_PHONE_NUMBER_ID}
      CLOUD_API_ACCESS_TOKEN: ${CLOUD_API_ACCESS_TOKEN}
      CLOUD_API_VERSION: ${CLOUD_API_VERSION}
      USER_ACCESS_TOKEN: ${USER_ACCESS_TOKEN}
      NEXT_PUBLIC_AXIOM_TOKEN: ${NEXT_PUBLIC_AXIOM_TOKEN}
      NEXT_PUBLIC_AXIOM_DATASET: ${NEXT_PUBLIC_AXIOM_DATASET}
      APP_SECRET: ${APP_SECRET}
      BUSINESS_ID: ${BUSINESS_ID}
      NEXT_PUBLIC_PB_URL: ${NEXT_PUBLIC_PB_URL}
      NEXT_PUBLIC_APP_ID: ${NEXT_PUBLIC_APP_ID}
      NEXT_PUBLIC_APP_URL: ${NEXT_PUBLIC_APP_URL}
      RESEND_API_KEY: ${RESEND_API_KEY}
      NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY: ${NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY}
      STRIPE_SECRET_KEY: ${STRIPE_SECRET_KEY}
      FIREBASE_PRIVATE_KEY: ${FIREBASE_PRIVATE_KEY}
      NEXT_PUBLIC_QNAME: ${NEXT_PUBLIC_QNAME}
      SQLITE_DB_PATH: ${SQLITE_DB_PATH}
    volumes:
      - /mnt/volume-hel1-1/docker/volumes/p44408w_pocketbase-data/_data:/data
  next:
    build:
      context: ./nextjs-docker
      args:
        NEXT_PUBLIC_AXIOM_TOKEN: ${NEXT_PUBLIC_AXIOM_TOKEN}
        NEXT_PUBLIC_AXIOM_DATASET: ${NEXT_PUBLIC_AXIOM_DATASET}
        NEXT_PUBLIC_APP_ID: ${NEXT_PUBLIC_APP_ID}
        NEXT_PUBLIC_PB_URL: ${NEXT_PUBLIC_PB_URL}
        NEXT_PUBLIC_APP_URL: ${NEXT_PUBLIC_APP_URL}
        NEXT_PUBLIC_QNAME: ${NEXT_PUBLIC_QNAME}
        FIREBASE_PRIVATE_KEY: ${FIREBASE_PRIVATE_KEY}
        STRIPE_SECRET_KEY: ${STRIPE_SECRET_KEY}
        NEXT_PUBLIC_BUN_SERVER_URL: ${NEXT_PUBLIC_BUN_SERVER_URL}
        NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY: ${NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY}
    ports:
      - "${HOST_PORT}:3001"
    environment:
      OPENAI_API_KEY: ${OPENAI_API_KEY}
      INNGEST_SIGNING_KEY: ${INNGEST_SIGNING_KEY}
      INNGEST_EVENT_KEY: ${INNGEST_EVENT_KEY}
      INNGEST_SERVE_PATH: ${INNGEST_SERVE_PATH}
      NODE_ENV: "production"
      WA_PHONE_NUMBER_ID: ${WA_PHONE_NUMBER_ID}
      CLOUD_API_ACCESS_TOKEN: ${CLOUD_API_ACCESS_TOKEN}
      CLOUD_API_VERSION: ${CLOUD_API_VERSION}
      USER_ACCESS_TOKEN: ${USER_ACCESS_TOKEN}
      NEXT_PUBLIC_AXIOM_TOKEN: ${NEXT_PUBLIC_AXIOM_TOKEN}
      NEXT_PUBLIC_AXIOM_DATASET: ${NEXT_PUBLIC_AXIOM_DATASET}
      APP_SECRET: ${APP_SECRET}
      BUSINESS_ID: ${BUSINESS_ID}
      NEXT_PUBLIC_PB_URL: ${NEXT_PUBLIC_PB_URL}
      NEXT_PUBLIC_APP_ID: ${NEXT_PUBLIC_APP_ID}
      NEXT_PUBLIC_APP_URL: ${NEXT_PUBLIC_APP_URL}
      RESEND_API_KEY: ${RESEND_API_KEY}
      NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY: ${NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY}
      STRIPE_SECRET_KEY: ${STRIPE_SECRET_KEY}
      FIREBASE_PRIVATE_KEY: ${FIREBASE_PRIVATE_KEY}
      NEXT_PUBLIC_QNAME: ${NEXT_PUBLIC_QNAME}
      NEXT_PUBLIC_BUN_SERVER_URL: ${NEXT_PUBLIC_BUN_SERVER_URL}
      GEMINI_API_KEY: ${GEMINI_API_KEY}
