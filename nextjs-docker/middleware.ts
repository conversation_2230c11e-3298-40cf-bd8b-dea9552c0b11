import { jwtDecode } from 'jwt-decode';
import dayjs from 'dayjs';
import relativeTime from 'dayjs/plugin/relativeTime';
import type { NextRequest } from 'next/server';
import { NextResponse } from 'next/server';
import PocketBase from 'pocketbase';
import { Account, IPhoneVerificationResponse, User } from './lib/types';
import { pb_url } from './state/consts';
import { getNextjsCookie } from './state/utils/server-cookie';

export const PATHS: Record<any, string> = {
  // development: "https://localhost:3001",
  development: process.env.NEXT_PUBLIC_APP_URL ?? 'https://localhost:3001',
  production: process.env.NEXT_PUBLIC_APP_URL ?? 'https://beta.app.wetarseel.ai',
};

let pb: PocketBase | null = null;

const updateAccountDetails = async (accountId: string, data: IPhoneVerificationResponse) => {
  try {
    const pb = await getPb();
    //data.data =
    // [
    //   {
    //     verified_name: 'Test Number',
    //     code_verification_status: 'NOT_VERIFIED',
    //     display_phone_number: '******-131-4313',
    //     quality_rating: 'GREEN',
    //     platform_type: 'CLOUD_API',
    //     throughput: { level: 'STANDARD' },
    //     webhook_configuration: { application: 'https://beta.app.wetarseel.ai/api/whatsapp' },
    //     id: '***************'
    //   }
    // ]

    let { code_verification_status, display_phone_number, platform_type, quality_rating, id } = data;

    const record = await pb.collection('accounts').update(accountId, {
      code_verification_status,
      display_phone_number: display_phone_number.replace(/\s/g, ''),
      platform_type,
      quality_rating,
      phone_id: id,
    });
    return record;
  } catch (e) {
    return e;
  }
};

const getPb = async () => {
  if (!pb) {
    pb = new PocketBase(process.env.NEXT_PUBLIC_PB_URL);
    pb.autoCancellation(false);
    await pb.collection('_superusers').authWithPassword('<EMAIL>', 'Jojo.********');
  }
  if (!pb.authStore.isValid) {
    await pb.collection('_superusers').authWithPassword('<EMAIL>', 'Jojo.********');
  }
  return pb;
};

export const updateAccountAccessToken = async (account: Account) => {
  if (account.waba_id) {
    let data = await (
      await fetch(`${PATHS[process.env.NODE_ENV]}/api/get-phone-verification?waba_id=${account.waba_id}`, {
        cache: 'no-store',
      })
    ).json();
    data = data.data.filter((_data: IPhoneVerificationResponse) => _data.id == account.phone_id)[0] as IPhoneVerificationResponse;
    await updateAccountDetails(account.id, data);

    const expiry_date = dayjs(account.expires_at);
    const now = dayjs();
    const expires_in_diff = expiry_date.diff(now, 'days');
    // const expires_in = dayjs(account.expires_at).fromNow()
    console.log('token expires in ' + expires_in_diff + ' days');
    const pb = await getPb();
    if (expires_in_diff <= 15) {
      try {
        const res = await fetch(`${PATHS[process.env.NODE_ENV]}/api/refresh-token-update?fb_exchange_token=${account.access_token}`);
        const status = await res.json();
        console.log(status);
        let old_access_token = account.access_token;
        let access_token = status.access_token;
        let expires_in = status.expires_in;
        let expires_at = dayjs().add(expires_in, 'second').format();
        console.log(expires_at);
        await pb.collection('accounts').update(account.id, { expires_at, access_token });
        if (status?.error) {
          console.log(status);
        }
        try {
          const res = await fetch(`${PATHS[process.env.NODE_ENV]}/api/refresh-token-revoke?access_token=${old_access_token}`);
          const status = await res.json();
          if (status?.success == 'false') {
            console.log(status);
          }
        } catch (error) {
          console.log(error);
        }
      } catch (error) {
        console.log(error);
      }
    }
  }
};

export async function middleware(request: NextRequest) {
  const response = NextResponse.next();
  const request_cookie = request.cookies.get('pb_auth');
  dayjs.extend(relativeTime);
  const cookie = await getNextjsCookie(request_cookie);
  const pb = new PocketBase(pb_url);

  if (cookie) {
    try {
      pb.authStore.loadFromCookie(cookie);
    } catch (error) {
      pb.authStore.clear();
      response.headers.set('set-cookie', pb.authStore.exportToCookie({ httpOnly: false }));
    }
  }

  try {
    if (pb.authStore.isValid) {
      const exp = jwtDecode(pb.authStore.token).exp;
      if (exp) {
        const timeLeft = exp - Math.floor(Date.now() / 1000);
        if (timeLeft < 60 && !pb.authStore.isSuperuser && pb.authStore.isValid) {
          await pb.collection('users').authRefresh({ expand: 'roles' });
        }
        if (timeLeft < 0) {
          pb.authStore.clear();
          response.headers.set('set-cookie', pb.authStore.exportToCookie({ httpOnly: false }));
        }
      }
    }
  } catch (e) {
    console.log('middleware, jwt token', pb.authStore.isValid, e);
  }

  if (!pb.authStore.record && !request.nextUrl.pathname.startsWith('/auth')) {
    const redirect_to = request.nextUrl.clone();
    redirect_to.pathname = '/auth';
    if (request.nextUrl.pathname) {
      redirect_to.search = new URLSearchParams({
        next: request.nextUrl.pathname,
      }).toString();
    } else {
      redirect_to.search = new URLSearchParams({
        next: '/',
      }).toString();
    }

    return NextResponse.redirect(redirect_to);
  }

  if (pb.authStore.record && request.nextUrl.pathname.startsWith('/auth')) {
    const next_url = request.headers.get('next-url') as string;
    if (next_url) {
      const redirect_to = request.nextUrl.clone();
      redirect_to.pathname = `/${next_url}`;

      return NextResponse.redirect(redirect_to);
    }
    // const account = await getAccountFromUser(pb, pb.authStore.record.id);
    let resultList;
    if (pb.authStore.isSuperuser) {
      resultList = await pb.collection<Account>('accounts').getList(1, 500, {
        expand: 'users_via_favorite.favorite',
      });
    } else {
      resultList = await pb.collection<Account>('accounts').getList(1, 500, {
        filter: `pb_user_id.id ?= '${pb.authStore.record.id}'`,
        expand: 'users_via_favorite.favorite',
      });
    }

    const account = resultList.items[0];
    const userFav = pb.authStore.record?.favorite;
    const redirectToPath = request.nextUrl.clone();
    redirectToPath.pathname = `/business/create`;
    if (userFav) {
      //user is already logged in and arrives to website and has favorite
      const userAccount = resultList.items.find((acc) => acc.id === userFav);
      if (userAccount) {
        updateAccountAccessToken(userAccount);
        redirectToPath.pathname = `/${userFav}/dashboard`;
      }
    } else if (account) {
      //user logged in and does not have favorite
      updateAccountAccessToken(account);
      redirectToPath.pathname = `/${account.id}/dashboard`;
    }

    return NextResponse.redirect(redirectToPath);
  }

  return response;
}

export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - api (API routes)
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     */
    '/((?!api|assets|_next/static|enc.js|_next/image|favicon.ico|shopify-invoice).*)',
  ],
};
