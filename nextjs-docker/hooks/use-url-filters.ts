'use client';

import { usePathname, useRouter, useSearchParams } from 'next/navigation';
import { useDebouncedCallback } from 'use-debounce';
import { useCallback } from 'react';

export function useUrlFilters() {
  const searchParams = useSearchParams();
  const pathname = usePathname();
  const { replace } = useRouter();

  const updateUrlParam = useCallback(
    (key: string, value: string | null, resetPage = true) => {
      const params = new URLSearchParams(searchParams);

      if (value && value.trim() !== '') {
        params.set(key, value);
      } else {
        params.delete(key);
      }

      if (resetPage) {
        params.set('page', '1');
      }

      replace(`${pathname}?${params.toString()}`);
    },
    [pathname, replace, searchParams]
  );
  const debouncedUpdateParam = useDebouncedCallback((key: string, value: string | null, onComplete?: () => void) => {
    updateUrlParam(key, value);
    // Use setTimeout to ensure focus restoration happens after the re-render
    if (onComplete) {
      setTimeout(onComplete, 0);
    }
  }, 400);

  // Generic filter handlers
  const handleTextFilter = useCallback(
    (key: string) => (value: string, onComplete?: () => void) => {
      debouncedUpdateParam(key, value || null, onComplete);
    },
    [debouncedUpdateParam]
  );

  const handleSelectFilter = useCallback(
    (key: string) => (value: string) => {
      updateUrlParam(key, value || null);
    },
    [updateUrlParam]
  );

  const handleBooleanFilter = useCallback(
    (key: string) => (value: boolean) => {
      updateUrlParam(key, value.toString());
    },
    [updateUrlParam]
  );

  const clearAllFilters = useCallback(() => {
    const params = new URLSearchParams();
    params.set('page', '1');
    replace(`${pathname}?${params.toString()}`);
  }, [pathname, replace]);

  const getFilterValue = useCallback((key: string) => searchParams.get(key), [searchParams]);

  const getFilterArray = useCallback(
    (key: string, separator: string) => {
      const value = searchParams.get(key);
      return value?.split(separator).map((item) => item.replace(/"/g, '')) || [];
    },
    [searchParams]
  );

  return {
    // Core functions
    updateUrlParam,
    debouncedUpdateParam,
    clearAllFilters,
    getFilterValue,
    getFilterArray,

    // Specific handlers
    handleTextFilter,
    handleSelectFilter,
    handleBooleanFilter,
    // Convenience methods for common filters
    handleNameSearch: (value: string, onComplete?: () => void) => handleTextFilter('name')(value, onComplete),
    handlePhoneSearch: (value: string, onComplete?: () => void) => handleTextFilter('phone_number')(value, onComplete),
    handleStatusFilter: handleSelectFilter('status'),
    handleActiveFilter: handleBooleanFilter('active'),
    handleOptOutFilter: handleBooleanFilter('optout'),

    // Complex filters
    handleTagsFilter: useCallback(
      (tags: string[]) => {
        const value = tags.length > 0 ? tags.map((tag) => `"${tag}"`).join(' && tags~') : null;
        updateUrlParam('tags', value);
      },
      [updateUrlParam]
    ),

    handleCountryFilter: useCallback(
      (countries: string[]) => {
        const value = countries.length > 0 ? countries.map((country) => `"${country}"`).join(' || country=') : null;
        updateUrlParam('country', value);
      },
      [updateUrlParam]
    ),
  };
}
