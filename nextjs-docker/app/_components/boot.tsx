'use client';

import { useEffect } from 'react';
import { getMessaging, getToken, onMessage } from 'firebase/messaging';

// Extend the Window interface to include firebaseApp
declare global {
  interface Window {
    firebaseApp: any;
    messaging: any;
  }
}
import PocketBase, { AuthModel } from 'pocketbase';
import { initializeApp } from 'firebase/app';
import { createDb, db } from '../db';
import { Account, User } from '@/lib/types';
import { pb_url } from '@/state/consts';
import posthog from 'posthog-js';

export function testPocketbaseCookie(cookie: string) {
  const pb = new PocketBase(pb_url);
  // load the auth store data from the request cookie string
  pb.authStore.loadFromCookie(cookie);

  if (pb.authStore.isValid) {
    return pb.authStore.record;
  }
  return 'pb_auth is invalid';
}

function requestPermission() {
  console.log('Requesting permission...');
  if (typeof Notification !== 'undefined') {
    Notification.requestPermission().then((permission) => {
      if (permission === 'granted') {
        console.log('Notification permission granted.');
      }
    });
  }
}

const Boot = ({ account }: { account: Account }) => {
  useEffect(() => {
    if (account) {
      const cookie = document.cookie;
      const pbUser = testPocketbaseCookie(cookie);
      createDb(account.id);
      if (pbUser !== 'pb_auth is invalid') {
        const user = pbUser as User;
        db.users.clear();
        db.users.put(user);
        posthog.identify(user.id, {
          email: user.email,
          name: user.name,
        });
      }
      db.accounts.clear();
      db.accounts.put(account);

      requestPermission();
      const firebaseConfig = {
        apiKey: 'AIzaSyBeTzxf-heSfoLnPTFMHvwE1OsznpvzaPA',
        authDomain: 'wetarseelfirebase.firebaseapp.com',
        projectId: 'wetarseelfirebase',
        storageBucket: 'wetarseelfirebase.firebasestorage.app',
        messagingSenderId: '************',
        appId: '1:************:web:57887ea5fd4dbec9fe4684',
        measurementId: 'G-P9ZMF0Y3P6',
      };

      window.firebaseApp = initializeApp(firebaseConfig);
      window.messaging = getMessaging(window.firebaseApp);

      onMessage(window.messaging, (payload) => {
        console.log('Message received. ', payload);
        // ...
      });

      getToken(window.messaging, { vapidKey: 'BLAnfgwnkmvbgv6x6EwyRpTshnnv3RNKijWs6XnluL--CjnDmkDeboRDRHVPpVc2tyFJa1uOGxP8WtuW0yFKV3U' })
        .then((currentToken) => {
          if (currentToken) {
            const pb = new PocketBase(pb_url);
            if (pbUser !== 'pb_auth is invalid' && pbUser) {
              const id = (pbUser as AuthModel)!.id;
              pb.collection('users').update(id, { notification: currentToken }).catch(console.log);
            }

            // Send the token to your server and update the UI if necessary
            // ...
          } else {
            // Show permission request UI
            console.log('No registration token available. Request permission to generate one.');
            // ...
          }
        })
        .catch((err) => {
          console.log('An error occurred while retrieving token. ', err);
          // ...
        });
    }
  }, [account.id]);
  return null;
};

export default Boot;
