'use client';
import { User } from '@/lib/types';
import ProfileDropdownOnlyLogout from './ProfileDropdownOnlyLogout';

const HeaderOnlyProfile = ({ user, user_avatar }: { user: User; user_avatar: string | undefined }) => {
  return (
    <div className="flex items-center px-2 sm:px-10 py-2 border-b-2">
      <div className="ml-auto">
        <ProfileDropdownOnlyLogout user={user} user_avatar={user_avatar} />
      </div>
    </div>
  );
};

export default HeaderOnlyProfile;
