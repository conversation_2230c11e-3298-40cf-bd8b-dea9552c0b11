'use client';
import { LogOut } from 'lucide-react';

import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from '@/components/ui/dropdown-menu';

import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';

import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/components/ui/alert-dialog';

import { User } from '@/lib/types';
import { useDialog } from '@/state/hooks/use-dialog';
import { logoutUser } from '@/state/user/user';

const ProfileDropdownOnlyLogout = ({ user, user_avatar }: { user: User; user_avatar: string | undefined }) => {
  const deactivateLeadDialog = useDialog();
  return (
    <>
      <DropdownMenu modal={false}>
        <DropdownMenuTrigger asChild>
          <Avatar className="hover:cursor-pointer border border-gray-200">
            <AvatarImage src={user_avatar} alt="@shadcn" />
            <AvatarFallback className="font-bold hover:bg-black hover:text-white">
              {user &&
                user.name
                  .toUpperCase()
                  .split(' ')
                  .map((chars) => chars.charAt(0))
                  .join('')}
            </AvatarFallback>
          </Avatar>
        </DropdownMenuTrigger>
        <DropdownMenuContent className="w-56">
          <DropdownMenuItem {...deactivateLeadDialog.triggerProps}>
            <LogOut className="mr-2 h-4 w-4" />
            <span>Log out</span>
          </DropdownMenuItem>
        </DropdownMenuContent>
      </DropdownMenu>
      <AlertDialog {...deactivateLeadDialog.dialogProps}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Are you sure you want to logout?</AlertDialogTitle>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction onClick={() => logoutUser()}>Continue</AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </>
  );
};

export default ProfileDropdownOnlyLogout;
