'use client';

import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { createBusiness } from '@/lib/actions';
import { User } from '@/lib/types';
import { ArrowRight, Loader2, Sparkles } from 'lucide-react';
import { useFormState, useFormStatus } from 'react-dom';

interface CreateBusinessFormProps {
  user: User;
}

const initialState = {
  message: '',
};

const SubmitButton = () => {
  const { pending } = useFormStatus();
  return (
    <>
      {pending ? (
        <Button className="w-full h-12 bg-emerald-600 hover:bg-emerald-700 text-white font-medium rounded-lg transition-colors" size="lg" disabled>
          Creating your Business
          <Loader2 className="ml-2 h-4 w-4 animate-spin" />
        </Button>
      ) : (
        <Button type="submit" className="w-full h-12 bg-emerald-600 hover:bg-emerald-700 text-white font-medium rounded-lg transition-colors" size="lg">
          Get Started
          <ArrowRight className="w-4 h-4 ml-2" />
        </Button>
      )}
    </>
  );
};
const CreateBusinessForm: React.FC<CreateBusinessFormProps> = ({ user }) => {
  const businessCreate = createBusiness.bind(null, user.id);
  const [state, formAction] = useFormState(businessCreate, initialState);

  return (
    <>
      <form className="max-w-md mx-auto" action={formAction}>
        <Card className="shadow-lg border-0 bg-white">
          <CardHeader className="text-center pb-6">
            <CardTitle className="text-2xl font-bold text-gray-900">Create Your Business</CardTitle>
            <CardDescription className="text-gray-600">Let's start by setting up your business profile</CardDescription>
          </CardHeader>

          <CardContent className="space-y-6">
            <div className="space-y-2">
              <Input
                placeholder="Company Name"
                type="text"
                name="companyName"
                required
                className="border-gray-200 focus:border-emerald-500 focus:ring-emerald-500"
              />
            </div>

            <SubmitButton />
            <div className="text-center">
              <p className="text-sm text-gray-500 flex items-center justify-center space-x-1">
                <Sparkles className="w-4 h-4" />
                <span>More customization options coming soon</span>
              </p>
            </div>
          </CardContent>
        </Card>
      </form>
    </>
  );
};
export default CreateBusinessForm;
