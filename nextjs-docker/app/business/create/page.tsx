import { server_component_pb } from '@/state/pb/server_component_pb';
import CreateBusinessForm from '../component/CreateBusinessForm';
import { User } from '@/lib/types';
import { getUser } from '@/lib/pocket';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Building2, Sparkles, ArrowRight, Users, Target, Zap } from 'lucide-react';
import Link from 'next/link';

export const dynamic = 'force-dynamic';
export default async ({ params }: { params: Record<string, string> }) => {
  const user = await getUser();
  if (!user) {
    throw new Error('user not authetnicated');
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-emerald-50 via-white to-teal-50">
      <div>
        {/* Main Content */}
        <main className="container mx-auto px-4 py-12">
          <div className="max-w-4xl mx-auto">
            {/* Hero Section */}
            <div className="text-center mb-12">
              {/* <div className="inline-flex items-center space-x-2 bg-emerald-100 text-emerald-700 px-4 py-2 rounded-full text-sm font-medium mb-6">
                <Sparkles className="w-4 h-4" />
                <span>Welcome to the future of business management</span>
              </div> */}

              <h1 className="text-4xl md:text-6xl font-bold text-gray-900 mb-6 leading-tight">
                {user.name} Welcome to{' '}
                <span className="text-[#40b86a] relative">
                  WeTarseel
                  <div className="absolute -bottom-2 left-0 right-0 h-1 bg-emerald-200 rounded-full"></div>
                </span>
              </h1>

              <p className="text-xl text-gray-600 mb-8 max-w-2xl mx-auto leading-relaxed">
                Transform your business operations with our comprehensive platform. Start your journey by creating your first business profile.
              </p>
            </div>

            {/* Features Grid */}
            <div className="grid md:grid-cols-3 gap-6 mb-12">
              <div className="text-center p-6 rounded-xl bg-white shadow-sm border border-gray-100">
                <div className="w-12 h-12 bg-emerald-100 rounded-lg flex items-center justify-center mx-auto mb-4">
                  <Users className="w-6 h-6 text-[#40b86a]" />
                </div>
                <h3 className="font-semibold text-gray-900 mb-2">Automated Replies</h3>
                <p className="text-sm text-gray-600">Set up smart auto-responses based on customer actions.</p>
              </div>

              <div className="text-center p-6 rounded-xl bg-white shadow-sm border border-gray-100">
                <div className="w-12 h-12 bg-emerald-100 rounded-lg flex items-center justify-center mx-auto mb-4">
                  <Target className="w-6 h-6 text-[#40b86a]" />
                </div>
                <h3 className="font-semibold text-gray-900 mb-2">Real-Time Message Logs</h3>
                <p className="text-sm text-gray-600">Monitor every message in real-time for faster response handling.</p>
              </div>

              <div className="text-center p-6 rounded-xl bg-white shadow-sm border border-gray-100">
                <div className="w-12 h-12 bg-emerald-100 rounded-lg flex items-center justify-center mx-auto mb-4">
                  <Zap className="w-6 h-6 text-[#40b86a]" />
                </div>
                <h3 className="font-semibold text-gray-900 mb-2">Fast Setup</h3>
                <p className="text-sm text-gray-600">Connect your WhatsApp Business API and start sending messages within minutes.</p>
              </div>
            </div>

            {/* Main Form Card */}
            <CreateBusinessForm user={user} />

            {/* Additional Info */}
            {/* <div className="text-center mt-12">
              <p className="text-gray-500 text-sm">
                Need help getting started?{' '}
                <a href="#" className="text-[#40b86a] hover:text-emerald-700 font-medium">
                  Check out our guide
                </a>
              </p>
            </div> */}
            <div className="text-center mt-12">
              <p className="text-gray-500 text-sm">
                To select business go to{' '}
                <Link href="/business/select" className="text-[#40b86a] hover:text-emerald-700 font-medium">
                  Select business
                </Link>
              </p>
            </div>
          </div>
        </main>
      </div>
    </div>
  );
};
