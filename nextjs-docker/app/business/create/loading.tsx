import { Card, CardContent, CardHeader } from '@/components/ui/card';
const Loading = () => {
  return (
    <div className="min-h-screen bg-gradient-to-br from-emerald-50 via-white to-teal-50">
      {/* Header Skeleton */}
      {/* Main Content Skeleton */}
      <main className="container mx-auto px-4 py-12">
        <div className="max-w-4xl mx-auto">
          {/* Hero Section Skeleton */}
          <div className="text-center mb-12">
            {/* Badge skeleton */}
            <div className="inline-flex items-center space-x-2 h-8 w-64 bg-gray-200 rounded-full animate-pulse mb-6"></div>

            {/* Title skeleton */}
            <div className="space-y-4 mb-6">
              <div className="h-12 bg-gray-200 rounded-lg animate-pulse mx-auto max-w-2xl"></div>
              <div className="h-12 bg-gray-200 rounded-lg animate-pulse mx-auto max-w-xl"></div>
            </div>

            {/* Description skeleton */}
            <div className="space-y-3 mb-8 max-w-2xl mx-auto">
              <div className="h-4 bg-gray-200 rounded animate-pulse"></div>
              <div className="h-4 bg-gray-200 rounded animate-pulse w-3/4 mx-auto"></div>
            </div>
          </div>

          {/* Features Grid Skeleton */}
          <div className="grid md:grid-cols-3 gap-6 mb-12">
            {[1, 2, 3].map((item) => (
              <div key={item} className="text-center p-6 rounded-xl bg-white shadow-sm border border-gray-100">
                <div className="w-12 h-12 bg-gray-200 rounded-lg mx-auto mb-4 animate-pulse"></div>
                <div className="h-5 bg-gray-200 rounded animate-pulse mb-2 w-3/4 mx-auto"></div>
                <div className="h-4 bg-gray-200 rounded animate-pulse w-full"></div>
              </div>
            ))}
          </div>

          {/* Main Form Card Skeleton */}
          <div className="max-w-md mx-auto">
            <Card className="shadow-lg border-0 bg-white">
              <CardHeader className="text-center pb-6">
                <div className="h-7 bg-gray-200 rounded animate-pulse mb-2 w-3/4 mx-auto"></div>
                <div className="h-4 bg-gray-200 rounded animate-pulse w-full"></div>
              </CardHeader>

              <CardContent className="space-y-6">
                <div className="space-y-2">
                  <div className="h-4 bg-gray-200 rounded animate-pulse w-1/3"></div>
                  <div className="h-12 bg-gray-200 rounded-lg animate-pulse"></div>
                </div>

                <div className="h-12 bg-gray-200 rounded-lg animate-pulse"></div>

                <div className="text-center">
                  <div className="h-4 bg-gray-200 rounded animate-pulse w-2/3 mx-auto"></div>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Additional Info Skeleton */}
          <div className="text-center mt-12">
            <div className="h-4 bg-gray-200 rounded animate-pulse w-48 mx-auto"></div>
          </div>
        </div>
      </main>
    </div>
  );
};

export default Loading;
