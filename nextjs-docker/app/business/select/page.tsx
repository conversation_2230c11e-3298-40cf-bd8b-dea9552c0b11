import { getUserAccountList, getuserFavorite } from '@/lib/pocket';
import { server_component_pb } from '@/state/pb/server_component_pb';
import { PlusCircleIcon, PlusIcon } from 'lucide-react';
import Link from 'next/link';
import Business from './component/Business';
import { User } from '@/lib/types';
import { BaseAuthStore } from 'pocketbase';
import { Button } from '@/components/ui/button';

export const dynamic = 'force-dynamic';
export default async ({ params }: { params: Record<string, string> }) => {
  const { pb, cookies } = await server_component_pb();
  const user = pb.authStore.record as User;

  const favorite = await getuserFavorite(user.id);
  if (!user) {
    throw new Error('user not authetnicated');
  }
  const authStore: BaseAuthStore = pb.authStore as BaseAuthStore;
  const accountList = await getUserAccountList(user.id, authStore.isSuperuser);
  return (
    <div className="mx-auto p-6 bg-gray-100 min-h-full">
      <div className="flex items-center justify-between">
        <div className="font-bold text-2xl my-3 w-full">Select your business</div>
        <Button variant={'secondary'} asChild>
          <Link href={`/business/create`}>
            <PlusCircleIcon className="mr-2 h-5 w-5" />
            Create new business
          </Link>
        </Button>
      </div>
      <Business accountList={accountList} _favorite={favorite} userId={user.id} />
    </div>
  );
};
