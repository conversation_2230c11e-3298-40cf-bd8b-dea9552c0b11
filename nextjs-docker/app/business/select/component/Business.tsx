'use client';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from '@/components/ui/dropdown-menu';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Tabs, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { updateAccountAccessToken } from '@/lib/actions';
import { handleSetFavorite } from '@/lib/pocket';
import { Account } from '@/lib/types';
import dayjs from 'dayjs';
import { ArrowUpDown, ExternalLink, Filter, Grid, Link2, List, Lock, Search, Star, StarIcon, StarOff, Unplug } from 'lucide-react';
import Link from 'next/link';
import { useEffect, useRef, useState } from 'react';

const Business = ({ accountList, _favorite, userId }: { accountList: Account[]; _favorite: string | null; userId: string }) => {
  const [favorite, setFavorite] = useState<string | null>(_favorite);
  const [businesses, setBusinesses] = useState<Account[]>(accountList);
  const [filteredBusinesses, setFilteredBusinesses] = useState<Account[]>(accountList);
  const [searchQuery, setSearchQuery] = useState('');
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');
  const [filterStatus, setFilterStatus] = useState<'all' | 'connected' | 'not-connected'>('all');
  const [sortBy, setSortBy] = useState<'name' | 'date'>('name');
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('asc');
  const searchInputRef = useRef<HTMLInputElement>(null);

  const handleClick = async (accountId: string) => {
    setFavorite(accountId);
    try {
      await handleSetFavorite(userId, accountId);
    } catch (error) {
      setFavorite(_favorite);
    }
  };

  // Focus search input on mount
  useEffect(() => {
    if (searchInputRef.current) {
      searchInputRef.current.focus();
    }
  }, []);

  // Filter and sort businesses
  useEffect(() => {
    let result = [...businesses];

    // Apply search filter
    if (searchQuery) {
      result = result.filter(
        (business) => business.name.toLowerCase().includes(searchQuery.toLowerCase()) || business.display_phone_number?.includes(searchQuery)
      );
    }

    // Apply connection status filter
    if (filterStatus !== 'all') {
      result = result.filter(
        (business) =>
          (filterStatus === 'connected' && business.platform_type == 'CLOUD_API') || (filterStatus === 'not-connected' && business.platform_type != 'CLOUD_API')
      );
    }

    // Apply sorting
    result.sort((a, b) => {
      if (sortBy === 'name') {
        return sortOrder === 'asc' ? a.name.localeCompare(b.name) : b.name.localeCompare(a.name);
      } else {
        return sortOrder === 'asc'
          ? new Date(a.created).getTime() - new Date(b.created).getTime()
          : new Date(b.created).getTime() - new Date(a.created).getTime();
      }
    });
    console.log({ favorite });
    const favoriteBusiness = result.find((business) => business.id === favorite);
    const nonFavoriteBusinesses = result.filter((business) => business.id !== favorite);
    setFilteredBusinesses([...(favoriteBusiness ? [favoriteBusiness] : []), ...nonFavoriteBusinesses]);
  }, [businesses, searchQuery, filterStatus, sortBy, sortOrder]);

  return (
    <div className="flex flex-row gap-1 flex-wrap">
      <div className="flex flex-col md:flex-row gap-4 w-full">
        <div className="relative flex-1">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
          <Input
            ref={searchInputRef}
            placeholder="Search by name or phone number"
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="pl-10"
          />
        </div>

        <div className="flex gap-2">
          <Select value={filterStatus} onValueChange={(value) => setFilterStatus(value as any)}>
            <SelectTrigger className="w-[180px]">
              <Filter className="h-4 w-4 mr-2" />
              <SelectValue placeholder="Filter by status" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All businesses</SelectItem>
              <SelectItem value="connected">Connected</SelectItem>
              <SelectItem value="not-connected">Not connected</SelectItem>
            </SelectContent>
          </Select>

          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="outline" className="flex gap-2">
                <ArrowUpDown className="h-4 w-4" />
                Sort
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuItem
                onClick={() => {
                  setSortBy('name');
                  setSortOrder('asc');
                }}
              >
                Name (A-Z)
              </DropdownMenuItem>
              <DropdownMenuItem
                onClick={() => {
                  setSortBy('name');
                  setSortOrder('desc');
                }}
              >
                Name (Z-A)
              </DropdownMenuItem>
              <DropdownMenuItem
                onClick={() => {
                  setSortBy('date');
                  setSortOrder('desc');
                }}
              >
                Newest first
              </DropdownMenuItem>
              <DropdownMenuItem
                onClick={() => {
                  setSortBy('date');
                  setSortOrder('asc');
                }}
              >
                Oldest first
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>

          <Tabs defaultValue={viewMode} onValueChange={(value) => setViewMode(value as 'grid' | 'list')}>
            <TabsList>
              <TabsTrigger value="grid">
                <Grid className="h-4 w-4" />
              </TabsTrigger>
              <TabsTrigger value="list">
                <List className="h-4 w-4" />
              </TabsTrigger>
            </TabsList>
          </Tabs>
        </div>
      </div>
      <div className="mt-4 text-sm text-muted-foreground mb-4">
        Showing {filteredBusinesses.length} of {businesses.length} businesses
      </div>
      {filteredBusinesses.length === 0 ? (
        <div className="text-center py-12">
          <p className="text-muted-foreground">No businesses found matching your criteria</p>
        </div>
      ) : viewMode === 'grid' ? (
        <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-3 xl:grid-cols-4 gap-4 w-full">
          {filteredBusinesses.map((business) => (
            <Card key={business.id} className="overflow-hidden hover:shadow-md transition-all hover:border-primary/50 hover:bg-muted/30">
              <Link
                prefetch={false}
                href={`/${business.id}/dashboard`}
                onClick={async () => await updateAccountAccessToken(business)}
                className="p-4 cursor-pointer h-full flex flex-col"
                key={business.id}
              >
                <CardContent className="p-0 flex-1 flex flex-col">
                  <div className="flex justify-between items-start">
                    <h3 className="font-medium text-lg truncate" title={business.name}>
                      {business.name}
                    </h3>
                    <Button
                      variant="ghost"
                      size="icon"
                      onClick={(e) => {
                        e.stopPropagation();
                        handleClick(business.id);
                      }}
                      className="h-8 w-8"
                    >
                      {favorite && business.id == favorite ? (
                        <Star className="h-5 w-5 fill-yellow-400 text-yellow-400" />
                      ) : (
                        <StarOff className="h-5 w-5 text-muted-foreground" />
                      )}
                    </Button>
                  </div>

                  <div className="flex-1">
                    {business.display_phone_number && <p className="text-sm text-muted-foreground mt-1">{business.display_phone_number}</p>}
                    {business.phone_id && <p className="text-xs text-muted-foreground">{business.phone_id}</p>}
                    {business.banned && (
                      <Badge variant={'destructive'}>
                        <span className="flex items-center gap-1">
                          Banned <span className="inline-block w-2 h-2 rounded-full bg-red-200 ml-1"></span>
                        </span>
                      </Badge>
                    )}
                  </div>

                  <div className="mt-4 flex justify-between items-center">
                    <Badge
                      variant={business.platform_type == 'CLOUD_API' ? 'outline' : 'secondary'}
                      className={business.platform_type == 'CLOUD_API' ? 'text-green-600 bg-green-50' : ''}
                    >
                      {business.platform_type == 'CLOUD_API' ? (
                        <span className="flex items-center gap-1">
                          Connected <span className="inline-block w-2 h-2 rounded-full bg-green-600 ml-1"></span>
                        </span>
                      ) : (
                        <span className="flex items-center gap-1">
                          Not Connected <ExternalLink className="h-3 w-3 ml-1" />
                        </span>
                      )}
                    </Badge>
                    <span className="text-xs text-muted-foreground">Created {dayjs(business.created).format('DD MMM YYYY')}</span>
                  </div>
                </CardContent>
              </Link>
            </Card>
          ))}
        </div>
      ) : (
        <div className="border rounded-lg overflow-hidden w-full bg-white">
          <div className="grid grid-cols-12 bg-muted p-3 text-sm font-medium">
            <div className="col-span-5">Business Name</div>
            <div className="col-span-3">Phone Number / ID</div>
            <div className="col-span-2">Status</div>
            <div className="col-span-2">Created</div>
          </div>
          <div className="divide-y">
            {filteredBusinesses.map((business) => (
              <Link
                prefetch={false}
                href={`/${business.id}/dashboard`}
                onClick={async () => await updateAccountAccessToken(business)}
                key={business.id}
                className="grid grid-cols-12 p-3 hover:bg-muted/50 cursor-pointer items-center transition-colors"
              >
                <div className="col-span-5 flex items-center gap-2">
                  <Button
                    variant="ghost"
                    size="icon"
                    onClick={(e) => {
                      e.stopPropagation();
                      handleClick(business.id);
                    }}
                    className="h-8 w-8"
                  >
                    {favorite && business.id == favorite ? (
                      <Star className="h-4 w-4 fill-yellow-400 text-yellow-400" />
                    ) : (
                      <StarOff className="h-4 w-4 text-muted-foreground" />
                    )}
                  </Button>
                  <span className="font-medium truncate" title={business.name}>
                    {business.name}
                  </span>
                </div>
                <div className="col-span-3 text-sm">
                  {business.display_phone_number ? (
                    <div>
                      <div className="text-muted-foreground">{business.display_phone_number}</div>
                      {business.phone_id && <div className="text-xs text-muted-foreground">{business.phone_id}</div>}
                    </div>
                  ) : (
                    '—'
                  )}
                </div>
                <div className="col-span-2">
                  <Badge
                    variant={business.platform_type == 'CLOUD_API' ? 'outline' : 'secondary'}
                    className={business.platform_type == 'CLOUD_API' ? 'text-green-600 bg-green-50' : ''}
                  >
                    {business.platform_type == 'CLOUD_API' ? 'Connected' : 'Not Connected'}
                  </Badge>
                </div>
                <div className="col-span-2 text-sm text-muted-foreground">{dayjs(business.created).format('DD MMM YYYY')}</div>
              </Link>
            ))}
          </div>
        </div>
      )}
    </div>
  );
};

export default Business;
