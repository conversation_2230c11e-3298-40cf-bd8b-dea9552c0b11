import { Card, CardContent } from '@/components/ui/card';
import { ArrowUpDown, Filter, Grid, List, Search, StarOff } from 'lucide-react';

const Loading = () => {
  return (
    <div className="animate-pulse p-6">
      <div className="font-bold text-2xl my-3 w-full">Select your business</div>
      {/* Header skeleton */}
      <div className="flex flex-col md:flex-row gap-4 mb-6">
        <div className="relative flex-1">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-300 h-4 w-4" />
          <div className="h-10 bg-gray-200 rounded-md pl-10 w-full"></div>
        </div>
        <div className="flex gap-2">
          <div className="h-10 bg-gray-200 rounded-md w-[180px] flex items-center px-3">
            <Filter className="h-4 w-4 mr-2 text-gray-300" />
            <div className="h-4 bg-gray-300 rounded w-24"></div>
          </div>
          <div className="h-10 bg-gray-200 rounded-md w-24 flex items-center justify-center px-3">
            <ArrowUpDown className="h-4 w-4 mr-2 text-gray-300" />
            <div className="h-4 bg-gray-300 rounded w-10"></div>
          </div>
          <div className="flex bg-gray-200 rounded-md p-1">
            <div className="p-2 rounded bg-gray-100">
              <Grid className="h-4 w-4 text-gray-400" />
            </div>
            <div className="p-2">
              <List className="h-4 w-4 text-gray-300" />
            </div>
          </div>
        </div>
      </div>
      {/* Showing X of Y businesses skeleton */}
      <div className="mt-4 text-sm text-gray-300 mb-4">
        <div className="h-4 bg-gray-200 rounded w-48"></div>
      </div>
      {/* Grid skeleton */}
      <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-3 xl:grid-cols-4 gap-4">
        {[...Array(8)].map((_, index) => (
          <Card key={index} className="overflow-hidden bg-gray-100 border-gray-200">
            <div className="p-4 h-full flex flex-col">
              <CardContent className="p-0 flex-1 flex flex-col">
                <div className="flex justify-between items-start">
                  <div className="h-6 bg-gray-300 rounded w-3/4"></div>
                  <div className="h-8 w-8 bg-gray-200 rounded-md flex items-center justify-center">
                    <StarOff className="h-5 w-5 text-gray-300" />
                  </div>
                </div>
                <div className="flex-1 mt-2 space-y-2">
                  <div className="h-4 bg-gray-300 rounded w-1/2"></div>
                  <div className="h-3 bg-gray-300 rounded w-1/3"></div>
                </div>
                <div className="mt-4 flex justify-between items-center">
                  <div className="h-6 bg-gray-200 rounded-md w-24"></div>
                  <div className="h-4 bg-gray-300 rounded w-20"></div>
                </div>
              </CardContent>
            </div>
          </Card>
        ))}
      </div>
    </div>
  );
};

export default Loading;
