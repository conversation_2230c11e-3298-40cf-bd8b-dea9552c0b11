import HeaderOnlyProfile from './component/HeaderOnlyProfile';
import { server_component_pb } from '@/state/pb/server_component_pb';
import { User } from '@/lib/types';

export default async function AuthLayout({
  children, // will be a page or nested layout
}: {
  children: React.ReactNode;
}) {
  const { pb, cookies } = await server_component_pb();
  const user = pb.authStore.record as User;
  return (
    <div className="flex h-dvh">
      <div className="min-h-dvh flex flex-col w-full">
        {/* Include shared UI here e.g. a header or sidebar */}
        <HeaderOnlyProfile user={user} user_avatar={user?.avatar} />
        <div className="flex-1 h-full overflow-y-auto">{children}</div>
      </div>
    </div>
  );
}
