'use client';

import { useEffect, useState } from 'react';
import introJs from 'intro.js';
import 'intro.js/introjs.css';

import 'intro.js/themes/introjs-nassim.css';
import { usePathname, useSearchParams } from 'next/navigation';

export const OnboardingFile = (account_id: any) => {
  const [intro, setIntro] = useState(introJs());
  const [sideBarOption, setSideBarOption] = useState(1);
  const [stepNumber, setStepNumber] = useState(1);

  const path = usePathname();
  const queryParams = useSearchParams();

  useEffect(() => {
    intro.exit(true);
    const timer = setTimeout(() => {
      // intro.exit()

      if (path == `/${account_id.account_id}/dashboard`) {
        startIntro([
          {
            element: '#templates',
            intro: 'Click this button to create new template',
            number: 2,
          },
        ]);
      } else if (path == `/${account_id.account_id}/templates` && !queryParams.has('sent')) {
        startIntro([
          {
            element: '#create-template1',
            intro: 'Click this button to create new template',
            number: 2,
          },
        ]);
      } else if (path == `/${account_id.account_id}/templates` && queryParams.has('sent')) {
        startIntro([
          {
            element: '#contacts',
            intro: 'The template has been sent for approval.. Click "contact" button to create contacts and list',
            number: 2,
          },
        ]);
      } else if (path == `/${account_id.account_id}/templates/manage/create-new` && !queryParams.has('category')) {
        startIntro([
          {
            element: '#enter-template-name',
            intro: 'Enter template name',
            number: 3,
          },
          {
            element: '#continue-template1',
            intro: 'Click continue',
            number: 4,
          },
        ]);
      } else if (path.includes(`/${account_id.account_id}/templates/manage/create-new`) && queryParams.has('category')) {
        startIntro([
          {
            element: '#select-header',
            intro: 'Select a title or choose which type of media you will use for this header.',
            tooltipClass: 'custom-left-tooltip',
            number: 5,
          },
          {
            element: '#enter-message',
            intro: 'Enter message text',
            number: 6,
          },
          {
            element: '#message-preview',
            intro: 'Here you can see how your message template will look like.',
            number: 7,
          },
          {
            element: '#footer',
            intro: 'you can add a footer if you want',
            number: 8,
          },
          {
            element: '#button',
            intro: 'you can add a button if you want',
            number: 9,
          },
          {
            element: '#submit',
            intro: 'click the submit button',
            number: 10,
          },
        ]);
      } else if (path == `/${account_id.account_id}/lead-management`) {
        startIntro([
          {
            element: '#upload-contact',
            intro:
              'You can create contacts manually by clicking "Add Contact" or you can upload contacts from an excel file by clicking "Bulk Import". Then click "Lists" to create a list of contacts to send a campaign to. ',
            number: 11,
          },
        ]);
      } else if (path == `/${account_id.account_id}/lead-management/add-contact`) {
        startIntro([
          {
            element: '#create-contact',
            intro: 'Click Add contact button once you have filled the deatils.',
            number: 12,
          },
        ]);
      } else if (path == `/${account_id.account_id}/upload-contact` && !queryParams.has('upload')) {
        startIntro([
          {
            element: '#choose-file',
            intro: 'Select a file to upload contacts from',
            number: 13,
          },
        ]);
      } else if (path == `/${account_id.account_id}/upload-contact` && queryParams.has('upload')) {
        startIntro([
          {
            element: '#map-headers',
            intro:
              'Please map the columns from your uploaded file to the corresponding database headers. For each column in your file, select the appropriate database header from the dropdown list.',
            number: 14,
          },
          {
            element: '#back',
            intro: 'If you want to change file click back.',
            number: 15,
          },
          {
            element: '#upload-file',
            intro: 'click to upload contacts',
            number: 16,
          },
        ]);
      }
      // else if (path == `/${account_id.account_id}/lead-management`) {
      // startIntro([{
      //     element: '#map-headers',
      //     intro: 'Please map the columns from your uploaded file to the corresponding database headers. For each column in your file, select the appropriate database header from the dropdown list.',
      //     number: 14
      //   },
      //   {
      //     element: '#back',
      //     intro: 'If you want to change file click back.',
      //     number: 15
      //   },
      //   {
      //     element: '#upload-file',
      //     intro: 'click to upload contacts',
      //     number: 16
      //   }])
      // }
      else if (path == `/${account_id.account_id}/list` && !queryParams.has('list_created')) {
        startIntro([
          {
            element: '#create-list',
            intro: 'Click to create a new list',
            number: 17,
          },
        ]);
      } else if (path == `/${account_id.account_id}/list/create-list`) {
        startIntro([
          {
            element: '#list-filter',
            intro: 'You can filter out contacts based on name,phone number,tags and status',
            number: 18,
          },
          {
            element: '#list-name',
            intro: 'Enter list name',
            number: 19,
          },
          {
            element: '#create-list',
            intro: 'Click to create list after you have selected the contacts',
            number: 20,
          },
        ]);
      } else if (path == `/${account_id.account_id}/list` && queryParams.has('list_created')) {
        startIntro([
          {
            element: '#campaign',
            intro: 'The list has been sent created.. Click "campaign" button to create and send campaign',
            number: 21,
          },
        ]);
      } else if (path == `/${account_id.account_id}/campaign`) {
        startIntro([
          {
            element: '#create-campaign',
            intro: 'Click this button to create a new campaign',
            number: 22,
          },
        ]);
      } else if (path == `/${account_id.account_id}/campaign/create`) {
        startIntro([
          {
            element: '#campaign_name',
            intro: 'Enter the name of the campaign',
            number: 23,
          },
          {
            element: '#select-template-campaign',
            intro: 'Click this button to select a template',
            number: 24,
          },
        ]);
      } else if (path == `/${account_id.account_id}/campaign/create/template`) {
        startIntro([
          {
            element: '#select-list-campaign',
            intro: 'After selecting a template,Click this button to select a list',
            number: 25,
          },
        ]);
      } else if (
        !path.includes(`/${account_id.account_id}/campaign/create/template`) &&
        path.includes(`/${account_id.account_id}/campaign/create/`) &&
        !queryParams.has('list')
      ) {
        startIntro([
          {
            element: '#list-campaign-next',
            intro: 'After selectinng a list, Click Next',
            number: 26,
          },
        ]);
      } else if (path.includes(`/${account_id.account_id}/campaign/create/`) && queryParams.has('list')) {
        startIntro([
          {
            element: '#schedule-campaign',
            intro:
              'Schedule your campaign, select "send now" option if you want to send the campaign right now or select "schedule" option if you want to send the campaign later',
            number: 27,
          },
          {
            element: '#send-campaign',
            intro: 'Click this to send the campaign',
            number: 28,
          },
        ]);
      }

      // startIntro([
      //     {
      //         element: '#dashboard-zzz',
      //         intro: 'dashboard intro called',
      //         number: 2
      //     }
      // ]);
    }, 2000);

    return () => clearTimeout(timer);
  }, [path, queryParams]);

  const startIntro = (steps: any) => {
    intro.setOptions({ steps });

    intro.start();
  };

  return <div></div>;
};
