// "use client"

// import { createContext, useContext, useState } from "react"
// import introJs from "intro.js"
// import "intro.js/introjs.css"
// // import "./intro-custom.css"
// import "intro.js/themes/introjs-nassim.css"

// const OnboardingContext = createContext()

// export const useOnboarding = () => {
//   return useContext(OnboardingContext)
// }

// export const OnboardingProvider = ({ children }) => {
//   const [intro, setIntro] = useState(introJs())
//   const [sideBarOption, setSideBarOption] = useState(1)
//   const [stepNumber, setStepNumber] = useState(1);

//   const sideBarOption_setter = (value) => {
//     setSideBarOption(value)
//   }

//   const startIntro = (steps, onDone) => {

//     intro.setOptions({ steps })
//     // if (onNext) {
//     //   intro.onchange(onNext)
//     // }
//     if (onDone) {
//       intro.oncomplete(onDone)
//     }
//     intro.start()
//   }

//   // const startIntro = (steps, onDone) => {
//   //   intro.setOptions({
//   //     steps: steps?.map((step, index) => {

//   //       setSideBarOption(step.number + 1)
//   //       return {
//   //         ...step
//   //         // ...step,
//   //         // tooltipClass: step.tooltipClass || '',
//   //       }
//   //     }),

//   //   });
//   //   // if (onDone) {
//   //   //   intro.oncomplete(onDone)
//   //   // }
//   //   intro.start()
//   // }

//   return (
//     <OnboardingContext.Provider value={{ startIntro, sideBarOption_setter, sideBarOption }} >
//       {children}
//     </OnboardingContext.Provider>
//   )
// }
