import { updateAnyCollectionByIds } from '@/lib/pocket';

export const dynamic = 'force-dynamic';
export const fetchCache = 'default-no-store';

// Create a simple async Next.js API route handler
export async function POST(req: Request, res: Response) {
  try {
    const body = await req.json();
    const { ids, collectionName, field } = body;
    // POST call
    const res = await updateAnyCollectionByIds(ids, collectionName, field);
    return new Response(null, {
      status: 200,
      headers: { 'Content-Type': 'text/plain' },
    });
  } catch (e) {
    console.log(e);
    return new Response('error', {
      status: 500,
      headers: { 'Content-Type': 'text/plain' },
    });
  }
}
