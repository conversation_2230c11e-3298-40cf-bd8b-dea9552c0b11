import { create_lead_and_add_to_list, getAccountByToken } from '@/lib/pocket';
import { Account, IExpandedAPISettings } from '@/lib/types';
import { Logger } from 'next-axiom';

export async function POST(request: Request) {
  const log = new Logger();
  const data = await request.json();
  const headers = request.headers;
  const authorization = headers.get('Authorization');
  const bearerToken: any = authorization?.split('Bearer ')[1];

  const apiKey: IExpandedAPISettings | null = await getAccountByToken(bearerToken ?? '');
  if (!apiKey) {
    console.log('body is ');
    console.log(data);
    log.error('error occured with status 401', { error: 'Unauthorized' });
    return new Response('Unauthorized', { status: 401 });
  }
  const account: Account = apiKey.expand?.account;

  const response = await create_lead_and_add_to_list(account, data);
  if (response?.status == 404) {
    console.log('body is ');
    console.log(data);
    log.error('error occured with status 404', { error: response.message });
  } else {
    console.log('body is ');
    console.log(data);
    log.info('success', { message: response?.message });
  }
  if (response) return new Response(response.message, { status: response.status });
}

// sample body(json) to be passed from postman:
// {
//   "name":"Umair Khan",
//   "phone_number":"+************",
//   "list_name":"customers_list"
// }
