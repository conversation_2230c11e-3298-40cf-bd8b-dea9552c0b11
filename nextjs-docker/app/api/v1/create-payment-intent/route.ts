import { getAccount, updateAccountStripeCustomerId } from '@/lib/pocket';
import { NextResponse } from 'next/server';
import Stripe from 'stripe';

const stripe = new Stripe(process.env.STRIPE_SECRET_KEY!, {});

export async function POST(request: Request) {
  const data = await request.json();

  try {
    const { amount, accountId } = data;

    // Get account details
    const account = await getAccount(accountId);
    if (!account) {
      return new Response('Account not found', { status: 404 });
    }

    let customerId = account.stripe_customer_id;

    if (!customerId) {
      const customer = await stripe.customers.create({
        name: account.name,
        email: account?.expand?.pb_user_id?.[0]?.email,
        metadata: {
          account_id: accountId,
        },
      });

      customerId = customer.id;

      // Save the customer ID to the account
      await updateAccountStripeCustomerId(accountId, customerId);
    }

    const paymentIntent = await stripe.paymentIntents.create({
      amount: Number(amount),
      currency: 'aed',
      payment_method_types: ['card'],
      customer: customerId,
      // Add these configurations to ensure payment method attachment works properly
      setup_future_usage: 'off_session', // Allow saving payment method for future use
      metadata: {
        account_id: accountId,
      },
    });

    if (!paymentIntent.client_secret) {
      return new Response('Failed to create payment intent', { status: 500 });
    }

    return new Response(
      JSON.stringify({
        clientSecret: paymentIntent.client_secret,
        customerId,
      }),
      {
        status: 200,
        headers: {
          'Content-Type': 'application/json',
        },
      }
    );
  } catch (error) {
    const errorMessage = (error as Error).message;
    return new Response(errorMessage, { status: 500 });
  }
}
