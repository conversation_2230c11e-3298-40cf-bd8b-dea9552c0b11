import Stripe from 'stripe';
import { getAccount, updateAccountStripeCustomerId } from '@/lib/pocket';

const stripe = new Stripe(process.env.STRIPE_SECRET_KEY!, {});

export async function POST(request: Request) {
  try {
    const data = await request.json();
    const { account_id } = data;

    if (!account_id) {
      return new Response('Account ID is required', { status: 400 });
    }

    // Get account details
    const account = await getAccount(account_id);
    if (!account) {
      return new Response('Account not found', { status: 404 });
    }

    let customerId = account.stripe_customer_id;

    // If customer doesn't exist in Stripe yet, create one
    if (!customerId) {
      const customer = await stripe.customers.create({
        name: account.name,
        email: account?.expand?.pb_user_id?.[0]?.email,
        metadata: {
          account_id: account_id
        }
      });
      
      customerId = customer.id;
      
      // Save the customer ID to the account
      await updateAccountStripeCustomerId(account_id, customerId);
    }

    // Create a SetupIntent to securely collect payment details
    const setupIntent = await stripe.setupIntents.create({
      customer: customerId,
      payment_method_types: ['card'],
      usage: 'off_session', // This allows the payment method to be used for future payments
    });

    return new Response(JSON.stringify({ 
      clientSecret: setupIntent.client_secret,
      customerId: customerId
    }), {
      status: 200,
      headers: {
        'Content-Type': 'application/json',
      },
    });
  } catch (error) {
    const errorMessage = (error as Error).message;
    return new Response(errorMessage, { status: 500 });
  }
}
