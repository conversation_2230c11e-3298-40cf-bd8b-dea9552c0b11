import { NextResponse } from 'next/server';
import Stripe from 'stripe';
import { getAccount, createPayment } from '@/lib/pocket';
import dayjs from 'dayjs';

const stripe = new Stripe(process.env.STRIPE_SECRET_KEY!, {});

export async function POST(request: Request) {
  try {
    const data = await request.json();
    const { account_id, invoices, amount } = data;

    if (!account_id || !invoices || !amount) {
      return new Response('Missing required parameters', { status: 400 });
    }

    // Get account details to retrieve customer information
    const account = await getAccount(account_id);

    if (!account) {
      return new Response('Account not found', { status: 404 });
    }

    // Check if the account has a Stripe customer ID
    if (!account.stripe_customer_id) {
      return new Response('No payment method found for this account', { status: 400 });
    }

    // Get the customer's default payment method
    const customer = await stripe.customers.retrieve(account.stripe_customer_id);

    if (!customer || customer.deleted) {
      return new Response('Customer not found in Stripe', { status: 400 });
    }

    // Get the customer's payment methods
    const paymentMethods = await stripe.paymentMethods.list({
      customer: account.stripe_customer_id,
      type: 'card',
    });

    if (paymentMethods.data.length === 0) {
      return new Response('No payment method found for this customer', { status: 400 });
    }

    // Use the first available payment method (or you could use the default one)
    const paymentMethodId = paymentMethods.data[0].id;

    // Calculate amount in cents (Stripe requires amounts in smallest currency unit)
    const amountInCents = Math.round(amount * 100);

    // Create a payment intent with the specific payment method
    const paymentIntent = await stripe.paymentIntents.create({
      amount: amountInCents,
      currency: account.currency?.toLowerCase() || 'aed',
      customer: account.stripe_customer_id,
      payment_method: paymentMethodId, // Specify the payment method
      payment_method_types: ['card'],
      off_session: true, // This is an off-session payment using saved method
      confirm: true, // Confirm the payment immediately
      description: `Invoice payment - ${invoices.length} invoice(s)`,
      metadata: {
        account_id: account_id,
        invoice_count: invoices.length.toString(),
      },
    });

    // If payment is successful
    if (paymentIntent.status === 'succeeded') {
      // Record the payment in our system (without triggering redirects)
      await createPayment(
        'saved-method',
        account_id,
        invoices,
        'success',
        undefined, // paymentError
        undefined, // paymentDate - don't provide this to avoid admin payment redirect
        undefined, // accountId - don't provide this to avoid redirect
        paymentIntent.id // payRef
      );

      return new Response(
        JSON.stringify({
          success: true,
          message: 'Payment processed successfully',
          payment_id: paymentIntent.id,
        }),
        {
          status: 200,
          headers: {
            'Content-Type': 'application/json',
          },
        }
      );
    } else if (paymentIntent.status === 'requires_action') {
      // Payment requires additional action (like 3D Secure)
      return new Response(
        JSON.stringify({
          success: false,
          requires_action: true,
          client_secret: paymentIntent.client_secret,
          message: 'Payment requires additional authentication',
        }),
        {
          status: 200,
          headers: {
            'Content-Type': 'application/json',
          },
        }
      );
    } else {
      // Payment failed for some other reason
      await createPayment('saved-method', account_id, invoices, 'failed', `Payment failed with status: ${paymentIntent.status}`);

      return new Response(
        JSON.stringify({
          success: false,
          message: `Payment failed with status: ${paymentIntent.status}`,
        }),
        {
          status: 400,
          headers: {
            'Content-Type': 'application/json',
          },
        }
      );
    }
  } catch (error: any) {
    const errorMessage = error.message || 'Unknown error occurred during payment processing';

    return new Response(
      JSON.stringify({
        success: false,
        message: errorMessage,
      }),
      {
        status: 500,
        headers: {
          'Content-Type': 'application/json',
        },
      }
    );
  }
}
