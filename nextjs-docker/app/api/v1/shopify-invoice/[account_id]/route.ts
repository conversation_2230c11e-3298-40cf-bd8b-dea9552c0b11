import {
  addMessage,
  create_lead_and_add_to_list,
  createShopifyOrder,
  getAccount,
  getShopifyInvoiceTemplate,
  increaseMessageLimitQuota,
  markMessageAsFailed,
  markMessageAsSentAndAddWAMID,
} from '@/lib/pocket';
import { PATHS } from '@/lib/utils';
import { send_template } from '@/lib/wa';

export async function POST(request: Request, { params }: { params: { account_id: string } }) {
  //get account id from webhook
  const account_id = params.account_id;
  //get data from webhook
  const data = await request.json();
  const account = await getAccount(account_id);
  //   const leadData = {
  //     name: '<PERSON><PERSON>',
  //     phone_number: '+************',
  //     list_name: 'Shopify Orders',
  //   };
  //get lead data from response obkject
  const leadData = {
    name: data?.customer?.first_name + ' ' + data?.customer?.last_name,
    phone_number: data?.customer?.default_address?.phone,
    list_name: 'Shopify Orders',
  };

  const response = await create_lead_and_add_to_list(account, leadData);
  if (response?.status == 404 || !response.lead) {
    console.log(response.message);
    return new Response(response.message, { status: response.status });
  }

  const shopifyOrderInvoice = await createShopifyOrder(account_id, data);

  const shopifyTemplate = await getShopifyInvoiceTemplate(account_id);

  const parameters = [leadData.name, data.name, `${PATHS[process.env.NODE_ENV]}/shopify-invoice/${shopifyOrderInvoice.id}`];

  const message = await addMessage(
    response?.lead.id,
    shopifyTemplate?.expand?.invoice_template.template_name ?? 'Shopify Invoice Template',
    'agent',
    account_id,
    null,
    'pending',
    shopifyTemplate?.expand?.invoice_template.id
  );

  const tempComponents = [
    {
      type: 'body',
      parameters: parameters.map((param) => {
        return {
          type: 'text',
          text: param,
        };
      }),
    },
  ];

  const res = await send_template({
    recipient_number: response?.lead?.phone_number,
    template: shopifyTemplate?.expand?.invoice_template?.template_name,
    phoneId: account.phone_id,
    components: tempComponents,
    templateLang: shopifyTemplate?.expand?.invoice_template?.template_body.language,
  });
  if ('error' in res) {
    await markMessageAsFailed(message, res.error.message);
    await increaseMessageLimitQuota(message);
  } else {
    await markMessageAsSentAndAddWAMID(message, res);
  }
  return new Response('working', { status: 200 });
}
