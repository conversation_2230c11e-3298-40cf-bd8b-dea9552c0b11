import { handleSubmit } from '@/lib/actions';
import {
  addLead,
  addMessage,
  getAccountByToken,
  getLeadByPhone,
  getTemplateFromDbByTemplateName,
  increaseMessageLimitQuota,
  markMessageAsFailed,
  markMessageAsSentAndAddWAMID,
  upsertConversation,
} from '@/lib/pocket';
import { type Account, IExpandedAPISettings } from '@/lib/types';
import { send_template } from '@/lib/wa';

interface IAPIBody {
  message?: string;
  to: string;
  template_name?: string;
  parameters: string[];
}

export async function POST(req: Request, res: Response) {
  try {
    const body: IAPIBody = await req.json();
    const headers = req.headers;
    const authorization = headers.get('Authorization');
    const bearerToken = authorization?.split('Bearer ')[1];

    const apiKey: IExpandedAPISettings | null = await getAccountByToken(bearerToken ?? '');
    if (!apiKey) {
      return new Response('Unauthorized', { status: 401 });
    }
    const account: Account = apiKey.expand?.account;
    const { message, to, template_name, parameters } = body;

    let lead = await getLeadByPhone(to, account.id);
    if (!lead) {
      // add lead
      lead = await addLead(to, account.id);
    }

    // get conversation
    const conversation = await upsertConversation(lead.id, account.id);

    if (template_name) {
      const template = await getTemplateFromDbByTemplateName({ account, template_name });
      const message = await addMessage(lead.id, template.template_name, 'agent', account.id, null, 'pending', template.id, null, null, null, null, "API");
      const tempComponents = [
        {
          type: 'body',
          parameters: (parameters ?? [])?.map((param) => {
            return {
              type: 'text',
              text: param,
            };
          }),
        },
      ];
      const res = await send_template({
        recipient_number: lead?.phone_number,
        template: template?.template_name,
        phoneId: account.phone_id,
        components: tempComponents,
        templateLang: template?.template_body.language,
      });
      if ('error' in res) {
        await markMessageAsFailed(message, res.error.message);
        await increaseMessageLimitQuota(message);
      } else {
        await markMessageAsSentAndAddWAMID(message, res);
      }
      // TODO: uncomment this line
      // await Promise.all([updateAccountRemainingLimit(account.id, 1), updateUserRemainingLimit(user.id, 1, accountId)]);
      return new Response(JSON.stringify(res, null, 2), { status: 200 });
    } else if (message) {
      const result = await sendTextMessage({
        message,
        leadId: lead.id,
        convoId: conversation.id,
        phoneNo: to,
        phoneId: account.phone_id,
        accountId: account.id,
      });
      return new Response(JSON.stringify(result, null, 2), { status: result?.status ?? 400 });
    }
    return new Response('Missing parameters', { status: 400 });
  } catch (error) {
    console.error(error);
    return new Response('Error', { status: 500 });
  }
}

const sendTextMessage = async ({
  message,
  leadId,
  convoId,
  phoneNo,
  phoneId,
  accountId,
  deliveryStatus,
}: {
  message: string;
  leadId: string;
  convoId: string;
  phoneNo: string;
  phoneId: string;
  accountId: string;
  deliveryStatus?: string;
}) => {
  const map = new Map();
  map.set('message', message);
  return await handleSubmit(
    {
      leadId,
      convoId,
      phoneNo,
      phoneId,
      accountId,
      deliveryStatus,
    },
    map
  );
};
