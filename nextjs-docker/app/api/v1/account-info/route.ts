// API route for fetching account information
import { getAccount } from '@/lib/pocket';

export async function GET(request: Request) {
  try {
    // Get the account_id from the query parameters
    const { searchParams } = new URL(request.url);
    const account_id = searchParams.get('account_id');

    if (!account_id) {
      return new Response('Account ID is required', { status: 400 });
    }

    // Get account details
    const account = await getAccount(account_id);
    if (!account) {
      return new Response('Account not found', { status: 404 });
    }

    // Return only the necessary information
    return new Response(JSON.stringify({
      id: account.id,
      name: account.name,
      stripe_customer_id: account.stripe_customer_id || null,
      currency: account.currency || 'USD',
      subscription_active: account.subscription_active
    }), {
      status: 200,
      headers: {
        'Content-Type': 'application/json',
      },
    });
  } catch (error) {
    const errorMessage = (error as Error).message;
    return new Response(errorMessage, { status: 500 });
  }
}
