import { PATHS } from '@/lib/utils';
import * as React from 'react';
import { Resend } from 'resend';
import { EmailTemplate } from '@/components/email-template';

// adding resend api key
const resend = new Resend(`re_2soPDivu_HVen2oemd51PwNbnx7Xaa7ui`);

export async function POST(email: string, resetToken: any, userId: string, username: string) {
  try {
    const { data, error } = await resend.emails.send({
      from: 'Wetarseel.ai <<EMAIL>>',
      // to: ['<EMAIL>'],
      to: [email],
      subject: 'Hello world',
      react: EmailTemplate({
        firstName: username,
        resetLink: `${PATHS[process.env.NODE_ENV]}/auth/reset?page=2&id=${userId}&rt=${resetToken}`,
      }) as React.ReactElement,
    });

    if (error) {
      return Response.json({ error }, { status: 500 });
    }

    return Response.json({ data });
  } catch (error) {
    return Response.json({ error }, { status: 500 });
  }
}
