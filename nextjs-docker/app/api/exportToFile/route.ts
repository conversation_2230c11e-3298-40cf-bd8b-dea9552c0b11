import { exportToFile, getAnyCollectionIdsByCollectionName } from '@/lib/pocket';
import { NextResponse } from 'next/server';

export const dynamic = 'force-dynamic';
export const fetchCache = 'default-no-store';

export async function POST(req: Request) {
  try {
    const res = await exportToFile();
    return Response.json(res, { status: 200 });
  } catch (error) {
    return NextResponse.json({ error: error }, { status: 500 });
  }
}
