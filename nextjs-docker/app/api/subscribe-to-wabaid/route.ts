import { unstable_noStore as noStore } from 'next/cache';
import { NextRequest } from 'next/server';

export const dynamic = 'force-dynamic';

export async function GET(req: NextRequest) {
  noStore();
  const APP_ID = process.env.NEXT_PUBLIC_APP_ID ?? '';
  const SYSTEM_TOKEN = process.env.CLOUD_API_ACCESS_TOKEN ?? '';
  const BUSINESS_ID = process.env.BUSINESS_ID ?? '';
  const VERSION = process.env.CLOUD_API_VERSION ?? '';
  const WABA_ID = req.nextUrl.searchParams.get('waba_id');
  try {
    let url = new URL(`https://graph.facebook.com/${VERSION}/${WABA_ID}/subscribed_apps?access_token=${SYSTEM_TOKEN}`);
    let response = await fetch(url, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
    });
    const data = await response.json();
    return Response.json(data);
  } catch (e) {
    console.error(e);
    return Response.error();
  }
}
