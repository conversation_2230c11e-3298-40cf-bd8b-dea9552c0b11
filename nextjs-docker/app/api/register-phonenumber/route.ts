import { unstable_noStore as noStore } from 'next/cache';
import { NextRequest } from 'next/server';

export const dynamic = 'force-dynamic';

export async function GET(req: NextRequest) {
  noStore();
  const APP_ID = process.env.NEXT_PUBLIC_APP_ID ?? '';
  const SYSTEM_TOKEN = process.env.CLOUD_API_ACCESS_TOKEN ?? '';
  const BUSINESS_ID = process.env.BUSINESS_ID ?? '';
  const VERSION = process.env.CLOUD_API_VERSION ?? '';
  const PHONE_NUMBER_ID = req.nextUrl.searchParams.get('phone_number_id');
  const pin = req.nextUrl.searchParams.get('pin');
  try {
    const body = {
      messaging_product: 'whatsapp',
      pin,
    };
    let url = new URL(`https://graph.facebook.com/${PHONE_NUMBER_ID}/register?access_token=${SYSTEM_TOKEN}`);
    let response = await fetch(url, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(body),
    });
    const data = await response.json();
    return Response.json(data);
  } catch (e) {
    console.error(e);
    return Response.error();
  }
}
