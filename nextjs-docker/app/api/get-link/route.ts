import { getPb } from '@/lib/pocket';
import { redirect } from 'next/navigation';
import { NextRequest } from 'next/server';

export async function GET(req: NextRequest) {
  const pb = await getPb();
  let url = req.nextUrl.searchParams.get('url') as string;
  let campaignId = req.nextUrl.searchParams.get('campaignId') as string;

  if (!url.startsWith('https')) {
    url = 'https://' + url;
  }
  const campaign = await pb.collection('campaigns').getOne(campaignId);
  let clickState = campaign.click_stats;
  if (!clickState) {
    clickState = {};
  }
  if (!clickState[url]) {
    clickState[url] = 0;
  }
  clickState[url] += 1;
  pb.collection('campaigns').update(campaignId, { click_stats: clickState });
  return redirect(url);
}
