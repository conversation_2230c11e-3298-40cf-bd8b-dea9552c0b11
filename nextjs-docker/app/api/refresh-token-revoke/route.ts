import { unstable_noStore as noStore } from 'next/cache';
import { NextRequest } from 'next/server';

export const dynamic = 'force-dynamic';

export async function GET(req: NextRequest) {
  noStore();
  const APP_ID = process.env.NEXT_PUBLIC_APP_ID ?? '';
  const APP_SECRET = process.env.APP_SECRET ?? '';
  const VERSION = process.env.CLOUD_API_VERSION ?? '';
  try {
    const url = new URL(`https://graph.facebook.com/${VERSION}/oauth/revoke?`);

    //TODO: Client_id and client_secret are to be stored in .env
    url.searchParams.append('client_id', APP_ID);
    url.searchParams.append('client_secret', APP_SECRET);
    url.searchParams.append('revoke_token', req.nextUrl.searchParams.get('access_token') ?? '');
    url.searchParams.append('access_token', req.nextUrl.searchParams.get('access_token') ?? '');
    const response = await fetch(url, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
    });
    return Response.json(await response.json());
  } catch (e) {
    console.error(e);
    return Response.error();
  }
}
