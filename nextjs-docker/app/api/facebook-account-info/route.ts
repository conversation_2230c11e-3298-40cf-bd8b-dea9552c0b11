import { NextRequest } from 'next/server';
import { unstable_noStore as noStore } from 'next/cache';
import { getAccount } from '@/lib/pocket';
import { Account } from '@/lib/types';
import { server_component_pb } from '@/state/pb/server_component_pb';
import dayjs from 'dayjs';

export const dynamic = 'force-dynamic';

/**
 * API route to fetch connected Facebook and WhatsApp account information
 *
 * @param req - NextRequest object containing account_id parameter
 * @returns Response with Facebook and WhatsApp account data or error
 */
export async function GET(req: NextRequest) {
  noStore();

  // Get authentication information
  const { pb, cookies } = await server_component_pb();
  const user = pb.authStore.record;
  if (!user) {
    return Response.json({ error: 'User not authenticated' }, { status: 401 });
  }

  const SYSTEM_TOKEN = process.env.CLOUD_API_ACCESS_TOKEN ?? '';
  const VERSION = process.env.CLOUD_API_VERSION ?? '';

  try {
    // Extract parameters from the request
    const accountId = req.nextUrl.searchParams.get('account_id') ?? '';

    if (!accountId) {
      return Response.json({ error: 'Account ID is required' }, { status: 400 });
    }

    // Get account details from the database
    const account = await getAccount(accountId);

    if (!account) {
      return Response.json({ error: 'Account not found' }, { status: 404 });
    }

    // If we don't have an access token or WABA ID, the account is not connected
    if (!account.access_token || !account.waba_id) {
      return Response.json({
        connected: false,
        message: 'Facebook account not connected',
      });
    }

    // Check if token is expired
    const expiry_date = dayjs(account.expires_at);
    const now = dayjs();
    const expires_in_diff = expiry_date.diff(now, 'days');
    const isExpired = expires_in_diff <= 0;

    // If we have a WABA ID, fetch phone numbers associated with it
    let phoneData = null;
    if (account.waba_id) {
      try {
        const url = new URL(`https://graph.facebook.com/${VERSION}/${account.waba_id}/phone_numbers`);
        url.searchParams.append('access_token', SYSTEM_TOKEN);

        const response = await fetch(url, {
          method: 'GET',
          headers: {
            'Content-Type': 'application/json',
          },
        });

        const phoneResponse = await response.json();
        if (phoneResponse && phoneResponse.data) {
          // Find the phone that matches our stored phone_id
          phoneData = phoneResponse.data.find((phone: any) => phone.id === account.phone_id) || null;
        }
      } catch (error) {
        console.error('Error fetching phone data:', error);
      }
    }

    // Get WhatsApp business app status
    let bizAppStatus = null;
    if (account.waba_id) {
      try {
        const url = new URL(`https://graph.facebook.com/${VERSION}/${account.waba_id}`);
        url.searchParams.append('fields', 'is_on_biz_app,platform_type');
        url.searchParams.append('access_token', SYSTEM_TOKEN);

        const response = await fetch(url, {
          method: 'GET',
          headers: {
            'Content-Type': 'application/json',
          },
        });

        bizAppStatus = await response.json();
      } catch (error) {
        console.error('Error fetching business app status:', error);
      }
    }

    // Format and return the response
    return Response.json({
      connected: true,
      account: {
        name: account.name,
        waba_id: account.waba_id,
        phone_id: account.phone_id,
        display_phone_number: account.display_phone_number,
        expires_at: account.expires_at,
        expires_in_days: expires_in_diff,
        token_expired: isExpired,
        quality_rating: account.quality_rating,
        platform_type: account.platform_type,
        code_verification_status: account.code_verification_status,
      },
      phone_details: phoneData,
      business_app_status: bizAppStatus,
    });
  } catch (error) {
    console.error('Error fetching Facebook account info:', error);
    return Response.json(
      {
        error: 'Failed to fetch Facebook account information',
        details: error,
      },
      { status: 500 }
    );
  }
}
