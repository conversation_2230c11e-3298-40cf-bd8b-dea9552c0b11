import { getUserAccountList, updateAccessTokenOfAccount } from '@/lib/pocket';
import { Account } from '@/lib/types';
import { server_component_pb } from '@/state/pb/server_component_pb';
import { NextRequest } from 'next/server';
import { unstable_noStore as noStore } from 'next/cache';

interface scopeInterface {
  scope: string;
  target_ids: [];
}

export const dynamic = 'force-dynamic';

export async function GET(req: NextRequest) {
  noStore();
  const { pb, cookies } = await server_component_pb();
  const user = pb.authStore.record;
  if (!user) {
    throw new Error('user not authetnicated');
  }
  const SYSTEM_TOKEN = process.env.CLOUD_API_ACCESS_TOKEN ?? '';
  const VERSION = process.env.CLOUD_API_VERSION ?? '';
  try {
    const accountId = req.nextUrl.searchParams.get('accountId') ?? '';
    const input_token = req.nextUrl.searchParams.get('input_token') ?? '';
    const url2 = new URL(`https://graph.facebook.com/${VERSION}/debug_token`);
    url2.searchParams.append('input_token', input_token);
    url2.searchParams.append('access_token', SYSTEM_TOKEN);
    const response2 = await fetch(url2, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
    });
    const data2 = await response2.json();
    const granularScope = data2.data.granular_scopes.find((scopeObj: scopeInterface) => scopeObj.scope === 'whatsapp_business_management');
    const expires_at = data2.data.expires_at;

    //   Get the first target ID in the target_ids array
    const firstTargetId = granularScope ? granularScope.target_ids[0] : null;

    //save waba_id to client business account
    const res = await updateAccessTokenOfAccount(accountId, firstTargetId, input_token, expires_at);
    //@ts-ignore
    if (res?.message) {
      return Response.json(res);
    }
    return Response.json({ waba_id: firstTargetId });
  } catch (e) {
    console.error(e);
    return Response.error();
  }
}
