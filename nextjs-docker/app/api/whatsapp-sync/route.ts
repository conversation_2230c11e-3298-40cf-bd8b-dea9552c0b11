import { NextRequest } from 'next/server';
import { unstable_noStore as noStore } from 'next/cache';
import { server_component_pb } from '@/state/pb/server_component_pb';

export const dynamic = 'force-dynamic';

/**
 * API route to trigger WhatsApp Business data synchronization
 * This endpoint calls the Facebook Graph API to sync SMB app state
 *
 * @param req - NextRequest object containing query parameters (phoneId, accessToken)
 * @returns Response with the sync operation result
 */
export async function POST(req: NextRequest) {
  noStore();

  // Get authentication information
  const { pb, cookies } = await server_component_pb();
  const user = pb.authStore.record;
  if (!user) {
    return Response.json({ error: 'User not authenticated' }, { status: 401 });
  }

  try {
    // Parse request body
    const body = await req.json();
    const { phoneId, accessToken, syncType = 'smb_app_state_sync' } = body;

    if (!phoneId) {
      return Response.json({ error: 'Phone ID is required' }, { status: 400 });
    }

    // Get access token (either from request or system token)
    const token = accessToken || process.env.CLOUD_API_ACCESS_TOKEN;
    if (!token) {
      return Response.json({ error: 'Access token is required' }, { status: 400 });
    }

    const VERSION = process.env.CLOUD_API_VERSION || 'v19.0';

    // Build URL for Facebook Graph API request
    const url = `https://graph.facebook.com/${VERSION}/${phoneId}/smb_app_data`;

    // Validate syncType (only allow supported types)
    if (!['smb_app_state_sync', 'history'].includes(syncType)) {
      return Response.json(
        {
          error: 'Invalid sync type. Supported values: smb_app_state_sync, history',
        },
        { status: 400 }
      );
    }

    // Make the request to Facebook
    const response = await fetch(url, {
      method: 'POST',
      headers: {
        Authorization: `Bearer ${token}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        messaging_product: 'whatsapp',
        sync_type: syncType,
      }),
    });

    // Parse and return the response
    const data = await response.json();

    if (!response.ok) {
      console.error('Error from Facebook API:', data);
      return Response.json(
        {
          error: 'Facebook API error',
          details: data,
        },
        { status: response.status }
      );
    }

    return Response.json({
      success: true,
      data: data,
      message: 'WhatsApp data sync initiated successfully',
    });
  } catch (e) {
    console.error('Error triggering WhatsApp sync:', e);
    return Response.json(
      {
        error: 'Failed to initiate WhatsApp data sync',
        details: e,
      },
      { status: 500 }
    );
  }
}
