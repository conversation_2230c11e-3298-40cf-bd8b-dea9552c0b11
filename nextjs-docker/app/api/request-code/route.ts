import { unstable_noStore as noStore } from 'next/cache';
import { NextRequest } from 'next/server';

export const dynamic = 'force-dynamic';

export async function GET(req: NextRequest) {
  noStore();
  const SYSTEM_TOKEN = process.env.CLOUD_API_ACCESS_TOKEN ?? '';
  const PHONE_NUMBER_ID = req.nextUrl.searchParams.get('phone_number_id');
  try {
    const body = {};
    let url = new URL(`https://graph.facebook.com/${PHONE_NUMBER_ID}/request_code?code_method=SMS&language=en_US&access_token=${SYSTEM_TOKEN}`);
    let response = await fetch(url, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(body),
    });
    const data = await response.json();
    return Response.json(data);
  } catch (e) {
    console.error(e);
    return Response.error();
  }
}
