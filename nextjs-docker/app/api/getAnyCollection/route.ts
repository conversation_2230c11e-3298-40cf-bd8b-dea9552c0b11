import { getAnyCollectionIdsByCollectionName } from '@/lib/pocket';
import { NextResponse } from 'next/server';

export const dynamic = 'force-dynamic';
export const fetchCache = 'default-no-store';

export async function POST(req: Request) {
  try {
    const body = await req.json();
    const filter = body?.filter ?? '';
    const feilds = body?.fields ?? '';
    const expand = body?.expand ?? '';
    if (!body.collectionName) throw new Error('Please add collection name');
    const res = await getAnyCollectionIdsByCollectionName(body.collectionName, filter, feilds, expand);
    return Response.json(res, { status: 200 });
  } catch (error) {
    return NextResponse.json({ error: error }, { status: 500 });
  }
}
