import { NextRequest } from 'next/server';
import { unstable_noStore as noStore } from 'next/cache';
import { server_component_pb } from '@/state/pb/server_component_pb';

interface BizAppStatusResponse {
  is_on_biz_app?: boolean;
  platform_type?: string;
  error?: any;
}

export const dynamic = 'force-dynamic';

/**
 * API route to fetch business app status from Facebook Graph API
 * 
 * @param req - NextRequest object containing query parameters
 * @returns Response with business app status data
 */
export async function GET(req: NextRequest) {
  noStore();
  
  // Get authentication information
  const { pb, cookies } = await server_component_pb();
  const user = pb.authStore.record;
  if (!user) {
    return Response.json({ error: 'User not authenticated' }, { status: 401 });
  }
  
  const SYSTEM_TOKEN = process.env.CLOUD_API_ACCESS_TOKEN ?? '';
  const VERSION = process.env.CLOUD_API_VERSION ?? '';
  
  try {
    // Extract parameters from the request
    const wabaId = req.nextUrl.searchParams.get('waba_id') ?? '';
    
    if (!wabaId) {
      return Response.json({ error: 'WABA ID is required' }, { status: 400 });
    }
    
    // Build URL for Facebook Graph API request
    const url = new URL(`https://graph.facebook.com/${VERSION}/${wabaId}`);
    url.searchParams.append('fields', 'is_on_biz_app,platform_type');
    url.searchParams.append('access_token', SYSTEM_TOKEN);
    
    // Make the request to Facebook
    const response = await fetch(url, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
    });
    
    // Parse and return the response
    const data = await response.json();
    
    const result: BizAppStatusResponse = {
      is_on_biz_app: data.is_on_biz_app,
      platform_type: data.platform_type
    };
    
    return Response.json(result);
  } catch (e) {
    console.error('Error fetching business app status:', e);
    return Response.json({ error: e }, { status: 500 });
  }
}
