/**
 * WhatsApp Webhook API Route
 *
 * This file handles all incoming webhook events from the WhatsApp Business API.
 * It processes various types of events including:
 * - Message template status updates (approved, rejected, paused)
 * - Message template quality updates
 * - Phone number quality/limit updates
 * - Message status updates (sent, delivered, read, failed)
 * - Incoming messages from customers
 *
 * The webhook follows Meta's WhatsApp Business API specifications:
 * https://developers.facebook.com/docs/whatsapp/cloud-api/webhooks
 */
import { recieveMessage } from '@/lib/recieveMessage';
import { IWhatsappHook, WAMessage, WARecieveMessage } from '@/lib/types';
import { Logger } from 'next-axiom';

import {
  increaseMessageLimitQuota,
  markMessageAsDelivered,
  markMessageAsFailedByWhatsappRoute,
  markMessageAsRead,
  markMessageSentByWhatsapp,
  markTemplateAsApproved,
  markTemplateAsApprovedByTemplateName,
  markTemplateAsPaused,
  markTemplateAsRejected,
  updateBusinessLimitStatus,
  updateTemplateCategory,
  updateTemplateQuality,
  storeHistoricalMessages,
  storeContact,
  getPb,
  upsertLead,
  upsertConversation,
} from '@/lib/pocket';
import { NextRequest, NextResponse } from 'next/server';

// Force dynamic rendering for this route to ensure it always processes fresh requests
export const dynamic = 'force-dynamic';
// Disable caching for this webhook endpoint
export const fetchCache = 'default-no-store';

/**
 * GET Handler - Used for webhook verification by WhatsApp
 *
 * WhatsApp requires webhook endpoints to verify via a GET request with specific parameters:
 * - hub.mode: Should be 'subscribe'
 * - hub.verify_token: Our predefined token 'HAPPY'
 * - hub.challenge: A challenge string WhatsApp sends that we must return
 *
 * @param req - The incoming request containing verification params
 * @returns HTTP 200 with challenge value if verified, or 403 if not
 */
export async function GET(req: NextRequest) {
  const mode = req.nextUrl.searchParams.get('hub.mode');
  const token = req.nextUrl.searchParams.get('hub.verify_token');
  const challenge = req.nextUrl.searchParams.get('hub.challenge');
  // check the mode and token sent are correct
  if (mode === 'subscribe' && token === 'HAPPY') {
    // respond with 200 OK and challenge token from the request
    return Response.json(Number(challenge), { status: 200 });
  } else {
    // respond with '403 Forbidden' if verify tokens do not match
    return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
  }
}

/**
 * POST Handler - Processes all incoming webhook events from WhatsApp
 *
 * This is the main handler for all WhatsApp webhook events. It receives notifications about:
 * 1. Message template status changes (approved/rejected/paused)
 * 2. Message template quality updates
 * 3. Phone number quality & messaging limit updates
 * 4. Message delivery status updates
 * 5. Incoming messages from users
 *
 * @param req - The incoming request containing webhook data
 * @returns HTTP 200 on success, 500 on error
 */
export async function POST(req: Request, res: Response) {
  const log = new Logger();
  try {
    const body: IWhatsappHook = await req.json();
    log.info('hook recieved', body);

    // Process all entries and changes in the webhook payload
    for (const entry of body.entry || []) {
      for (const change of entry.changes || []) {
        const field = change.field;
        const value = change.value;

        // SECTION 1: Handle message template status updates (approved/rejected/paused)
        if (field == 'message_template_status_update') {
          const templateChangeEvent = value?.event;

          // Handle APPROVED template event
          if (templateChangeEvent == 'APPROVED') {
            const { message_template_id, message_template_name } = value;
            try {
              // Try to mark the template as approved using its ID
              const res = await markTemplateAsApproved(message_template_id.toString());
            } catch (error) {
              log.error('Error in mark template as approved', {
                error: JSON.stringify(error),
              });
              try {
                // Fallback: try to mark as approved using template name if ID fails
                const res = await markTemplateAsApprovedByTemplateName(message_template_name);
                log.info('marked as approved', res);
              } catch (error) {
                log.error('Error in mark template as approved by name', {
                  error: JSON.stringify(error),
                });
              }
            }
            log.info('marked as approved', res);
          }

          // Handle REJECTED template event
          if (templateChangeEvent == 'REJECTED') {
            const { message_template_id, reason } = value;
            try {
              const res = await markTemplateAsRejected(message_template_id, reason ?? 'Error');
            } catch (error) {
              log.error('Error in mark template as rejected', {
                error: JSON.stringify(error),
              });
            }
            log.info('marked as rejected', res);
          }

          // Handle PAUSED template event
          if (templateChangeEvent == 'PAUSED') {
            const { message_template_id, reason, other_info } = value;
            try {
              //TODO: remove console.log
              console.log(value);
              const res = await markTemplateAsPaused(message_template_id, reason ?? 'Error', other_info.description);
            } catch (error) {
              log.error('Error in mark template as Paused', {
                error: JSON.stringify(error),
              });
            }
          }
        }

        // Handle SMB App State Sync events (contact syncs from WhatsApp Business)
        if (field == 'smb_app_state_sync') {
          log.info('Received smb_app_state_sync event', value);

          const { state_sync, metadata } = value;
          if (state_sync && state_sync.length > 0) {
            for (const syncItem of state_sync) {
              if (syncItem.type === 'contact') {
                try {
                  const business_phone_number_id = metadata?.phone_number_id;

                  if (!business_phone_number_id) {
                    log.error('Missing phone_number_id in smb_app_state_sync metadata');
                    continue;
                  }

                  log.info(`Processing contact sync: ${syncItem.action} - ${syncItem.contact.phone_number}`, {
                    contact: syncItem.contact,
                    action: syncItem.action,
                    business_phone_number_id,
                  });

                  // Store the contact using our storeContact method
                  const result = await storeContact(business_phone_number_id, syncItem);

                  log.info(`Successfully processed contact sync`, {
                    result,
                    phone_number: syncItem.contact.phone_number,
                    action: syncItem.action,
                  });
                } catch (error) {
                  log.error('Error processing contact sync', {
                    error: JSON.stringify(error),
                    syncItem,
                  });
                }
              } else {
                // Log other types of sync events for future implementation
                log.info(`Unhandled sync type: ${syncItem.type}`, syncItem);
              }
            }
          }
        }

        // Handle history events (historical message data from WhatsApp)
        if (field == 'history') {
          log.info('Received history event', { field });

          const { history, metadata } = value;
          if (history && history.length > 0) {
            try {
              const display_phone_number = metadata?.display_phone_number;
              const phone_number_id = metadata?.phone_number_id;

              log.info(`Processing history data for ${display_phone_number}`, {
                phone_number_id,
                historyChunksCount: history.length,
              });

              // Sort history chunks by chunk_order to ensure they're processed in sequence
              const sortedHistoryChunks = [...history].sort((a, b) => {
                return a.metadata.chunk_order - b.metadata.chunk_order;
              });

              log.info('Sorted chunks by chunk_order', {
                originalOrder: history.map((chunk) => chunk.metadata.chunk_order),
                sortedOrder: sortedHistoryChunks.map((chunk) => chunk.metadata.chunk_order),
              });

              // Process each chunk of history data in order
              for (const historyChunk of sortedHistoryChunks) {
                const { metadata: chunkMetadata, threads } = historyChunk;

                log.info(`Processing history chunk ${chunkMetadata.chunk_order}`, {
                  phase: chunkMetadata.phase,
                  progress: chunkMetadata.progress,
                  threadsCount: threads.length,
                });

                // Process each thread (conversation) in the chunk
                for (const thread of threads) {
                  const threadId = thread.id;

                  // Sort messages by timestamp if needed
                  const sortedMessages = [...thread.messages].sort((a, b) => {
                    return parseInt(a.timestamp) - parseInt(b.timestamp);
                  });

                  log.info(`Processing thread ${threadId}`, {
                    messagesCount: sortedMessages.length,
                  });

                  // TODO: Store or process the historical messages
                  // This would typically involve:
                  // 1. Finding the conversation/thread in your database
                  // 2. Storing the historical messages in chronological order
                  // 3. Updating conversation metrics or analytics
                  //
                  // Example (implementation will depend on your data model):
                  await storeHistoricalMessages(phone_number_id, threadId, sortedMessages);
                }
              }

              log.info('Successfully processed history chunk data');
            } catch (error) {
              log.error('Error processing history data', {
                error: JSON.stringify(error),
              });
            }
          }
        }

        // Handle message echoes (messages sent by the business)
        if (field == 'smb_message_echoes') {
          log.info('Received smb_message_echoes event', { field });

          const { message_echoes, metadata } = value;
          if (message_echoes && message_echoes.length > 0) {
            try {
              const display_phone_number = metadata?.display_phone_number;
              const phone_number_id = metadata?.phone_number_id;

              log.info(`Processing message echoes for ${display_phone_number}`, {
                phone_number_id,
                echosCount: message_echoes.length,
              });

              // Process each echo message
              for (const echo of message_echoes) {
                const { from, to, id, timestamp, type } = echo;

                log.info(`Processing echo message ${id}`, {
                  from,
                  to,
                  timestamp,
                  type,
                  messageContent: echo.text?.body || 'Non-text message',
                });

                // TODO: Store or process the echo message
                // This would typically involve:
                // 1. Finding the conversation in your database
                // 2. Storing the outbound message if not already stored
                // 3. Updating conversation metrics or analytics
                //
                // Example (implementation will depend on your data model):
                await storeOutboundMessage(phone_number_id, from, to, id, type, echo);
              }

              log.info('Successfully processed message echoes');
            } catch (error) {
              log.error('Error processing message echoes', {
                error: JSON.stringify(error),
              });
            }
          }
        }

        // SECTION 2: Handle message template quality score updates
        if (field == 'message_template_quality_update') {
          const { previous_quality_score, new_quality_score, message_template_id } = value;
          try {
            log.info(`updating template quality ${message_template_id}`);
            const res = await updateTemplateQuality(message_template_id, previous_quality_score, new_quality_score);
          } catch (e) {
            log.error('Error in update template quality', {
              error: JSON.stringify(e),
            });
          }
        }

        // SECTION 3: Handle phone number quality and messaging limit updates
        if (field == 'phone_number_quality_update') {
          const { event, display_phone_number, current_limit, old_limit } = value;
          try {
            log.info(`updating phone number quality ${display_phone_number}`);
            const res = await updateBusinessLimitStatus(display_phone_number, event ?? '', current_limit, old_limit);
          } catch (e) {
            log.error(`Error in update phone number quality ${display_phone_number}`, {
              error: JSON.stringify(e),
            });
          }
        }

        // Extract message and status information from the webhook payload
        const message: WAMessage[] = value?.messages;
        const { contacts, statuses } = value;

        // SECTION 4: Handle message status updates (sent/delivered/read/failed)
        if (statuses && statuses.length > 0) {
          // Process all status updates in the payload
          for (const statusUpdate of statuses) {
            const id = statusUpdate.id;
            const status = statusUpdate.status;

            log.info(`Processing status update for message ${id}`, {
              status,
              recipient: statusUpdate.recipient_id,
            });

            // Handle 'sent' status updates
            if (status == 'sent') {
              try {
                const res = await markMessageSentByWhatsapp(id, status);
                log.info(`Marked as sent ${id}`);
              } catch (error) {
                log.error(`Error in mark message ${id} sent by whatwsapp`, {
                  error: JSON.stringify(error),
                });
              }
            }

            // Handle 'delivered' status updates
            if (status == 'delivered') {
              try {
                const res = await markMessageAsDelivered(id, status);
                log.info(`Marked as delivered ${id}`);
              } catch (error) {
                log.error(`Error in mark message ${id} as delivered`, {
                  error: JSON.stringify(error),
                });
              }
            }

            // Handle 'read' status updates
            if (status == 'read') {
              try {
                const res = await markMessageAsRead(id, status);
                log.info(`Marked as read ${id}`);
              } catch (error) {
                log.error(`Error in mark message ${id} as read`, {
                  error: JSON.stringify(error),
                });
              }
            }

            // Handle 'failed' status updates
            if (status == 'failed') {
              try {
                log.info(`Message ${id} marked as failed`);

                const errorDetails = statusUpdate.errors?.[0];
                // Standard failure handling for other error codes
                const messageRecord = await markMessageAsFailedByWhatsappRoute(id, errorDetails);

                // If the failed message was a template, we may need to adjust quota
                if (messageRecord.template) {
                  try {
                    log.info('Increasing message limit quota');
                    const res2 = await increaseMessageLimitQuota(messageRecord);
                  } catch (error) {
                    log.error('Error in increasing message limit quota', {
                      error: JSON.stringify(error),
                    });
                  }
                }
              } catch (error) {
                log.error(`Error in mark message ${id} as failed by whatsapp route`, {
                  error: JSON.stringify(error),
                });
              }
            }
          }
        }

        // Extract the business phone number ID for handling incoming messages
        const business_phone_number_id = value?.metadata?.phone_number_id;

        // SECTION 5: Handle incoming messages from users
        if (message && message.length > 0) {
          for (const msg of message) {
            // Check for errors in the message
            const { errors } = msg ?? {};

            if (errors && errors.length > 0) {
              log.error('Incoming message contains errors', {
                errors: JSON.stringify(errors),
                messageId: msg.id,
              });
              continue; // Skip this message and process the next one
            }

            // Process each incoming message
            if (msg.from) {
              log.info(`Processing incoming message from ${msg.from}`, {
                messageId: msg.id,
                type: msg.type,
              });

              recieveMessage(
                {
                  ...msg,
                  business_phone_number_id,
                } as WARecieveMessage,
                contacts,
                log
              );
            }
          }
        }
      }
    }
    // Flush all logs to ensure they're properly written
    await log.flush();

    // Return 200 OK to acknowledge receipt of the webhook
    return new Response(null, {
      status: 200,
      headers: { 'Content-Type': 'text/plain' },
    });
  } catch (e) {
    // Log any errors and return 500 status
    console.log(e);
    log.error(JSON.stringify(e));
    await log.flush();
    return new Response('error', {
      status: 500,
      headers: { 'Content-Type': 'text/plain' },
    });
  }
}

const storeOutboundMessage = async (phone_number_id: string, from: string, to: string, id: string, type: string, echo: any) => {
  try {
    const pb = await getPb();
    
    // Find the account using phone_number_id
    const account = await pb.collection('accounts').getFirstListItem(`phone_id = "${phone_number_id}"`);
    if (!account) {
      throw new Error(`Account not found for phone_number_id: ${phone_number_id}`);
    }

    // Create or get the lead (recipient of outbound message)
    const lead = await upsertLead(to, account.id);
    
    // Create or get the conversation
    const conversation = await upsertConversation(lead.id, account.id);

    // Prepare message data
    const messageData = {
      user: lead.id,
      message: echo.text?.body || `${type} message`, // Use text body or message type as fallback
      account: account.id,
      from: 'agent' as const, // Outbound messages are from agent
      delivery_status: 'sent' as const, // Echo messages are already sent
      wamid: id, // WhatsApp message ID
      convo_id: conversation.id,
      type: type || 'text',
      created_by: null, // System generated outbound message
    };

    // Check if message already exists to avoid duplicates
    try {
      const existingMessage = await pb.collection('messages').getFirstListItem(`wamid = "${id}"`);
      console.log(`Message with wamid ${id} already exists, skipping storage`);
      return existingMessage;
    } catch (error) {
      // Message doesn't exist, proceed with creation
    }

    // Store the message
    const storedMessage = await pb.collection('messages').create(messageData);
    
    // Update conversation with latest message
    await pb.collection('conversations').update(conversation.id, {
      message: storedMessage.id,
      last_message_created: new Date().toISOString(),
    });

    console.log(`Successfully stored outbound message ${id} for conversation ${conversation.id}`);
    return storedMessage;
    
  } catch (error) {
    console.error('Error storing outbound message:', error);
    throw error;
  }
};
