import { createOpenAI } from '@ai-sdk/openai';
import { streamObject } from 'ai';
import { z } from 'zod';

// Allow streaming responses up to 30 seconds
export const maxDuration = 30;
const openai = createOpenAI({
  baseURL: 'https://api.deepseek.com/v1',
});
export async function POST(req: Request) {
  const context = await req.json();
  const { accountName, date, templatePrompt, accountId } = context;
  console.log({ accountName, date });
  if (templatePrompt) {
    const result = streamObject({
      model: openai('deepseek-chat'),
      schema: z.object({
        content: z.string().max(256),
      }),
      prompt: `Generate a whatsapp marketing message with emojis using the following prompt: ${templatePrompt}, make sure the new lines are preserved`,
    });
    return result.toTextStreamResponse();
  } else {
    const result = streamObject({
      model: openai('deepseek-chat'),
      schema: z.object({
        content: z.string().max(256),
      }),
      prompt: `generate a template name with maximum 256 characters, using this name ${accountName} with no spaces and words joined by an underscore, add todays date ${date} in it, and add some random english words`,
    });

    return result.toTextStreamResponse();
  }
}
