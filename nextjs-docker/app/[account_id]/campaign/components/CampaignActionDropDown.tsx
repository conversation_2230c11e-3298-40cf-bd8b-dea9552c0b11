import { Button } from '@/components/ui/button';
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuLabel, DropdownMenuTrigger } from '@/components/ui/dropdown-menu';
import { IExpandedCampaign } from '@/lib/types';
import { useDialog } from '@/state/hooks/use-dialog';
import { Row } from '@tanstack/react-table';
import clsx from 'clsx';

import { AlertDialog, AlertDialogCancel, AlertDialogContent, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle } from '@/components/ui/alert-dialog';
import { useToast } from '@/hooks/use-toast';
import { deleteCampaign } from '@/lib/pocket';
import { BarChartHorizontalBig, CirclePause, CirclePlay, Copy, Loader2, MoreHorizontal, Pencil, Trash } from 'lucide-react';
import Link from 'next/link';
import { useState } from 'react';

const CampaignActionDropDown = ({ row }: { row: Row<IExpandedCampaign> }) => {
  const leads_list_id = row.original.leads_list.map((id) => `list=${id}`).join('&');
  const template_id = row.original.template;
  const campaign_name = row.original.name;
  const isDraft = row.original.draft_url ? true : false;
  const deleteMessageDialog = useDialog();
  const toggleCampaignDialog = useDialog();
  const [loading, setLoading] = useState(false);
  const { toast } = useToast();
  const handleDeleteCampaign = async () => {
    try {
      setLoading(true);
      await deleteCampaign(row.id, row.original.account);
      toast({
        variant: 'success',
        description: `Campaign ${row.original.name} Deleted successfully`,
      });
    } catch (error) {
      toast({
        variant: 'destructive',
        description: 'There was an error. Try again!',
      });
    }
    deleteMessageDialog.dismiss();
    setLoading(false);
  };

  // const handleToggleCampaign = async () => {
  //   try {
  //     setLoading(true);
  //     await toggleCampaign(row.id, row.original.stopped, row.original.account);
  //     toast({
  //       variant: 'success',
  //       description: `Campaign ${row.original.name} has been ${isStopped ? 'resumed' : 'stopped'} successfully`,
  //     });
  //   } catch (error) {
  //     toast({
  //       variant: 'destructive',
  //       description: 'There was an error. Try again!',
  //     });
  //   }

  //   toggleCampaignDialog.dismiss();
  //   setLoading(false);
  // };

  return (
    <>
      <DropdownMenu modal={false}>
        <DropdownMenuTrigger asChild>
          <Button variant="ghost" className="h-8 w-8 p-0 hover:bg-gray-100">
            <span className="sr-only">Open menu</span>
            <MoreHorizontal className="h-4 w-4" />
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent align="end">
          <DropdownMenuLabel>Actions</DropdownMenuLabel>
          <Link
            prefetch={false}
            aria-disabled={isDraft}
            href={`campaign/create/${template_id}/summary?campaign_name=${campaign_name}&${leads_list_id}&duplicate=true`}
            className={clsx('cursor-pointer', isDraft && 'pointer-events-none')}
          >
            <DropdownMenuItem disabled={isDraft}>
              <Copy className="mr-2 h-4 w-4 text-orange-500" />
              <span className="text-orange-500">Duplicate</span>
            </DropdownMenuItem>
          </Link>
          <Link prefetch={false} aria-disabled={isDraft} href={`campaign/${row.id}`} className={clsx('cursor-pointer', isDraft && 'pointer-events-none')}>
            <DropdownMenuItem disabled={isDraft}>
              <BarChartHorizontalBig className="mr-2 h-4 w-4 text-blue-500" />
              <span className="text-blue-500">View</span>
            </DropdownMenuItem>
          </Link>
          {isDraft && (
            <Link prefetch={false} href={row.original.draft_url ?? ''}>
              <DropdownMenuItem>
                <Pencil className="mr-2 h-4 w-4 text-green-500" />
                <span className="text-green-500">Edit</span>
              </DropdownMenuItem>
            </Link>
          )}
          {isDraft && (
            <DropdownMenuItem {...deleteMessageDialog.triggerProps}>
              <Trash className="mr-2 h-4 w-4 text-red-600" />
              <span className="text-red-600">Delete</span>
            </DropdownMenuItem>
          )}
          {/* {!isDraft &&
            (isStopped ? (
              <DropdownMenuItem {...toggleCampaignDialog.triggerProps}>
                <CirclePlay className="mr-2 h-4 w-4 text-green-600" />
                <span className="text-green-600">Resume</span>
              </DropdownMenuItem>
            ) : (
              <DropdownMenuItem {...toggleCampaignDialog.triggerProps}>
                <CirclePause className="mr-2 h-4 w-4 text-red-600" />
                <span className="text-red-600">Stop</span>
              </DropdownMenuItem>
            ))} */}
        </DropdownMenuContent>
      </DropdownMenu>
      <AlertDialog {...deleteMessageDialog.dialogProps}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Are you sure you want to delete the campaign?</AlertDialogTitle>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            {loading ? (
              <Button className="mb-0" disabled variant={'destructive'}>
                Deleting
                <Loader2 className="ml-2 animate-spin" />
              </Button>
            ) : (
              <>
                <Button onClick={() => handleDeleteCampaign()} variant={'destructive'} className="mb-0">
                  Yes
                </Button>
              </>
            )}
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>

      {/* <AlertDialog {...toggleCampaignDialog.dialogProps}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Are you sure you want to {isStopped ? 'Resume' : 'Stop'} the campaign</AlertDialogTitle>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            {loading ? (
              <Button className="mb-0" disabled variant={'destructive'}>
                {isStopped ? 'Resuming' : 'Stopping'}
                <Loader2 className="ml-2 animate-spin" />
              </Button>
            ) : (
              <>
                <Button onClick={() => handleToggleCampaign()} variant={'destructive'} className="mb-0">
                  Yes
                </Button>
              </>
            )}
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog> */}
    </>
  );
};

export default CampaignActionDropDown;
