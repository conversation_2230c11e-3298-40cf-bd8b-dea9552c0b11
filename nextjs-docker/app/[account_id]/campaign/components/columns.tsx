'use client';

import { But<PERSON> } from '@/components/ui/button';
import { IExpandedCampaign } from '@/lib/types';
import { ColumnDef } from '@tanstack/react-table';
import dayjs from 'dayjs';
import { ArrowUpDown, ExternalLink, PauseCircleIcon, RefreshCcw } from 'lucide-react';
// import utc from 'dayjs/plugin/utc'
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
import Link from 'next/link';
import CampaignActionDropDown from './CampaignActionDropDown';
import ScheduledTimeCell from './ScheduledTimeCell';

export const columns: ColumnDef<IExpandedCampaign>[] = [
  {
    accessorKey: 'name',
    id: 'campaign name',
    size: 150,
    maxSize: 450,
    minSize: 150,
    header: ({ column }) => {
      return (
        <Button variant="ghost" className="px-0 mb-0" onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}>
          Campaign Name
          <ArrowUpDown className="ml-2 h-4 w-4" />
        </Button>
      );
    },
    cell: ({ row }) => {
      return (
        <TooltipProvider>
          <Tooltip delayDuration={0}>
            <TooltipTrigger asChild>
              <div className="font-medium truncate">{row.original.name}</div>
            </TooltipTrigger>
            <TooltipContent>
              <p>{row.original.name}</p>
            </TooltipContent>
          </Tooltip>
        </TooltipProvider>
      );
    },
  },
  {
    id: 'list name',
    size: 300,
    maxSize: 300,
    minSize: 100,
    header: ({ column }) => {
      return (
        <Button variant="ghost" className="px-0 mb-0">
          List Name
        </Button>
      );
    },
    cell: ({ row }) => {
      const leads_list = [row.original.expand?.leads_list].flat();
      return (
        <div className="flex flex-col">
          {leads_list.map((leads) => (
            <Link
              key={leads.id}
              prefetch={false}
              className="group hover:underline"
              href={`list/view-list?listId=${leads?.id}&listName=${leads?.name}`}
              target="_blank"
            >
              <div className="flex gap-2 items-center text-sm">
                {leads?.name}
                <ExternalLink className="h-4 w-4 text-gray-700 group-hover:text-black flex-shrink-0" />
              </div>
            </Link>
          ))}
        </div>
      );
    },
  },
  {
    accessorKey: 'status',
    maxSize: 300,
    minSize: 50,
    size: 100,
    header: ({ column }) => {
      return (
        <Button variant="ghost" className="px-0 mb-0" onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}>
          Status
          <ArrowUpDown className="ml-2 h-4 w-4" />
        </Button>
      );
    },
    cell: ({ row }) => {
      return (
        <div className="flex items-center space-x-2">
          <div className="text-sm">{row.original.type}</div>
          {row.original.type != 'Draft' && row.original.status == 'Stopped' && (
            <TooltipProvider>
              <Tooltip delayDuration={0}>
                <TooltipTrigger asChild>
                  <div>
                    <PauseCircleIcon className="h-4 w-4" />
                  </div>
                </TooltipTrigger>
                <TooltipContent>
                  <div>Campaign is stopped</div>
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>
          )}

          {row.original.status == 'Retrying' && (
            <TooltipProvider>
              <Tooltip delayDuration={0}>
                <TooltipTrigger asChild>
                  <div>
                    <RefreshCcw className="h-4 w-4 text-orange-600 animate-spin" />
                  </div>
                </TooltipTrigger>
                <TooltipContent>
                  <div>Campaign is retrying</div>
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>
          )}
          {row.original.status == 'Sending' && (
            <TooltipProvider>
              <Tooltip delayDuration={0}>
                <TooltipTrigger asChild>
                  <div>
                    <RefreshCcw className="h-4 w-4 text-gray-600 animate-spin" />
                  </div>
                </TooltipTrigger>
                <TooltipContent>
                  <div>Campaign is in progress</div>
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>
          )}
        </div>
      );
    },
  },
  {
    accessorKey: 'type',
    maxSize: 300,
    minSize: 100,
    header: ({ column }) => {
      return (
        <Button variant="ghost" className="px-0 mb-0">
          Type
        </Button>
      );
    },
    cell: ({ row }) => {
      return <ScheduledTimeCell row={row} />;
    },
  },
  {
    accessorKey: 'expand.template.template_name',
    id: 'template name',
    maxSize: 300,
    minSize: 100,
    header: ({ column }) => {
      return (
        <Button variant="ghost" className="px-0 mb-0">
          Template
        </Button>
      );
    },
    cell: ({ row }) => {
      return (
        <TooltipProvider>
          <Tooltip delayDuration={0}>
            <TooltipTrigger asChild>
              <div className="truncate">{row.original.expand?.template?.template_name ?? ''}</div>
            </TooltipTrigger>
            <TooltipContent>
              <p>{row.original?.expand?.template?.template_name ?? ''}</p>
            </TooltipContent>
          </Tooltip>
        </TooltipProvider>
      );
    },
  },
  {
    id: 'created by',
    maxSize: 300,
    minSize: 100,
    header: ({ column }) => {
      return (
        <Button variant="ghost" className="px-0 mb-0">
          Created By
        </Button>
      );
    },
    cell: ({ row }) => {
      return <div className="text-sm">{row.original.expand.created_by?.name ?? 'System'}</div>;
    },
  },
  {
    accessorKey: 'created',
    id: 'created at',
    size: 160,
    header: ({ column }) => {
      return (
        <Button variant="ghost" className="px-0 mb-0" onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}>
          Created At
          <ArrowUpDown className="ml-2 h-4 w-4" />
        </Button>
      );
    },
    cell: ({ row }) => {
      return <div className="text-sm">{dayjs(row.getValue('created at')).format('MMMM D, YYYY h:mm A')}</div>;
    },
  },
  {
    accessorKey: 'updated',
    id: 'updated at',
    size: 160,
    header: ({ column }) => {
      return (
        <Button variant="ghost" className="px-0 mb-0" onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}>
          Updated At
          <ArrowUpDown className="ml-2 h-4 w-4" />
        </Button>
      );
    },
    cell: ({ row }) => {
      return <div className="text-sm">{dayjs(row.getValue('updated at')).format('MMMM D, YYYY h:mm A')}</div>;
    },
  },
  {
    id: 'actions',
    size: 50,
    maxSize: 50,
    minSize: 50,
    cell: ({ row }) => <CampaignActionDropDown row={row} />,
  },
];
