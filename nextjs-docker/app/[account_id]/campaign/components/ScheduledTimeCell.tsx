'use client';
import { But<PERSON> } from '@/components/ui/button';
import { HoverCard, HoverCardContent, HoverCardTrigger } from '@/components/ui/hover-card';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { useToast } from '@/components/ui/use-toast';
import { updateCampaign } from '@/lib/pocket';
import { IExpandedCampaign } from '@/lib/types';
import { AdapterDayjs } from '@mui/x-date-pickers/AdapterDayjs';
import { DateTimePicker } from '@mui/x-date-pickers/DateTimePicker';
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';
import { Row } from '@tanstack/react-table';
import dayjs, { Dayjs } from 'dayjs';
import relativeTime from 'dayjs/plugin/relativeTime';
import { Loader2 } from 'lucide-react';
import { useParams } from 'next/navigation';
import { useState } from 'react';

const ScheduledTimeCell = ({ row }: { row: Row<IExpandedCampaign> }) => {
  const { toast } = useToast();
  dayjs.extend(relativeTime);
  const [date, setDate] = useState<Dayjs | null>(dayjs(row.original.scheduled_time));
  const [loading, setLoading] = useState(false);
  const param = useParams();
  const account_id = param.account_id as string;
  const type = row.original.type;
  const isScheduled = type == 'Scheduled';
  const [popoverOpen, setPopoverOpen] = useState(false);
  const handleUpdateCampaign = async (id: string) => {
    try {
      setLoading(true);
      if (!date) {
        alert('Select Date');
        setLoading(false);
        return;
      }
      const res = await updateCampaign(id, date.toDate().toUTCString(), account_id);
      await (await fetch(`${process.env.NEXT_PUBLIC_BUN_SERVER_URL}/update-campaign?campaignId=${id}&newDate=${date.toDate().toUTCString()}`)).json();
      if (res) {
        toast({
          variant: 'success',
          description: `Campaign ${row.original.name} time updated successfully!`,
        });
      } else {
        toast({
          variant: 'destructive',
          description: 'There was an error',
        });
      }
    } catch (error) {
      toast({
        variant: 'destructive',
        description: 'There was an error. Try again!',
      });
    }
    setLoading(false);
    setPopoverOpen(false);
  };
  if (row.original.scheduled_time) {
    return (
      <HoverCard>
        <HoverCardTrigger className="cursor-help">
          <Popover open={popoverOpen} onOpenChange={setPopoverOpen}>
            <PopoverTrigger disabled={!isScheduled}>
              {isScheduled ? (
                <div className="underline decoration-dotted decoration-emerald-600 text-sm">Scheduled {dayjs().to(row.original.scheduled_time)}</div>
              ) : (
                <div className="text-sm">Sent {dayjs().to(row.original.scheduled_time)}</div>
              )}
            </PopoverTrigger>
            <PopoverContent className="w-80 space-y-4">
              <LocalizationProvider dateAdapter={AdapterDayjs}>
                <DateTimePicker
                  label="Select Date"
                  value={date}
                  onChange={(newValue) => setDate(newValue)}
                  disablePast
                  className="w-full"
                  slotProps={{
                    field: {
                      readOnly: true,
                    },
                  }}
                />
              </LocalizationProvider>
              <div className="flex justify-end">
                {loading ? (
                  <Button className="justify-end" disabled variant={'secondary'}>
                    Updating
                    <Loader2 className="ml-2 animate-spin" />
                  </Button>
                ) : (
                  <>
                    <Button onClick={() => handleUpdateCampaign(row.id)} variant={'secondary'} className="justify-end">
                      Update
                    </Button>
                  </>
                )}
              </div>
            </PopoverContent>
          </Popover>
        </HoverCardTrigger>
        <HoverCardContent className="text-xs">Scheduled Time: {dayjs(row.original.scheduled_time).format('MMMM D, YYYY h:mm A')}</HoverCardContent>
      </HoverCard>
    );
  }
  if (row.original.type == 'Draft') {
    return <div className="text-sm">Drafted</div>;
  }
  return <div className="text-sm">Sent {dayjs().to(row.original.created)}</div>;
};

export default ScheduledTimeCell;
