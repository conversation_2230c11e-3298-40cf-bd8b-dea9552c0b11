import { Button } from '@/components/ui/button';
import Link from 'next/link';

import { HoverCard, HoverCardContent, HoverCardTrigger } from '@/components/ui/hover-card';
import { getCampaign, getMessageLimitsByUser, getUser } from '@/lib/pocket';
import { Info, PlusCircle } from 'lucide-react';
import type { Metadata } from 'next';
import { columns } from './components/columns';
import { DataTable } from './components/data-table';

export const metadata: Metadata = {
  title: 'Campaign',
  description: '...',
};

export default async ({ params }: { params: Record<string, string> }) => {
  // noStore();
  const user = await getUser();
  const campaigns = await getCampaign(params.account_id);
  const messageLimit = await getMessageLimitsByUser(user.id, params.account_id);

  if (!campaigns || campaigns.length === 0) {
    return (
      <div className="h-full w-full flex flex-col justify-center items-center space-y-4">
        <div>Looks like you do not have any campaigns!</div>

        <div className="space-x-2">
          {messageLimit && messageLimit?.remaining_limit > 0 ? (
            <Button variant={'secondary'} asChild>
              <Link href={`/${params.account_id}/campaign/create`}>
                <PlusCircle className="mr-2 h-5 w-5" />
                Create Campaign
              </Link>
            </Button>
          ) : (
            <HoverCard defaultOpen={true} openDelay={0}>
              <HoverCardTrigger className="hover:cursor-help">
                <Button variant={'secondary'} disabled>
                  <PlusCircle className="mr-2 h-5 w-5" />
                  Create Campaign
                </Button>
              </HoverCardTrigger>
              <HoverCardContent className="w-96 text-sm">
                You dont have enough quota to create a campaign. Please upgrade your package to send more messages.
              </HoverCardContent>
            </HoverCard>
          )}
        </div>
      </div>
    );
  }

  return (
    <div className="mx-auto p-6 bg-gray-100 min-h-full">
      <div className="flex justify-between mb-4">
        <div>
          <div className="text-gray-800 text-xl font-bold sm:text-2xl">Campaign List</div>
          <p className="text-gray-600 mt-2">Select the campaign which you would like to view</p>
          <HoverCard>
            <HoverCardTrigger>
              <div className="text-gray-600 flex space-x-2 items-center group hover:underline hover:cursor-help">
                <div>
                  Hover over the <span className="font-bold">Type</span> field cell to see when the campaign is scheduled for.
                </div>
                <Info className="h-5 w-5" />
              </div>
            </HoverCardTrigger>
            <HoverCardContent className="w-96">
              <img src="/assets/schedule_hover.gif" alt="video" className="rounded-md" />
            </HoverCardContent>
          </HoverCard>
          <HoverCard>
            <HoverCardTrigger>
              <div className="text-gray-600 flex space-x-2 items-center group hover:underline hover:cursor-help">
                <div>
                  Click on the <span className="font-bold">Type</span> field cell to update the scheduled time if status is scheduled.
                </div>

                <Info className="h-5 w-5" />
              </div>
            </HoverCardTrigger>
            <HoverCardContent className="w-96">
              <img src="/assets/schedule_click.gif" alt="video" className="rounded-md mb-2" />
              <div className="text-xs">You can only update the status if the scheduled time is ahead of current date.</div>
            </HoverCardContent>
          </HoverCard>
        </div>
        <div className="space-x-2">
          {messageLimit && messageLimit?.remaining_limit > 0 ? (
            // {!messageLimit ? (
            <Button variant={'secondary'} asChild>
              <Link href={`/${params.account_id}/campaign/create`}>
                <PlusCircle className="mr-2 h-5 w-5" />
                Create Campaign
              </Link>
            </Button>
          ) : (
            <HoverCard defaultOpen={false} openDelay={0}>
              <HoverCardTrigger className="hover:cursor-help">
                <Button variant={'secondary'} disabled>
                  <PlusCircle className="mr-2 h-5 w-5" />
                  Create Campaign
                </Button>
              </HoverCardTrigger>
              <HoverCardContent className="w-96 text-sm">
                You do not have enough quota to create a campaign. Please upgrade your package to send more messages.
              </HoverCardContent>
            </HoverCard>
          )}
        </div>
      </div>
      <DataTable columns={columns} data={campaigns} account_id={params.account_id} />
    </div>
  );
};
