'use client';
import { Card } from '@/components/ui/card';

const CampaignCost = ({ convertedValue, currency }: { convertedValue: number; currency: string | null }) => {
  return (
    <Card className="">
      <div className="p-4">
        <div className="flex items-center justify-between space-x-1">
          <span className="text-lg font-semibold text-gray-600 dark:text-gray-300">Campaign Cost:</span>
          <div className="text-right">
            <span className="text-xl font-bold text-gray-900 dark:text-white">{convertedValue.toFixed(2)}</span>
            <span className="ml-1 text-sm font-medium text-gray-500 dark:text-gray-400">{currency ? currency : 'USD'}</span>
          </div>
        </div>
      </div>
    </Card>
  );
};

export default CampaignCost;
