'use client';

import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Checkbox } from '@/components/ui/checkbox';
import { HoverCard, HoverCardContent, HoverCardTrigger } from '@/components/ui/hover-card';
import { IExpandedMessageAndRepliedList } from '@/lib/types';
import { ColumnDef } from '@tanstack/react-table';
import { ArrowUpDown, Info } from 'lucide-react';
import ResendButton from '../ResendButton';

// This type is used to define the shape of our data.
// You can use a Zod schema here if you want.

export const columns: ColumnDef<IExpandedMessageAndRepliedList>[] = [
  {
    size: 10,
    id: 'select',
    header: ({ table }) => (
      <Checkbox
        checked={table.getIsAllPageRowsSelected() || (table.getIsSomePageRowsSelected() && 'indeterminate')}
        onCheckedChange={(value) => {
          if ((table.getIsSomePageRowsSelected() && 'indeterminate') == 'indeterminate') {
            table.resetRowSelection();
          } else {
            const pageRows = table.getRowModel().rows;

            // Check if we are selecting or deselecting

            // Toggle selection only for rows that meet the conditions
            pageRows.forEach((row) => {
              if (!row.original.expand?.user?.opt_out && row.original.delivery_status !== 'failed') {
                row.toggleSelected(true);
              }
            });
          }
        }}
        aria-label="Select all"
      />
    ),
    cell: ({ row }) => {
      if (!row.original.expand?.user?.opt_out && row.original.delivery_status !== 'failed') {
        return <Checkbox type="button" checked={row.getIsSelected()} onCheckedChange={(value) => row.toggleSelected(!!value)} aria-label="Select row" />;
      }
      // Optionally, return null or some placeholder for rows that don't meet the condition
      return null;
    },
    enableSorting: false,
    enableHiding: false,
  },
  {
    accessorKey: 'expand.user.name',
    id: 'name',
    maxSize: 300,
    minSize: 150,
    header: ({ column }) => {
      return (
        <Button variant="ghost" className="px-0 mb-0" type="button" onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}>
          Name
          <ArrowUpDown className="ml-2 h-4 w-4" />
        </Button>
      );
    },
    cell: ({ row }) => {
      return <div className="font-medium truncate">{row.original.expand?.user?.name ?? 'N/A'}</div>;
    },
  },
  {
    accessorKey: 'expand.user.phone_number',
    id: 'Phone Number',
    size: 125,
    header: ({ column }) => {
      return (
        <Button variant="ghost" className="px-0 mb-0" onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}>
          Phone Number
          <ArrowUpDown className="ml-2 h-4 w-4" />
        </Button>
      );
    },
  },
  {
    accessorKey: 'expand.user.replied',
    id: 'replied',
    minSize: 50,
    header: ({ column }) => {
      return (
        <Button variant="ghost" className="px-0 mb-0" type="button" onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}>
          Lead reply
          <ArrowUpDown className="ml-2 h-4 w-4" />
        </Button>
      );
    },
    cell: ({ row }) => {
      let index = row.original.expand?.user?.expand?.replied?.findIndex((item) => item.campaign == row.original.campaign) ?? -1;
      if (index != -1) {
        let replied = row.original.expand?.user?.expand?.replied[index];
        return (
          <HoverCard openDelay={0}>
            <HoverCardTrigger asChild className="hover:cursor-help">
              <div>
                <Badge className="rounded-full" variant={'success'}>
                  Replied
                </Badge>
                <div className="line-clamp-1">Message: {replied?.message}</div>
              </div>
            </HoverCardTrigger>
            <HoverCardContent className="text-xs">
              <div>Message: {replied?.message}</div>
            </HoverCardContent>
          </HoverCard>
        );
      } else {
        return <>No Reply</>;
      }
    },
    filterFn: (row, columnId, filterValue) => {
      // Check if the filter is active
      if (filterValue) {
        // Return true if there's a reply in the replied array
        const index = row.original.expand?.user?.expand?.replied?.findIndex((item) => item.campaign == row.original.campaign) ?? -1;
        return index !== -1;
      }
      // If filter is not active, return all rows
      return true;
    },
  },
  {
    accessorKey: 'expand.user.opt_out',
    id: 'Is Opted',
    minSize: 50,
    header: ({ column }) => {
      return (
        <Button variant="ghost" className="px-0 mb-0" onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}>
          Is Opted
          <ArrowUpDown className="ml-2 h-4 w-4" />
        </Button>
      );
    },
    cell: ({ row }) => {
      if (!row.original.expand?.user?.opt_out) {
        return <>Opted In</>;
      } else {
        return (
          <HoverCard openDelay={0}>
            <HoverCardTrigger asChild>
              <div className="flex space-x-2 items-center hover:cursor-help">
                <Badge className="rounded-full" variant={'destructive'}>
                  Opted Out
                </Badge>
                <Info className="h-4 w-4 text-destructive" />
              </div>
            </HoverCardTrigger>
            <HoverCardContent className="text-xs">
              <div>User has clicked on "Stop Promotions" button.</div>
              <div>User does not want to receive promotion content</div>
            </HoverCardContent>
          </HoverCard>
        );
      }
      // return <>{row.original.expand?.user?.opt_out.toString() ?? ''}</>;
    },
  },
  {
    accessorKey: 'delivery_status',
    header: ({ column }) => {
      return (
        <Button variant="ghost" className="px-0 mb-0" onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}>
          Devliery Status
          <ArrowUpDown className="ml-2 h-4 w-4" />
        </Button>
      );
    },
    cell: ({ row }) => {
      const RetryCount = () => {
        if (row.original.retried_attempts > 0) {
          return (
            <HoverCard openDelay={0}>
              <HoverCardTrigger asChild className="hover:cursor-help">
                <Badge className="rounded-full" variant={'secondary'}>
                  {row.original.retried_attempts}
                </Badge>
              </HoverCardTrigger>
              <HoverCardContent className="text-xs">Number of times this message was retried.</HoverCardContent>
            </HoverCard>
          );
        } else {
          return <></>;
        }
      };
      switch (row.original.delivery_status) {
        case 'delivered':
          return (
            <div className="flex space-x-2 items-center">
              <div>Delivered</div>
              <RetryCount />
            </div>
          );
          break;
        case 'read':
          return (
            <div className="flex space-x-2 items-center">
              <div>Read</div>
              <RetryCount />
            </div>
          );
        case 'failed':
          return (
            <div className="flex space-x-2 items-center">
              <HoverCard openDelay={0}>
                <HoverCardTrigger asChild>
                  <div className="flex space-x-2 items-center hover:cursor-help">
                    <Badge className="rounded-full" variant={'destructive'}>
                      Failed
                    </Badge>
                    <Info className="h-4 w-4 text-destructive" />
                  </div>
                </HoverCardTrigger>
                <HoverCardContent className="text-xs">
                  Error: {row.original.error}. Its possible that the customer's whatsapp number is incorrect or does not exist. Its also possible that you sent
                  multiple marketing messages to the same customer in a short period of time, wait 24 hours before sending another message.
                </HoverCardContent>
              </HoverCard>
              <RetryCount />
            </div>
          );
          break;
        default:
          break;
      }
      return `${row.original.delivery_status}`;
    },
  },
  // {
  //   accessorKey: 'created',
  //   header: ({ column }) => {
  //     return (
  //       <Button variant="ghost" className="px-0 mb-0" onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}>
  //         Created At
  //         <ArrowUpDown className="ml-2 h-4 w-4" />
  //       </Button>
  //     );
  //   },
  //   cell: ({ row }) => {
  //     return dayjs(row.getValue('created')).format('MMMM D, YYYY h:mm A');
  //   },
  // },
  // {
  //   accessorKey: 'updated',
  //   header: ({ column }) => {
  //     return (
  //       <Button variant="ghost" className="px-0 mb-0" onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}>
  //         Updated At
  //         <ArrowUpDown className="ml-2 h-4 w-4" />
  //       </Button>
  //     );
  //   },
  //   cell: ({ row }) => {
  //     return dayjs(row.getValue('updated')).format('MMMM D, YYYY h:mm A');
  //   },
  // },
  // {
  //   id: 'action',
  //   cell: ({ row, table }) => {
  //     if (row.original.delivery_status == 'failed') {
  //       if (!row.original.expand?.user?.opt_out) {
  //         return <ResendButton messageId={row.original.id} messagingLimit={table.options.meta?.messagingLimit} />;
  //       }
  //     }
  //   },
  // },
];
