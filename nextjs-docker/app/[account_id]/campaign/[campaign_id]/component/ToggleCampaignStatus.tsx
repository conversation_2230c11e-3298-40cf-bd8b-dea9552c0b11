'use client';
import { Button } from '@/components/ui/button';
import { Card } from '@/components/ui/card';
import { setCampaignStatus } from '@/lib/pocket';
import { IExpandedCampaign } from '@/lib/types';
import { Loader2 } from 'lucide-react';
import { useState } from 'react';

const ToggleCampaignStatus = ({ oneCampaign }: { oneCampaign: IExpandedCampaign }) => {
  const [loading, setLoading] = useState(false);
  const handleToggleCampaign = async (id: string, status: string, account: string) => {
    setLoading(true);
    await setCampaignStatus(id, status, account);
    setLoading(false);
  };
  if (loading) {
    return (
      <Button variant="default" disabled>
        <Loader2 className="h-4 w-4 animate-spin mr-2" />
        Loading
      </Button>
    );
  }
  if (oneCampaign.status == 'Paused') {
    return (
      <Button variant="default" onClick={() => handleToggleCampaign(oneCampaign.id, 'Sending', oneCampaign.account)}>
        Resume Campaign
      </Button>
    );
  }
  if (oneCampaign.status == 'Sending') {
    return (
      <div className="flex items-center gap-2">
        <Button variant="warning" onClick={() => handleToggleCampaign(oneCampaign.id, 'Paused', oneCampaign.account)}>
          Pause Campaign
        </Button>
        <Button variant="destructive" onClick={() => handleToggleCampaign(oneCampaign.id, 'Stopped', oneCampaign.account)}>
          Stop Campaign
        </Button>
      </div>
    );
  }
};

export default ToggleCampaignStatus;
