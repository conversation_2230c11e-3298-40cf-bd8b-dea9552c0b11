'use client';
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { RefreshCw, CheckCircle2, AlertCircleIcon, XCircle } from 'lucide-react';
import dayjs from 'dayjs';
import { IExpandedCampaign } from '@/lib/types';
import { Button } from '@/components/ui/button';
import { useState } from 'react';
import { setCampaignStatus } from '@/lib/pocket';

interface RetryInfo {
  currentAttempt: number;
  maxRetries: number;
  retryTimes: Date[];
}

export default function CampaignRetryTimeline({ oneCampaign }: { oneCampaign: IExpandedCampaign }) {
  const [loading, setLoading] = useState(false);
  const handleToggleCampaign = async (id: string, status: string, account: string) => {
    setLoading(true);
    await setCampaignStatus(id, status, account);
    setLoading(false);
  };

  //If retry count is 1, that means retry date has been updated by 1 day. If retry count is 2, that means retry date has been updated by 2 days. If retry count is 3, that means retry date has been updated by 3 days.
  //For frontend to view the retry timeline, we need to calculate the retry times based on the retry count and retry date.
  const next_retry_date = oneCampaign.next_retry_date;

  const calculateRetryInfo = (retryDate: string): RetryInfo => {
    let retryTimes = [dayjs(retryDate).toDate(), dayjs(retryDate).add(1, 'day').toDate(), dayjs(retryDate).add(2, 'day').toDate()];
    if (oneCampaign.retry_count == 1) {
      retryTimes = [dayjs(retryDate).subtract(1, 'day').toDate(), dayjs(retryDate).toDate(), dayjs(retryDate).add(1, 'day').toDate()];
    }
    if (oneCampaign.retry_count == 2) {
      retryTimes = [dayjs(retryDate).subtract(2, 'day').toDate(), dayjs(retryDate).subtract(1, 'day').toDate(), dayjs(retryDate).toDate()];
    }
    if (oneCampaign.retry_count == 3) {
      retryTimes = [dayjs(retryDate).subtract(3, 'day').toDate(), dayjs(retryDate).subtract(2, 'day').toDate(), dayjs(retryDate).subtract(1, 'day').toDate()];
    }

    return {
      currentAttempt: oneCampaign.retry_count,
      maxRetries: 3,
      retryTimes,
    };
  };

  //No need for collace since this card will only be rendered if next retry date is present.
  const retryInfo = calculateRetryInfo(next_retry_date?.toString() ?? new Date().toUTCString());

  return (
    <Card className="w-full">
      <CardHeader className="pb-3">
        <CardTitle className="text-lg font-medium">Retry Information</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="flex flex-col space-y-4">
          <div className="flex items-center space-x-2">
            <span className="text-sm font-medium">Current Attempt:</span>
            <Badge variant="secondary" className="text-xs px-2 py-1">
              {retryInfo.currentAttempt} / {retryInfo.maxRetries}
            </Badge>
          </div>
          <div className="relative">
            <div className="absolute left-5 top-0 h-full w-px bg-border" />
            <div className="relative flex flex-col space-y-4">
              {retryInfo.retryTimes.map((time, index) => (
                <div key={index} className="flex items-center space-x-4">
                  <div
                    className={`w-10 h-10 rounded-full border flex items-center justify-center bg-background ${
                      index < retryInfo.currentAttempt ? 'text-primary' : 'text-muted-foreground'
                    }`}
                  >
                    {index < retryInfo.currentAttempt ? (
                      <CheckCircle2 className="w-6 h-6 text-green-500" />
                    ) : oneCampaign.retry_status == 'failed' ? (
                      <XCircle className="w-6 h-6 text-destructive" />
                    ) : (
                      index + 1
                    )}
                  </div>
                  <div>
                    <p className="text-xs text-muted-foreground">{dayjs(time).format('MMMM D, YYYY h:mm A')}</p>
                  </div>
                </div>
              ))}
            </div>
          </div>
          {oneCampaign.retry_status == 'failed' ? (
            <div className="flex items-center justify-center text-sm text-destructive pt-2">
              <AlertCircleIcon className="w-4 h-4 mr-2 text-destructive" />
              {oneCampaign.retry_status_reason}. Campaign will not be retried further.
            </div>
          ) : oneCampaign.retry_count < 3 ? (
            <div>
              <div className="flex items-center justify-center text-sm text-muted-foreground pt-2">
                <RefreshCw className="w-4 h-4 mr-2" />
                Next retry scheduled for {dayjs(retryInfo.retryTimes[retryInfo.currentAttempt]).format('MMMM D, YYYY h:mm A')}
              </div>
              {/* <div className="text-sm text-muted-foreground pt-2 mx-auto">
                <div className="mb-2">
                  <span className="text-red-500">Note:</span> If you cancel the retry, the campaign will not be retried further.
                </div>
                <Button variant="destructive" onClick={() => handleToggleCampaign(oneCampaign.id, 'Completed', oneCampaign.account)} disabled={loading}>
                  {loading && <RefreshCw className="animate-spin mr-2" />} Cancel Retry
                </Button>
              </div> */}
            </div>
          ) : (
            <div className="flex items-center justify-center text-sm text-green-600 pt-2">
              <CheckCircle2 className="w-4 h-4 mr-2 text-green-600" />
              All retries successful.
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );
}
