import { Badge } from '@/components/ui/badge';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';

const RetryPerformance = ({ retryData }: { retryData: any[] }) => {
  // Calculate retry comparisons
  const retryComparisons = retryData.slice(1).map((current, index) => {
    const previous = retryData[index];
    const deliveredDiff = current.delivered_count - previous.delivered_count;
    const sentDiff = current.sent_count - previous.sent_count;
    const deliveredPercentage = previous.delivered_count > 0 ? ((deliveredDiff / previous.delivered_count) * 100).toFixed(1) : deliveredDiff > 0 ? '∞' : '0';
    const sentPercentage = previous.sent_count > 0 ? ((sentDiff / previous.sent_count) * 100).toFixed(1) : sentDiff > 0 ? '∞' : '0';

    return {
      retryAttempt: current.retry_attempt,
      previousAttempt: previous.retry_attempt,
      deliveredDiff,
      sentDiff,
      deliveredPercentage,
      sentPercentage,
      timestamp: current.timestamp,
    };
  });
  return (
    <>
      <Card>
        <CardHeader>
          <CardTitle className="text-lg">Retry Performance Comparison</CardTitle>
          <p className="text-sm text-gray-600">Message delivery improvements between retry attempts</p>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {retryComparisons.map((comparison, index) => (
              <div key={index} className="border rounded-lg p-4 bg-gray-50">
                <div className="flex justify-between items-center mb-3">
                  <h4 className="font-semibold text-gray-900">
                    Retry {comparison.retryAttempt} {comparison.previousAttempt != 0 && `vs Retry ${comparison.previousAttempt}`}
                  </h4>
                  <span className="text-xs text-gray-500">{new Date(comparison.timestamp).toLocaleString()}</span>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {/* Delivered Messages Comparison */}
                  <div className="space-y-2">
                    <h5 className="text-sm font-medium text-gray-700">Delivered Messages</h5>
                    <div className="flex items-center justify-between">
                      <span className="text-sm text-gray-600">Difference:</span>
                      <div className="flex items-center gap-2">
                        <span className={`font-semibold ${comparison.deliveredDiff >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                          {comparison.deliveredDiff >= 0 ? '+' : ''}
                          {comparison.deliveredDiff}
                        </span>
                        <Badge variant={comparison.deliveredDiff >= 0 ? 'default' : 'destructive'} className="text-xs">
                          {comparison.deliveredPercentage === '∞' ? 'New' : `${comparison.deliveredDiff >= 0 ? '+' : ''}${comparison.deliveredPercentage}%`}
                        </Badge>
                      </div>
                    </div>
                    <div className="text-xs text-gray-500">
                      Previous: {retryData[comparison.previousAttempt].delivered_count} → Current: {retryData[comparison.retryAttempt].delivered_count}
                    </div>
                  </div>

                  {/* Sent Messages Comparison */}
                  <div className="space-y-2">
                    <h5 className="text-sm font-medium text-gray-700">Sent Messages</h5>
                    <div className="flex items-center justify-between">
                      <span className="text-sm text-gray-600">Difference:</span>
                      <div className="flex items-center gap-2">
                        <span className={`font-semibold ${comparison.sentDiff >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                          {comparison.sentDiff >= 0 ? '+' : ''}
                          {comparison.sentDiff}
                        </span>
                        <Badge variant={comparison.sentDiff >= 0 ? 'default' : 'destructive'} className="text-xs">
                          {comparison.sentPercentage === '∞' ? 'New' : `${comparison.sentDiff >= 0 ? '+' : ''}${comparison.sentPercentage}%`}
                        </Badge>
                      </div>
                    </div>
                    <div className="text-xs text-gray-500">
                      Previous: {retryData[comparison.previousAttempt].sent_count} → Current: {retryData[comparison.retryAttempt].sent_count}
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    </>
  );
};

export default RetryPerformance;
