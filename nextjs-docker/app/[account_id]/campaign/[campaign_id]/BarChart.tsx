'use client';

import { <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, XAxis } from 'recharts';

import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';

import { ChartConfig, ChartContainer, ChartTooltip, ChartTooltipContent } from '@/components/ui/chart';

const chartConfig = {
  visitors: {
    label: 'Visitors',
    color: '',
  },
  sent: {
    label: 'Sent',
    color: 'hsl(var(--chart-1))',
  },
  failed: {
    label: 'Failed',
    color: 'hsl(var(--chart-2))',
  },
  delivered: {
    label: 'Delivered',
    color: 'hsl(var(--chart-3))',
  },
  read: {
    label: 'Read',
    color: 'hsl(var(--chart-4))',
  },
  replied: {
    label: 'Replied',
    color: 'hsl(var(--chart-5))',
  },
  pending: {
    label: 'Pending',
    color: 'hsl(var(--chart-5))',
  },
  opt_out: {
    label: 'Opted Out',
    color: 'hsl(var(--chart-6))',
  },
  'sent from wetarseel': {
    label: 'Sent from weTarseel',
    color: 'hsl(var(--chart-7))',
  },
} satisfies ChartConfig;

export function MyBarChart({ title, subTitle, data }: { title: string; subTitle: string; data: any }) {
  const chartData = data.datasets[0].data
    .map((value: number, index: number) => {
      const label = data.labels[index] as keyof typeof chartConfig;
      return {
        status: data.labels[index],
        users: value,
        fill: chartConfig[label]?.color,
      };
    })
    .filter((item: any) => item.users > 0);
  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle>{title}</CardTitle>
        <CardDescription>{subTitle}</CardDescription>
      </CardHeader>
      <CardContent>
        <ChartContainer config={chartConfig}>
          <BarChart accessibilityLayer data={chartData}>
            <CartesianGrid vertical={false} />
            <XAxis
              dataKey="status"
              tickLine={false}
              tickMargin={10}
              axisLine={false}
              tickFormatter={(value) => chartConfig[value as keyof typeof chartConfig]?.label}
            />
            <ChartTooltip cursor={false} content={<ChartTooltipContent hideLabel />} />
            <Bar
              dataKey="users"
              strokeWidth={2}
              radius={8}
              // activeIndex={2}
              // activeBar={({ ...props }) => {
              //   return (
              //     <Rectangle
              //       {...props}
              //       fillOpacity={0.8}
              //       stroke={props.payload.fill}
              //       strokeDasharray={4}
              //       strokeDashoffset={4}
              //     />
              //   )
              // }}
            >
              <LabelList position="top" offset={12} className="fill-foreground" fontSize={12} />
            </Bar>
          </BarChart>
        </ChartContainer>
      </CardContent>
      <CardFooter className="flex-col items-start gap-2 text-sm">
        {/* <div className="flex gap-2 font-medium leading-none">
          Trending up by 5.2% this month <TrendingUp className="h-4 w-4" />
        </div>
        <div className="leading-none text-muted-foreground">
          Showing total visitors for the last 6 months
        </div> */}
      </CardFooter>
    </Card>
  );
}
