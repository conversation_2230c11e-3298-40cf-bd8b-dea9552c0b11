import TemplatePreview from '@/components/shared/TemplatePreview';
import TemplatePreviewCarousel from '@/components/shared/TemplatePreviewCarousel';
import { Badge } from '@/components/ui/badge';
import { BorderBeam } from '@/components/ui/border-beam';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Separator } from '@/components/ui/separator';
import { getAccount, getBussinessCurrency, getCampaignById, getCampaignMessagesById, getMessageLimitsByUser, getPricingData, getUser } from '@/lib/pocket';
import dayjs from 'dayjs';
import { Converter } from 'easy-currencies';
import { Clock, ExternalLink, Lock, RefreshCcw } from 'lucide-react';
import { unstable_noStore } from 'next/cache';
import Link from 'next/link';
import GoBackButton from './GoBackButton';
import MyChart from './MyChart';
import CampaignClientSideDate from './component/CampaignClientSideDate';
import CampaignCost from './component/CampaignCost';
import CampaignRetryCard from './component/CampaignRetryCard';
import ToggleCampaignStatus from './component/ToggleCampaignStatus';
import { columns } from './component/columns';
import { DataTable } from './component/data-table';
import RetryPerformance from './component/RetryPerformance';

export const dynamic = 'force-dynamic';
export const fetchCache = 'default-no-store';

export default async ({ params }: { params: Record<string, string> }) => {
  unstable_noStore();
  const user = await getUser();
  const converter = new Converter();
  const currency = await getBussinessCurrency(params.account_id);
  const account = await getAccount(params.account_id);
  const convertTo = currency ?? 'USD';
  let totalCost = 0;
  const pricingList = await getPricingData();
  const campaignMessages = await getCampaignMessagesById(params.campaign_id);
  let messagingLimit = await getMessageLimitsByUser(user.id, params.account_id);
  const oneCampaign = await getCampaignById(params.campaign_id);
  const dataObject = {
    sent: 0,
    failed: 0,
    delivered: 0,
    read: 0,
    pending: 0,
    replied: 0,
    opt_out: 0,
    'sent from wetarseel': 0,
  };
  const labels = Object.entries(dataObject).map((item) => item[0]);
  const others_cost = pricingList?.filter((pricing) => pricing.country_name == 'other');
  campaignMessages.forEach((message) => {
    if (message.delivery_status != 'failed' && message.delivery_status != 'pending' && message.delivery_status != 'sent from wetarseel') {
      const template_category = message?.expand?.template?.category;
      if (message.expand?.user?.country) {
        const country = message.expand?.user?.country;
        const countryCost = pricingList?.filter(
          (pricing) => country.toUpperCase() == pricing.country_name.toUpperCase() || pricing.iso_codes.includes(country.toUpperCase())
        );
        if (countryCost?.length) {
          if (template_category == 'UTILITY') {
            totalCost = totalCost + countryCost[0].utility;
          } else {
            totalCost = totalCost + countryCost[0].marketing;
          }
        } else {
          if (template_category == 'UTILITY') {
            totalCost = totalCost + (others_cost && others_cost[0].utility);
          } else {
            totalCost = totalCost + (others_cost && others_cost[0].marketing);
          }
        }
      } else {
        if (template_category == 'UTILITY') {
          totalCost = totalCost + (others_cost && others_cost[0].utility);
        } else {
          totalCost = totalCost + (others_cost && others_cost[0].marketing);
        }
      }
    }
    dataObject[message.delivery_status] += 1;
  });

  dataObject.replied = oneCampaign.reply_count;
  dataObject.opt_out = oneCampaign.opt_out_count;
  const mappedData = Object.entries(dataObject).map((item) => item[1]);

  const convertedValue = await converter.convert(totalCost, 'USD', convertTo);

  const data = {
    labels: labels,
    datasets: [
      {
        label: 'Messages Overview',
        data: mappedData,
        backgroundColor: [
          'rgba(75, 192, 192, 0.2)', //sent green
          'rgba(255, 99, 132, 0.2)', //failed red blue
          'rgba(255, 206, 86, 0.2)', //delivered yellow
          'rgba(54, 162, 235, 0.2)', //read blue
          'rgba(153, 102, 255, 0.2)', //pending purple
          'rgba(255, 159, 64, 0.2)', //sent from we tarseel orange
          'rgba(255, 159, 64, 0.2)', //sent from we tarseel orange
          'rgba(255, 159, 64, 0.2)', //opt out count orange
        ],
        borderColor: [
          'rgba(75, 192, 192, 1)', //sent green
          'rgba(255, 99, 132, 1)', //failed red blue
          'rgba(255, 206, 86, 1)', //delivered yellow
          'rgba(54, 162, 235, 1)', //read blue
          'rgba(153, 102, 255, 1)', //pending purple
          'rgba(255, 159, 64, 1)', //sent from we tarseel orange
          'rgba(255, 159, 64, 1)', //sent from we tarseel orange
          'rgba(255, 159, 64, 1)', //opt out count orange
        ],
        borderWidth: 1,
      },
    ],
  };

  let campaignTemplate = oneCampaign.expand.template;

  const dataForCsv = campaignMessages
    .filter((messages) => messages.delivery_status != 'sent' && messages.delivery_status != 'sent from wetarseel')
    .map((campaign) => {
      let index = campaign.expand?.user?.expand?.replied?.findIndex((item) => item.campaign == campaign.campaign) ?? -1;
      let lead_reply = null;
      let message = null;
      let replied_time = null;
      if (index != -1) {
        let replied = campaign.expand?.user?.expand?.replied[index];
        message = replied?.message;
        replied_time = replied?.updated;
        lead_reply = 'yes';
      }
      return {
        lead_name: campaign.expand?.user?.name ?? '',
        lead_phone_number: campaign.expand?.user?.phone_number ?? '',
        lead_reply: lead_reply ?? '',
        delivery_status: campaign.delivery_status,
        message: message ?? '',
        replied_time: replied_time ?? '',
        opted_out: campaign.expand?.user?.opt_out ?? '',
      };
    });
  const campaignData = campaignMessages.filter((messages) => messages.delivery_status != 'sent' && messages.delivery_status != 'sent from wetarseel');
  const failedRows = campaignData.filter((data) => data.delivery_status == 'failed');
  return (
    <div className="mx-auto p-6 bg-gray-100 min-h-full">
      <Card className="bg-white border-b border-gray-200 mb-4">
        <CardContent className="p-4">
          <div className="flex justify-between">
            <div>
              <div className="flex space-x-2 items-center">
                <h1 className="text-2xl font-bold text-gray-900">Campaign Summary</h1>
                {oneCampaign.status == 'Scheduled' && <Badge variant={'secondary'}>Scheduled</Badge>}

                {oneCampaign.status == 'Completed' && <Badge variant={'success'}>Published</Badge>}
                {oneCampaign.status == 'Retrying' && (
                  <Badge variant={'pending'}>
                    <RefreshCcw className="animate-spin mr-2 h-4 w-4" />
                    Retrying
                  </Badge>
                )}
                {oneCampaign.status == 'Sending' && (
                  <Badge variant={'pending'}>
                    <RefreshCcw className="animate-spin mr-2 h-4 w-4" />
                    Campaign is in progress
                  </Badge>
                )}
                {oneCampaign.status == 'Stopped' && <Badge variant={'destructive'}>Campaign is Stopped</Badge>}
              </div>
              <p className="mt-1 text-sm text-gray-500">View your campaign report</p>
              <div className="mb-2">
                {oneCampaign.expand?.leads_list && (
                  <>
                    <div className='mt-2 mb-1 text-sm text-gray-600"'>Lists in campaign</div>
                    <div>
                      {oneCampaign.expand.leads_list.map((list) => (
                        <Link href={`/${params.account_id}/list/view-list?listId=${list.id}&listName=${list.name}`} target="_blank">
                          <Badge key={list.id} variant="secondary">
                            {list.name}
                            <ExternalLink className="h-4 w-4 ml-2" />
                          </Badge>
                        </Link>
                      ))}
                    </div>
                  </>
                )}
              </div>
              <GoBackButton />
            </div>

            <div className="flex flex-col items-end">
              <div className="flex gap-2">
                <div>
                  {oneCampaign.scheduled_time && (
                    <>
                      <div className="flex space-x-2 items-center">
                        <Clock className="h-4 w-4 text-gray-500" />
                        <p className="text-sm font-medium text-gray-500">Campaign Scheduled at</p>
                      </div>
                      <CampaignClientSideDate _date={oneCampaign.scheduled_time} />
                    </>
                  )}
                </div>
                {oneCampaign.scheduled_time && <Separator orientation="vertical" />}
                <div>
                  <p className="text-sm font-medium text-gray-500">Campaign Created At</p>
                  <CampaignClientSideDate _date={oneCampaign.created} />
                </div>
              </div>
              <div className="mt-2">
                <CampaignCost convertedValue={convertedValue} currency={currency} />
              </div>
              <div className="mt-2">{/* <ToggleCampaignStatus oneCampaign={oneCampaign} /> */}</div>
            </div>
          </div>
        </CardContent>
      </Card>

      {account.allow_failed_retry_campaigns ? (
        failedRows.length > 0 &&
        oneCampaign.next_retry_date && (
          <div className="mb-4">
            <div className="mb-4">
              <CampaignRetryCard oneCampaign={oneCampaign} />
            </div>
            <div>
              <RetryPerformance retryData={oneCampaign.retry_performance} />
            </div>
          </div>
        )
      ) : (
        <div className="mb-4">
          <div className="relative flex w-full flex-col items-center justify-center overflow-hidden rounded-lg border bg-background ">
            <Card className="w-full blur-sm shadow-none">
              <CardHeader className="pb-3">
                <CardTitle className="text-lg font-medium">Retry Information</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="flex flex-col space-y-4">
                  <div className="flex items-center space-x-2">
                    <span className="text-sm font-medium">Current Attempt:</span>
                    <Badge variant="secondary" className="text-xs px-2 py-1">
                      0 / 3
                    </Badge>
                  </div>
                  <div className="relative">
                    <div className="absolute left-5 top-0 h-full w-px bg-border" />
                    <div className="relative flex flex-col space-y-4">
                      {['0', '0', '0'].map((time, index) => (
                        <div key={index} className="flex items-center space-x-4">
                          <div className={`w-10 h-10 rounded-full border flex items-center justify-center bg-background`}>{index + 1}</div>
                          <div>
                            <p className="text-xs text-muted-foreground">{dayjs().format('MMMM D, YYYY h:mm A')}</p>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
            <div className="absolute inset-0 flex items-center justify-center">
              <div className="flex items-center justify-center flex-col space-y-2">
                <div className="inline-flex items-center justify-center w-16 h-16 rounded-full bg-muted">
                  <Lock className="w-8 h-8 text-muted-foreground motion-preset-seesaw" aria-hidden="true" />
                </div>
                <div className="text-xl text-gray-600">
                  Retry Campaigns is for <span className="text-blue-500 font-medium">premium users</span> only
                </div>
              </div>
            </div>
            <BorderBeam size={500} duration={10} />
            {/* colorFrom={'#22c55e'} colorTo={#10b821}  */}
          </div>
        </div>
      )}

      {/* <div className="max-w-3xl items-center justify-center mx-auto mb-4"> */}
      <div className="w-full mb-4">
        <MyChart
          title={`Campaign Name: ${oneCampaign.name}`}
          subTitle={`Total Sent Messages: ${campaignMessages.length} out of ${oneCampaign.total_messages_count}`}
          data={data}
          totalMessages={campaignMessages.length}
        />
      </div>

      <DataTable
        columns={columns}
        params={params}
        data={campaignData}
        oneCampaign={oneCampaign}
        dataForCsv={dataForCsv}
        totalCost={totalCost}
        messagingLimit={messagingLimit}
        account={account}
        failedRows={failedRows}
      />
      <div className="text-gray-800 text-xl font-bold sm:text-2xl my-2">Template Preview</div>
      {['basic-template', ''].includes(campaignTemplate.type ?? '') ? (
        <div className="max-w-[360px]">
          <TemplatePreview template={oneCampaign.expand.template} />
        </div>
      ) : (
        <TemplatePreviewCarousel template={campaignTemplate} />
      )}
    </div>
  );
};
