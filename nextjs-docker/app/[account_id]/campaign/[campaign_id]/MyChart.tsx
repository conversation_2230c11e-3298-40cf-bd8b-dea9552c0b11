'use client';

import React from 'react';
import { MyB<PERSON><PERSON><PERSON> } from './BarChart';
import { MyPie<PERSON>hart } from './PieChart';

export const options = {
  plugins: {
    title: {
      display: true,
      text: 'Chart.js Bar Chart - Stacked',
    },
  },
  responsive: true,
  scales: {
    x: {
      stacked: true,
    },
    y: {
      stacked: true,
    },
  },
};

const MyChart = ({ title, subTitle, data, totalMessages }: { title: string; subTitle: string; data: any; totalMessages: number }) => {
  return (
    <div className="flex w-full gap-4">
      <MyBarChart title={title} subTitle={subTitle} data={data} />
      <MyPieChart
        title={'Campaign Messages Status Breakdown'}
        subTitle={'Percentage of Each Status from the Total Messages Sent'}
        data={data}
        totalMessages={totalMessages}
      />
    </div>
  );
};

export default MyChart;
