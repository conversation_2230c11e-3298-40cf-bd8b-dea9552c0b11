'use client';

import { <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON> } from 'recharts';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { ChartConfig, ChartContainer } from '@/components/ui/chart';

const chartConfig = {
  replied: {
    label: 'Replied',
    color: '#6575FA',
  },
  read: {
    label: 'Read',
    color: '#0077b6',
  },
  delivered: {
    label: 'Delivered',
    color: '#fca311',
  },
  sent: {
    label: 'Sent',
    color: '#1EA153',
  },
  failed: {
    label: 'Failed',
    color: '#d90429',
  },
  opt_out: {
    label: 'Opted Out',
    color: '#92a200',
  },
} satisfies ChartConfig;

export function MyPieChart({ title, subTitle, data, totalMessages }: { title: string; subTitle: string; data: any; totalMessages: number }) {
  // const totalMessages = data.datasets[0].data.reduce((acc: number, value: number) => acc + value, 0);
  const replied = data.datasets[0].data[data.labels.indexOf('replied')] || 0;
  const failed = data.datasets[0].data[data.labels.indexOf('failed')] || 0;
  const opt_out = data.datasets[0].data[data.labels.indexOf('opt_out')] || 0;
  const delivered = data.datasets[0].data[data.labels.indexOf('delivered')] + data.datasets[0].data[data.labels.indexOf('read')] + opt_out || 0;
  const read = data.datasets[0].data[data.labels.indexOf('read')] + opt_out || 0;
  const sent = data.datasets[0].data[data.labels.indexOf('sent')] + delivered;

  const chartDataWithPercentage = [
    {
      status: 'Delivered',
      users: delivered,
      fill: chartConfig.delivered.color,
      percentage: ((delivered / totalMessages) * 100).toFixed(2),
    },
    {
      status: 'Sent',
      users: sent,
      fill: chartConfig.sent.color,
      percentage: ((sent / totalMessages) * 100).toFixed(2),
    },
    {
      status: 'Replied',
      users: replied,
      fill: chartConfig.replied.color,
      percentage: ((replied / totalMessages) * 100).toFixed(2),
    },
    {
      status: 'Failed',
      users: failed,
      fill: chartConfig.failed.color,
      percentage: ((failed / totalMessages) * 100).toFixed(2),
    },
    {
      status: 'Read',
      users: read,
      fill: chartConfig.read.color,
      percentage: ((read / totalMessages) * 100).toFixed(2),
    },
    {
      status: 'Opted Out',
      users: opt_out,
      fill: chartConfig.opt_out.color,
      percentage: ((opt_out / totalMessages) * 100).toFixed(2),
    },
  ].filter((item) => item.users > 0);

  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle>{title}</CardTitle>
        <CardDescription>{subTitle}</CardDescription>
      </CardHeader>
      <CardContent>
        <div className="flex flex-wrap justify-center gap-4">
          {chartDataWithPercentage.map((chartData: any, index: number) => (
            <div key={index} className="w-[calc(50%-1rem)] min-w-[200px]">
              <ChartContainer config={chartConfig}>
                <PieChart width={200} height={200}>
                  <Pie
                    data={[chartData]}
                    dataKey="users"
                    nameKey="status"
                    innerRadius={50}
                    outerRadius={75}
                    fill="#8884d8"
                    label={({ status, percentage }) => `${percentage}%`}
                    labelLine={true}
                    style={{ fontSize: '14px' }}
                  >
                    <Cell key={`cell-${index}`} fill={chartData.fill} />
                    <Label
                      value={`${chartData.status} : ${chartData.users}`}
                      position="center"
                      className="total-messages-label"
                      style={{ fontSize: '14px', fontWeight: 'bold' }}
                    />
                  </Pie>
                </PieChart>
              </ChartContainer>
            </div>
          ))}
        </div>
      </CardContent>
      <CardFooter className="flex-col items-start gap-2 text-sm">{/* Add any footer content here if needed */}</CardFooter>
    </Card>
  );
}

// 'use client';

// import { PieChart, Pie, Cell, Label } from 'recharts';
// import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
// import { ChartConfig, ChartContainer, ChartTooltip, ChartTooltipContent } from '@/components/ui/chart';

// const chartConfig = {
//     replied: {
//         label: 'Replied',
//         color: '#6575FA',
//     },
//     read: {
//         label: 'Read',
//         color: '#0077b6'
//     },
//     delivered: {
//         label: 'Delivered',
//         color: '#fca311',
//     },
//     sent: {
//         label: 'Sent',
//         color: "#1EA153"
//     },
//     failed: {
//         label: 'Failed',
//         color: '#d90429',
//     }
// } satisfies ChartConfig;

// export function MyPieChart({ title, subTitle, data }: { title: string; subTitle: string; data: any }) {
//     const totalMessages = data.datasets[0].data.reduce((acc: number, value: number) => acc + value, 0);

//     const delivered = data.datasets[0].data[data.labels.indexOf('delivered')] + data.datasets[0].data[data.labels.indexOf('read')] || 0;
//     const read = data.datasets[0].data[data.labels.indexOf('read')] || 0;
//     const sent = data.datasets[0].data[data.labels.indexOf('sent')] + data.datasets[0].data[data.labels.indexOf('delivered')] + data.datasets[0].data[data.labels.indexOf('read')] || 0;
//     const replied = data.datasets[0].data[data.labels.indexOf('replied')] || 0;
//     const failed = data.datasets[0].data[data.labels.indexOf('failed')] || 0;

//     const chartDataWithPercentage = [
//         {
//             status: 'Delivered',
//             users: delivered,
//             fill: chartConfig.delivered.color,
//             percentage: (delivered / totalMessages * 100).toFixed(2),
//         },
//         {
//             status: 'Sent',
//             users: sent,
//             fill: chartConfig.sent.color,
//             percentage: (sent / totalMessages * 100).toFixed(2),
//         },
//         {
//             status: 'Replied',
//             users: replied,
//             fill: chartConfig.replied.color,
//             percentage: (replied / totalMessages * 100).toFixed(2),
//         },
//         // {
//         //     status: 'Failed',
//         //     users: failed,
//         //     fill: chartConfig.failed.color,
//         //     percentage: (failed / totalMessages * 100).toFixed(2),
//         // },
//         {
//             status: 'Read',
//             users: read,
//             fill: chartConfig.read.color,
//             percentage: (read / totalMessages * 100).toFixed(2),
//         },
//     ].filter(item => item.users > 0);

//     return (
//         <Card className='w-full'>
//             <CardHeader>
//                 <CardTitle>{title}</CardTitle>
//                 <CardDescription>{subTitle}</CardDescription>
//             </CardHeader>
//             <div className="mt-4 text-center text-sm font-bold text-red-600">
//                 Failed: {((failed / totalMessages) * 100).toFixed(2)}%
//             </div>
//             <CardContent>
//                 <ChartContainer config={chartConfig}>
//                     <PieChart width={300} height={300}>
//                         <Pie
//                             data={chartDataWithPercentage}
//                             dataKey="users"
//                             nameKey="status"
//                             innerRadius={100}
//                             outerRadius={150}
//                             fill="#8884d8"
//                             label={({ status, percentage }) => `${status}: ${percentage}%`}
//                             labelLine={true}
//                         >
//                             {chartDataWithPercentage.map((entry, index) => (
//                                 <Cell key={`cell-${index}`} fill={entry.fill} />
//                             ))}
//                             <Label
//                                 value={`Total Messages Sent: ${totalMessages}`}
//                                 position="center"
//                                 className="total-messages-label"
//                                 style={{ fontSize: '14px', fontWeight: 'bold' }}
//                             />
//                         </Pie>
//                     </PieChart>
//                 </ChartContainer>
//             </CardContent>
//             <CardFooter className="flex-col items-start gap-2 text-sm">
//             </CardFooter>
//         </Card>
//     );
// }
