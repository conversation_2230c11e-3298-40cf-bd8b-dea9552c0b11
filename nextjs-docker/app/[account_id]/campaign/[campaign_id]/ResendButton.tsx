'use client';
import { But<PERSON> } from '@/components/ui/button';
import { HoverCard, HoverCardContent, HoverCardTrigger } from '@/components/ui/hover-card';
import { resendMessage } from '@/lib/pocket';
import { IMessagingLimit } from '@/lib/types';
import { RotateCw } from 'lucide-react';
import { useParams } from 'next/navigation';
import { useFormStatus } from 'react-dom';

const Btn = ({ isDisabled }: any) => {
  const { pending } = useFormStatus();

  return (
    <>
      {!pending ? (
        <HoverCard>
          <HoverCardTrigger className="hover:cursor-help">
            <Button type="submit" variant={'outline'} disabled={isDisabled().disabled} size={'sm'}>
              <RotateCw className="h-4 w-4 text-black mr-2" />
              Resend
            </Button>
          </HoverCardTrigger>
          <HoverCardContent className="text-xs">
            <div>{isDisabled().text}</div>
          </HoverCardContent>
        </HoverCard>
      ) : (
        <Button variant={'outline'} disabled size={'sm'}>
          <RotateCw className="animate-spin h-4 w-4 text-blue-500 mr-2" />
          Resending
        </Button>
      )}
    </>
  );
};
const ResendButton = ({ messageId, messagingLimit }: { messageId: string; messagingLimit: IMessagingLimit | null | undefined }) => {
  const params = useParams();
  const isDisabled = () => {
    if (messagingLimit && messagingLimit?.remaining_limit < 1) {
      return { disabled: true, text: 'Message limit quota exceeding' };
    } else if (messagingLimit == null) {
      return { disabled: true, text: 'No package assigned' };
    } else {
      return { disabled: false, text: 'Resending message will reduce message quota' };
    }
  };
  return (
    <form action={resendMessage} className="flex items-start flex-col space-y-2">
      <Btn isDisabled={isDisabled} />
      <input hidden defaultValue={messageId} type="text" name="id" />
      <input hidden defaultValue={params.account_id} type="text" name="accountId" />
      <input hidden defaultValue={params.campaign_id} type="text" name="campaignId" />
    </form>
  );
};

export default ResendButton;
