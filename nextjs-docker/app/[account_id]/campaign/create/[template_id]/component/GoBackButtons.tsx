'use client';
import { Button } from '@/components/ui/button';
import Link from 'next/link';
import { useParams, useSearchParams } from 'next/navigation';
import React from 'react';

const GoBackButton = () => {
  const params = useParams();
  const { account_id, campaign_id, template_id } = params;
  const searchParams = useSearchParams();
  const campaign_name = searchParams.get('campaign_name');
  const listIds = searchParams.getAll('list');
  ('campaign_name');
  const duplicate = searchParams.get('duplicate') ? `&duplicate=true` : '';
  const retarget = searchParams.get('retarget') ? `&retarget=true` : '';
  let linkUrl = `/${account_id}/campaign/create/template/${template_id}?campaign_name=${campaign_name}&${duplicate}${retarget}`;
  if (listIds && listIds.length > 0) {
    let listIdsJoined = listIds.join('&list=');
    linkUrl = `/${account_id}/campaign/create/template/${template_id}?campaign_name=${campaign_name}&list=${listIdsJoined}${duplicate}${retarget}`;
  }
  return (
    <Link href={linkUrl}>
      <Button type="button" variant={'link'}>
        Go Back
      </Button>
    </Link>
  );
};

export default GoBackButton;
