'use client';
import { Button } from '@/components/ui/button';
import { IExpandedList } from '@/lib/types';
import { ChevronRight } from 'lucide-react';

const NextButton = ({ isSelected, selectedRows }: { isSelected: boolean; selectedRows: IExpandedList[] }) => {
  return (
    <div className="w-full flex justify-end">
      {selectedRows.map((item, index) => {
        return <input type="text" hidden defaultValue={item.id} name="list" key={index} />;
      })}
      {isSelected ? (
        <Button className="mb-0" type="submit">
          Next <ChevronRight className="ml-2 h-4 w-4" />
        </Button>
      ) : (
        <Button className="mb-0" id="list-campaign-next" disabled>
          Next <ChevronRight className="ml-2 h-4 w-4" />
        </Button>
      )}
    </div>
  );
};

export default NextButton;
