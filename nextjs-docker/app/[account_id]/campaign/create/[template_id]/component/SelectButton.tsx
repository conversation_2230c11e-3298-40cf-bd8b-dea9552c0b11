'use client';

import { Button } from '@/components/ui/button';
import Link from 'next/link';
import { useParams } from 'next/navigation';
import React from 'react';

const SelectButton = ({ id }: { id: string }) => {
  const params = useParams();
  const account_id = params.account_id;
  const template_id = params.template_id;

  return (
    <Link href={`/${account_id}/campaign/create/${template_id}/${id}`}>
      <Button variant={'ghost'} className="mb-0">
        Select
      </Button>
    </Link>
  );
};

export default SelectButton;
