'use client';

import { Button } from '@/components/ui/button';
import { Checkbox } from '@/components/ui/checkbox';
import { HoverCard, HoverCardContent, HoverCardTrigger } from '@/components/ui/hover-card';
import { IExpandedListAndLeadCount } from '@/lib/types';
import { ColumnDef } from '@tanstack/react-table';
import dayjs from 'dayjs';
import { ArrowUpDown, ExternalLink } from 'lucide-react';
import Link from 'next/link';

// This type is used to define the shape of our data.
// You can use a Zod schema here if you want.

export const columns: ColumnDef<IExpandedListAndLeadCount>[] = [
  {
    id: 'select',
    size: 20,
    maxSize: 20,
    minSize: 20,
    header: ({ table }) => (
      <Checkbox
        checked={table.getIsAllPageRowsSelected() || (table.getIsSomePageRowsSelected() && 'indeterminate')}
        onCheckedChange={(value) => table.toggleAllPageRowsSelected(!!value)}
        aria-label="Select all"
      />
    ),
    cell: ({ row }) => (
      <Checkbox value={row.original.id} checked={row.getIsSelected()} onCheckedChange={(value) => row.toggleSelected(!!value)} aria-label="Select row" />
    ),
    enableSorting: false,
    enableHiding: false,
  },
  {
    accessorKey: 'name',
    size: 300,
    maxSize: 300,
    minSize: 150,
    header: ({ column }) => {
      return (
        <Button variant="ghost" className="px-0 mb-0" type="button" onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}>
          Name
          <ArrowUpDown className="ml-2 h-4 w-4" />
        </Button>
      );
    },
    cell: ({ row }) => {
      const id = row.original.id;
      const name = row.original.name;
      return (
        <Link prefetch={false} className="group hover:underline" href={`/${row.original.account}/list/view-list?listId=${id}&listName=${name}`} target="_blank">
          <div className="flex gap-2 items-center break-all">
            {name}
            <ExternalLink className="h-4 w-4 text-gray-700 group-hover:text-black" />
          </div>
        </Link>
      );
    },
  },
  {
    id: 'no_of_contacts',
    size: 150,
    maxSize: 300,
    minSize: 150,
    header: ({ column }) => {
      return (
        <Button variant="ghost" className="px-0 mb-0" type="button">
          Number of contacts
        </Button>
      );
    },
    cell: ({ row }) => {
      const totalContacts = row.original?.leads_count;
      return totalContacts;
    },
  },
  {
    id: 'campaigns',
    size: 150,
    maxSize: 300,
    minSize: 150,
    header: ({ column }) => {
      return (
        <Button variant="ghost" className="px-0 mb-0" type="button">
          Campaigns
        </Button>
      );
    },
    cell: ({ row }) => {
      const campaigns = row.original.expand?.campaigns_via_leads_list;
      return (
        <HoverCard>
          <HoverCardTrigger asChild>
            <div className="flex items-center hover:cursor-help flex-wrap gap-2">
              {campaigns && campaigns.length > 0 ? (
                campaigns.slice(0, 3).map((campaigns, index) => (
                  <div
                    key={index}
                    className="inline-flex items-center whitespace-nowrap border px-2 bg-secondary text-secondary-foreground rounded-lg border-solidfont-normal"
                  >
                    {campaigns.name}
                  </div>
                ))
              ) : (
                <div>N/A</div>
              )}
              {campaigns && (
                <div className="underline decoration-dotted decoration-green-600 font-semibold">{campaigns.length > 3 && `+${campaigns.length - 3}`}</div>
              )}
            </div>
          </HoverCardTrigger>
          <HoverCardContent className="w-80">
            <div className="mb-2">All Campaigns:</div>
            <div className="flex gap-2 flex-wrap">
              {campaigns &&
                campaigns.map((campaigns, index) => (
                  <div
                    key={index}
                    className="inline-flex items-center whitespace-nowrap border px-2 bg-secondary text-secondary-foreground rounded-lg border-solidfont-normal"
                  >
                    {campaigns.name}
                  </div>
                ))}
            </div>
          </HoverCardContent>
        </HoverCard>
      );
    },
  },
  {
    accessorKey: 'created',
    size: 150,
    maxSize: 300,
    minSize: 150,
    header: ({ column }) => {
      return (
        <Button variant="ghost" className="px-0 mb-0" type="button" onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}>
          Created At
          <ArrowUpDown className="ml-2 h-4 w-4" />
        </Button>
      );
    },
    cell: ({ row }) => {
      return dayjs(row.getValue('created')).format('MMMM D, YYYY h:mm A');
    },
  },
];
