'use client';
import { But<PERSON> } from '@/components/ui/button';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { ILead } from '@/lib/types';
import { useState } from 'react';

const ViewListButton = async ({ leads, name }: { leads: ILead[]; name: string }) => {
  const [leadsData, setLeads] = useState(leads);
  const [searchQuery, setSearchQuery] = useState('');

  const handleSearch = (e: React.ChangeEvent<HTMLInputElement>) => {
    const query = e.target.value;
    setSearchQuery(query);
    if (query === '') {
      setLeads(leads);
    } else {
      const filteredLeads = leads.filter((lead) => lead.name.toLowerCase().includes(query.toLowerCase()));
      setLeads(filteredLeads);
    }
  };
  return (
    <Dialog>
      <DialogTrigger asChild>
        <Button variant="outline">View List</Button>
      </DialogTrigger>
      <DialogContent className="max-h-dvh overflow-y-auto">
        <DialogHeader>
          <DialogTitle>{name}</DialogTitle>
          <DialogDescription>View Leads in the list</DialogDescription>
        </DialogHeader>
        <div className="mb-4">
          <Input
            type="text"
            value={searchQuery}
            onChange={handleSearch}
            placeholder="Search leads..."
            // className="w-full p-2 border border-gray-300 rounded"
          />
        </div>
        <div className="">
          {leadsData.map((lead, index) => (
            <div className="border-b border-gray-200 p-2">
              {index + 1}) {lead.name}
            </div>
          ))}
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default ViewListButton;
