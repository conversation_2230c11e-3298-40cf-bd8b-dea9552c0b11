import { getAllLists } from '@/lib/pocket';
import type { Metadata } from 'next';
import { columns } from './component/columns';
import { DataTable } from './component/data-table';

export const metadata: Metadata = {
  title: 'Select List',
  description: '...',
};

export default async ({ params, searchParams }: { params: Record<string, string>; searchParams: { [key: string]: string | string[] | undefined } }) => {
  // noStore();
  const lists = await getAllLists(params.account_id);
  const duplicate = searchParams?.duplicate;
  return (
    <div className="space-y-4 mb-10 w-full px-10">
      <div className="text-center font-bold mt-5 py-2 bg-white rounded-lg shadow-sm w-full">Select a list now</div>
      <form action={`/${params.account_id}/campaign/create/${params.template_id}/summary`}>
        <input type="text" hidden name="campaign_name" defaultValue={searchParams.campaign_name} />
        {duplicate && <input type="text" hidden name="duplicate" defaultValue={'true'} />}

        <DataTable columns={columns} data={lists} />
      </form>
    </div>
  );
};
