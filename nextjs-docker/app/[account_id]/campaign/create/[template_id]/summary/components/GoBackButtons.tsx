'use client';
import { Button } from '@/components/ui/button';
import Link from 'next/link';
import { useParams, useSearchParams } from 'next/navigation';
import React from 'react';

const GoBackButton = () => {
  const params = useParams();
  const searchParams = useSearchParams();

  const campaignName = searchParams.get('campaign_name');
  const listIds = searchParams.getAll('list');
  const template_id = params.template_id;
  const account_id = params.account_id;
  const duplicate = searchParams.get('duplicate') ? `&duplicate=${searchParams.get('duplicate')}` : '';
  const retarget = searchParams.get('retarget') ? `&retarget=true` : '';
  let listIdsJoined = listIds.join('&list=');
  let linkUrl = `/${account_id}/campaign/create/${template_id}?campaign_name=${campaignName}&list=${listIdsJoined}${duplicate}`;
  if (retarget) {
    linkUrl = `/${account_id}/campaign/create/template/${template_id}?campaign_name=${campaignName}&list=${listIdsJoined}${duplicate}${retarget}`;
  }

  return (
    <Link href={linkUrl}>
      <Button type="button" variant={'link'}>
        Go Back
      </Button>
    </Link>
  );
};

export default GoBackButton;
