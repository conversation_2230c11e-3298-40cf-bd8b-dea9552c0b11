'use client';
import { useToast } from '@/components/ui/use-toast';
import { createCampaign, revalidateCampaignAndRedirect } from '@/lib/actions';
import { IDuplicateData, IExpandedList, IMessagingLimit, ITemplateDatabase } from '@/lib/types';
import dayjs from 'dayjs';
import { useEffect } from 'react';
import { useFormState } from 'react-dom';
import CampaignCharacterCountInput from '../../../components/CampaignCharacterCountInput';
import Schedule from './Schedule';

type ISummaryProps = {
  params: Record<string, string>;
  searchParams: { [key: string]: string[] | string };
  res: IExpandedList[];
  template: ITemplateDatabase;
  messagingLimit: IMessagingLimit | null;
  listLength: number;
  campaignDuplicateLeadArray: IDuplicateData[];
  totalCost: number;
  currency: string;
};

const Summary = ({ params, searchParams, res, template, messagingLimit, listLength, campaignDuplicateLeadArray, totalCost, currency }: ISummaryProps) => {
  const account_id = params.account_id;
  const initialState: any = {
    message: {
      status: 0,
      type: '',
      scheduled: null,
    },
  };

  const [state, formAction] = useFormState(createCampaign, initialState);
  const { toast } = useToast();
  useEffect(() => {
    if (state?.message.status === 403) {
      toast({
        variant: 'destructive',
        description: state?.message.error,
      });
    }
    if (state?.message.status == 400) {
      toast({
        variant: 'destructive',
        description: `Something went wrong. Please try again later`,
      });
    }
    if (state?.message.status == 200) {
      if (state?.message.type == 'sendNow') {
        toast({
          variant: 'success',
          description: 'Campaign sent successfully',
        });
      } else {
        toast({
          variant: 'success',
          description: `Campaign Scheduled for ${dayjs(state?.message.scheduled)}`,
        });
      }
      revalidateCampaignAndRedirect(account_id)
        .then()
        .catch((e) => console.log(e));
    }
  }, [state]);
  return (
    <form className="space-y-4 w-full px-10" action={formAction}>
      <input type="text" hidden name="account_id" defaultValue={params.account_id} />
      <input type="text" hidden name="template_id" defaultValue={params.template_id} />
      <input type="text" hidden name="template_name" defaultValue={template.template_name} />
      <input type="text" hidden name="list_id" defaultValue={JSON.stringify([searchParams.list])} />
      <input type="number" hidden name="duplicateLeadsLength" defaultValue={campaignDuplicateLeadArray.length} />
      {!searchParams.duplicate && !state?.message?.error?.includes('Select another unique name') && (
        <input type="text" hidden name="campaign_name" defaultValue={searchParams.campaign_name} />
      )}
      {(searchParams.duplicate || state?.message?.error?.includes('Select another unique name')) && (
        <>
          <div className="text-center font-bold py-2 bg-white rounded-lg shadow-sm ">Campaign Name</div>
          <div className="flex items-center space-x-4">
            <CampaignCharacterCountInput message={state?.message.error ?? ''} />
          </div>
          {state?.message.status == 403 && <div className="text-xs text-center text-red-600 mt-2">{state.message.error}</div>}
        </>
      )}
      <Schedule
        lists={res}
        template={template}
        params={params}
        searchParams={searchParams}
        campaign_name={searchParams.campaign_name as string}
        messagingLimit={messagingLimit}
        listLength={listLength}
        campaignDuplicateLeadArray={campaignDuplicateLeadArray}
        totalCost={totalCost}
        currency={currency}
      />
    </form>
  );
};

export default Summary;
