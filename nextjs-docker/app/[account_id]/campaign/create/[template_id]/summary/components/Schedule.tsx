'use client';

import { Label } from '@/components/ui/label';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { IDuplicateData, IExpandedList, IMessagingLimit, ITemplateDatabase } from '@/lib/types';
import { AlertCircle, ChevronRight, CircleCheck, ExternalLink, Loader2, Pencil, RefreshCcw, UserMinus, Users, UserX } from 'lucide-react';
import { useState } from 'react';

import { Button } from '@/components/ui/button';

import CancelButton from '@/components/shared/CancelButton';
import TemplatePreview from '@/components/shared/TemplatePreview';
import TemplatePreviewCarousel from '@/components/shared/TemplatePreviewCarousel';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Checkbox } from '@/components/ui/checkbox';
import { HoverCard, HoverCardContent, HoverCardTrigger } from '@/components/ui/hover-card';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
import { AdapterDayjs } from '@mui/x-date-pickers/AdapterDayjs';
import { DateTimePicker } from '@mui/x-date-pickers/DateTimePicker';
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';
import clsx from 'clsx';
import dayjs, { Dayjs } from 'dayjs';
import Link from 'next/link';
import { useSearchParams } from 'next/navigation';
import { useFormStatus } from 'react-dom';
import GoBackButton from './GoBackButtons';

const Schedule = ({
  lists,
  template,
  params,
  searchParams,
  campaign_name,
  messagingLimit,
  listLength,
  campaignDuplicateLeadArray,
  totalCost,
  currency,
}: {
  lists: IExpandedList[];
  template: ITemplateDatabase;
  params: Record<string, string>;
  searchParams: { [key: string]: string[] | string };
  campaign_name: string;
  messagingLimit: IMessagingLimit | null;
  listLength: number;
  campaignDuplicateLeadArray: IDuplicateData[];
  totalCost: number;
  currency: string;
}) => {
  const { pending } = useFormStatus();
  const advancedFormat = require('dayjs/plugin/advancedFormat');
  dayjs.extend(advancedFormat);
  const [selected, setSelected] = useState('sendNow');
  const [date, setDate] = useState<Dayjs | null>(dayjs());
  const [checked, setChecked] = useState<boolean>(false);
  const [auto, setAuto] = useState<boolean | 'indeterminate'>(false);
  const [listArray, setListArray] = useState<{
    list: { listName: string; listId: string }[];
    leadId: string;
    leadName: string;
  }>();
  const searchParamsHook = useSearchParams();
  const listIds = searchParamsHook.getAll('list');
  const duplicate = searchParamsHook.get('duplicate') ? `&duplicate=${searchParamsHook.get('duplicate')}` : '';
  const retarget = searchParamsHook.get('retarget') ? `&retarget=true` : '';
  let linkUrl = '';
  if (listIds && listIds.length > 0) {
    let listIdsJoined = listIds.join('&list=');
    linkUrl = `/${params.account_id}/campaign/create/template/${template.id}?campaign_name=${campaign_name}&list=${listIdsJoined}${duplicate}${retarget}`;
  } else {
    linkUrl = `/${params.account_id}/campaign/create/template/${template.id}?campaign_name=${campaign_name}${duplicate}${retarget}`;
  }

  let totalContacts = 0;
  totalContacts -= campaignDuplicateLeadArray.length;
  let limitLeft = (messagingLimit?.remaining_limit ?? 0) - totalContacts;
  const isDisabled = () => {
    if ((messagingLimit?.remaining_limit ?? 0) < totalContacts) {
      return { disabled: true, text: 'Message limit quota exceeding' };
    }
    if (selected == 'schedule' && !date) {
      return { disabled: true, text: 'Select Date' };
    }
    if (selected == 'sendNow' && !checked) {
      return { disabled: true, text: 'Agree with warning terms' };
    }
    if (auto) {
      return { disabled: false, text: '' };
    }
    if (campaignDuplicateLeadArray.length == 0 && !auto) {
      return { disabled: false, text: '' };
    }
    if (!auto) {
      return { disabled: true, text: 'Agree with wetarseel decide' };
    }
    return { disabled: false, text: '' };
  };

  return (
    <div className="space-y-4">
      <div>
        <div className="text-xl font-bold mb-2">Campaign Details</div>
        <div className="p-5 bg-white rounded-lg shadow-sm">
          <div className="font-bold text-xl pb-5">List Details</div>
          <div className={`grid grid-cols-${Math.min(listLength, 5)} gap-4 mb-4`}>
            {lists.map((list) => {
              let leads = 0;
              let optedOutLeads = 0;
              let inActiveLeads = 0;
              let blockLeads = 0;
              list?.expand?.leads_via_list.forEach((listItem) => {
                leads += 1;
                if (!listItem.active) {
                  inActiveLeads += 1;
                } else if (listItem.opt_out) {
                  optedOutLeads += 1;
                } else if (listItem.blocked) {
                  blockLeads += 1;
                }
              });
              const activeContacts = leads - inActiveLeads - optedOutLeads - blockLeads;
              totalContacts += activeContacts;
              limitLeft = (messagingLimit?.remaining_limit ?? 0) - totalContacts;

              return (
                <Link
                  key={list.id}
                  href={`/${params.account_id}/list/view-list?listId=${list.id}&listName=${list.name}`}
                  target="_blank"
                  className="no-underline text-inherit"
                >
                  <Card className="overflow-hidden transition-shadow hover:shadow-md hover:bg-green-50">
                    <CardHeader className="bg-green-50 p-4">
                      <CardTitle className="flex items-center justify-between">
                        <TooltipProvider delayDuration={0}>
                          <Tooltip>
                            <TooltipTrigger asChild>
                              <div className="flex space-x-2 items-center w-full">
                                <div className="text-green-700 text-sm truncate flex-1">{list.name}</div>
                                <ExternalLink className="h-4 w-4 text-green-500 flex-shrink-0" />
                              </div>
                            </TooltipTrigger>
                            <TooltipContent>{list.name}</TooltipContent>
                          </Tooltip>
                        </TooltipProvider>
                      </CardTitle>
                    </CardHeader>
                    <CardContent className="p-4">
                      <div className="space-y-2">
                        <div className="flex items-center justify-between">
                          <div className="flex items-center text-sm text-gray-600">
                            <Users className="h-4 w-4 mr-2 flex-shrink-0" />
                            <span className="truncate mr-2">Leads:</span>
                          </div>
                          <Badge variant={'secondary'}>{leads}</Badge>
                        </div>
                        <div className="flex items-center justify-between">
                          <span className="flex items-center text-sm text-gray-600">
                            <UserMinus className="h-4 w-4 mr-2 flex-shrink-0" />
                            <span className="truncate mr-2">Opted-Out Leads:</span>
                          </span>
                          <Badge variant={'secondary'}>{optedOutLeads}</Badge>
                        </div>
                        <div className="flex items-center justify-between">
                          <span className="flex items-center text-sm text-gray-600">
                            <UserMinus className="h-4 w-4 mr-2 flex-shrink-0" />
                            <span className="truncate mr-2">Blocked Leads:</span>
                          </span>
                          <Badge variant={'secondary'}>{blockLeads}</Badge>
                        </div>
                        <div className="flex items-center justify-between">
                          <span className="flex items-center text-sm text-gray-600">
                            <UserX className="h-4 w-4 mr-2 flex-shrink-0" />
                            <span className="truncate mr-2">In-Active Leads:</span>
                          </span>
                          <Badge variant={'secondary'}>{inActiveLeads}</Badge>
                        </div>
                        <div className="flex items-center justify-between pt-2 border-t">
                          <span className="flex items-center font-medium">
                            <span className="truncate mr-2">Total Contacts:</span>
                          </span>
                          <Badge variant={'black'}>{activeContacts}</Badge>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                </Link>
              );
            })}
          </div>
          <div className="font-bold text-xl pb-2">Duplicate Leads</div>
          {campaignDuplicateLeadArray.length > 0 ? (
            <div>
              <Alert variant="destructive" className="">
                <AlertCircle className="h-4 w-4" />
                <AlertTitle>Oops!</AlertTitle>
                <AlertDescription>
                  You have <span className="font-semibold">{campaignDuplicateLeadArray.length}</span> duplicate leads.
                </AlertDescription>
              </Alert>
              <div className="my-4">
                <div className="flex items-center space-x-2">
                  <Checkbox id="auto" onCheckedChange={(e) => setAuto(e)} />
                  <Label htmlFor="auto" className="text-lg hover:cursor-pointer">
                    Let <span className="font-bold text-green-500">Wetarseel</span> decide
                  </Label>
                  <div className="text-xs">(Wetarseel will automatically filter out duplicate contacts and send only one message to each contact)</div>
                </div>
                <div>Or</div>
                <div className="font-semibold">Remove duplicate leads manually by editing the list</div>
                <div className="flex space-x-2 items-center">
                  <div className="text-xs text-gray-800">Refresh the page once you edit the list</div>
                  <RefreshCcw className="h-4 w-4" />
                </div>
              </div>
              <div className={`grid grid-cols-2 gap-4 mb-4`}>
                <Card className="overflow-hidden transition-shadow hover:shadow-md">
                  <CardHeader className="bg-gray-200 py-2 px-4">
                    <CardTitle className="flex items-center justify-between">
                      <div className="text-black text-base truncate flex-1">Leads</div>
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="mt-4 p-0">
                    <ScrollArea className="h-40">
                      {campaignDuplicateLeadArray.map((item) => (
                        <div
                          className={clsx('hover:bg-gray-100 px-4 hover:cursor-pointer', listArray?.leadName == item.leadName ? 'bg-gray-100' : 'bg-white')}
                          onClick={() =>
                            setListArray({
                              list: item.multipleList,
                              leadName: item.leadName,
                              leadId: item.leadId,
                            })
                          }
                        >
                          <div className="flex border-b border-gray-200 py-2">
                            <div className="flex-1">{item.leadName}</div>
                            <ChevronRight />
                          </div>
                        </div>
                      ))}
                    </ScrollArea>
                  </CardContent>
                </Card>
                <Card className="overflow-hidden transition-shadow hover:shadow-md">
                  <CardHeader className="bg-gray-200 py-2 px-4">
                    <CardTitle className="flex items-center justify-between">
                      <div className="text-black text-base truncate flex-1">List</div>
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="p-4">
                    <ScrollArea className="h-40">
                      {listArray?.list ? (
                        <div>
                          <div className="font-bold pb-2 text-base border-b border-gray-200">{listArray?.leadName}</div>
                          {listArray?.list.map((item) => {
                            return (
                              <div className="flex border-b border-gray-200 py-2">
                                <Link
                                  className="flex space-x-2 items-center w-full hover:underline"
                                  target="_blank"
                                  href={`/${params.account_id}/list/edit-list?listId=${item.listId}&listName=${item.listName}&leadId=${listArray.leadId}`}
                                >
                                  <div className="text-ellipsis overflow-hidden flex-1">{item.listName}</div>
                                  <Pencil className="h-4 w-4  flex-shrink-0" />
                                </Link>
                              </div>
                            );
                          })}
                        </div>
                      ) : (
                        <div>Select Lead</div>
                      )}
                    </ScrollArea>
                  </CardContent>
                </Card>
              </div>
            </div>
          ) : (
            <Alert variant="success" className="mb-2">
              <CircleCheck className="h-4 w-4" />
              <AlertTitle>Congratulations</AlertTitle>
              <AlertDescription>You don't have any duplicate leads.</AlertDescription>
            </Alert>
          )}
          <div className="flex space-x-2 items-center my-4">
            <div>
              <HoverCard openDelay={0}>
                <HoverCardTrigger className="hover:cursor-help">
                  <div className="flex items-center">
                    <div className="font-bold w-48">Selected Template:</div>
                    <Badge variant={'secondary'} className="text-base">
                      {template.template_name}
                    </Badge>
                  </div>
                </HoverCardTrigger>
                <HoverCardContent className="w-full max-w-[360px] h-64 overflow-auto">
                  {template?.type == 'basic-template' || template?.type == 'utility-template' ? (
                    <TemplatePreview template={template} />
                  ) : (
                    <TemplatePreviewCarousel template={template} />
                  )}
                </HoverCardContent>
              </HoverCard>
            </div>
            <Button variant={'link'} asChild size={'sm'}>
              <Link href={linkUrl}>Change Template</Link>
            </Button>
          </div>
          {!searchParams.duplicate && (
            <div className="flex items-center my-4">
              <div className="w-48 font-bold">Campaign Name:</div>
              <div>{campaign_name ?? ''}</div>
            </div>
          )}
          <div className="">
            <div className="mb-1 text-gray-600 font-normal">Message quota after sending the campaign:</div>
            <div className="flex items-center space-x-2 mb-2">
              <div className="font-semibold text-lg">
                {messagingLimit?.remaining_limit ?? 0} - {totalContacts}
              </div>
              <ChevronRight className="mx-2" />
              <Badge
                variant={
                  (messagingLimit?.remaining_limit ?? 0) - totalContacts == 0
                    ? 'pending'
                    : (messagingLimit?.remaining_limit ?? 0) - totalContacts < 0
                      ? 'destructive'
                      : 'success'
                }
              >
                {(messagingLimit?.remaining_limit ?? 0) - totalContacts}
              </Badge>
            </div>
            {limitLeft < 0 && (
              <Alert variant="destructive">
                <AlertCircle className="h-4 w-4" />
                <AlertTitle>Quota Exceeded</AlertTitle>
                <AlertDescription>Your message quota is exceeding the limit. Please adjust your campaign settings.</AlertDescription>
              </Alert>
            )}
            {limitLeft == 0 && (
              <Alert variant="warning">
                <AlertCircle className="h-4 w-4" />
                <AlertTitle>Quota Finish</AlertTitle>
                <AlertDescription>Message quota will finish</AlertDescription>
              </Alert>
            )}
          </div>
        </div>
      </div>

      <div id="campaign-cost" className="p-6 bg-sky-100 rounded-xl shadow-md">
        {/* <div className="flex items-center"> */}
        <Label className="text-2xl tracking-tight font-extralight text-gray-800">Estimated Cost of running this campaign is</Label>
        <div className="flex items-center">
          <span className="text-2xl font-thin text-gray-900">{totalCost.toFixed(2)}</span>
          <span className="ml-2 text-sm font-medium text-gray-500">{currency}</span>
        </div>
        {/* </div> */}
        <div className="text-sm font-normal mt-3 text-gray-600 leading-relaxed border-t border-gray-300 pt-3">
          <div className="font-semibold text-gray-800">Note:</div>
          <div>
            The cost of this campaign may vary due to fluctuations in USD exchange rates. The WhatsApp marketing message costs are charged in USD and differ by
            country. This estimated cost is based on successful delivery to each lead in this campaign.
          </div>
          <div>This cost assumes all of the messages are succesfully delivered. If messages get failed to send, then the cost will be less.</div>
        </div>
      </div>

      <div id="schedule-campaign" className="p-5 bg-white rounded-lg shadow-sm">
        <RadioGroup className="space-y-4" onValueChange={(value) => setSelected(value)} value={selected}>
          <div className="flex items-center space-x-2">
            <RadioGroupItem value="sendNow" id="r1" />
            <Label htmlFor="r1">Send Now</Label>
          </div>
          <div className="flex items-center space-x-2">
            <RadioGroupItem value="schedule" id="r2" />
            <Label htmlFor="r2">Schedule</Label>
          </div>
        </RadioGroup>
        {selected == 'schedule' && (
          <div>
            <div className="gap-4 flex items-center">
              <div className="font-bold w-32">Select Date</div>
              <LocalizationProvider dateAdapter={AdapterDayjs}>
                <DateTimePicker
                  label="Select Date"
                  value={date}
                  onChange={(newValue) => setDate(newValue)}
                  disablePast
                  slotProps={{
                    field: {
                      readOnly: true,
                    },
                  }}
                />
              </LocalizationProvider>
            </div>
          </div>
        )}
      </div>
      {selected == 'sendNow' && (
        <Alert variant="destructive" className="bg-white">
          <AlertCircle className="h-4 w-4" />
          <AlertTitle>Warning</AlertTitle>
          <div className="flex items-center space-x-2 my-2">
            <Checkbox id="terms" className="border-red-500/50" checked={checked} onCheckedChange={(checked: boolean) => setChecked(checked)} />
            <label htmlFor="terms">
              <AlertDescription>Are you sure! It will send message</AlertDescription>
            </label>
          </div>
        </Alert>
      )}
      <input type="text" value={selected} name="sendType" hidden />
      <input type="text" value={date?.toDate().toUTCString()} name="date" hidden />
      {/* Added 5 mintues and set seconds to 0 because when campaign is created the date can be off by some minutes */}
      {/* If the retry is before 24 hours facebook will have issues, that is why it is ok to have some grace period */}
      <input type="text" value={dayjs(date).add(5, 'minute').add(1, 'day').set('second', 0).toDate().toUTCString()} name="next_retry_date" hidden />
      <input type="text" value={totalContacts} name="totalContacts" hidden />
      <div className="w-full flex justify-between pb-10">
        <CancelButton />
        <div className="flex justify-between gap-4">
          <GoBackButton />
          {pending ? (
            <Button disabled>
              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              Adding {totalContacts} messages to queue. This can take some time
            </Button>
          ) : (
            <HoverCard openDelay={0}>
              <HoverCardTrigger className="hover:cursor-help">
                <Button id="send-campaign" type="submit" disabled={isDisabled().disabled}>
                  Create Campaign
                </Button>
              </HoverCardTrigger>
              {isDisabled().disabled && (
                <HoverCardContent className="text-xs">
                  <div>{isDisabled().text}</div>
                </HoverCardContent>
              )}
            </HoverCard>
          )}
        </div>
      </div>
    </div>
  );
};

export default Schedule;
