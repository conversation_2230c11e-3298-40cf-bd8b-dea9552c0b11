import { getAllLeadsByListIds, getBussinessCurrency, getMessageLimitsByUser, getPricingData, getTemplateFromDbByTemplateId, getUser } from '@/lib/pocket';
import { IDuplicateData, IExpandedList, User } from '@/lib/types';
import Summary from './components/Summary';
import { Converter } from 'easy-currencies';
export const dynamic = 'force-dynamic';
export const fetchCache = 'default-no-store';

export default async ({ params, searchParams }: { params: Record<string, string>; searchParams: { [key: string]: string[] | string } }) => {
  const user = await getUser();
  if (!user) {
    return; // User not found
  }
  let messagingLimit = await getMessageLimitsByUser(user.id, params.account_id);
  const template = await getTemplateFromDbByTemplateId(params.template_id);

  const converter = new Converter();
  const accountCurrency: any = await getBussinessCurrency(params.account_id);
  const currency = accountCurrency ? accountCurrency : 'USD';
  const list_ids = [searchParams.list].flat();
  const res = (await getAllLeadsByListIds(list_ids)) as IExpandedList[];
  let combinedLeads: any = [];

  {
    /*campaign estimate cost*/
  }
  let totalCost = 0;
  const pricingList = await getPricingData();
  const others_cost = pricingList?.filter((pricing) => pricing.country_name == 'other');

  res.forEach((res) => {
    res?.expand?.leads_via_list.forEach((lead) => {
      combinedLeads.push([lead.id, lead]);
    });
  });
  const combinedLeads_without_duplicacy = Array.from(new Map(combinedLeads).values());
  const validLeads = combinedLeads_without_duplicacy.filter((lead: any) => lead.active && !lead.opt_out && !lead.blocked);

  validLeads.forEach((lead: any) => {
    if (lead?.country) {
      const country = lead?.country;
      const countryCost = pricingList?.filter(
        (pricing) => country.toUpperCase() == pricing.country_name.toUpperCase() || pricing.iso_codes.includes(country.toUpperCase())
      );
      if (countryCost?.length) {
        if (template.category == 'UTILITY') {
          totalCost = totalCost + countryCost[0].utility;
        } else {
          totalCost = totalCost + countryCost[0].marketing;
        }
      } else {
        if (template.category == 'UTILITY') {
          totalCost = totalCost + (others_cost && others_cost[0].utility);
        } else {
          totalCost = totalCost + (others_cost && others_cost[0].marketing);
        }
      }
    } else {
      if (template.category == 'UTILITY') {
        totalCost = totalCost + (others_cost && others_cost[0].utility);
      } else {
        totalCost = totalCost + (others_cost && others_cost[0].marketing);
      }
    }
  });

  const convertedValue = await converter.convert(totalCost, 'USD', currency);

  type leadData = {
    leadName: string;
    leadId: string;
    listName: string;
    optOut: boolean;
    status: boolean;
    listId: string;
  };

  let campaignDuplicateLeadArray: IDuplicateData[] = [];
  let duplicate_array = [];

  for (let list of res) {
    let data_array = [];
    if (list?.expand) {
      for (let lead of list?.expand?.leads_via_list) {
        data_array.push({
          leadName: lead.name,
          leadId: lead.id,
          listName: list.name,
          listId: list.id,
          optOut: lead.opt_out,
          blocked: lead.blocked,
          status: lead.active,
        });
      }
      duplicate_array.push(data_array);
    }
  }

  const flattenedArray = duplicate_array.flat();

  const map = new Map();

  flattenedArray.forEach((item) => {
    const key = item.leadId;
    if (map.has(key)) {
      map.set(key, [...map.get(key), item]);
    } else {
      map.set(key, [item]);
    }
  });

  const duplicates: leadData[] = [];

  map.forEach((value) => {
    if (value.length > 1) {
      duplicates.push(...value);
    }
  });

  const groupedData: Record<string, IDuplicateData> = duplicates.reduce(
    (acc, curr) => {
      const { leadId, leadName, listName, optOut, status, listId } = curr;

      if (!acc[leadId]) {
        acc[leadId] = {
          leadName,
          leadId,
          optOut,
          status,
          multipleList: [],
        };
      }

      acc[leadId].multipleList.push({ listName, listId });

      return acc;
    },
    {} as Record<string, IDuplicateData>
  );

  const structuredArray = Object.values(groupedData);

  if (structuredArray.length) {
    structuredArray.forEach((item: any) => {
      campaignDuplicateLeadArray.push(item);
    });
  }
  return (
    <Summary
      params={params}
      searchParams={searchParams}
      res={res}
      template={template}
      messagingLimit={messagingLimit}
      listLength={res.length}
      campaignDuplicateLeadArray={campaignDuplicateLeadArray}
      totalCost={convertedValue}
      currency={currency}
    />
  );
};
