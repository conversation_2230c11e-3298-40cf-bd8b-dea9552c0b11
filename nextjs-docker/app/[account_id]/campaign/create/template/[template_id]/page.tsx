import { getTemplateFromDbByTemplateId } from '@/lib/pocket';
import type { Metadata } from 'next';
import VarParamsForCampaignTemplate from './components/VarParamsForCampaignTemplate';
import VarParamsForCampaignTemplateCarousel from './components/VarParamsForCampaignCarouselTemplate';

export const metadata: Metadata = {
  title: 'Template Preview',
  description: '...',
};

export const dynamic = 'force-dynamic';
// export const fetchCache = 'default-no-store'

export default async ({ params, searchParams }: { params: Record<string, string>; searchParams: { [key: string]: string } }) => {
  // noStore();
  const template = await getTemplateFromDbByTemplateId(params.template_id);
  const option = ['name', 'phone_number'];
  return (
    <div className="flex h-full flex-col bg-gray-100 space-y-4">
      <div className="w-[340px]">
        {(template.type == 'basic-template' || template.type == 'utility-template') && (
          <VarParamsForCampaignTemplate accountId={params.account_id} optionArray={option} template={template} campaign_name={searchParams.campaign_name} />
        )}
        {template.type == 'carousel-template' && (
          <VarParamsForCampaignTemplateCarousel
            accountId={params.account_id}
            optionArray={option}
            template={template}
            campaign_name={searchParams.campaign_name}
          />
        )}
      </div>
    </div>
  );
};
