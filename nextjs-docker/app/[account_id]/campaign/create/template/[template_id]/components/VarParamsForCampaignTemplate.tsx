'use client';
import MessagePreview from '@/components/shared/MessagePreview';
import { IBody, ICallToActionBtn, IFooter, IHeader, IQuickReplyBtn } from '@/app/[account_id]/templates/manage/[template_operation]/types';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import MultipleSelector, { Option } from '@/components/ui/multiple-selector-template';
import { updateTemplateImage, updateTemplateParamsAction } from '@/lib/actions';
import { ITemplateDatabase } from '@/lib/types';
import { Loader2 } from 'lucide-react';
import { useEffect, useState } from 'react';
import { useFormState, useFormStatus } from 'react-dom';
import { uploadPdfDocument } from '@/lib/pocket';
import ImageGalleryDialog from '@/app/[account_id]/send-message/[id]/components/ImageGalleryDialog';
import DocumentGalleryDialog from '@/app/[account_id]/send-message/[id]/components/DocumentGalleryDialog';
import { useToast } from '@/hooks/use-toast';
import { useRouter } from 'next/navigation';
import { Toaster } from '@/components/ui/toaster';
import { createComponents } from '@/app/[account_id]/send-message/[id]/components/helper';
import VideoGalleryDialog from '@/app/[account_id]/send-message/[id]/components/VideoGalleryDialog';

type IVarParamsForCampaignTemplateProps = {
  template: ITemplateDatabase;
  optionArray: string[];
  accountId: string;
  campaign_name: string;
};

const VarParamsForCampaignTemplate = ({ template, optionArray, accountId, campaign_name }: IVarParamsForCampaignTemplateProps) => {
  const {
    header: initHeader,
    body: initBody,
    footer,
    callToActionButtons,
    quickReplyButtons,
    headerVariablesArray,
    bodyVariablesArray,
  } = createComponents({ components: template.template_body.components, template });

  const [header, _setHeader] = useState(initHeader);
  const [body, _setBody] = useState(initBody);

  const [loading, setLoading] = useState(false);

  const [galleryDialog, setGalleryDialog] = useState(false);

  const setHeader = (header: Partial<IHeader>) => {
    _setHeader((_header) => ({ ..._header, ...header }));
  };

  function updateBodyText(value: string, index: number) {
    _setBody((prevBody: any) => ({
      ...prevBody,
      example: {
        ...prevBody.example,
        body_text: prevBody.example.body_text.map((item: any, i: number) => (i === index ? value : item)),
      },
    }));
  }

  const handleBodyVariableChange = (val: string, index: number) => {
    updateBodyText(val, index);
    // bodyVariablesArray[index] = val;

    // setBody((prevBody) => ({
    //   ...prevBody,
    //   example: { body_text: bodyVariablesArray },
    // }));
  };

  const handleHeaderVariableChange = (val: string, index: number) => {
    headerVariablesArray[index] = val;
    setHeader({ example: { header_text: headerVariablesArray } });
  };

  const handleSetImage = async (url: string) => {
    setLoading(true);
    try {
      await updateTemplateImage(template, url);
    } catch (error) {
      alert('Error in updating image');
    }
    setLoading(false);
    setHeader({ fileUrl: url });
  };

  const handleSetFile = (url: string) => {
    setHeader({ fileUrl: url });
  };

  const handleDocumentChange = async (event: any) => {
    const file = event.target.files[0];
    const formData = new FormData();
    formData.append('file', file);
    const _file = await uploadPdfDocument(accountId, formData);
    handleSetImage(_file.url);
    setGalleryDialog(false);
    setLoading(false);
    try {
    } catch (error) {
      console.log(error);
      alert('some error occured');
    }
  };

  const OPTIONS: Option[] = optionArray.map((item) => ({
    label: item,
    value: item,
  }));

  const router = useRouter();
  const { toast } = useToast();

  const initialState = {
    message: {
      status: 0,
      description: '',
    },
  };

  const handleSubmit = updateTemplateParamsAction.bind(null, template, accountId, header.fileUrl ?? '', header, body, campaign_name);

  const [state, formAction] = useFormState(handleSubmit, initialState);

  const SendTemplateButton = () => {
    const { pending } = useFormStatus();
    return (
      <>
        {pending ? (
          <Button className="bg-gray-900" disabled>
            <Loader2 className="h-4 w-4 animate-spin mr-2" />
            Updating
          </Button>
        ) : (
          <Button className="w-32 bg-gradient-to-r from-green-500 to-emerald-500 hover:bg-gradient-to-bl focus:ring-4 focus:outline-none mb-0" type="submit">
            Update
          </Button>
        )}
      </>
    );
  };
  useEffect(() => {
    if (state?.message.status == 400) {
      toast({
        variant: 'destructive',
        description: state.message.description,
      });
    } else if (state?.message.status == 200) {
      toast({
        variant: 'success',
        description: state.message.description,
      });
      router.push(`/${accountId}/campaign/create/template/${template.id}?updated=true&campaign_name=${campaign_name}`);
    }
  }, [state]);
  return (
    <form action={formAction}>
      <div className="text-xl font-bold mb-4 text-center">Message Preview</div>
      {header.format == 'IMAGE' && (
        <div className="mb-2 bg-white p-4 rounded-md">
          <div className="mb-2">You can change the image or continue with default image</div>
          <div className="flex space-x-2 items-center">
            <ImageGalleryDialog handleSetImage={handleSetImage} accountId={accountId} />
            {loading && <Loader2 className="animate-spin text-blue-500" />}
          </div>
        </div>
      )}
      {header.format == 'VIDEO' && (
        <div className="mb-2 bg-white p-4 rounded-md">
          <div className="mb-2">You can change the video or continue with default video</div>
          <div className="flex space-x-2 items-center">
            <VideoGalleryDialog handleSetVideo={handleSetImage} accountId={accountId} />
            {loading && <Loader2 className="animate-spin text-blue-500" />}
          </div>
        </div>
      )}
      {header.format == 'DOCUMENT' && (
        <div className="mb-2">
          <DocumentGalleryDialog
            accountId={accountId}
            handleSetDocument={handleSetFile}
            handleDocumentChange={handleDocumentChange}
            loading={loading}
            setGalleryDialog={setGalleryDialog}
            galleryDialog={galleryDialog}
          />
        </div>
      )}
      <div>
        <MessagePreview body={body} footer={footer} callToActionButtons={callToActionButtons} header={header} quickReplyButtons={quickReplyButtons} />
      </div>
      {(header.example?.header_text || body.example) && (
        <div className="bg-white p-4 rounded-md shadow-lg">
          <div className="border-b border-gray-200">
            <div className="pb-4">
              <div>Parameters</div>
              <div className="pb-1">You can personalize messages. Select value from dropdown or type your own custom attribute and then press enter.</div>
              <div className="text-xs font-semibold">Click update when you are done setting parameters</div>
            </div>
          </div>
          <div className="gap-2">
            <div>
              {header.example && (
                <>
                  <div className="font-bold my-2">Header</div>
                  {header.example?.header_text?.map((headerVariables, index) => {
                    return (
                      <div className="flex space-x-2 items-center">
                        <div className="self-center">{index + 1}</div>
                        <div className="flex-1">
                          <div className="text-xs mb-1">Value</div>
                          <MultipleSelector
                            defaultOptions={OPTIONS}
                            placeholder="Type here"
                            hidePlaceholderWhenSelected
                            onChange={(option) => handleHeaderVariableChange(option[0]?.value, index)}
                            creatable
                            maxSelected={1}
                            className="bg-white"
                            emptyIndicator={<p className="text-center text-lg leading-10 text-gray-600 dark:text-gray-400">no results found.</p>}
                          />
                        </div>
                        <div className="flex-1">
                          <div className="text-xs mb-1">Default value</div>
                          <Input type="text" name={`header-default-${index + 1}`} placeholder="Parameter Value" required maxLength={15} />
                        </div>
                      </div>
                    );
                  })}
                </>
              )}
            </div>
            <div>
              {body.example && (
                <>
                  <div className="font-bold my-2">Body</div>
                  {body.example?.body_text.map((bodyVariables, index) => {
                    return (
                      <div className="flex space-x-2 items-center mb-3">
                        <div className="text-xs mt-4">{index + 1}</div>
                        <div>
                          <div className="text-xs mb-1">
                            Value <span className="text-red-500">*</span>
                          </div>
                          <MultipleSelector
                            defaultOptions={OPTIONS}
                            placeholder="Type here"
                            hidePlaceholderWhenSelected
                            onChange={(option) => handleBodyVariableChange(option[0]?.value, index)}
                            creatable
                            maxSelected={1}
                            className="bg-white"
                            emptyIndicator={<p className="text-center text-lg leading-10 text-gray-600 dark:text-gray-400">no results found.</p>}
                          />
                        </div>
                        <div>
                          <div className="text-xs mb-1">
                            Default value <span className="text-red-500">*</span>
                          </div>
                          <Input type="text" name={`body-default-${index + 1}`} placeholder="Parameter Value" required maxLength={15} />
                        </div>
                      </div>
                    );
                  })}
                </>
              )}
            </div>
          </div>
        </div>
      )}
      <div className="mt-4">
        {/* {saveChangesWarning && <div className="text-xs my-2 p-4 border rounded-md border-orange-400"><Info className="text-orange-400 mb-1"/>You have unchanged changes</div>} */}
        {(header.example?.header_text || body.example) && <SendTemplateButton />}
      </div>
      <Toaster />
    </form>
  );
};

export default VarParamsForCampaignTemplate;
