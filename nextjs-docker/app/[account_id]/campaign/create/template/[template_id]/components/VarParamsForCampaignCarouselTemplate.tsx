'use client';
import { createComponentsCarousel } from '@/app/[account_id]/send-message/[id]/components/helper';
import MessagePreviewCarousel from '@/app/[account_id]/templates/manage/[template_operation]/components/MessagePreviewCarousel';
import { whatsApptoHtml } from '@/app/[account_id]/templates/manage/[template_operation]/components/utils';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import MultipleSelector, { Option } from '@/components/ui/multiple-selector-template';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { updateTemplateParamsActionCarousel } from '@/lib/actions';
import { ITemplateDatabase } from '@/lib/types';
import { isTemplateSubmitDisabled } from '@/lib/utils';
import { Loader2 } from 'lucide-react';
import Link from 'next/link';
import { useState } from 'react';
import { useFormState, useFormStatus } from 'react-dom';

type IVarParamsForCampaignTemplateProps = {
  template: ITemplateDatabase;
  optionArray: string[];
  accountId: string;
  campaign_name: string;
};
const VarParamsForCampaignTemplateCarousel = ({ template, optionArray, accountId, campaign_name }: IVarParamsForCampaignTemplateProps) => {
  const componentsArray = template.template_body.components[1]?.cards || [];

  const cards = componentsArray.map((eachCard) => createComponentsCarousel(eachCard));

  const [cardsState, setCardsState] = useState(cards);
  const [selectedCardIndex, setSelectedCardIndex] = useState(0);
  const [carousel_params, setCarousel_params] = useState<any>();
  const [messageBody, setMessageBody] = useState<any>();

  // const submitDisabled = isTemplateSubmitDisabled(template, cardsState, messageBody, carousel_params);
  const submitDisabled = false;

  const hasVariables = !!template.template_body.components[0].example || cardsState.some((card: any) => !!card?.body?.example);

  const setCardState = (index: number, key: keyof (typeof cardsState)[number], value: any) => {
    setCardsState((prev: any) => prev.map((card: any, i: number) => (i === index ? { ...card, [key]: value } : card)));
  };

  const replaceVariablesToText = () => {
    let str = template.template_body.components[0].text;
    let regexPattern = /\{\{\d+\}\}/g;
    let matches = [];
    let values = undefined;
    try {
      matches = [...str.matchAll(regexPattern)];
      values = messageBody?.body ?? str;

      // Iterate through all matches
      for (let i = 0; i < matches.length; i++) {
        // Replace the match with the corresponding value
        if (values?.[i]) {
          str = str.replace(matches[i][0], values[i]);
        }
      }
      return whatsApptoHtml(str);
    } catch (error) {
      return str;
    }
  };

  const handleBodyVariableChange = (val: string, varIndex: number) => {
    const bodyVariablesArray = [...(cardsState[selectedCardIndex]?.components?.[1]?.example?.body_text || [])];
    bodyVariablesArray[varIndex] = val;
    handleCarouselParamsChange(val, varIndex, 'body');
  };

  const handleMessageBodyVariableChange = (val: string, varIndex: number, type: string) => {
    setMessageBody((prevParams: any) => {
      let updatedParams: any = {
        defaultBody: [...(prevParams?.defaultBody || [])],
        body: [...(prevParams?.body || [])],
      };
      if (type == 'defaultBody') {
        updatedParams.defaultBody[varIndex] = val;
      } else if (type == 'body') {
        updatedParams.body[varIndex] = val;
      }
      return updatedParams;
    });
  };

  const handleCarouselParamsChange = (value: string, index: number, type: string) => {
    setCarousel_params((prevParams: any) => {
      const updatedParams = [...(prevParams || [])];

      if (!updatedParams[selectedCardIndex]) {
        updatedParams[selectedCardIndex] = { body: [], defaultBody: [], cardIndex: selectedCardIndex };
      }

      const currentCard = updatedParams[selectedCardIndex];

      currentCard.body = currentCard.body || [];
      currentCard.defaultBody = currentCard.defaultBody || [];
      currentCard.cardIndex = selectedCardIndex;

      if (type == 'defaultBody') {
        currentCard.defaultBody[index] = value;
      } else if (type == 'body') {
        currentCard.body[index] = value;
      }
      return updatedParams;
    });
  };

  const OPTIONS: Option[] = optionArray.map((item) => ({
    label: item,
    value: item,
  }));

  const messageInitialState = {
    message: {
      status: 0,
    },
  };

  const handleSubmit = () => updateTemplateParamsActionCarousel(template, messageBody, carousel_params, campaign_name, accountId);

  const [state, formAction] = useFormState(handleSubmit, messageInitialState);
  const SendTemplateButton = () => {
    const { pending } = useFormStatus();
    return (
      <>
        {pending ? (
          <Button className="mt-4 bg-gray-900" disabled>
            <Loader2 className="h-4 w-4 animate-spin mr-2" />
            Updating
          </Button>
        ) : (
          <Button
            disabled={submitDisabled}
            className="mt-4 w-32 bg-gradient-to-r from-green-500 to-emerald-500 hover:bg-gradient-to-bl focus:ring-4 focus:outline-none mb-0"
            type="submit"
          >
            Update
          </Button>
        )}
      </>
    );
  };

  return (
    <form action={formAction}>
      {/* <form> */}
      <div className="text-xl font-bold mb-4 text-center">Message Preview</div>

      {/* Message Preview Carousel */}
      <div>
        <MessagePreviewCarousel body={template.template_body.components[0]} cards={cards} />
      </div>

      {/* variable box starts */}
      {hasVariables && (
        <div className="bg-white p-4 rounded-md shadow-lg">
          <div>Parameters</div>
          <div>You can personalize messages. Select value from dropdown or type your own custom attribute and then press enter.</div>

          {/* Message Body */}
          {template.template_body.components[0].example && (
            <div>
              <div className="font-bold my-2">Message Body</div>
              {template.template_body.components[0].example?.body_text.map((bodyVariables: any, index: any) => {
                return (
                  <div key={index} className="flex space-x-2 items-center mb-3">
                    <div className="text-xs mt-4">{index + 1}</div>
                    <div className="flex-1">
                      <div className="text-xs mb-1">
                        Value <span className="text-red-500">*</span>
                      </div>
                      <MultipleSelector
                        defaultOptions={OPTIONS}
                        placeholder="Type here"
                        hidePlaceholderWhenSelected
                        onChange={(option) => handleMessageBodyVariableChange(option[0]?.value, index, 'body')}
                        creatable
                        maxSelected={1}
                        className="bg-white"
                        emptyIndicator={<p className="text-center text-lg leading-10 text-gray-600 dark:text-gray-400">no results found.</p>}
                      />
                    </div>
                    <div className="flex-1">
                      <div className="text-xs mb-1">
                        Default value <span className="text-red-500">*</span>
                      </div>
                      {/* <Input type="text" name={`body-default-${index + 1}`} placeholder="Parameter Value" onChange={(e) => handleBodyVariableChange(e, null, index)} /> */}
                      <Input
                        type="text"
                        onChange={(e) => {
                          handleMessageBodyVariableChange(e.target.value, index, 'defaultBody');
                        }}
                        name={`body-default-${index + 1}`}
                        placeholder="Parameter Value"
                        required
                        maxLength={15}
                      />
                    </div>
                  </div>
                );
              })}
            </div>
          )}

          {/* Card Selection Dropdown */}
          {cardsState.some((card: any) => !!card?.body?.example) && (
            <div className="mt-8 mb-4">
              <div className="text-sm mb-2">Select card number to add parameters to</div>
              <Select value={selectedCardIndex.toString()} onValueChange={(value) => setSelectedCardIndex(Number(value))}>
                <SelectTrigger className="w-32 border-gray-400 border-2 ml-4">
                  <SelectValue placeholder="Select Card" />
                </SelectTrigger>
                <SelectContent>
                  {cardsState.map((_: any, index: number) => (
                    <SelectItem key={index} value={index.toString()}>
                      Card {index + 1}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          )}

          {/* Card Variables (Header & Body) */}
          {cardsState[selectedCardIndex].components[1]?.example && (
            <div>
              {cardsState[selectedCardIndex]?.components[1]?.example && <div className="font-bold mb-2">Body</div>}

              {cardsState[selectedCardIndex]?.components[1]?.example?.body_text.map((bodyVariables: any, index: number) => {
                return (
                  <div key={index} className="flex space-x-2 items-center">
                    <div className="self-center">{index + 1}</div>
                    <div className="flex-1">
                      <div className="text-xs mb-1">
                        Value <span className="text-red-500">*</span>
                      </div>
                      <MultipleSelector
                        value={
                          carousel_params?.[selectedCardIndex]?.body[index]
                            ? [
                                {
                                  label: carousel_params?.[selectedCardIndex]?.body[index]?.trim(),
                                  value: carousel_params?.[selectedCardIndex]?.body[index]?.trim(),
                                },
                              ]
                            : []
                        }
                        defaultOptions={OPTIONS}
                        placeholder="Type here"
                        hidePlaceholderWhenSelected
                        onChange={(option) => {
                          handleBodyVariableChange(option[0]?.value, index);
                        }}
                        creatable
                        maxSelected={1}
                        className="bg-white"
                        emptyIndicator={<p className="text-center text-lg leading-10 text-gray-600 dark:text-gray-400">no results found.</p>}
                      />
                    </div>
                    <div className="flex-1">
                      <div className="text-xs mb-1">
                        Default value <span className="text-red-500">*</span>
                      </div>
                      <Input
                        value={carousel_params?.[selectedCardIndex]?.defaultBody?.[index] ?? ''}
                        onChange={(e) => {
                          handleCarouselParamsChange(e.target.value, index, 'defaultBody');
                        }}
                        type="text"
                        name={`body-default-${index + 1}`}
                        placeholder="Parameter Value"
                        required
                        maxLength={15}
                      />
                    </div>
                  </div>
                );
              })}
            </div>
          )}
          <SendTemplateButton />
        </div>
      )}
      {/* variable box ends */}
    </form>
  );
};

export default VarParamsForCampaignTemplateCarousel;
