import CancelButton from '@/components/shared/CancelButton';
import { getAccount, getApprovedTemplatesFromDb } from '@/lib/pocket';
import type { Metadata } from 'next';
import { ReactElement } from 'react';
import GoBackButton from './component/GoBackButtons';
import { columns } from './component/columns';
import { DataTable } from './component/data-table';
import NextButton from './component/NextButton';

export const metadata: Metadata = {
  title: 'Select Template',
  description: '...',
};

export default async ({ params, children }: { params: Record<string, string>; children: ReactElement }) => {
  // noStore();

  // const template_body = JSON.parse(searchParams?.template_body.toString()) as IComponent[]
  const account = await getAccount(params.account_id);
  const templates = await getApprovedTemplatesFromDb(account);

  return (
    <div className="space-y-4 mb-10 w-full relative">
      <div className="text-center font-bold mt-5 py-2 bg-white rounded-lg shadow-sm w-full">Now select a template</div>
      <div className="flex space-x-4">
        <div className="p-5 bg-white rounded-lg shadow-sm w-full">
          <DataTable columns={columns} data={templates} />
        </div>
        {children}
      </div>
      <div className="flex sticky bottom-0 bg-gray-100 p-4 w-full">
        <div className="flex-1">
          <CancelButton />
        </div>
        <div className="flex justify-between gap-4">
          <GoBackButton />
          <NextButton templates={templates} />
        </div>
      </div>
    </div>
  );
};
