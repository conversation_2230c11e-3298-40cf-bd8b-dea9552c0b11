'use client';

import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { ITemplateDatabase } from '@/lib/types';
import { ColumnDef } from '@tanstack/react-table';
import dayjs from 'dayjs';
import { ArrowUpDown } from 'lucide-react';
import SelectButton from './SelectButton';

export const columns: ColumnDef<ITemplateDatabase>[] = [
  { accessorKey: 'id' },
  {
    accessorKey: 'template_name',
    header: ({ column }) => {
      return (
        <Button variant="ghost" className="px-0 mb-0" onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}>
          Template Name
          <ArrowUpDown className="ml-2 h-4 w-4" />
        </Button>
      );
    },
  },
  {
    accessorKey: 'template_body.category',
    id: 'Category',
    header: ({ column }) => {
      return (
        <Button variant="ghost" className="px-0 mb-0" onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}>
          Category
          <ArrowUpDown className="ml-2 h-4 w-4" />
        </Button>
      );
    },
    cell: ({ cell }) => {
      let value = cell.getValue() as string;
      return value.toLowerCase().charAt(0).toUpperCase() + value.toLowerCase().slice(1);
    },
  },
  {
    accessorKey: 'new_quality_score',
    header: ({ column }) => {
      return (
        <Button variant="ghost" className="px-0 mb-0">
          Quality
        </Button>
      );
    },
    cell: ({ row }) => {
      switch (row.original.new_quality_score) {
        case 'GREEN':
          return (
            <Badge className="rounded-full" variant={'success'}>
              High
            </Badge>
          );
          break;
        case 'YELLOW':
          return (
            <Badge className="rounded-full" variant={'pending'}>
              Medium
            </Badge>
          );
          break;
        case 'RED':
          return (
            <Badge className="rounded-full" variant={'destructive'}>
              Low
            </Badge>
          );
        default:
          return (
            <Badge className="rounded-full" variant={'unknown'}>
              Unkown
            </Badge>
          );
      }
    },
  },
  {
    accessorKey: 'type',
    header: ({ column }) => {
      return (
        <Button variant="ghost" className="px-0 mb-0" onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}>
          Created At
          <ArrowUpDown className="ml-2 h-4 w-4" />
        </Button>
      );
    },
    cell: ({ row }) => {
      return (
        <>
          {row.original.type
            ?.split('-')
            .map((word) => word.charAt(0).toUpperCase() + word.slice(1)) // Capitalize the first letter
            .join(' ')}
        </>
      );
    },
  },
  {
    accessorKey: 'created',
    header: ({ column }) => {
      return (
        <Button variant="ghost" className="px-0 mb-0" onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}>
          Created At
          <ArrowUpDown className="ml-2 h-4 w-4" />
        </Button>
      );
    },
    cell: ({ row }) => {
      return dayjs(row.getValue('created')).format('MMMM D, YYYY h:mm A');
    },
  },
  {
    id: 'business',
    header: ({ column }) => {
      return (
        <Button variant="ghost" className="px-0 mb-0" onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}>
          Business
        </Button>
      );
    },
    cell: ({ row }) => {
      return <>{row.original.expand?.account?.name}</>;
    },
  },
  {
    accessorKey: 'action',
    header: ({ column }) => {
      return <div>Action</div>;
    },
    cell: ({ row }) => {
      const template_id = row.getValue('id') as string;
      return <SelectButton template_id={template_id} />;
    },
  },
];
