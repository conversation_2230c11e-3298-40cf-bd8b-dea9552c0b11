'use client';

import { Button } from '@/components/ui/button';
import { ArrowRight } from 'lucide-react';
import Link from 'next/link';
import { useParams, useSearchParams } from 'next/navigation';
import React from 'react';

const SelectButton = ({ template_id }: { template_id: string }) => {
  const params = useParams();
  const searchParams = useSearchParams();
  const account_id = params.account_id;
  const campaign_name = searchParams.get('campaign_name');
  const listIds = searchParams.getAll('list');
  const duplicate = searchParams.get('duplicate') ? `&duplicate=true` : '';
  const retarget = searchParams.get('retarget') ? `&retarget=true` : '';

  let linkUrl = `/${account_id}/campaign/create/template/${template_id}?campaign_name=${campaign_name}${duplicate}${retarget}`;
  if (listIds && listIds.length > 0) {
    let listIdsJoined = listIds.join('&list=');
    linkUrl = `/${account_id}/campaign/create/template/${template_id}?campaign_name=${campaign_name}&list=${listIdsJoined}${duplicate}${retarget}`;
  }
  return (
    <Link href={linkUrl}>
      <Button className="bg-white border text-black mb-0 hover:text-white group">
        Select
        <ArrowRight className="h-4 w-4 ml-2 text-black group-hover:text-white" />
      </Button>
    </Link>
  );
};

export default SelectButton;
