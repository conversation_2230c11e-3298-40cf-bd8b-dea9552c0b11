'use client';
import { Button } from '@/components/ui/button';
import { ITemplateDatabase } from '@/lib/types';
import { ChevronRight } from 'lucide-react';
import Link from 'next/link';
import { useParams, useSearchParams } from 'next/navigation';
import { HoverCard, HoverCardContent, HoverCardTrigger } from '@/components/ui/hover-card';

const NextButton = ({ templates }: { templates: ITemplateDatabase[] }) => {
  const params = useParams();
  const searchParams = useSearchParams();
  const currentTemplate = templates.filter((template) => template.id == params.template_id)[0];
  const headerComponent = currentTemplate?.template_body.components.find((item) => item.type == 'HEADER');
  const bodyComponent = currentTemplate?.template_body.components.find((item) => item.type.toUpperCase() == 'BODY');
  const imageHeader = currentTemplate?.template_body?.components?.[0];

  const campaign_name = searchParams.get('campaign_name');
  const listIds = searchParams.getAll('list');
  const duplicate = searchParams.get('duplicate') ? `&duplicate=true` : '';
  const retarget = searchParams.get('retarget') ? `&retarget=true` : '';

  const account_id = params.account_id;
  const template_id = params.template_id;

  let linkUrl = `/${account_id}/campaign/create/${template_id}?campaign_name=${campaign_name}${duplicate}`;
  if (listIds && listIds.length > 0) {
    let listIdsJoined = listIds.join('&list=');
    linkUrl = `/${account_id}/campaign/create/${template_id}?campaign_name=${campaign_name}&list=${listIdsJoined}${duplicate}`;
    if (retarget) {
      linkUrl = `/${account_id}/campaign/create/${template_id}/summary?campaign_name=${campaign_name}&list=${listIdsJoined}${duplicate}${retarget}`;
    }
  }

  const setSaveableRoute = () => {
    if (searchParams.get('updated')) {
      return true;
    }
    if (headerComponent?.example?.header_text || bodyComponent?.example) {
      return false;
    }
    return true;
  };

  let btnText = 'Select List';
  if (retarget) {
    btnText = 'Next';
  }
  let saveableRoute = setSaveableRoute();

  return (
    <div id="select-list-campaign" className="w-full flex justify-end">
      {params.template_id && saveableRoute ? (
        <Button className="mb-0" asChild>
          <Link href={linkUrl}>
            {btnText} <ChevronRight className="ml-2 h-4 w-4" />
          </Link>
        </Button>
      ) : (
        <HoverCard openDelay={0}>
          <HoverCardTrigger className="hover:cursor-help">
            <Button className="mb-0" disabled>
              {btnText} <ChevronRight className="ml-2 h-4 w-4" />
            </Button>
          </HoverCardTrigger>
          <HoverCardContent className="text-xs">Click Update button first to update params</HoverCardContent>
        </HoverCard>
      )}
    </div>
  );
};

export default NextButton;
