'use client';
import { Button } from '@/components/ui/button';
import Link from 'next/link';
import { useParams, useSearchParams } from 'next/navigation';
import React from 'react';

const GoBackButton = () => {
  const pathname = useParams();
  const searchParams = useSearchParams();
  const { account_id, campaign_id } = pathname;
  const campaign_name = searchParams.get('campaign_name');
  return (
    <Link
      href={
        pathname.template_id
          ? `/${account_id}/campaign/create/template?campaign_name=${campaign_name}`
          : `/${account_id}/campaign/create?campaign_name=${campaign_name}`
      }
    >
      <Button type="button" className="mb-0" variant={'link'}>
        Go Back
      </Button>
    </Link>
  );
};

export default GoBackButton;
