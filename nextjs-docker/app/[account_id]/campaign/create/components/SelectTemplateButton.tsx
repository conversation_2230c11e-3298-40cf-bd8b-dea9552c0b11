import { Button } from '@/components/ui/button';
import { ChevronRight, Loader2 } from 'lucide-react';
import React from 'react';
import { useFormStatus } from 'react-dom';

const SelectTemplateButton = () => {
  const { pending } = useFormStatus();

  return (
    <>
      {pending ? (
        <Button disabled>
          Select Template <Loader2 className="animate-spin ml-2 h-4 w-4" />
        </Button>
      ) : (
        <Button type="submit">
          Select Template <ChevronRight className="ml-2 h-4 w-4" id="select-template-campaign" />
        </Button>
      )}
    </>
  );
};

export default SelectTemplateButton;
