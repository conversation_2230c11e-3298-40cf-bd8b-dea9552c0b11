'use client';
import clsx from 'clsx';
import { BookUser, CalendarCheck2, LayoutTemplateIcon, PencilIcon } from 'lucide-react';
import Link from 'next/link';
import { useParams, usePathname, useSearchParams } from 'next/navigation';
import React from 'react';

const CircleBreadCrumbs = () => {
  const pathname = usePathname();
  const params = useParams();
  const searchParams = useSearchParams();
  const campaignName = searchParams.get('campaign_name');
  const listIds = searchParams.getAll('list');
  const templateId = params.template_id;
  const duplicate = searchParams.get('duplicate') ? `&duplicate=true` : '';
  const retarget = searchParams.get('retarget') ? `&retarget=true` : '';

  let array2 = [
    {
      id: 1,
      text: 'Step 1: Campaign Name',
      description: 'Give your campaign a unique name',
      icon: PencilIcon,
      filled: false,
    },
    {
      id: 2,
      text: 'Step 2: Select a template',
      description: 'Choose an exciting template',
      icon: LayoutTemplateIcon,
      filled: false,
    },
    {
      id: 3,
      text: 'Step 3: Select lists',
      description: 'Select your contact lists',
      icon: BookUser,
      filled: false,
    },
    {
      id: 4,
      text: 'Step 4: Schedule your campaign',
      description: 'Select a best time',
      icon: CalendarCheck2,
      filled: false,
    },
  ];
  if (pathname.includes('campaign')) {
    array2[0].filled = true;
  }
  if (params.template_id || pathname.includes('template')) {
    array2[1].filled = true;
  }
  if (params.template_id && !pathname.includes('template')) {
    array2[2].filled = true;
  }
  if (pathname.includes('summary')) {
    array2[3].filled = true;
  }

  const getLinkUrl = (id: number) => {
    if (id == 1) {
      return `/${params.account_id}/campaign/create/`;
    }
    if (id == 2) {
      if (templateId) {
        if (listIds && listIds.length > 0) {
          let listIdsJoined = listIds.join('&list=');
          return `/${params.account_id}/campaign/create/template/${templateId}?campaign_name=${campaignName}&list=${listIdsJoined}${duplicate}${retarget}`;
        }
        return `/${params.account_id}/campaign/create/template/${templateId}?campaign_name=${campaignName}${duplicate}${retarget}`;
      }
      return `/${params.account_id}/campaign/create/template?campaign_name=${campaignName}${duplicate}${retarget}`;
    }
    if (id == 3) {
      if (listIds && listIds.length > 0) {
        let listIdsJoined = listIds.join('&list=');
        return `/${params.account_id}/campaign/create/${templateId}?campaign_name=${campaignName}&list=${listIdsJoined}${duplicate}${retarget}`;
      }
      return `/${params.account_id}/campaign/create/${templateId}?campaign_name=${campaignName}${duplicate}${retarget}`;
    }
    return `${pathname}?${searchParams.toString()}`;
  };
  return (
    <div className="flex items-center justify-center w-full">
      {array2.map((item) => {
        const IconComponent = item.icon;
        if (item.filled) {
          return (
            <Link
              aria-disabled={item.id == 3 && searchParams.get('retarget') != null}
              key={item.text}
              prefetch={false}
              className={clsx(
                'flex items-center space-x-4 p-4 bg-white border border-gray-100 border-b-4 border-b-green-600 w-full h-full hover:bg-green-50 hover:cursor-pointer',
                item.id == 3 && searchParams.get('retarget') != null && 'pointer-events-none'
              )}
              href={getLinkUrl(item.id)}
            >
              <div>
                <IconComponent className="text-green-600" />
              </div>
              <div>
                <div className="font-semibold text-sm text-green-600">{item.text}</div>
                <div className="text-sm text-gray-600">{item.description}</div>
              </div>
            </Link>
          );
        } else {
          return (
            <div key={item.text} className="flex items-center space-x-4 p-4 bg-white border border-gray-100 border-b-4 border-b-gray-600  w-full h-full">
              <div>
                <IconComponent className="text-gray-600" />
              </div>
              <div>
                <div className="font-semibold text-sm text-gray-800">{item.text}</div>
                <div className="text-sm text-gray-600">{item.description}</div>
              </div>
            </div>
          );
        }
      })}
    </div>
  );
};

export default CircleBreadCrumbs;
