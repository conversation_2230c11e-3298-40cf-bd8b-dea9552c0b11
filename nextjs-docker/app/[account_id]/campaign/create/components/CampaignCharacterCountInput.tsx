'use client';
import { HoverCard, HoverCardContent, HoverCardTrigger } from '@/components/ui/hover-card';
import { Input } from '@/components/ui/input';
import clsx from 'clsx';
import { CircleHelp } from 'lucide-react';
import { useSearchParams } from 'next/navigation';
import React, { useRef, useState } from 'react';

const CampaignCharacterCountInput = ({ message }: { message: string }) => {
  const searchParams = useSearchParams();
  const campaign_name = searchParams.get('campaign_name') ?? '';
  const [value, setValue] = useState(campaign_name);
  const inputRef = useRef<HTMLInputElement | null>(null);
  return (
    <>
      <div className="flex items-center space-x-4">
        <HoverCard openDelay={0}>
          <HoverCardTrigger>
            <div className={clsx('flex items-center space-x-1 hover:text-green-800 hover:cursor-help', message ? 'text-red-400' : 'text-black')}>
              <CircleHelp className="h-5 w-5" />
              <label htmlFor="campaign_name" className="whitespace-nowrap">
                Campaign Name
              </label>
            </div>
          </HoverCardTrigger>
          <HoverCardContent className="text-xs">
            You can enter any combination of letters, digits, spaces, and underscores. For example: John_Doe123 or hello world 2024
          </HoverCardContent>
        </HoverCard>
      </div>
      <Input
        required
        type="text"
        id="campaign_name"
        name="campaign_name"
        placeholder="Campaign Name"
        maxLength={30}
        onChange={(e) => setValue(e.target.value)}
        ref={inputRef}
        value={value}
        pattern="[a-zA-Z0-9 _]+"
        className={message && 'border-red-400'}
      />
      <div className="text-base">{value.length}/30</div>
    </>
  );
};

export default CampaignCharacterCountInput;
