'use client';
import { Button } from '@/components/ui/button';
import Link from 'next/link';
import { useParams, useSearchParams } from 'next/navigation';
import React from 'react';

const GoBackButton = () => {
  const pathname = useParams();
  const searchParams = useSearchParams();
  const campaign_name = searchParams.get('campaign_name');
  const { account_id, campaign_id } = pathname;
  return (
    <Link href={`/${account_id}/campaign/create/template?campaign_name=${campaign_name}`}>
      <Button type="button" variant={'link'}>
        Go Back
      </Button>
    </Link>
  );
};

export default GoBackButton;
