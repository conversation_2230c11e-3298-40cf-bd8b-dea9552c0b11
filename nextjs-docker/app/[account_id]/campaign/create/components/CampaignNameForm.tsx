'use client';
import CancelButton from '@/components/shared/CancelButton';
import { isCampaignNameUnique } from '@/lib/actions';
import { useFormState } from 'react-dom';
import CampaignCharacterCountInput from './CampaignCharacterCountInput';
import SelectTemplateButton from './SelectTemplateButton';

const CampaignNameForm = ({ params }: { params: Record<string, string> }) => {
  const campaignUnique = isCampaignNameUnique.bind(null, params.account_id);
  const [message, formAction] = useFormState(campaignUnique, null);
  return (
    <form action={formAction} className="space-y-5 w-3/4">
      <div>
        <div className="flex items-center space-x-4">
          <CampaignCharacterCountInput message={message?.message ?? ''} />
        </div>
        {message?.message && <div className="text-xs text-center text-red-600 mt-2">{message.message}</div>}
      </div>
      <div className="flex w-full justify-between">
        <CancelButton />
        <SelectTemplateButton />
      </div>
    </form>
  );
};

export default CampaignNameForm;
