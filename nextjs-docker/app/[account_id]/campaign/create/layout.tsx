import { Toaster } from '@/components/ui/toaster';
import { ReactElement } from 'react';
import CircleBreadCrumbs from './components/CircleBreadCrumbs';

export default async ({ params, children }: { params: Record<string, string>; children: ReactElement }) => {
  return (
    <div className="flex items-center min-h-full flex-col bg-gray-100 gap-4 p-6">
      <CircleBreadCrumbs />
      <>{children}</>
      <Toaster />
    </div>
  );
};
