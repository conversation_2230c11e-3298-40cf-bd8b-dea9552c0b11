import { Logger } from 'next-axiom';

import { getCampaignMessages } from '@/lib/pocket';
import <PERSON><PERSON><PERSON> from './MyChart';
import MyL<PERSON><PERSON><PERSON> from './MyLineChart';
import dayjs from 'dayjs';

export default async ({ params }: { params: Record<string, string> }) => {
  // noStore();
  type CampaignData = {
    sent: number;
    failed: number;
    delivered: number;
    read: number;
    pending: number;
    'sent from wetarseel': number;
  };

  type CampaignObject = {
    [campaignName: string]: CampaignData;
  };

  type DataObjectBar = CampaignObject[];

  const log = new Logger();
  const campaigns = await getCampaignMessages(params.account_id);
  // log.info("all messages of campaigns", campaignResults);
  await log.flush();

  const dataObjectPie = {
    sent: 0,
    failed: 0,
    delivered: 0,
    read: 0,
    pending: 0,
    'sent from wetarseel': 0,
  };
  const bgColors = [
    'rgba(75, 192, 192, 1)', //sent green
    'rgba(255, 99, 132, 1)', //failed red blue
    'rgba(255, 206, 86, 1)', //delivered yellow
    'rgba(54, 162, 235, 1)', //read blue
    'rgba(153, 102, 255, 1)', //pending purple
    'rgba(255, 159, 64, 1)', //sent from we tarseel orange
  ];
  const labels = Object.entries(dataObjectPie).map((item) => item[0]);
  let dataValues = campaigns.map((campaign) =>
    campaign.expand.messages_via_campaign.map((message) => {
      dataObjectPie[message.delivery_status] += 1;
    })
  );

  // const dataObjectBar = [{"campaign1": { sent: 0, failed: 0, delivered: 0, read: 0, pending: 0, "sent from wetarseel": 0 }}, ]
  let dataObjectBar: DataObjectBar = [];

  campaigns.map((campaign, index) =>
    campaign.expand.messages_via_campaign.map((message) => {
      const idx = dataObjectBar.findIndex((item) => item[campaign.name] !== undefined);
      if (idx !== -1) {
        dataObjectBar[idx][campaign.name][message.delivery_status] += 1;
      } else {
        let newCampaignData: CampaignData = {
          sent: 0,
          failed: 0,
          delivered: 0,
          read: 0,
          pending: 0,
          'sent from wetarseel': 0,
        };
        newCampaignData[message.delivery_status] = 1;
        let newCampaignObject: CampaignObject = {
          [campaign.name]: newCampaignData,
        };
        dataObjectBar.push(newCampaignObject);
      }
    })
  );

  const data = {
    labels: labels,
    datasets: [
      {
        label: 'Messages Overview',
        data: Object.entries(dataObjectPie).map((item) => item[1]),
        backgroundColor: [
          'rgba(75, 192, 192, 0.2)', //sent green
          'rgba(255, 99, 132, 0.2)', //failed red blue
          'rgba(255, 206, 86, 0.2)', //delivered yellow
          'rgba(54, 162, 235, 0.2)', //read blue
          'rgba(153, 102, 255, 0.2)', //pending purple
          'rgba(255, 159, 64, 0.2)', //sent from we tarseel orange
        ],
        borderColor: [
          'rgba(75, 192, 192, 1)', //sent green
          'rgba(255, 99, 132, 1)', //failed red blue
          'rgba(255, 206, 86, 1)', //delivered yellow
          'rgba(54, 162, 235, 1)', //read blue
          'rgba(153, 102, 255, 1)', //pending purple
          'rgba(255, 159, 64, 1)', //sent from we tarseel orange
        ],
        borderWidth: 1,
      },
    ],
  };

  const data2 = {
    labels: campaigns.map((campaign) => dayjs(campaign?.created).format('MMM DD')),
    datasets: dataObjectBar.map((campaignObject, idx) => {
      return {
        label: Object.keys(campaignObject)[0],
        data: campaignObject[Object.keys(campaignObject)[0]],
        backgroundColor: bgColors[idx],
        stack: 'Stack 0',
      };
    }),
  };
  return (
    <div className="max-w-3xl pt-10 mx-auto px-4 md:px-8">
      <MyChart data={data} />
      <MyLineChart data={data2} />
    </div>
  );
};
