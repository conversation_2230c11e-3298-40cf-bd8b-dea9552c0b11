import { getAccount, getRecentCampaigns } from '@/lib/pocket';
import { IPhoneVerificationResponse, User } from '@/lib/types';
import { server_component_pb } from '@/state/pb/server_component_pb';
import { Metadata, ResolvingMetadata } from 'next';
import Dashboard from './component/Dashboard';
import { Suspense } from 'react';

type Props = {
  params: { account_id: string };
  searchParams: { [key: string]: string | string[] | undefined };
};

export async function generateMetadata({ params, searchParams }: Props, parent: ResolvingMetadata): Promise<Metadata> {
  // read route params
  const accountId = params.account_id;
  let account = await getAccount(accountId);

  return {
    title: `Dashboard - ${account.name}`,
    description: `Dashboard - ${account.name}`,
  };
}

export default async ({ params }: { params: Record<string, string> }) => {
  const { pb, cookies } = await server_component_pb();
  const user = pb.authStore.record as User;

  if (!user) {
    return; // User not found
  }
  const appId = process.env.NEXT_PUBLIC_APP_ID;
  const accountId = params.account_id;
  let account = await getAccount(accountId);
  let campaigns = await getRecentCampaigns(account.id);

  let data = {
    verified_name: account.name,
    code_verification_status: account.code_verification_status,
    display_phone_number: account.display_phone_number,
    quality_rating: account.quality_rating,
    platform_type: account.platform_type,
    id: account.phone_id,
  } as IPhoneVerificationResponse;
  return (
    <div className="p-6 bg-gray-100 min-h-full">
      <Suspense fallback={<p>Loading</p>}>
        <Dashboard appId={appId} data={data} user={user} account={account} campaigns={campaigns} />
      </Suspense>
    </div>
  );
};
