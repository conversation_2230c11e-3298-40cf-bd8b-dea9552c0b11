'use client';

import { But<PERSON> } from '@/components/ui/button';
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from '@/components/ui/collapsible';
import { Dialog, DialogClose, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { InputOTP, InputOTPGroup, InputOTPSeparator, InputOTPSlot } from '@/components/ui/input-otp';
import { useToast } from '@/components/ui/use-toast';
import { revalidateFullPath, updateAccountAccessToken } from '@/lib/actions';
import { Account, IPhoneVerificationResponse } from '@/lib/types';
import { ChevronDown, Loader2, MessageCircle, MessageSquare, UserPlus2 } from 'lucide-react';
import { usePathname } from 'next/navigation';
import { useState } from 'react';

const VerificationSteps = ({ data, account }: { data: IPhoneVerificationResponse | undefined; account: Account }) => {
  const { toast } = useToast();
  const [loading, setLoading] = useState(false);
  const [pin, setValue] = useState('');
  const [OTPDialogOpen, setOTPDialogOpen] = useState(false);
  const [verifyOtpLoading, setVerifyOtpLoading] = useState(false);
  const [otp, setOTP] = useState('');
  const pathname = usePathname();

  const sendVerificationCode = async () => {
    setLoading(true);
    try {
      const res = await fetch(`/api/request-code?phone_number_id=${data?.id}`);
      const result = await res.json();
      if (result?.success) {
        toast({
          variant: 'success',
          description: 'OTP Sent Successfully',
        });
        setOTPDialogOpen(true);
      } else {
        toast({
          variant: 'destructive',
          title: result.error.error_user_title,
          description: `Error: ${result.error.error_user_msg}`,
        });
      }
    } catch (error) {
      toast({
        variant: 'destructive',
        description: `Error in sending OTP`,
      });
    }
    setLoading(false);
  };

  const handleVerifyCode = async () => {
    setVerifyOtpLoading(true);
    try {
      const res = await fetch(`/api/verify-code?phone_number_id=${data?.id}&code=${otp}`);
      const result = await res.json();
      if (result?.success) {
        toast({
          variant: 'success',
          description: 'Verification Successful',
        });
        setOTPDialogOpen(false);
        await updateAccountAccessToken(account);
        revalidateFullPath(pathname);
      } else {
        toast({
          variant: 'destructive',
          title: result.error.error_user_title,
          description: `Error: ${result.error.error_user_msg}`,
        });
      }
    } catch (error) {
      toast({
        variant: 'destructive',
        description: `Error: Something went wrong`,
      });
    }
    setVerifyOtpLoading(false);
  };
  const generateTwoFactor = async () => {
    setLoading(true);
    try {
      //TODO: save phone number to database
      //TODO: if two factor is already done then add phonenumber to db

      if (data?.display_phone_number) {
        // await updateAccountDetails(account.id, data);
        const res = await fetch(`/api/register-phonenumber?pin=${pin}&phone_number_id=${data?.id}`);
        const result = await res.json();
        if (result?.success) {
          toast({
            variant: 'success',
            description: 'Pin generated succesfully',
          });
          await subscribeToUserWaba();
          await updateAccountAccessToken(account);
          revalidateFullPath(pathname);
        } else {
          toast({
            variant: 'destructive',
            title: `${result.error.error_user_title}`,
            description: `${result.error.error_user_msg}`,
          });
        }
      } else {
        toast({
          variant: 'destructive',
          description: 'Phone number not found!',
        });
      }
    } catch (e) {
      console.log(e);
      toast({
        variant: 'destructive',
        description: JSON.stringify(e),
      });
    } finally {
      setLoading(false);
    }
  };

  const subscribeToUserWaba = async () => {
    try {
      const res = await fetch(`/api/subscribe-to-wabaid?waba_id=${account.waba_id}`);
      const result = await res.json();
      if (result?.success) {
        toast({
          variant: 'success',
          description: 'Subscribe Successfully!',
        });
      } else {
        toast({
          variant: 'destructive',
          description: 'Error subscribing!',
        });
      }
    } catch (error) {
      console.log(error);
      toast({
        variant: 'destructive',
        description: 'Something went wrong in subscribing!',
      });
    }
  };

  return (
    <div className="bg-white p-5 space-y-4 rounded-lg">
      <div className="flex flex-row items-center">{account.waba_id == '' && <div className="flex-1">Setup free Whatsapp Business account</div>}</div>
      {account.waba_id != '' ? (
        <div className="p-5 bg-green-100 border border-gray-200 rounded-lg">
          <div className="flex items-center w-full">
            <div className="space-x-2 flex-1 flex items-center">
              <MessageCircle className="text-green-800" />
              <div className="text-green-800 font-bold">Connected successfuly!</div>
              {data?.platform_type == 'NOT_APPLICABLE' && (
                <div>
                  <div className="text-green-800 font-bold">
                    Please wait while your business gets approved from <span className="text-blue-800">Meta</span>.
                    <span className="text-base font-normal"> This can take can upto 30 minutes</span>
                  </div>
                </div>
              )}
            </div>
            {data?.platform_type == 'NOT_APPLICABLE' ? (
              <div className="bg-orange-500 rounded-lg px-2 py-1 text-white ">Pending</div>
            ) : (
              <div className="bg-green-500 rounded-lg px-2 py-1 text-white ">Live</div>
            )}
          </div>
        </div>
      ) : (
        <>
          <Collapsible className="p-5 bg-green-100 border border-gray-200 rounded-lg">
            <CollapsibleTrigger className="flex items-center w-full">
              <div className="space-x-2 flex-1 flex items-center">
                <MessageCircle className="text-green-800" />
                <div className="text-green-800 font-bold">Apply for Whatsapp Business Api</div>
              </div>
              <div>
                <ChevronDown />
              </div>
            </CollapsibleTrigger>

            <CollapsibleContent className="py-5 text-sm text-gray-600">
              <div className="flex items-center">
                <div className="flex-1">Click on Continue With Facebook to apply for WhatsApp Business API</div>
                <div className="">
                  {/* <Login
                    className="bg-blue-500 rounded-lg w-52 py-2 text-center text-white font-bold"
                    btnTitle={"Continue with Facebook"}
                  /> */}
                </div>
              </div>
            </CollapsibleContent>
          </Collapsible>
        </>
      )}

      {/* //if two factor value is false meaning account has two factor */}

      {data?.code_verification_status == 'VERIFIED' ? (
        <div className="p-5 bg-white border border-gray-200 rounded-lg">
          <div className="flex items-center w-full">
            <div className="space-x-2 flex-1 flex items-center">
              <MessageSquare />
              <div className=" font-bold">Phone and display verification</div>
            </div>
            <div className="bg-green-500 rounded-lg px-2 py-1 text-white ">Verified</div>
          </div>
        </div>
      ) : data?.code_verification_status == 'EXPIRED' ? (
        <>
          <Collapsible className="p-5 bg-white border border-gray-200 rounded-lg">
            <CollapsibleTrigger className="flex items-center w-full">
              <div className="space-x-2 flex-1 flex items-center">
                <MessageSquare className="text-red-800" />
                <div className="text-red-800 font-bold text-left">Your code verification has been expired</div>
              </div>
              <div>
                <ChevronDown />
              </div>
            </CollapsibleTrigger>
            <CollapsibleContent className="py-5 text-sm text-gray-600">
              <div className="mb-2">
                Click send code to recieve your OTP. You will either receive it by SMS or call. Once recieved enter in the prompt to verify
              </div>
              <div>
                <Dialog
                  open={OTPDialogOpen}
                  onOpenChange={(open) => {
                    setOTP('');
                  }}
                >
                  {loading ? (
                    <Button variant="outline" disabled>
                      <Loader2 className="animate-spin mr-2 h-5 w-5" />
                      Sending Code
                    </Button>
                  ) : (
                    <Button variant="outline" onClick={sendVerificationCode}>
                      Send code
                    </Button>
                  )}
                  <DialogContent className="sm:max-w-md">
                    <DialogHeader>
                      <DialogTitle>Enter the code you recived</DialogTitle>
                    </DialogHeader>
                    <div className="flex items-center space-x-2 justify-center">
                      <div className="grid flex-1 gap-2 justify-center">
                        <InputOTP maxLength={6} value={otp} onChange={(value) => setOTP(value)}>
                          <InputOTPGroup>
                            <InputOTPSlot index={0} />
                            <InputOTPSlot index={1} />
                            <InputOTPSlot index={2} />
                          </InputOTPGroup>
                          <InputOTPSeparator />
                          <InputOTPGroup>
                            <InputOTPSlot index={3} />
                            <InputOTPSlot index={4} />
                            <InputOTPSlot index={5} />
                          </InputOTPGroup>
                        </InputOTP>
                      </div>
                    </div>
                    <DialogFooter className="justify-end">
                      {!verifyOtpLoading ? (
                        <Button type="button" variant="secondary" onClick={handleVerifyCode}>
                          Submit
                        </Button>
                      ) : (
                        <Button type="button" variant="secondary" disabled>
                          <Loader2 className="animate-spin mr-2 h-5 w-5" />
                          Verifying
                        </Button>
                      )}
                      <DialogClose asChild>
                        <Button type="button" onClick={() => setOTPDialogOpen(false)}>
                          Close
                        </Button>
                      </DialogClose>
                    </DialogFooter>
                  </DialogContent>
                </Dialog>
              </div>
            </CollapsibleContent>
          </Collapsible>
        </>
      ) : (
        (data?.code_verification_status == 'NOT_VERIFIED' || account.two_factor) && (
          <>
            <Collapsible className="p-5 bg-white border border-gray-200 rounded-lg">
              <CollapsibleTrigger className="flex items-center w-full">
                <div className="space-x-2 flex-1 flex items-center">
                  <UserPlus2 />
                  <div className="font-bold">Register your Whatsapp Business number</div>
                </div>
                <div>
                  <ChevronDown />
                </div>
              </CollapsibleTrigger>
              <CollapsibleContent className="py-5 text-sm text-gray-600">
                <div>Generate a 6 digit pin to complete registeration</div>
                {account?.waba_id == '' && <div>First apply for Whatsapp Business in order to generate pin</div>}
                <div>
                  <Dialog onOpenChange={() => setValue('')}>
                    {loading ? (
                      <Button type="button" variant="secondary" disabled>
                        <Loader2 className="animate-spin mr-2 h-5 w-5" />
                        Loading
                      </Button>
                    ) : (
                      <DialogTrigger asChild className="mt-2">
                        <Button variant="outline" disabled={account?.waba_id == ''}>
                          Generate
                        </Button>
                      </DialogTrigger>
                    )}
                    <DialogContent className="sm:max-w-md">
                      <DialogHeader>
                        <DialogTitle>Two Factor Authentication</DialogTitle>
                        <DialogDescription>Remember this code!</DialogDescription>
                      </DialogHeader>
                      <div className="flex items-center space-x-2 justify-center">
                        <div className="grid flex-1 gap-2 justify-center">
                          <InputOTP maxLength={6} value={pin} onChange={(value) => setValue(value)}>
                            <InputOTPGroup>
                              <InputOTPSlot index={0} />
                              <InputOTPSlot index={1} />
                              <InputOTPSlot index={2} />
                            </InputOTPGroup>
                            <InputOTPSeparator />
                            <InputOTPGroup>
                              <InputOTPSlot index={3} />
                              <InputOTPSlot index={4} />
                              <InputOTPSlot index={5} />
                            </InputOTPGroup>
                          </InputOTP>
                        </div>
                      </div>
                      <DialogFooter className="sm:justify-start">
                        <DialogClose asChild>
                          <Button type="button" variant="secondary" onClick={generateTwoFactor}>
                            Save
                          </Button>
                        </DialogClose>
                      </DialogFooter>
                    </DialogContent>
                  </Dialog>
                </div>
              </CollapsibleContent>
            </Collapsible>
          </>
        )
      )}
      {data?.platform_type != 'CLOUD_API' && (
        <Collapsible className="p-5 bg-white border border-gray-200 rounded-lg">
          <CollapsibleTrigger className="flex items-center w-full">
            <div className="space-x-2 flex-1 flex items-center">
              <UserPlus2 />
              <div className="font-bold">Register your Whatsapp Business number</div>
            </div>
            <div>
              <ChevronDown />
            </div>
          </CollapsibleTrigger>
          <CollapsibleContent className="py-5 text-sm text-gray-600">
            <div>Generate a 6 digit pin to complete registeration</div>
            {account?.waba_id == '' && <div>First apply for Whatsapp Business in order to generate pin</div>}
            <div>
              <Dialog onOpenChange={() => setValue('')}>
                {loading ? (
                  <Button type="button" variant="secondary" disabled>
                    <Loader2 className="animate-spin mr-2 h-5 w-5" />
                    Loading
                  </Button>
                ) : (
                  <DialogTrigger asChild className="mt-2">
                    <Button variant="outline" disabled={account?.waba_id == ''}>
                      Generate
                    </Button>
                  </DialogTrigger>
                )}
                <DialogContent className="sm:max-w-md">
                  <DialogHeader>
                    <DialogTitle>Two Factor Authentication</DialogTitle>
                    <DialogDescription>Remember this code!</DialogDescription>
                  </DialogHeader>
                  <div className="flex items-center space-x-2 justify-center">
                    <div className="grid flex-1 gap-2 justify-center">
                      <InputOTP maxLength={6} value={pin} onChange={(value) => setValue(value)}>
                        <InputOTPGroup>
                          <InputOTPSlot index={0} />
                          <InputOTPSlot index={1} />
                          <InputOTPSlot index={2} />
                        </InputOTPGroup>
                        <InputOTPSeparator />
                        <InputOTPGroup>
                          <InputOTPSlot index={3} />
                          <InputOTPSlot index={4} />
                          <InputOTPSlot index={5} />
                        </InputOTPGroup>
                      </InputOTP>
                    </div>
                  </div>
                  <DialogFooter className="sm:justify-start">
                    <DialogClose asChild>
                      <Button type="button" variant="secondary" onClick={generateTwoFactor}>
                        Save
                      </Button>
                    </DialogClose>
                  </DialogFooter>
                </DialogContent>
              </Dialog>
            </div>
          </CollapsibleContent>
        </Collapsible>
      )}
      {/* <Button onClick={subscribeToUserWaba}>Subscribe</Button> */}
    </div>
  );
};

export default VerificationSteps;
