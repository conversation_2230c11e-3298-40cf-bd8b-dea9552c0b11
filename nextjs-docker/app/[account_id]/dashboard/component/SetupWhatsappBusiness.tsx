'use client';
import FBLoginButton from '@/components/FBLoginButton';

const SetupWhatsappBusiness = ({ appId }: { appId?: string }) => {
  return (
    <div className="p-5 bg-[#075E54] text-white rounded-lg">
      <div className="font-bold mb-2">Start WhatsApp Engagement for your Business</div>
      {/* <div className="w-1/2 text-xs mb-2">
        Your account is currently in test mode.You can try & experience a
        feature like Broadcasting, Live chat with your test Contact via Test
        Business Number Test Business number : +************
      </div> */}
      <div className="mb-1">You'll need to Apply for WhatsApp Business API to use WeTarseel for your Business</div>
      <FBLoginButton appId={appId} className="bg-white rounded-lg w-52 py-2 text-center text-gray-500" btnTitle={'Apply for whatsapp business'} />
    </div>
  );
};

export default SetupWhatsappBusiness;
