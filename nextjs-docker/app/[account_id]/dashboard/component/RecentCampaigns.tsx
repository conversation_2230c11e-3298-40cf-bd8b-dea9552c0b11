import { <PERSON>, Card<PERSON>ontent, <PERSON><PERSON>eader, CardTitle } from '@/components/ui/card';
import { Progress } from '@/components/ui/progress';
import { Account, ICampaign } from '@/lib/types';
import { ArrowRight } from 'lucide-react';
import Link from 'next/link';

const RecentCampaigns = ({ account, campaigns }: { account: Account; campaigns: ICampaign[] }) => {
  const calculatePercentage = (value: number, total: number) => {
    if (total === 0) return 0;
    return ((value / total) * 100).toFixed(1);
  };
  return (
    <div className="p-6 space-y-6">
      <h1 className="text-3xl font-bold">Recent Campaign Performances</h1>
      <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
        {campaigns.map((campaign) => (
          <Link href={`campaign/${campaign.id}`} key={campaign.id}>
            <Card className="hover:shadow-lg transition-shadow">
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">{campaign.name}</CardTitle>
                <ArrowRight className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{calculatePercentage(campaign.read_count, campaign.sent_count)}% Read</div>
                <Progress value={Number(calculatePercentage(campaign.delivered_count, campaign.sent_count))} className="h-2 mt-2" />
                <div className="grid grid-cols-3 gap-4 mt-4">
                  <div className="flex flex-col">
                    <span className="text-sm font-medium text-muted-foreground">Replied</span>
                    <span className="text-lg font-semibold">{calculatePercentage(campaign.reply_count, campaign.sent_count)}%</span>
                  </div>
                  <div className="flex flex-col">
                    <span className="text-sm font-medium text-muted-foreground">Delivered</span>
                    <span className="text-lg font-semibold">{calculatePercentage(campaign.delivered_count, campaign.sent_count)}%</span>
                  </div>
                  <div className="flex flex-col">
                    <span className="text-sm font-medium text-muted-foreground">Sent</span>
                    <span className="text-lg font-semibold">{campaign.sent_count}</span>
                  </div>
                </div>
              </CardContent>
            </Card>
          </Link>
        ))}
      </div>
    </div>
  );
};

export default RecentCampaigns;
