'use client';

import { useEffect, useState } from 'react';
import { useParams } from 'next/navigation';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { ExternalLink, AlertCircle, CheckCircle2, XCircle, Loader2, RefreshCw, ChevronDown, ChevronRight } from 'lucide-react';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { PATHS } from '@/lib/utils';
import FBLoginButton from '@/components/FBLoginButton';
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from '@/components/ui/collapsible';

interface FacebookInfoProps {
  appId?: string;
}

export const FacebookInformation = ({ appId }: FacebookInfoProps) => {
  const params = useParams<{ account_id: string }>();
  const accountId = params.account_id;
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [facebookData, setFacebookData] = useState<any>(null);

  // Collapsible section states (all collapsed by default)
  const [isAccountDetailsExpanded, setIsAccountDetailsExpanded] = useState(false);
  const [isPhoneDetailsExpanded, setIsPhoneDetailsExpanded] = useState(false);
  const [isBusinessAppExpanded, setIsBusinessAppExpanded] = useState(false);

  const fetchFacebookInfo = async () => {
    try {
      setLoading(true);
      setError(null);

      const response = await fetch(`/api/facebook-account-info?account_id=${accountId}`, {
        cache: 'no-store',
      });

      const data = await response.json();
      setFacebookData(data);
    } catch (err) {
      setError('Failed to fetch Facebook information');
      console.error('Error fetching Facebook data:', err);
    } finally {
      setLoading(false);
    }
  };

  const handleRefresh = async () => {
    setRefreshing(true);
    await fetchFacebookInfo();
    setRefreshing(false);
  };

  useEffect(() => {
    fetchFacebookInfo();
  }, [accountId]);

  const renderConnectionStatus = () => {
    if (!facebookData?.connected) {
      return (
        <Alert variant="destructive" className="mb-4">
          <AlertCircle className="h-4 w-4" />
          <AlertTitle>Not Connected</AlertTitle>
          <AlertDescription>Your Facebook account is not connected. Please connect using the button below.</AlertDescription>
          <div className="mt-4">
            <FBLoginButton appId={appId} className="bg-blue-500 hover:bg-blue-600" btnTitle="Connect with Facebook" />
          </div>
        </Alert>
      );
    }

    return null;
  };

  const renderTokenStatus = () => {
    if (!facebookData?.connected || !facebookData?.account) return null;

    const { token_expired, expires_in_days } = facebookData.account;

    if (token_expired || Number(expires_in_days) <= 0) {
      return (
        <Alert variant="destructive" className="mb-4">
          <AlertCircle className="h-4 w-4" />
          <AlertTitle>Token Expired</AlertTitle>
          <AlertDescription>Your access token has expired. Please reconnect your Facebook account.</AlertDescription>
          <div className="mt-4">
            <FBLoginButton appId={appId} className="bg-blue-500 hover:bg-blue-600" btnTitle="Reconnect Facebook Account" />
          </div>
        </Alert>
      );
    } else if (expires_in_days <= 15) {
      return (
        <Alert variant="warning" className="mb-4">
          <AlertCircle className="h-4 w-4" />
          <AlertTitle>Token Expiring Soon</AlertTitle>
          <AlertDescription>Your access token will expire in {expires_in_days} days. Consider renewing it soon.</AlertDescription>
        </Alert>
      );
    }

    return null;
  };

  if (loading) {
    return (
      <div className="flex flex-col items-center justify-center p-6 bg-white rounded-lg shadow">
        <Loader2 className="h-8 w-8 animate-spin text-gray-400 mb-2" />
        <p>Loading Facebook information...</p>
      </div>
    );
  }

  if (error) {
    return (
      <Alert variant="destructive">
        <AlertCircle className="h-4 w-4" />
        <AlertTitle>Error</AlertTitle>
        <AlertDescription>
          {error}. Please try again later.
          <Button variant="outline" size="sm" className="ml-2" onClick={fetchFacebookInfo}>
            Try Again
          </Button>
        </AlertDescription>
      </Alert>
    );
  }

  if (!facebookData?.connected) {
    return renderConnectionStatus();
  }

  const { account, phone_details, business_app_status } = facebookData;

  return (
    <div className="space-y-4">
      <div className="flex justify-between items-center">
        <h2 className="text-xl font-semibold">Facebook & WhatsApp Information</h2>
        <Button variant="outline" size="sm" onClick={handleRefresh} disabled={refreshing}>
          {refreshing ? (
            <>
              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              Refreshing...
            </>
          ) : (
            <>
              <RefreshCw className="mr-2 h-4 w-4" />
              Refresh
            </>
          )}
        </Button>
      </div>

      {renderTokenStatus()}

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        {/* Account Details Section */}
        <Card>
          <Collapsible open={isAccountDetailsExpanded} onOpenChange={setIsAccountDetailsExpanded} className="w-full">
            <CardHeader className="pb-2 cursor-pointer" onClick={() => setIsAccountDetailsExpanded(!isAccountDetailsExpanded)}>
              <div className="flex items-center">
                {isAccountDetailsExpanded ? <ChevronDown className="h-4 w-4 mr-2" /> : <ChevronRight className="h-4 w-4 mr-2" />}
                <div>
                  <CardTitle>Account Details</CardTitle>
                  <CardDescription>Information about your connected Facebook business account</CardDescription>
                </div>
              </div>
            </CardHeader>
            <CollapsibleContent>
              <CardContent>
                <dl className="space-y-2">
                  <div className="flex justify-between">
                    <dt className="font-medium">Business Name:</dt>
                    <dd>{account.name}</dd>
                  </div>
                  <div className="flex justify-between">
                    <dt className="font-medium">WhatsApp Business ID:</dt>
                    <dd className="font-mono text-sm">{account.waba_id}</dd>
                  </div>
                  <div className="flex justify-between">
                    <dt className="font-medium">Platform Type:</dt>
                    <dd>
                      <Badge variant={account.platform_type === 'CLOUD_API' ? 'success' : 'outline'}>{account.platform_type || 'Unknown'}</Badge>
                    </dd>
                  </div>
                  <div className="flex justify-between">
                    <dt className="font-medium">Token Status:</dt>
                    <dd>
                      {account.token_expired || Number(account.expires_in_days) === 0 ? (
                        <Badge variant="destructive">Expired</Badge>
                      ) : (
                        <Badge variant="success">Valid ({Number(account.expires_in_days)} days)</Badge>
                      )}
                    </dd>
                  </div>
                  <div className="flex justify-between">
                    <dt className="font-medium">Quality Rating:</dt>
                    <dd>
                      <Badge variant={account.quality_rating === 'GREEN' ? 'success' : account.quality_rating === 'YELLOW' ? 'pending' : 'destructive'}>
                        {account.quality_rating || 'Unknown'}
                      </Badge>
                    </dd>
                  </div>
                </dl>
              </CardContent>
            </CollapsibleContent>
          </Collapsible>
        </Card>

        {/* Phone Details Section */}
        <Card>
          <Collapsible open={isPhoneDetailsExpanded} onOpenChange={setIsPhoneDetailsExpanded} className="w-full">
            <CardHeader className="pb-2 cursor-pointer" onClick={() => setIsPhoneDetailsExpanded(!isPhoneDetailsExpanded)}>
              <div className="flex items-center">
                {isPhoneDetailsExpanded ? <ChevronDown className="h-4 w-4 mr-2" /> : <ChevronRight className="h-4 w-4 mr-2" />}
                <div>
                  <CardTitle>Phone Details</CardTitle>
                  <CardDescription>Information about your WhatsApp phone number</CardDescription>
                </div>
              </div>
            </CardHeader>
            <CollapsibleContent>
              <CardContent>
                <dl className="space-y-2">
                  <div className="flex justify-between">
                    <dt className="font-medium">Phone Number:</dt>
                    <dd>{account.display_phone_number}</dd>
                  </div>
                  <div className="flex justify-between">
                    <dt className="font-medium">Phone ID:</dt>
                    <dd className="font-mono text-sm">{account.phone_id}</dd>
                  </div>
                  <div className="flex justify-between">
                    <dt className="font-medium">Verification Status:</dt>
                    <dd>
                      <Badge
                        variant={
                          account.code_verification_status === 'VERIFIED'
                            ? 'success'
                            : account.code_verification_status === 'NOT_VERIFIED'
                              ? 'pending'
                              : account.code_verification_status === 'EXPIRED'
                                ? 'destructive'
                                : 'outline'
                        }
                      >
                        {account.code_verification_status || 'Unknown'}
                      </Badge>
                    </dd>
                  </div>
                  {phone_details?.certificates && (
                    <div className="flex justify-between">
                      <dt className="font-medium">Certificates:</dt>
                      <dd>
                        {phone_details.certificates?.map((cert: string) => (
                          <Badge key={cert} variant="outline" className="mr-1">
                            {cert}
                          </Badge>
                        ))}
                      </dd>
                    </div>
                  )}
                  {phone_details?.throughput_status && (
                    <div className="flex justify-between">
                      <dt className="font-medium">Status:</dt>
                      <dd>
                        <Badge>{phone_details.throughput_status}</Badge>
                      </dd>
                    </div>
                  )}
                </dl>
              </CardContent>
            </CollapsibleContent>
          </Collapsible>
        </Card>
      </div>

      {/* Business App Status Section */}
      <Card>
        <Collapsible open={isBusinessAppExpanded} onOpenChange={setIsBusinessAppExpanded} className="w-full">
          <CardHeader className="pb-2 cursor-pointer" onClick={() => setIsBusinessAppExpanded(!isBusinessAppExpanded)}>
            <div className="flex items-center">
              {isBusinessAppExpanded ? <ChevronDown className="h-4 w-4 mr-2" /> : <ChevronRight className="h-4 w-4 mr-2" />}
              <div>
                <CardTitle>Business App Status</CardTitle>
                <CardDescription>Information about the business app connection</CardDescription>
              </div>
            </div>
          </CardHeader>
          <CollapsibleContent>
            <CardContent>
              <dl className="space-y-2">
                <div className="flex justify-between items-center">
                  <dt className="font-medium">Connected to Business App:</dt>
                  <dd>
                    {business_app_status?.is_on_biz_app ? (
                      <span className="flex items-center">
                        <CheckCircle2 className="h-5 w-5 text-green-500 mr-2" />
                        Connected
                      </span>
                    ) : (
                      <span className="flex items-center">
                        <XCircle className="h-5 w-5 text-red-500 mr-2" />
                        Not connected
                      </span>
                    )}
                  </dd>
                </div>
                <div className="flex justify-between">
                  <dt className="font-medium">Platform Type:</dt>
                  <dd>{business_app_status?.platform_type || 'Unknown'}</dd>
                </div>
              </dl>
            </CardContent>
          </CollapsibleContent>
        </Collapsible>
      </Card>
    </div>
  );
};
