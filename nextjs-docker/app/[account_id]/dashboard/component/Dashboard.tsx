'use client';

import { Toaster } from '@/components/ui/toaster';
import SetupWhatsappBusiness from './SetupWhatsappBusiness';
import VerificationSteps from './VerificationSteps';
import type { Account, ICampaign, IPhoneVerificationResponse, User } from '@/lib/types';
import RecentCampaigns from './RecentCampaigns';
import { useState } from 'react';
import { ListResult } from 'pocketbase';
import { revalidateFullPath, updateAccountAccessToken } from '@/lib/actions';
import { usePathname } from 'next/navigation';
import { RefreshCcw } from 'lucide-react';
import { WhatsappQr } from '@/components/whatsapp-qr';
import { FacebookInformation } from './FacebookInformation';

interface IDashboardProps {
  data: IPhoneVerificationResponse;
  appId?: string;
  user: User;
  account: Account;
  campaigns: ListResult<ICampaign>;
}

const Dashboard = ({ data, user, appId, account, campaigns }: IDashboardProps) => {
  const [latestUpdateloading, setLatestUpdateloading] = useState(false);
  const pathname = usePathname();

  const handleRefresh = async () => {
    setLatestUpdateloading(true);
    await updateAccountAccessToken(account);
    revalidateFullPath(pathname);
    setLatestUpdateloading(false);
  };

  return (
    <>
      <Toaster />
      <div className="space-y-4">
        <div className="flex items-center justify-between space-x-1 flex-col md:flex-row">
          <div className="flex items-center space-x-1 flex-col md:flex-row">
            <div id="dashboard-zzz" className="text-gray-600">
              Hey {user.name}, Welcome to WeTarseel!
            </div>
            <div className="text-gray-600">
              {account.display_phone_number
                ? `Your business phone number is ${account.display_phone_number}`
                : 'Connect your business to get your business phone number'}
            </div>
          </div>
          <div className="flex space-x-2 items-center  hover:cursor-pointer" onClick={() => handleRefresh()}>
            {latestUpdateloading ? (
              <>
                <div>Refreshing</div>
                <RefreshCcw className="animate-spin h-4 w-4" />
              </>
            ) : (
              <>
                <div>Refresh to get latest updates</div>
                <RefreshCcw className="h-4 w-4" />
              </>
            )}
          </div>
        </div>
        <div>
          <FacebookInformation appId={appId} />
        </div>

        {/* if waba id exists then dont show apply for whatsapp */}
        {(account?.waba_id === '' || account?.reconnect) && <SetupWhatsappBusiness appId={appId} />}
        {/* //send data and account to VerificationSteps */}
        <VerificationSteps data={data} account={account} />
        <WhatsappQr number={(account.display_phone_number ?? '').replace(/-/g, '')} />
        {campaigns.totalItems > 0 && account.show_recent_campaigns && <RecentCampaigns account={account} campaigns={campaigns.items} />}
      </div>
    </>
  );
};

export default Dashboard;
