import { Button } from '@/components/ui/button';
import { addTeam, getAgents, getTeams } from '@/lib/pocket';
import type { Metadata } from 'next';
import Link from 'next/link';
import { columns } from './components/columns';
import { DataTable } from './components/data-table';
import { PlusCircle, PlayCircle } from 'lucide-react';
import { Toaster } from '@/components/ui/toaster';
import { Select } from '@/components/ui/select';
import { addAgentToTeam } from '@/lib/actions';
import Render from './render';
import { Input } from '@/components/ui/input';
import { Suspense } from 'react';

export const metadata: Metadata = {
  title: 'Teams',
};

export default async ({ params, searchParams }: { params: Record<string, string>; searchParams: Record<string, string> }) => {
  const teams = await getTeams(params.account_id);
  let team;
  let allAgents = await getAgents(params.account_id);
  let agents = null;

  const selectedTeamId = searchParams?.['selected-team'];
  if (selectedTeamId) {
    agents = await getAgents(params.account_id, selectedTeamId);
    team = teams.find((team) => team.id === selectedTeamId);
  }

  return (
    <div className="mx-auto p-6 bg-gray-100 min-h-full">
      <Toaster />
      <div className="flex mb-4">
        <div className="flex-1">
          <div className="text-gray-800 text-xl font-bold sm:text-2xl">Team List</div>
          <p className="text-gray-600 mt-2">Select the team which you would like to edit or delete</p>
        </div>
      </div>
      <Suspense fallback={<div className="flex justify-center items-center h-full">Loading...</div>}>
        <Render teams={teams} team={team} allAgents={allAgents} agents={agents} />
      </Suspense>
    </div>
  );
};
