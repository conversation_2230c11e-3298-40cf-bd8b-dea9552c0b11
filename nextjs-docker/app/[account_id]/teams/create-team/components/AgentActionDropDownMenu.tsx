'use client';

import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuLabel, DropdownMenuTrigger } from '@/components/ui/dropdown-menu';

import {
  AlertDialog,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from '@/components/ui/alert-dialog';
import { Button } from '@/components/ui/button';

import { useToast } from '@/components/ui/use-toast';
import { deleteAgentById } from '@/lib/pocket';
import { User } from '@/lib/types';
import { Row } from '@tanstack/react-table';
import { Loader2, MoreHorizontal, Pencil, Trash } from 'lucide-react';
import Link from 'next/link';
import { useParams } from 'next/navigation';
import { useState } from 'react';

const AgentActionDropDownMenu = ({ row }: { row: Row<User> }) => {
  const params = useParams<{ account_id: string }>();
  const { toast } = useToast();
  const [loading, setLoading] = useState(false);
  const [open, setOpen] = useState(false);
  const handleDeleteAgent = async () => {
    try {
      setLoading(true);
      await deleteAgentById(row.id, params.account_id);
      toast({
        variant: 'success',
        description: 'Agent Deleted Successfully',
      });
    } catch (error) {
      console.log(error);
      toast({
        variant: 'destructive',
        description: 'There was an error in deleting the agent. Try again!',
      });
    }
    setOpen(false);
    setLoading(false);
  };

  return (
    <>
      <AlertDialog open={open} onOpenChange={(isOpen) => setOpen(isOpen)}>
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="ghost" className="h-8 w-8 p-0 hover:bg-gray-100 mb-0">
              <span className="sr-only">Open menu</span>
              <MoreHorizontal className="h-4 w-4" />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end">
            <DropdownMenuLabel>Actions</DropdownMenuLabel>
            <Link href={`agent/edit-agent?id=${row.id}`}>
              <DropdownMenuItem>
                <Pencil className="mr-2 h-4 w-4 text-green-600" />
                <span className="text-green-600">Edit</span>
              </DropdownMenuItem>
            </Link>

            <AlertDialogTrigger asChild>
              <DropdownMenuItem>
                <Trash className="mr-2 h-4 w-4 text-red-600" />
                <span className="text-red-600">Delete</span>
              </DropdownMenuItem>
            </AlertDialogTrigger>
          </DropdownMenuContent>
        </DropdownMenu>

        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Are you sure you want to delete the agent {row.original.name}?</AlertDialogTitle>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            {loading ? (
              <Button className="mb-0" disabled>
                Deleting
                <Loader2 className="ml-2 animate-spin" />
              </Button>
            ) : (
              <>
                <Button onClick={() => handleDeleteAgent()} variant={'destructive'} className="mb-0">
                  Yes Delete
                </Button>
              </>
            )}
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </>
  );
};

export default AgentActionDropDownMenu;
