import { Button } from '@/components/ui/button';
import { useToast } from '@/components/ui/use-toast';
import { saveFlow } from '@/lib/pocket';
import { IAutomation } from '@/lib/types';
import { useReactFlow } from '@xyflow/react';
import { useRouter } from 'next/navigation';

export const SaveButton = ({ accountId, selectedAutomation }: { accountId: string; selectedAutomation?: IAutomation }) => {
  const reactFlowRef = useReactFlow();
  const router = useRouter();
  const { toast } = useToast();
  return (
    <Button
      onClick={async () => {
        // use document queryselector to get name and description
        const name = (document.querySelector('input[name="name"]') as HTMLInputElement)?.value;
        const description = (document.querySelector('textarea[name="description"]') as HTMLTextAreaElement)?.value;

        const snapShot = JSON.stringify(reactFlowRef.toObject());
        console.log('Saving flow', snapShot, name, description);
        const result = await saveFlow({
          accountId,
          selectedAutomation,
          flow: snapShot,
          name,
          description,
        });
        if (!selectedAutomation) {
          router.push(`/${accountId}/flows/create-automation/${result.id}`);
        }
        toast({
          variant: 'success',
          description: 'Flow automation saved successfully',
        });
      }}
    >
      Save
    </Button>
  );
};
