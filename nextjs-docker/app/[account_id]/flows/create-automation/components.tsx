'use client';

import React from 'react';
import { Background, BackgroundVariant, Controls, Edge, MiniMap, Node, ReactFlow, SmoothStepEdge } from '@xyflow/react';

import { IAutomation, ITemplateDatabase } from '@/lib/types';
import { CardDescription, CardTitle } from '@/components/ui/card';

import '@xyflow/react/dist/style.css';
import './styles.css';
import { actions, bankingActions, conditions, nodeTypes, triggers } from './sidebar';
import { SaveButton } from './SaveButton';
import useNodeUtils from './useNodeUtils';
import { Conditions } from './Conditions';
import { Actions } from './Actions';
import { Triggers } from './Triggers';
import { Banking } from './Banking';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import Link from 'next/link';
import { Badge } from '@/components/ui/badge';

export const FlowComponent = ({
  selectedAutomation,
  initialNodes = [],
  initialEdges = [],
  accountId,
}: {
  templateName: string;
  selectedAutomation?: {
    expand: {
      template: ITemplateDatabase;
    };
  } & IAutomation;
  templates: Record<string, any>;
  initialNodes: Node[];
  initialEdges: Edge<any>[];
  accountId: string;
}) => {
  const { onEdgeClick, onConnect } = useNodeUtils();
  return (
    <div className="w-full px-10">
      <div className="text-blue-600 underline">
        <Link href={`/${accountId}/flows`}>Back to automations</Link>
      </div>
      <div className="mb-8">
        <CardTitle>Automation Workspace</CardTitle>
        <CardDescription>Use the drag and drop UI to create an automation whenever there is a message by the user</CardDescription>
      </div>
      <div>
        {selectedAutomation?.template ? (
          <div>
            <div>
              This automation will only run when a user responds to a conversation in which the template
              <Badge className="mx-2">{selectedAutomation?.expand?.template.template_name}</Badge> was sent
            </div>
          </div>
        ) : null}
        {/* Add name and description */}
        <div className="flex gap-4 mb-4">
          <div className="w-1/2">
            <label className="block text-sm font-medium text-gray-700">Name</label>
            <Input defaultValue={selectedAutomation?.name} type="text" name="name" id="name" />
          </div>
          <div className="w-1/2">
            <label className="block text-sm font-medium text-gray-700">Description</label>
            <Textarea defaultValue={selectedAutomation?.description} id="description" name="description" rows={1}></Textarea>
          </div>
          <div className="py-5">
            <SaveButton accountId={accountId} selectedAutomation={selectedAutomation} />
          </div>
        </div>
      </div>
      <div className="flex gap-2 w-full" style={{ height: 'calc(100% - 230px)' }}>
        <div>
          {/* Triggers */}
          <Triggers triggers={triggers} />

          {/* Actions */}
          <Actions actions={actions} />

          {/* Banking Section - Only visible for specific accounts */}
          <Banking bankingActions={bankingActions} />

          {/* Conditions */}
          <Conditions conditions={conditions} />
        </div>
        <div className="ml-7 w-full">
          <ReactFlow defaultNodes={initialNodes} defaultEdges={initialEdges} nodeTypes={nodeTypes} onEdgeClick={onEdgeClick} onConnect={onConnect}>
            <Controls />
            <MiniMap />
            <Background variant={BackgroundVariant.Dots} gap={12} size={1} />
          </ReactFlow>
        </div>
      </div>
    </div>
  );
};
