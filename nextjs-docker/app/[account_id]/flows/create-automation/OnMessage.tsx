import { Handle, Position } from '@xyflow/react';
import useNodeUtils from './useNodeUtils';

function OnMessage(all: any) {
  const { deleteNode } = useNodeUtils();

  return (
    <div>
      <div className="flex items-baseline gap-2 border p-2 bg-yellow-100 h-full">
        <div className="text-sm">On Message</div>
        <button className="text-red-600 ml-auto block" onClick={() => deleteNode(all.id)}>
          x
        </button>
      </div>
      <Handle type="source" position={Position.Bottom} id={all.id} isConnectable />
    </div>
  );
}

export default OnMessage;
