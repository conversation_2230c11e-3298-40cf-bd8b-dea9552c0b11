import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Handle, Position, useReactFlow } from '@xyflow/react';
import { useCallback } from 'react';

function TextMessage(all: any) {
  const { updateNodeData } = useReactFlow();
  const onChange = useCallback((evt: any) => {
    updateNodeData(all.id, { text: evt.target.value });
  }, []);

  return (
    <div style={{ height: all.data?.height ?? 320, border: 'none' }}>
      <div className="border p-2 bg-white">
        <div className="mb-2 text-sm">Send Question</div>
        <Textarea id="text" name="text" onChange={onChange} className="nodrag" rows={1} />
        <div className="">
          <div className="mb-2 text-sm">Reply variable</div>
          <Input name="var" />
        </div>
      </div>
      <Handle type="source" position={Position.Bottom} id="a" />
    </div>
  );
}

export default TextMessage;
