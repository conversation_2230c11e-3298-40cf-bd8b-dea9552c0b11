import { Handle, Position, useNodeId } from '@xyflow/react';
import useNodeUtils, { HEIGHT } from './useNodeUtils';

function VerifyCustomer() {
  const id = useNodeId();

  if (!id) {
    return null;
  }

  const { deleteNode } = useNodeUtils();

  return (
    <div id={id} className="bg-white/50 p-2 rounded-md">
      <div className="p-2 bg-blue-100 h-full relative" style={{ minHeight: HEIGHT }}>
        <Handle type="target" position={Position.Left} id={id} isConnectable />
        <button className="text-red-600 absolute top-2 right-2" onClick={() => deleteNode(id)}>
          x
        </button>
        <div className="mb-2 text-sm font-medium">Verify Customer</div>
        <div className="flex items-center justify-center py-4">
          <div className="text-sm text-center">
            This node verifies if a customer is registered to WhatsApp Banking Service
          </div>
        </div>
        <Handle type="source" position={Position.Right} id={id} isConnectable />
      </div>
    </div>
  );
}

export default VerifyCustomer;
