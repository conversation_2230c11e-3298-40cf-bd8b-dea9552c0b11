'use client';

import { type IImageWithUrl, type User } from '@/lib/types';
import { createContext, ReactNode } from 'react';

export const FlowContext = createContext<{
  agents: Partial<User>[];
  images: IImageWithUrl[];
  accountId: string;
}>({
  images: [],
  agents: [],
  accountId: '',
});

export const FlowProvider = ({
  children,
  agents,
  images,
  accountId,
}: {
  children: ReactNode;
  agents: Partial<User>[];
  images: IImageWithUrl[];
  accountId: string;
}) => {
  return <FlowContext.Provider value={{ images, agents, accountId }}>{children}</FlowContext.Provider>;
};
