'use client';

import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from '@/components/ui/dropdown-menu';

import { Button } from '@/components/ui/button';

import { getAccountImage, getFilesForDialogs } from '@/lib/pocket';
import { IImage } from '@/lib/types';
import { AudioLinesIcon, FileIcon, ImageIcon, Paperclip, VideoIcon } from 'lucide-react';
import { memo, useState } from 'react';
import FileGalleryDialog from '../../live-chat/[convo_id]/component/FileGalleryDialog';

const ServiceMessageFileSelectDropDown = ({ accountId, handleSetImage }: { accountId: string; handleSetImage: any }) => {
  const [open, setIsOpen] = useState(false);
  const [dialogType, setDialogType] = useState('images');
  const [files, setFiles] = useState<IImage[]>([]);
  const [filesLoading, setFilesLoading] = useState<boolean>(false);
  const [businessImage, setBusinessImage] = useState<string | null>(null);

  const getFiles = async (type: string) => {
    setIsOpen(true);
    setDialogType(type);
    setFilesLoading(true);
    const _files = await getFilesForDialogs(accountId, type);
    const businessImage = await getAccountImage(accountId);
    setBusinessImage(businessImage);
    setFiles(_files.files);
    setFilesLoading(false);
  };

  return (
    <>
      <DropdownMenu modal={false}>
        <DropdownMenuTrigger asChild>
          <Button variant={'outline'} type="button">
            <Paperclip className="mr-2 " /> Select Attachment
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent>
          <DropdownMenuItem onClick={() => getFiles('images')}>
            <ImageIcon className="mr-2 h-4 w-4 " />
            <span>Images</span>
          </DropdownMenuItem>
          <DropdownMenuItem onClick={() => getFiles('videos')}>
            <VideoIcon className="mr-2 h-4 w-4 " />
            <span>Video</span>
          </DropdownMenuItem>
          <DropdownMenuItem onClick={() => getFiles('documents')}>
            <FileIcon className="mr-2 h-4 w-4 " />
            <span>Document</span>
          </DropdownMenuItem>
          <DropdownMenuItem onClick={() => getFiles('audios')}>
            <AudioLinesIcon className="mr-2 h-4 w-4 " />
            <span>Audio</span>
          </DropdownMenuItem>
        </DropdownMenuContent>
      </DropdownMenu>
      {open && (
        <FileGalleryDialog
          accountId={accountId}
          handleSetImage={handleSetImage}
          dialogType={dialogType}
          open={open}
          setIsOpen={setIsOpen}
          files={files}
          filesLoading={filesLoading}
          setFiles={setFiles}
          businessImage={businessImage}
        />
      )}
    </>
  );
};

export default memo(ServiceMessageFileSelectDropDown);
