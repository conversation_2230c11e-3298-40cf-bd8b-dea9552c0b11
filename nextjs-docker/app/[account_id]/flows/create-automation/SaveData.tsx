import { Input } from '@/components/ui/input';
import { <PERSON><PERSON>, Position, useReactFlow } from '@xyflow/react';
import { useCallback } from 'react';

function SaveData(all: any) {
  const { updateNodeData } = useReactFlow();
  const onChange = useCallback((evt: any) => {
    updateNodeData(all.id, { text: evt.target.value });
  }, []);

  return (
    <div>
      <Handle type="target" position={Position.Left} id="a" />
      <button className="text-red-600 absolute top-2 right-2" onClick={() => all.data.deleteNode(all.id)}>
        x
      </button>
      <div className="border p-2 bg-white">
        <div className="mb-2 text-sm">Save Reply with name</div>
        <Input defaultValue={all.data.text} name="var" onChange={onChange} className="w-full nodrag" />
      </div>
      <Handle type="source" position={Position.Right} id="a" />
    </div>
  );
}

export default SaveData;
