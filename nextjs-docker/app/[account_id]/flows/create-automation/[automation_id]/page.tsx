import { getAccount, getAgents, getAllTemplatesFromDb, getAutomation, getFilesForDialogs, getImages, getUserById } from '@/lib/pocket';
import { FlowComponent } from '../components';
import { server_component_pb } from '@/state/pb/server_component_pb';
import { User } from '@/lib/types';
import { Edge, Node, ReactFlowProvider } from '@xyflow/react';
import { FlowProvider } from '../FlowContext';

export const dynamic = 'force-dynamic';
export const revalidate = 0;
const FlowsPage = async ({ params }: { params: Record<string, string> }) => {
  const { automation_id, account_id: accountId } = params;
  const { pb } = await server_component_pb();
  const user = pb.authStore.record as User;
  const userById = await getUserById(user.id);
  const account = await getAccount(accountId);
  let selectedAutomation;
  let initialNodes: Node[] = [];
  let initialEdges: Edge<any>[] = [];
  if (automation_id) {
    selectedAutomation = await getAutomation(automation_id);
    initialNodes = selectedAutomation?.flow?.nodes ?? [];
    initialEdges = selectedAutomation?.flow?.edges ?? [];
  }
  const templates = await getAllTemplatesFromDb(account, userById);
  const templateName = params.template_name;
  // const template = templates.find((template) => template.template_name === templateName);

  const images = await getFilesForDialogs(accountId, 'images');
  const agents = (await getAgents(accountId)).map((agent) => ({ id: agent.id, name: agent.name }));

  return (
    <ReactFlowProvider>
      <FlowProvider images={images.files} agents={agents} accountId={accountId}>
        <FlowComponent
          templateName={templateName}
          selectedAutomation={selectedAutomation}
          initialNodes={initialNodes}
          initialEdges={initialEdges}
          accountId={accountId}
          templates={templates}
        />
      </FlowProvider>
    </ReactFlowProvider>
  );
};

export default FlowsPage;
