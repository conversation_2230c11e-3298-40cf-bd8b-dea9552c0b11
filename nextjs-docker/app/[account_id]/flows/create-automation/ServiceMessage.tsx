import { Textarea } from '@/components/ui/textarea';
import { IImageWithUrl } from '@/lib/types';
import { <PERSON>le, Node, Position, useNodeId, useNodesData, useReactFlow } from '@xyflow/react';
import { useCallback, useContext, useEffect, useRef } from 'react';
import { FlowContext } from './FlowContext';
import useNodeUtils, { HEIGHT, IMG_HEIGHT } from './useNodeUtils';
import ServiceMessageFileSelectDropDown from './ServiceMessageFileSelectDropDown';
import { Document } from '@/components/Document';
import { Video } from '@/components/Video';
import Image from 'next/image';

interface IServiceData {
  data: {
    options: { id: string; label: string }[];
    text: string;
    images: IImageWithUrl[];
    selectedImage: IImageWithUrl;
    ai: boolean;
    type: string;
  };
}

function ServiceMessage() {
  const { updateNodeData, updateNode, getNodes, getNode } = useReactFlow();
  const id = useNodeId();
  const nodeData: IServiceData = useNodesData(id ?? '') as unknown as IServiceData;

  const { images, accountId } = useContext(FlowContext);
  if (!id) {
    return null;
  }
  const options = nodeData?.data?.options ?? [];
  const text = nodeData?.data?.text ?? '';
  const selectedImage = nodeData?.data?.selectedImage;
  const selectedImageType = nodeData.data.selectedImage?.type ?? 'images';
  const isAiEnabled = nodeData?.data?.ai ?? false;

  const { deleteNode, addButtonNode } = useNodeUtils();
  const onChange = useCallback((evt: any) => {
    updateNodeData(id, { text: evt.target.value });
  }, []);
  const textareaRef = useRef(null);
  const imageRef = useRef<HTMLImageElement>(null);

  const deleteAllButtons = () => {
    const textAreaHeight = document.getElementById(id)?.querySelector(`textarea`)?.clientHeight ?? 0;
    options.forEach((option: { id: string }) => {
      deleteNode(option.id);
    });
    updateNode(id, {
      height: textAreaHeight + HEIGHT,
    });
    updateNodeData(id, {
      options: [],
    });
  };

  const toggleAi = () => {
    updateNodeData(id, {
      ai: !isAiEnabled,
    });
    // If enabling AI, hide buttons
    if (!isAiEnabled) {
      deleteAllButtons();
    }
  };

  useEffect(() => {
    if (selectedImage) {
      deleteAllButtons();
    }
  }, [selectedImage]);

  useEffect(() => {
    const textarea = textareaRef.current;

    if (textarea) {
      const resizeObserver = new ResizeObserver((entries) => {
        for (let entry of entries) {
          // get all child nodes
          const nodes = getNodes();
          const childNodes = nodes.filter((node: Node) => node.parentId === id).map((n) => n.id);
          updateNode(id, {
            height: entry.contentRect.height + HEIGHT + childNodes.length * 90,
          });
          childNodes.forEach((nodeId: string) => {
            updateNode(nodeId, {
              position: {
                x: 10,
                y: entry.contentRect.height + HEIGHT + childNodes.indexOf(nodeId) * 90,
              },
            });
          });
        }
      });

      resizeObserver.observe(textarea);

      // Cleanup observer on unmount
      return () => {
        resizeObserver.unobserve(textarea);
      };
    }
  }, [selectedImage]);

  useEffect(() => {
    updateNode(id, {
      height: (getNode(id)?.measured?.height ?? 0) + (options.length - 1) * 90,
    });
  }, [selectedImage]);
  return (
    <div id={id} className="bg-white/50 p-2 rounded-md min-w-64 w-full">
      <div className="p-2 bg-white/50 h-full relative" style={{ minHeight: HEIGHT + options.length * 90 }}>
        <Handle type="target" position={Position.Left} id={id} isConnectable />
        <button className="text-red-600 absolute top-2 right-2" onClick={() => deleteNode(id)}>
          x
        </button>
        <div className="mb-2 text-sm">Send Message</div>
        {!selectedImage ? (
          <div className="mb-2">
            <ServiceMessageFileSelectDropDown
              handleSetImage={(url: string, name: string, type: string) => {
                const textAreaHeight = document.getElementById(id)?.querySelector(`textarea`)?.clientHeight ?? 0;
                updateNodeData(id, {
                  selectedImage: { id: `${id}_${images.length + 1}`, url, type, name },
                });
                updateNode(id, {
                  height: IMG_HEIGHT + textAreaHeight + HEIGHT,
                });
                // select node by data-id
                const e = document.querySelector(`[data-id="${id}"]`);

                if (e) {
                  (e as HTMLElement).style.height = `${IMG_HEIGHT + textAreaHeight + HEIGHT}px`;
                }
              }}
              accountId={accountId}
            />
          </div>
        ) : (
          <div className="mb-2 relative bg-gray-100 p-2 rounded-md border border-gray-200 ">
            <button
              className="text-red-600 absolute top-2 right-2"
              onClick={() => {
                updateNodeData(id, {
                  selectedImage: null,
                });
              }}
            >
              x
            </button>
            {selectedImageType == 'images' ? (
              <img height={IMG_HEIGHT} ref={imageRef} src={selectedImage.url} alt="selected image" className="w-32 h-32 object-cover" />
            ) : selectedImage.type == 'videos' ? (
              <Video file={null} src={selectedImage.url} />
            ) : (
              <Document fileName={selectedImage.name as string} url={selectedImage.url} />
            )}
          </div>
        )}

        {/* AI Toggle Button */}
        <button onClick={toggleAi} className={`mb-2 p-1 text-sm ${isAiEnabled ? 'bg-green-200' : 'bg-slate-200'}`}>
          Toggle AI {isAiEnabled ? 'On' : 'Off'}
        </button>

        {/* Conditional rendering based on AI state */}
        {!isAiEnabled && selectedImageType != 'audios' && (
          <>
            <Textarea ref={textareaRef} defaultValue={text} id="text" name="text" onChange={onChange} className="nodrag mb-2 w-[400px]" rows={5} />
            {!selectedImage ? (
              <button
                onClick={() => {
                  const textAreaHeight = document.getElementById(id)?.querySelector(`textarea`)?.clientHeight ?? 0;
                  const newOptions = [
                    ...options,
                    {
                      id: `${id}_${options.length + 1}`,
                      label: `Option ${options.length + 1}`,
                    },
                  ];
                  updateNodeData(id, {
                    options: newOptions,
                  });
                  updateNode(id, {
                    height: HEIGHT + textAreaHeight + newOptions.length * 90,
                  });
                  addButtonNode(`${id}_${options.length + 1}`, id);
                }}
                className="text-sm bg-slate-200 p-1 mr-2"
              >
                {' '}
                + Add Option
              </button>
            ) : null}
            {!selectedImage ? (
              <button className="text-red-600" onClick={deleteAllButtons}>
                {' '}
                Remove all options
              </button>
            ) : null}
          </>
        )}
        {isAiEnabled && (
          <div className="bg-green-100 p-3 rounded-md flex items-center justify-center mb-2">
            <span>AI will generate content automatically</span>
          </div>
        )}
        <Handle type="source" position={Position.Right} id={id} isConnectable />
      </div>
    </div>
  );
}

export default ServiceMessage;
