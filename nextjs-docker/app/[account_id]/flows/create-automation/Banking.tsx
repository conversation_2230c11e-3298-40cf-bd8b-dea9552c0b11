import { Button } from '@/components/ui/button';
import useNodeUtils from './useNodeUtils';
import { IAction } from '@/lib/types';
import { useParams } from 'next/navigation';

// List of account IDs that should see the banking section
const BANKING_ENABLED_ACCOUNTS = ['mitf', 'banking-account-1', 'banking-account-2', 'mg7jso5gfhqiftc'];

export const Banking = ({
  bankingActions,
}: {
  bankingActions: {
    icon: JSX.Element;
    label: IAction;
  }[];
}) => {
  const { genericClick } = useNodeUtils();
  const params = useParams();
  const accountId = params.account_id as string;
  
  // Only show banking section for specific accounts
  if (!BANKING_ENABLED_ACCOUNTS.includes(accountId)) {
    return null;
  }
  
  return (
    <div className="flex flex-col gap-2 mb-4">
      <div className="flex-1">
        <h1 className="font-semibold text-lg">Banking</h1>
        <div className="text-sm text-gray-500">Banking specific actions</div>
      </div>
      <div>
        {bankingActions.map((action) => {
          return (
            <Button
              onClick={() => genericClick(action.label)}
              variant="trigger"
              className="flex gap-2 items-center mb-2 text-md font-normal pl-1"
              key={action.label}
            >
              <div className="border bg-blue-50 p-1 border-blue-300 rounded-md text-blue-700">{action.icon}</div>
              <div>{action.label}</div>
            </Button>
          );
        })}
      </div>
    </div>
  );
};
