import { useCallback, useContext } from 'react';
import { <PERSON><PERSON>, Position, useReactFlow } from '@xyflow/react';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { type User as IUser } from '@/lib/types';
import useNodeUtils from './useNodeUtils';
import { FlowContext } from './FlowContext';

function AssignAgent(all: any) {
  const { updateNodeData } = useReactFlow();
  const { agents } = useContext(FlowContext);
  const onChange = useCallback(
    (selectedAgent: any) => {
      updateNodeData(all.id, { selectedAgent });
    },
    [updateNodeData]
  );

  const { deleteNode } = useNodeUtils();

  return (
    <div className="relative">
      <button className="text-red-600 absolute top-2 right-2" onClick={() => deleteNode(all.id)}>
        x
      </button>
      <div className="border p-2 bg-white w-[320px]">
        <div className="mb-2 text-sm">Assign agent</div>
        <Select defaultValue={all.data.selectedAgent} onValueChange={onChange} name="agent">
          <SelectTrigger name="agent">
            <SelectValue placeholder="Select" />
          </SelectTrigger>
          <SelectContent position="popper">
            {agents.map((agent: Partial<IUser>) => (
              <SelectItem key={agent.id} value={agent?.id ?? ''}>
                {agent.name}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>
      <Handle type="target" position={Position.Left} id={all.id} isConnectable />
    </div>
  );
}

export default AssignAgent;
