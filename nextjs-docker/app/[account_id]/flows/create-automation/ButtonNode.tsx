import { Input } from '@/components/ui/input';
import { <PERSON><PERSON>, Node, Position, useReactFlow } from '@xyflow/react';
import { useCallback } from 'react';

function ButtonNode(all: any) {
  const { updateNodeData, setNodes, getNodes } = useReactFlow();

  const onChange = useCallback((evt: any) => {
    updateNodeData(all.id, { text: evt.target.value });
  }, []);

  const deleteNode = (id: string) => {
    all.data.deleteNode(id);
    const _nodes = getNodes();
    // get the parent node
    const parent = _nodes.find((node) => node.id === all.data.parentId);
    const options: Record<string, string>[] = (parent?.data?.options as Record<string, string>[]) ?? [];
    if (options && options?.length > 0) {
      updateNodeData(all.data.parentId, {
        options: options?.filter((option: any) => option.id !== id),
      });
    }
    // find all the child nodes and fix their y positions
    const childNodes = _nodes.filter((node) => node.parentId === all.data.parentId);
    const newNodes = childNodes.map((node, index) => {
      return {
        ...node,
        position: {
          ...node.position,
          y: 120 + (index - 1) * 90,
        },
      };
    });
    setNodes((nodes: Node[]) => {
      return nodes.map((node) => {
        return newNodes.find((n) => n.id === node.id) ?? node;
      });
    });
  };
  return (
    <div>
      <div className="p-2">
        {/* <button className="text-red-600 absolute top-2 right-2" onClick={() => deleteNode(all.id)}>
          x
        </button> */}
        <div className="mb-2 text-sm">Option</div>
        <Input maxLength={24} defaultValue={all.data.text} id="text" name="text" onChange={onChange} className="w-full nodrag" />
      </div>
      <Handle type="source" position={Position.Right} id={all.id} isConnectable />
    </div>
  );
}

export default ButtonNode;
