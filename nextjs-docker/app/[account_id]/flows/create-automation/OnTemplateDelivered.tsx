import { Handle, Position } from '@xyflow/react';
import { useCallback } from 'react';

function OnTemplateDelivered(all: any) {
  const onChange = useCallback((evt: any) => {
    console.log(evt.target.value);
  }, []);

  return (
    <div>
      <div className="flex items-baseline gap-2 border p-2 bg-yellow-100 h-full">
        <div className="text-sm">On Template Delivered</div>
        <button className="text-red-600 ml-auto block" onClick={() => all.data.deleteNode(all.id)}>
          x
        </button>
      </div>
      <Handle type="source" position={Position.Bottom} id={all.id} isConnectable />
    </div>
  );
}

export default OnTemplateDelivered;
