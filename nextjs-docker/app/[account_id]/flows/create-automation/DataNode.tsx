import { Handle, Position, useReactFlow } from '@xyflow/react';
import useNodeUtils from './useNodeUtils';

function DataNode(all: any) {
  const { updateNodeData } = useReactFlow();
  const { deleteNode } = useNodeUtils();
  return (
    <div>
      <div className="border p-2 bg-white w-[320px]">
        <Handle type="target" position={Position.Top} id={all.id} isConnectable />
        <button className="text-red-600 absolute top-2 right-2" onClick={() => deleteNode(all.id)}>
          x
        </button>
        <div>I am a data node</div>
      </div>
      <Handle type="source" position={Position.Bottom} id={all.id} isConnectable />
    </div>
  );
}

export default DataNode;
