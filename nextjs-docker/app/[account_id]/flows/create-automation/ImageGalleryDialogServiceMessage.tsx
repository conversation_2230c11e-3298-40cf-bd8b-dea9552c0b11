'use client';
//Not used anymore
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from '@/components/ui/alert-dialog';
import { Button } from '@/components/ui/button';
import { ScrollArea } from '@/components/ui/scroll-area';
import { IImage } from '@/lib/types';
import clsx from 'clsx';
import { Check, Upload } from 'lucide-react';
import Image from 'next/image';
import { useState } from 'react';

const ImageGalleryDialogServiceMessage = ({ images, handleSetImage }: { images: IImage[]; handleSetImage: any }) => {
  const [selectedImage, setSelectedImage] = useState<IImage>();
  return (
    <AlertDialog>
      <AlertDialogTrigger asChild>
        <Button variant={'outline'}>
          Select Image
          <Upload className="ml-2 h-5 w-5" />
        </Button>
      </AlertDialogTrigger>
      <AlertDialogContent className="max-w-full w-9/12 h-[90vh] flex flex-col">
        <AlertDialogHeader>
          <AlertDialogTitle>Select Image</AlertDialogTitle>
        </AlertDialogHeader>
        <ScrollArea className="flex-grow">
          <div className="grid grid-cols-5 gap-4 p-4">
            {images.map((image) => (
              <div
                key={image.id}
                className={clsx(
                  'p-2 border rounded-md relative hover:cursor-pointer h-32 hover:border-gray-400',
                  selectedImage?.url == image.url ? 'border-blue-500' : 'border-gray-200 '
                )}
                onClick={() => setSelectedImage(image)}
              >
                <div
                  className={clsx(
                    'absolute z-20 h-5 w-5 border flex items-center justify-center rounded-md bg-white',
                    selectedImage?.url == image.url ? 'border-gray-100 text-blue-600' : 'border-gray-200 text-black'
                  )}
                >
                  {selectedImage?.url == image.url && <Check />}
                </div>
                <Image src={image.url} fill alt="image" className="rounded-md object-contain" sizes="(max-width: 600px) 100vw, 50vw" />
              </div>
            ))}
          </div>
        </ScrollArea>
        <AlertDialogFooter>
          <AlertDialogCancel>Cancel</AlertDialogCancel>
          <AlertDialogAction disabled={!selectedImage} onClick={() => handleSetImage(selectedImage?.url ?? '')}>
            Continue
          </AlertDialogAction>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  );
};

export default ImageGalleryDialogServiceMessage;
