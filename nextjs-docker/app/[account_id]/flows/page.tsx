import { ActiveAutomationsComponent } from '@/components/active-automations';
import { AutomationSelectorComponent } from '@/components/automation-selector';
import { CampaignAutomationComponent } from '@/components/campaign-automation';
import ConnectYourBusiness from '@/components/shared/ConnectYourBusiness';
import { Button } from '@/components/ui/button';
import { getAccount, getAllTemplatesFromDb, getAutomations, getUserById } from '@/lib/pocket';
import { server_component_pb } from '@/state/pb/server_component_pb';
import { PlusCircle } from 'lucide-react';

export const dynamic = 'force-dynamic';
export const fetchCache = 'default-no-store';

const Flows = async ({ params }: { params: Record<string, string> }) => {
  const accountId = params.account_id;
  const account = await getAccount(accountId);
  if (!account.access_token) {
    return (
      <div className="bg-gray-100  w-full flex items-center justify-center h-full">
        <ConnectYourBusiness account={account} />
      </div>
    );
  }
  const { pb } = await server_component_pb();
  const user = pb.authStore.record;
  const userById = await getUserById((user as any).id);

  const templates = await getAllTemplatesFromDb(account, userById);

  // get all automations for this account
  const automations = await getAutomations(accountId);
  return (
    <div className="container mx-auto px-4 py-8">
      <div className="mb-12">
        <h1 className="text-3xl font-bold">Create Automation Flows</h1>
        <p>
          Created automated replies or interactive flows based on what customers message you, you can create generalized flows for any message or create
          different flows for each campaigns
        </p>
      </div>
      <AutomationSelectorComponent />
      <CampaignAutomationComponent templates={templates} />
      <ActiveAutomationsComponent automations={automations} />
    </div>
  );
};

export default Flows;

const CreateAutomationButton = () => {
  return (
    <Button>
      <PlusCircle className="mr-2 h-4 w-4" />
      Create New Automation
    </Button>
  );
};
