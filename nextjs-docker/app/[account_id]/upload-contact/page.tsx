import { CSVForm } from '@/components/csvform';
import { Button } from '@/components/ui/button';
import { getAllLists } from '@/lib/pocket';
import Link from 'next/link';

export const maxDuration = 50000;

export default async ({ params }: { params: Record<string, string> }) => {
  const lists = await getAllLists(params.account_id);
  return (
    <div className="h-full w-full flex items-center justify-center">
      <div className="absolute top-20 right-10">
        <Link prefetch={false} href={`/${params.account_id}/lead-management`}>
          <Button type="button">Go to contacts list</Button>
        </Link>
      </div>
      <div className="max-w-[400px]">
        <CSVForm lists={lists} />
      </div>
    </div>
  );
};
