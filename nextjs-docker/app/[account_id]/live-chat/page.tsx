import ConnectYourBusiness from '@/components/shared/ConnectYourBusiness';
import {
  agentsWithConvos,
  getAccount,
  getAgents,
  getAllTags,
  getApprovedTemplatesFromDb,
  getMessageLimitsByUser,
  getSavedMessages,
  getTeams,
  getUser,
} from '@/lib/pocket';
import StateChatLayout from './components/StateChatLayout';

export const viewport = {
  viewportFit: 'cover',
  maximumScale: 1,
};

export default async function LiveChatLayout({ params }: { params: { [key: string]: string } }) {
  const { account_id } = params;
  const account = await getAccount(account_id);
  if (account.access_token) {
    const user = await getUser();
    const [allTags, agentsOfAccount, /*parentConversations,*/ templates, messagingLimit, savedMessages, teams] = await Promise.all([
      getAllTags(params.account_id),
      agentsWithConvos(account_id),
      getApprovedTemplatesFromDb(account),
      getMessageLimitsByUser(user.id, params.account_id),
      getSavedMessages(params.account_id, user.id),
      getTeams(params.account_id),
    ]).catch((e) => {
      console.log(e);
      throw e;
    });
    // if (parentConversations.length === 0) {
    //   return <div className="m-10">You do not have any messages yet</div>;
    // }

    return (
      <div data-testid="live-chat-layout" className="flex h-full w-full">
        <StateChatLayout
          account_id={account_id}
          phone_id={account.phone_id}
          // parentConversations={parentConversations}
          agentsOfAccount={agentsOfAccount}
          allTags={allTags}
          templates={templates}
          messagingLimit={messagingLimit}
          savedMessages={savedMessages}
          currentUser={user}
          teams={teams}
        />
      </div>
    );
  } else {
    return (
      <div data-testid="live-chat-layout" className="flex h-full w-full">
        <div className="flex w-full">
          <div className="bg-white w-1/4">
            <div className="w-full p-4 border-b border-gray-100 mx-auto">
              <div className="font-bold text-center">Conversations</div>
            </div>
            <div className="p-4 space-y-4">
              <div className="bg-gray-200 w-full h-8 rounded-md"></div>
              <div className="bg-gray-200 w-full h-8 rounded-md"></div>
              <div className="bg-gray-200 w-full h-8 rounded-md"></div>
              <div className="bg-gray-200 w-full h-8 rounded-md"></div>
              <div className="bg-gray-200 w-full h-8 rounded-md"></div>
              <div className="bg-gray-200 w-full h-8 rounded-md"></div>
              <div className="bg-gray-200 w-full h-8 rounded-md"></div>
              <div className="bg-gray-200 w-full h-8 rounded-md"></div>
              <div className="bg-gray-200 w-full h-8 rounded-md"></div>
              <div className="bg-gray-200 w-full h-8 rounded-md"></div>
              <div className="bg-gray-200 w-full h-8 rounded-md"></div>
              <div className="bg-gray-200 w-full h-8 rounded-md"></div>
            </div>
          </div>
          <div className="bg-gray-100 w-full flex items-center justify-center h-full">
            <ConnectYourBusiness account={account} />
          </div>
        </div>
      </div>
    );
  }
}
