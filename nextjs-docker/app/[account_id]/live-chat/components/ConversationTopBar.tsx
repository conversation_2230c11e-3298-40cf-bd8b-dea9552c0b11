'use client';

import { db } from '@/app/db';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Skeleton } from '@/components/ui/skeleton';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
import { getConversationExpiry } from '@/lib/utils';
import { useLiveQuery } from 'dexie-react-hooks';
import { AlertCircleIcon, Clock, ClockIcon, TagIcon } from 'lucide-react';
import { Badge } from '@/components/ui/badge';
import clsx from 'clsx';

const ConversationTopBar = ({ convo_id }: { convo_id: string }) => {
  const conversations = useLiveQuery(() => {
    return db.conversations.where({ convo_id: convo_id }).toArray();
  }, [convo_id]);

  const messages = useLiveQuery(
    () => {
      return convo_id
        ? db.messages
            .where({
              convo_id: convo_id,
              from: 'user',
            })
            .sortBy('created')
        : [];
    },
    [convo_id],
    null
  );
  const createdAt = messages?.[messages.length - 1]?.created;
  const lead = conversations?.[0] as any;
  const tags = lead?.tags ? JSON.parse(lead.tags) : [];
  let remainingTime = null;
  let isWithin24Hour = null;
  let hours = null;
  let minutes = null;

  if (conversations?.[0]) {
    if (createdAt) {
      remainingTime = getConversationExpiry(createdAt).timeInMinsToExpire * 60;
      isWithin24Hour = getConversationExpiry(createdAt).isWithin24Hour;
      hours = Math.floor(remainingTime / 3600);
      minutes = Math.floor((remainingTime % 3600) / 60)
        .toString()
        .padStart(2, '0');
    }

    return (
      <div className="hidden sm:flex flex-wrap justify-end gap-2 h-[60px] items-center p-5 border-b shadow-sm bg-gray-100/40 space-x-4">
        {lead ? (
          <div className="flex-1 flex items-center space-x-2">
            <div className="font-medium">{lead?.name ? <div>{lead?.name}</div> : <div>{lead?.phone_number}</div>}</div>
            <div>-</div>
            <div>{lead?.phone_number}</div>
            <div className="flex items-center gap-2">
              {tags.slice(0, 3).map((_tags: string, index: number) => (
                <Badge variant={'secondary'} key={index}>
                  <TagIcon className="h-3 w-3 mr-1" />
                  {_tags}
                </Badge>
              ))}
              {tags.length > 3 && <div>+{tags.length - 3}</div>}
            </div>
            {conversations?.[0].chat_block && <Badge variant={'destructive'}>Blocked</Badge>}
          </div>
        ) : (
          <Skeleton className="w-[200px] h-6" />
        )}

        {createdAt ? (
          isWithin24Hour ? (
            <Alert variant={'default'} className="max-w-fit py-1">
              <AlertDescription className="flex items-center space-x-2 text-sm">
                <ClockIcon className="h-4 w-4" />
                <span>Conversation expires in</span>
                <span className={clsx(hours && (hours > 20 ? 'text-green-500' : hours > 10 ? 'text-orange-500' : 'text-red-500'))}>
                  {hours !== null && hours > 0 && `${hours}h `}
                  {minutes}m
                </span>
              </AlertDescription>
            </Alert>
          ) : (
            <Alert variant={'destructive'} className="max-w-fit py-1 hover:cursor-help">
              <TooltipProvider delayDuration={0}>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <AlertDescription className="flex items-center space-x-2">
                      <Clock className="h-4 w-4" />
                      <span>Conversation Expired</span>
                    </AlertDescription>
                  </TooltipTrigger>
                  <TooltipContent>
                    <p>24 hour window has ended. Conversation will start once user messages again.</p>
                  </TooltipContent>
                </Tooltip>
              </TooltipProvider>
            </Alert>
          )
        ) : (
          <Alert variant={'warning'} className="max-w-fit py-1 hover:cursor-help">
            <TooltipProvider delayDuration={0}>
              <Tooltip>
                <TooltipTrigger asChild>
                  <AlertDescription className="flex items-center space-x-2">
                    <AlertCircleIcon className="h-4 w-4" />
                    <span>Waiting for Conversation to start</span>
                  </AlertDescription>
                </TooltipTrigger>
                <TooltipContent>
                  <p>Conversation will start with user messages first</p>
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>
          </Alert>
        )}
      </div>
    );
  } else {
    return (
      <div className="flex h-[60px] items-center p-5 border-b shadow-sm bg-gray-100/40 space-x-4">
        <div className="flex-1">
          <Skeleton className="w-[200px] h-6" />
        </div>
        <div className="flex items-center space-x-2">
          <Skeleton className="w-[200px] h-8" />
        </div>
      </div>
    );
  }
};

export default ConversationTopBar;
