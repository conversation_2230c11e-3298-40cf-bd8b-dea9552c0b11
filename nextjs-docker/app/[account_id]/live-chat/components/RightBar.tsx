'use client';
import { db } from '@/app/db';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from '@/components/ui/alert-dialog';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import MultipleSelector, { Option } from '@/components/ui/multiple-selector';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Separator } from '@/components/ui/separator';
import { useToast } from '@/components/ui/use-toast';
import { updateMetaTags, updateRecordByIdRightBar } from '@/lib/pocket';
import { IExpandedConversationsWithMessages, ILead, Team, User } from '@/lib/types';
import dayjs from 'dayjs';
import { AlignJustify, CheckCircle, Clock, ExternalLink, Loader2, PencilIcon, Phone, PlusCircle, PlusIcon, Trash, Undo2, User as UserIcon } from 'lucide-react';
import Link from 'next/link';
import { useEffect, useRef, useState } from 'react';
import AgentSelectDropDown from '../[convo_id]/component/AgentSelectDropDown';
import { useParams } from 'next/navigation';
import { Input } from '@/components/ui/input';
import clsx from 'clsx';
import { useFormStatus } from 'react-dom';
import { Sheet, SheetContent, SheetTrigger } from '@/components/ui/sheet';

const RightBar = ({
  allTags,
  lead,
  convo_id,
  otags,
  currentUser,
  teams,
  conversation,
  agentsOfAccount,
}: {
  allTags: Option[];
  lead: ILead;
  otags: any[];
  currentUser: User;
  teams: Team[];
  convo_id: string;
  agentsOfAccount: User[];
  conversation: IExpandedConversationsWithMessages;
}) => {
  const metaParsed: Record<string, string> = typeof lead?.meta === 'string' ? JSON.parse(((lead?.meta as any) ?? {}).toString()) : lead?.meta;
  const formRef = useRef<HTMLFormElement>(null);
  const [meta, setMeta] = useState(
    metaParsed ? Object.entries(metaParsed).map(([key, value]) => ({ key, value: value as string | number, deleted: false, edit: false })) : []
  );
  const { toast } = useToast();
  const [isSaving, setIsSaving] = useState(false);
  const [tags, setTags] = useState<Option[]>([]);

  const params = useParams();
  const accountId = params.account_id as string;
  useEffect(() => {
    setTags(otags.length > 0 ? otags.map((tag) => ({ label: tag, value: tag })) : []);
    setMeta(metaParsed ? Object.entries(metaParsed).map(([key, value]) => ({ key, value, deleted: false, edit: false })) : []);
  }, [lead]);

  // Delete an item
  const toggleDelete = (key: string) => {
    setMeta((prev) => prev.map((item) => (item.key === key ? { ...item, deleted: !item.deleted } : item)));
  };

  // edit an item
  const toggleEdit = (key: string) => {
    setMeta((prev) => prev.map((item) => (item.key === key ? { ...item, edit: !item.edit } : item)));
  };

  const [itemKey, setItemKey] = useState('');
  const [itemValue, setItemValue] = useState('');
  const handleAddNewItem = () => {
    const itemToAdd = {
      key: itemKey,
      value: itemValue,
      deleted: false,
      edit: false,
    };

    setMeta([...meta, itemToAdd]);
    setItemKey('');
    setItemValue('');
  };

  const convertFilteredArrayToObject = () => {
    return meta
      .filter((item: any) => !item.deleted) // Filter out items where deleted is true
      .reduce((acc: Record<string, any>, { key, value }: { key: string; value: any }) => {
        acc[key] = value; // Add key-value pairs to the accumulator
        return acc;
      }, {});
  };

  const MetaTags = ({ item }: { item: { key: string; value: string | number; deleted: boolean; edit: boolean } }) => {
    const [itemKey, setItemKey] = useState(item.key);
    const [itemValue, setItemValue] = useState(item.value);

    const updateItem = () => {
      setMeta((prevMeta) => prevMeta.map((prevItem) => (prevItem.key === item.key ? { ...item, value: itemValue, key: itemKey, edit: false } : prevItem)));
      setItemKey('');
      setItemValue('');
    };

    return (
      <div className="flex flex-col space-y-3">
        <div className={clsx('flex items-center gap-2 text-sm', item.deleted && 'line-through')}>
          {item.edit ? (
            <Input placeholder="Key" value={itemKey} onChange={(e) => setItemKey(e.target.value)} />
          ) : (
            <span className="font-medium">{item.key}:</span>
          )}
          {item.edit ? (
            <Input placeholder="Value" value={itemValue} onChange={(e) => setItemValue(e.target.value)} />
          ) : (
            <span className="text-primary">{item.value}</span>
          )}

          {item.deleted ? (
            <Undo2 className="ml-2 h-4 w-4 text-black hover:cursor-pointer flex-shrink-0" onClick={() => toggleDelete(item.key)} />
          ) : (
            !item.edit && (
              <Trash className="ml-2 h-4 w-4 text-red-600 hover:text-red-800 hover:cursor-pointer flex-shrink-0" onClick={() => toggleDelete(item.key)} />
            )
          )}
          {item.edit ? (
            <CheckCircle className="ml-2 h-4 w-4 text-black hover:cursor-pointer flex-shrink-0" onClick={() => updateItem()} />
          ) : (
            <PencilIcon className="ml-2 h-4 w-4 text-black hover:cursor-pointer flex-shrink-0" onClick={() => toggleEdit(item.key)} />
          )}
        </div>
      </div>
    );
  };

  const AlertDialogSaveTriggerButton = ({ isSaving }: { isSaving: boolean }) => {
    const { pending } = useFormStatus();
    return (
      <AlertDialogTrigger asChild>
        {isSaving || pending ? (
          <Button disabled variant={'secondary'}>
            Saving <Loader2 className="ml-2 animate-spin" />
          </Button>
        ) : (
          <Button variant={'secondary'} type="button">
            Save Changes
          </Button>
        )}
      </AlertDialogTrigger>
    );
  };

  const campaign_history = conversation?.campaign_history ? JSON.parse(conversation.campaign_history as unknown as string) : [];
  const [isOpen, setIsOpen] = useState(false);

  return (
    <>
      {/* Sheet for mobile (smaller than sm) */}
      <div className="lg:hidden">
        <Sheet>
          <SheetTrigger asChild>
            <Button variant={'ghost'} size="icon">
              <AlignJustify />
            </Button>
          </SheetTrigger>
          <SheetContent side="right" className="p-0 w-full max-w-md">
            {lead && (
              <>
                <div className="flex p-0 items-start bg-white gap-x-2 overflow-y-scroll grow h-dvh w-full">
                  <form
                    className="flex flex-col gap-2 px-4 w-full py-4"
                    ref={formRef}
                    action={async (formData) => {
                      setIsSaving(true);
                      if (meta.length > 0 && meta.filter((item) => item.edit).length > 0) {
                        setIsSaving(false);
                        return alert('Please save the edited meta tags before saving the contact.');
                      }
                      let updatedMeta = convertFilteredArrayToObject();
                      const record = await updateRecordByIdRightBar(lead.id, accountId, formData, tags, updatedMeta);
                      if (record.status == 200 && record.record) {
                        setMeta(Object.entries(record.record?.meta).map(([key, value]) => ({ key, value, deleted: false, edit: false })));
                        await db.conversations.update(convo_id, {
                          tags: JSON.stringify(record.record.tags),
                          status: record.record.status,
                          meta: record.record.meta,
                        });
                        toast({
                          variant: 'success',
                          description: 'The contact has been updated successfully',
                        });
                      } else {
                        toast({
                          variant: 'destructive',
                          description: 'There was an error in updating the contact.',
                        });
                      }
                      setIsSaving(false);
                    }}
                  >
                    <div>Lead Actions</div>
                    <Card>
                      <CardHeader className="pb-3">
                        <CardTitle className="text-base font-medium">Agent Assigned to:</CardTitle>
                      </CardHeader>
                      <CardContent>
                        <AgentSelectDropDown
                          currentUser={currentUser}
                          convo_id={convo_id}
                          account_id={accountId}
                          teams={teams}
                          agentsOfAccount={agentsOfAccount}
                          assignedAgent={conversation?.expand?.assigned_agent}
                        />
                      </CardContent>
                    </Card>
                    <Card>
                      <CardHeader className="pb-3">
                        <CardTitle className="text-base font-medium">User Details</CardTitle>
                      </CardHeader>
                      <CardContent className="space-y-1">
                        <div className="flex items-center gap-2 text-sm">
                          <UserIcon className="w-4 h-4 text-muted-foreground" />
                          <div className="text-primary">{lead.name}</div>
                        </div>
                        <div className="flex items-center gap-2 text-sm">
                          <Phone className="w-4 h-4 text-muted-foreground" />
                          <div className="text-primary">{lead.phone_number}</div>
                        </div>
                      </CardContent>
                    </Card>
                    <Card>
                      <CardHeader className="pb-3">
                        <CardTitle className="text-base font-medium">Attributes</CardTitle>
                      </CardHeader>
                      <CardContent className="space-y-2">
                        <div className="space-y-1">
                          <label className="text-sm font-medium">Status</label>
                          <Select name="status" defaultValue={lead.status}>
                            <SelectTrigger>
                              <SelectValue placeholder="Select status" />
                            </SelectTrigger>
                            <SelectContent>
                              <SelectItem value="New">New</SelectItem>
                              <SelectItem value="Prospect">Prospect</SelectItem>
                              <SelectItem value="Opportunity">Opportunity</SelectItem>
                            </SelectContent>
                          </Select>
                        </div>
                      </CardContent>
                    </Card>
                    <Card>
                      <CardHeader className="pb-3">
                        <CardTitle className="text-base font-medium">Tags</CardTitle>
                      </CardHeader>
                      <CardContent>
                        <MultipleSelector
                          key={allTags.length}
                          onChange={setTags}
                          value={tags}
                          defaultOptions={allTags}
                          creatable={true}
                          placeholder="Add or remove tags"
                          className="border border-slate-200"
                          badgeClassName="bg-gray-600 text-white"
                          showTagIcon
                          emptyIndicator={<p className="text-center bg-white text-lg leading-10 text-gray-600 dark:text-gray-400">no results found.</p>}
                        />
                      </CardContent>
                    </Card>
                    <Card>
                      <CardHeader className="pb-3">
                        <CardTitle className="text-base font-medium">Campaign History</CardTitle>
                      </CardHeader>
                      <CardContent className="space-y-3">
                        {campaign_history.length > 0
                          ? campaign_history.map((campaign: any) => {
                              return (
                                <div className="space-y-3">
                                  <div className="space-y-1">
                                    <p className="text-sm font-medium">{campaign.name}</p>
                                    <div className="flex items-center text-xs text-muted-foreground">
                                      <Clock className="w-3 h-3 mr-1" />
                                      {dayjs(campaign.created).format('DD/MM/YY')}
                                    </div>
                                  </div>
                                  <Separator />
                                </div>
                              );
                            })
                          : 'No Campaign History'}
                      </CardContent>
                    </Card>
                    <Card>
                      <CardHeader className="pb-3">
                        <CardTitle className="text-base font-medium">Meta</CardTitle>
                      </CardHeader>
                      <CardContent className="space-y-4">
                        {meta.length > 0 ? (
                          meta.map((item, index) => <MetaTags key={index} item={item} />)
                        ) : (
                          <div>
                            <div>No Meta Information</div>
                          </div>
                        )}
                        <div className="flex flex-col space-y-2">
                          <div className="flex items-center space-x-2 ">
                            <div className="text-sm font-medium">Key</div>
                            <MultipleSelector
                              maxSelected={1}
                              creatable
                              placeholder="key"
                              onChange={(a) => {
                                if (a?.[0]?.value) {
                                  setItemKey(a[0].value);
                                }
                              }}
                              defaultOptions={meta.map((m) => ({ label: m.key, value: m.key }))}
                            />
                          </div>
                          <div className="flex items-center space-x-2 ">
                            <div className="text-sm font-medium">Value</div>
                            <Input placeholder="Value" value={itemValue} onChange={(e) => setItemValue(e.target.value)} />
                          </div>
                          <Button onClick={() => handleAddNewItem()} variant={'ghost'} type="button" disabled={itemKey.length === 0 || itemValue.length === 0}>
                            <PlusCircle className="mr-2 h-4 w-4 text-green-600" /> Add
                          </Button>
                        </div>
                      </CardContent>
                    </Card>
                  </form>
                </div>

                <div className="sticky bottom-0 left-0 w-full p-4 bg-white border-t border-slate-200">
                  <div className="flex gap-3">
                    <AlertDialog>
                      <AlertDialogSaveTriggerButton isSaving={isSaving} />
                      <AlertDialogContent>
                        <AlertDialogHeader>
                          <AlertDialogTitle>Save Changes</AlertDialogTitle>
                          <AlertDialogDescription>Are you sure you want to update the contact.</AlertDialogDescription>
                        </AlertDialogHeader>
                        <AlertDialogFooter>
                          <AlertDialogCancel>Cancel</AlertDialogCancel>
                          <AlertDialogAction
                            onClick={async () => {
                              formRef.current?.requestSubmit();
                            }}
                          >
                            Yes
                          </AlertDialogAction>
                        </AlertDialogFooter>
                      </AlertDialogContent>
                    </AlertDialog>

                    <Link prefetch={false} target="_blank" href={`/${accountId}/edit-contact?id=${lead?.id}`}>
                      <Button type="button" variant={'outline'}>
                        Details <ExternalLink className="ml-2 h-5 w-5" />
                      </Button>
                    </Link>
                  </div>
                </div>
              </>
            )}
          </SheetContent>
        </Sheet>
      </div>
      {/* Sidebar for sm+ screens */}
      <div
        className={clsx(
          'hidden lg:flex flex-col overflow-hidden transition-all duration-200 ease-in-out',
          isOpen ? 'w-full max-w-[340px]' : 'w-[40px] max-w-[40px]'
        )}
        style={{ overflowX: 'hidden' }}
      >
        <div className="items-start">
          <Button variant={'ghost'} onClick={() => setIsOpen((open) => !open)} size="icon">
            <AlignJustify />
          </Button>
        </div>
        {isOpen && lead && (
          <>
            <div className="flex p-0 items-start bg-white gap-x-2 overflow-y-scroll grow h-dvh w-full">
              <form
                className="flex flex-col gap-2 px-4 w-full"
                ref={formRef}
                action={async (formData) => {
                  setIsSaving(true);
                  if (meta.length > 0 && meta.filter((item) => item.edit).length > 0) {
                    setIsSaving(false);
                    return alert('Please save the edited meta tags before saving the contact.');
                  }
                  let updatedMeta = convertFilteredArrayToObject();
                  const record = await updateRecordByIdRightBar(lead.id, accountId, formData, tags, updatedMeta);
                  if (record.status == 200 && record.record) {
                    setMeta(Object.entries(record.record?.meta).map(([key, value]) => ({ key, value, deleted: false, edit: false })));
                    await db.conversations.update(convo_id, {
                      tags: JSON.stringify(record.record.tags),
                      status: record.record.status,
                      meta: record.record.meta,
                    });
                    toast({
                      variant: 'success',
                      description: 'The contact has been updated successfully',
                    });
                  } else {
                    toast({
                      variant: 'destructive',
                      description: 'There was an error in updating the contact.',
                    });
                  }
                  setIsSaving(false);
                }}
              >
                <Card>
                  <CardHeader className="pb-3">
                    <CardTitle className="text-base font-medium">Agent Assigned to:</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <AgentSelectDropDown
                      currentUser={currentUser}
                      convo_id={convo_id}
                      account_id={accountId}
                      teams={teams}
                      agentsOfAccount={agentsOfAccount}
                      assignedAgent={conversation?.expand?.assigned_agent}
                    />
                  </CardContent>
                </Card>
                <Card>
                  <CardHeader className="pb-3">
                    <CardTitle className="text-base font-medium">User Details</CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-1">
                    <div className="flex items-center gap-2 text-sm">
                      <UserIcon className="w-4 h-4 text-muted-foreground" />
                      <div className="text-primary">{lead.name}</div>
                    </div>
                    <div className="flex items-center gap-2 text-sm">
                      <Phone className="w-4 h-4 text-muted-foreground" />
                      <div className="text-primary">{lead.phone_number}</div>
                    </div>
                  </CardContent>
                </Card>
                <Card>
                  <CardHeader className="pb-3">
                    <CardTitle className="text-base font-medium">Attributes</CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-2">
                    <div className="space-y-1">
                      <label className="text-sm font-medium">Status</label>
                      <Select name="status" defaultValue={lead.status}>
                        <SelectTrigger>
                          <SelectValue placeholder="Select status" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="New">New</SelectItem>
                          <SelectItem value="Prospect">Prospect</SelectItem>
                          <SelectItem value="Opportunity">Opportunity</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                  </CardContent>
                </Card>
                <Card>
                  <CardHeader className="pb-3">
                    <CardTitle className="text-base font-medium">Tags</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <MultipleSelector
                      key={allTags.length}
                      onChange={setTags}
                      value={tags}
                      defaultOptions={allTags}
                      creatable={true}
                      placeholder="Add or remove tags"
                      className="border border-slate-200"
                      badgeClassName="bg-gray-600 text-white"
                      showTagIcon
                      emptyIndicator={<p className="text-center bg-white text-lg leading-10 text-gray-600 dark:text-gray-400">no results found.</p>}
                    />
                  </CardContent>
                </Card>
                <Card>
                  <CardHeader className="pb-3">
                    <CardTitle className="text-base font-medium">Campaign History</CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-3">
                    {campaign_history.length > 0
                      ? campaign_history.map((campaign: any) => {
                          return (
                            <div className="space-y-3">
                              <div className="space-y-1">
                                <p className="text-sm font-medium">{campaign.name}</p>
                                <div className="flex items-center text-xs text-muted-foreground">
                                  <Clock className="w-3 h-3 mr-1" />
                                  {dayjs(campaign.created).format('DD/MM/YY')}
                                </div>
                              </div>
                              <Separator />
                            </div>
                          );
                        })
                      : 'No Campaign History'}
                  </CardContent>
                </Card>
                <Card>
                  <CardHeader className="pb-3">
                    <CardTitle className="text-base font-medium">Meta</CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    {meta.length > 0 ? (
                      meta.map((item, index) => <MetaTags key={index} item={item} />)
                    ) : (
                      <div>
                        <div>No Meta Information</div>
                      </div>
                    )}
                    <div className="flex flex-col space-y-2">
                      <div className="flex items-center space-x-2 ">
                        <div className="text-sm font-medium">Key</div>
                        <MultipleSelector
                          maxSelected={1}
                          creatable
                          placeholder="key"
                          onChange={(a) => {
                            if (a?.[0]?.value) {
                              setItemKey(a[0].value);
                            }
                          }}
                          defaultOptions={meta.map((m) => ({ label: m.key, value: m.key }))}
                        />
                      </div>
                      <div className="flex items-center space-x-2 ">
                        <div className="text-sm font-medium">Value</div>
                        <Input placeholder="Value" value={itemValue} onChange={(e) => setItemValue(e.target.value)} />
                      </div>
                      <Button onClick={() => handleAddNewItem()} variant={'ghost'} type="button" disabled={itemKey.length === 0 || itemValue.length === 0}>
                        <PlusCircle className="mr-2 h-4 w-4 text-green-600" /> Add
                      </Button>
                    </div>
                  </CardContent>

                  <CardContent></CardContent>
                </Card>
              </form>
            </div>

            <div className="sticky bottom-0 left-0 w-full py-1 px-1 lg:p-4">
              <div className="flex gap-3">
                <AlertDialog>
                  <AlertDialogSaveTriggerButton isSaving={isSaving} />
                  <AlertDialogContent>
                    <AlertDialogHeader>
                      <AlertDialogTitle>Save Changes</AlertDialogTitle>
                      <AlertDialogDescription>Are you sure you want to update the contact.</AlertDialogDescription>
                    </AlertDialogHeader>
                    <AlertDialogFooter>
                      <AlertDialogCancel>Cancel</AlertDialogCancel>
                      <AlertDialogAction
                        onClick={async () => {
                          formRef.current?.requestSubmit();
                        }}
                      >
                        Yes
                      </AlertDialogAction>
                    </AlertDialogFooter>
                  </AlertDialogContent>
                </AlertDialog>

                <Link prefetch={false} target="_blank" href={`/${accountId}/edit-contact?id=${lead?.id}`}>
                  <Button type="button" variant={'outline'}>
                    Details <ExternalLink className="ml-2 h-5 w-5" />
                  </Button>
                </Link>
              </div>
            </div>
          </>
        )}
      </div>
    </>
  );
};

export default RightBar;
