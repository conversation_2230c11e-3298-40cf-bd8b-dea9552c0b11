'use client';
import { db } from '@/app/db';
import { hookParams, MyEditor, RichTextEditorToolbar } from '@/components/editor';
import { Button } from '@/components/ui/button';
import { User } from '@/lib/types';
import { useEditor } from '@tiptap/react';
import clsx from 'clsx';
import { SendHorizonal } from 'lucide-react';
import React, { RefObject, useState } from 'react';

const AgentInputBox = ({
  currentUser,
  isWithin24Hour,
  unAssigned,
  disabled,
  formRef,
  convo_id,
  setSubmitLoading,
  isBlocked,
}: {
  currentUser: User;
  isWithin24Hour: boolean;
  unAssigned: boolean;
  disabled: boolean;
  formRef: RefObject<HTMLFormElement>;
  convo_id: string;
  setSubmitLoading: React.Dispatch<React.SetStateAction<boolean>>;
  isBlocked: boolean;
}) => {
  const [inputvalue, setInputValue] = useState('');
  let placeholder = isBlocked
    ? 'Lead is blocked. You cannot message'
    : !isWithin24Hour
      ? 'Waiting for the customer to respond...'
      : unAssigned
        ? 'Type a message...'
        : disabled
          ? 'You are not assigned to this conversation'
          : 'Type a message...';

  let contentEditable = !disabled && isWithin24Hour && !isBlocked;
  const hookParams_ = hookParams({
    placeholder,
    setInputValue,
    enterHandler: formRef,
    type: 'input-box',
    setSubmitLoading: setSubmitLoading,
  });
  // @ts-ignore
  const editor = useEditor(hookParams_);

  return (
    <>
      <textarea hidden defaultValue={inputvalue} name="message" />
      <div
        className={clsx(
          'flex min-h-[40px] max-h-[200px] w-full rounded-md border-2 bg-white px-3 py-2 text-sm placeholder:text-slate-500 focus-visible:outline-none focus:ring-0 focus:outline-none resize-none z-20 flex-1',
          disabled || !isWithin24Hour ? 'border-gray-300 placeholder:text-gray-300 hover:cursor-not-allowed' : 'border-gray-400'
        )}
      >
        <MyEditor placeholder={placeholder} setInputValue={setInputValue} editor={editor} contentEditable={contentEditable} />
      </div>
      {/* {contentEditable && <div className="hidden 2xl:block">{editor ? <RichTextEditorToolbar editor={editor} type={'input-box'} /> : null}</div>} */}
      {contentEditable && (
        <Button
          className="border-green-700 rounded-full"
          variant={'outline'}
          type="button"
          onClick={() => {
            setSubmitLoading(true);
            if (unAssigned) {
              db.conversations.update(convo_id, { 'expand.assigned_agent': currentUser, assigned_agent: currentUser.id });
            }
            formRef?.current?.requestSubmit();
            setInputValue('');
            editor?.commands.clearContent();
          }}
        >
          <SendHorizonal className="h-4 w-4 text-green-700" />
        </Button>
      )}
    </>
  );
};

export default AgentInputBox;
