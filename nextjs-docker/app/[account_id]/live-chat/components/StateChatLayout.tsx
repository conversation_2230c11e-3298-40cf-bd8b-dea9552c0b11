'use client';
import { db } from '@/app/db';
import { ChatLayout } from '@/components/chat-layout';
import { Option } from '@/components/ui/multiple-selector';
import { Toaster } from '@/components/ui/toaster';
import {
  AgentWithConvos,
  IConversation,
  IConvoFromSQL,
  IExpandedConversation,
  IExpandedConversationsWithMessages,
  IMessage,
  IMessagingLimit,
  ITemplateDatabase,
  Team,
  User,
} from '@/lib/types';
import { pb_url } from '@/state/consts';
import { useSearchParams } from 'next/navigation';
import PocketBase from 'pocketbase';
import { useEffect, useState } from 'react';
import { ConversationPage } from '../[convo_id]/conversation';
import { getConversationsWithaReply } from '@/lib/pocket';
import { useLiveQuery } from 'dexie-react-hooks';
// import { getConversationsWithaReplyFull } from '@/lib/pocket';
// import { Loader2 } from 'lucide-react';

const StateChatLayout = ({
  account_id,
  // parentConversations,
  // allMessages,
  agentsOfAccount,
  allTags,
  templates,
  messagingLimit,
  savedMessages,
  currentUser,
  teams,
  phone_id,
}: {
  account_id: string;
  // parentConversations: IExpandedConversationsWithMessages[];
  // allMessages: IExpandedMessage[];
  agentsOfAccount: AgentWithConvos[];
  allTags: Option[];
  templates: ITemplateDatabase[];
  messagingLimit: IMessagingLimit | null;
  savedMessages: IMessage[] | null;
  currentUser: User;
  teams: Team[];
  phone_id: string;
}) => {
  const searchParams = useSearchParams();
  const userId = searchParams.get('user_id');

  const [loading, setLoading] = useState(false);

  const mergeConvos = (_parentConversations: IExpandedConversationsWithMessages[]) => {
    db.conversations.bulkPut(
      _parentConversations.map((convo) => {
        const tempConvo = { ...convo };
        if (!tempConvo?.updated) {
          tempConvo.updated = new Date();
        }
        return tempConvo;
      })
    );
  };

  const refetchConversations = async () => {
    console.time('new parent conversations arrived');
    // mergeConvos(parentConversations);
    setLoading(true);
    getConversationsWithaReply(account_id).then((allConvos) => {
      const _allConvos = allConvos.map((convo: IConvoFromSQL) => {
        const tempConvo: IExpandedConversationsWithMessages = {
          ...convo,
          tags: convo.tags,
          team_name: convo.team_name,
          team_id: convo.team_id,
          expand: {
            ...(convo.assigned_agent
              ? {
                  assigned_agent: agentsOfAccount.find((agent) => agent.id === convo.assigned_agent),
                }
              : undefined),
            from: {
              ...convo,
              tags: convo.tags as unknown as string[],
            },
          },
        };
        return tempConvo;
      });
      mergeConvos(_allConvos);

      setLoading(false);
    });
  };
  const [convo_id, setConvoId] = useState('');
  const conversations = useLiveQuery(() => db.conversations.toArray(), [], []);
  useEffect(() => {
    if (convo_id === '' && conversations.length > 0) {
      setConvoId(conversations[0].convo_id);
    }
  }, [conversations]);

  // Whenever open live chat for the first time, refetch conversations
  useEffect(() => {
    refetchConversations();
  }, [account_id]);

  useEffect(() => {
    const effect = async () => {
      const pb = new PocketBase(pb_url);
      const resultList = await pb.collection<IConversation>('conversations').getFirstListItem(`from.id = "${userId}"`, {
        expand: 'campaign_history, assigned_agent, from',
      });
      if (savedMessages) {
        db.savedMessages.bulkPut(savedMessages);
      }
      const convo = await db.conversations.where({ convo_id: resultList?.id }).toArray();
      if (convo.length === 0) {
        console.log('convo not found in indexeddb');
        // @ts-expect-error
        db.conversations.put(resultList).then(() => {
          setConvoId(resultList.id);
        });
      } else {
        setConvoId(resultList.id);
      }
    };
    if (userId) {
      try {
        effect();
      } catch (error) {
        console.log(error);
      }
    }
  }, [userId]);

  return (
    <div className="flex flex-col w-full" data-testid="state-chat-layout">
      <div className="flex h-full">
        <ChatLayout
          convo_id={convo_id}
          setConvoId={setConvoId}
          account_id={account_id}
          allTags={allTags}
          agentsOfAccount={agentsOfAccount}
          refetchConversations={refetchConversations}
          currentUser={currentUser}
          teams={teams}
          loading={loading}
          phone_id={phone_id}
        >
          {convo_id ? (
            <ConversationPage convo_id={convo_id} accountId={account_id} templates={templates} messagingLimit={messagingLimit} currentUser={currentUser} />
          ) : (
            <div>Please select a conversation</div>
          )}
        </ChatLayout>
        {/* <div className='overflow-scroll'> */}
        {/* </div> */}
      </div>
      <Toaster />
    </div>
  );
};

export default StateChatLayout;
