import { Skeleton } from '@/components/ui/skeleton';
import { MessageSquare, Send, Smile, Paperclip } from 'lucide-react';

export default function ChatSkeleton() {
  return (
    <div className="flex h-screen bg-background">
      {/* Left Sidebar */}
      <div className="hidden md:flex flex-col w-64 border-r">
        {/* Search bar skeleton */}
        <div className="p-4">
          <Skeleton className="h-9 w-full rounded-md" />
        </div>

        {/* Conversation list skeletons */}
        <div className="flex-1 overflow-auto p-2 space-y-2">
          {[...Array(8)].map((_, i) => (
            <div key={i} className="flex items-center space-x-4 p-2">
              <Skeleton className="h-10 w-10 rounded-full" />
              <div className="space-y-2 flex-1">
                <Skeleton className="h-4 w-24" />
                <Skeleton className="h-3 w-full" />
              </div>
              <Skeleton className="h-4 w-8" />
            </div>
          ))}
        </div>
      </div>

      {/* Main Chat Area */}
      <div className="flex-1 flex flex-col relative">
        {/* Header */}
        <div className="border-b p-4 flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <Skeleton className="h-10 w-10 rounded-full" />
            <div>
              <Skeleton className="h-5 w-32" />
              <Skeleton className="h-4 w-24 mt-1" />
            </div>
          </div>
          <div className="flex items-center space-x-4">
            <Skeleton className="h-8 w-24" />
            <Skeleton className="h-8 w-8 rounded-full" />
          </div>
        </div>

        {/* Chat Messages */}
        <div className="flex-1 overflow-auto p-4 space-y-4">
          {[...Array(5)].map((_, i) => (
            <div key={i} className={`flex ${i % 2 === 0 ? 'justify-start' : 'justify-end'}`}>
              <div className={`flex ${i % 2 === 0 ? 'flex-row' : 'flex-row-reverse'} items-start space-x-2 max-w-[80%]`}>
                <Skeleton className="h-8 w-8 rounded-full flex-shrink-0" />
                <div className="space-y-2">
                  <Skeleton className={`h-4 w-24 ${i % 2 === 0 ? 'ml-0' : 'ml-auto'}`} />
                  <Skeleton className="h-20 w-72 rounded-lg" />
                  <Skeleton className="h-3 w-16" />
                </div>
              </div>
            </div>
          ))}
        </div>

        {/* Floating "Loading chats" text */}
        <div className="absolute inset-0 flex items-center justify-center pointer-events-none">
          <div className="bg-background/80 text-black px-4 py-2 rounded-full text-lg font-semibold shadow-lg">Loading chats...</div>
        </div>

        {/* Message Input */}
        <div className="border-t p-4">
          <div className="flex items-center space-x-2">
            <Skeleton className="flex-1 h-10 rounded-md" />
            <div className="flex items-center space-x-2">
              <Smile className="w-6 h-6 text-muted-foreground" />
              <Paperclip className="w-6 h-6 text-muted-foreground" />
              <Send className="w-6 h-6 text-muted-foreground" />
            </div>
          </div>
        </div>
      </div>

      {/* Right Info Panel */}
      <div className="hidden lg:flex flex-col w-64 border-l">
        <div className="p-4 space-y-4">
          <Skeleton className="h-32 w-full rounded-lg" />
          <div className="space-y-2">
            <Skeleton className="h-4 w-full" />
            <Skeleton className="h-4 w-3/4" />
            <Skeleton className="h-4 w-1/2" />
          </div>
        </div>
      </div>
    </div>
  );
}
