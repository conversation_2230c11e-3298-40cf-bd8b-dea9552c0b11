import { Button } from '@/components/ui/button';
import { getPb } from '@/lib/pocket';
import type { IExpandedMessage } from '@/lib/types';
import { parseMessageContent } from '@/lib/utils';
import Image from 'next/image';
import { useEffect, useRef } from 'react';
import TemplatePreview from '@/components/shared/TemplatePreview';
import TemplatePreviewCarousel from '@/components/shared/TemplatePreviewCarousel';
import Link from 'next/link';
import { Audio } from './component/Audio';
import PDFIcon from '@/components/PDFIcon';
import { FileIcon, Share, SquareArrowOutUpRight, SquareArrowOutUpRightIcon } from 'lucide-react';
import { toast } from '@/components/ui/use-toast';

// biome-ignore lint/suspicious/noExplicitAny: <explanation>
declare let L: any;

export const MessageItem = ({ message }: { message: Partial<IExpandedMessage> }) => {
  const map = useRef(null);
  useEffect(() => {
    if (message.type === 'location' && typeof L !== 'undefined' && map.current === null) {
      map.current = L.map(`map_${message.id}`).setView([message.location?.latitude, message.location?.longitude], 13);
      L.tileLayer('https://tile.openstreetmap.org/{z}/{x}/{y}.png', {
        maxZoom: 19,
        attribution: '&copy; <a href="http://www.openstreetmap.org/copyright">OpenStreetMap</a>',
      }).addTo(map.current);
    }
    if (message.file && !message?.url) {
      getPb()
        .then((pb) => {
          message.url = pb.files.getURL(message, message.file ?? '');
        })
        .catch(console.error);
    }
  }, [message]);
  if (message.file) {
    if (message.type === 'image' && message.url) {
      return (
        <div>
          <a href={message.url} target="_blank" rel="noreferrer">
            <Image
              alt={message.file}
              src={message.url}
              width={400}
              height={200}
              className="mx-auto rounded-lg mb-2 w-full max-w-xs sm:max-w-md md:max-w-lg h-auto"
              style={{ objectFit: 'contain' }}
            />
          </a>
          <div>{message.message == 'image' ? '' : message.message}</div>
        </div>
      );
    }
    if (message.type === 'audio' && message.url) {
      // biome-ignore lint/a11y/useMediaCaption: <explanation>
      return <Audio src={message.url} />;
    }
    if (message.type === 'video' && message.url) {
      // biome-ignore lint/a11y/useMediaCaption: <explanation>
      return <video controls height={200} width={250} src={message.url} />;
    }
    if (message.type === 'document' && message.url) {
      const ext = message.file.split('.').pop();
      if (ext === 'pdf') {
        return (
          <div className="flex items-center space-x-2">
            {/* <object data={message.url} type="application/pdf" width="200" height="200"> */}
            {/* <embed src={message.url} type="application/pdf" /> */}
            {/* </object> */}
            <PDFIcon />
            <div className="truncate overflow-hidden w-40">{message.file}</div>

            {/* <FileIcon /> */}
            <Link className="text-sm underline" href={message.url} target="_blank" rel="noreferrer">
              <SquareArrowOutUpRightIcon className="h-4 w-4" />
            </Link>
          </div>
        );
      } else {
        return (
          <div>
            <h2>{message.file}</h2>
            <a className="text-sm underline" href={message.url} target="_blank" rel="noreferrer">
              Download File
            </a>
          </div>
        );
      }
    }
    if (message.type === 'sticker' && message.url) {
      return (
        <div>
          <a href={message.url} target="_blank" rel="noreferrer">
            <Image alt={message.file} src={message.url} width={200} height={200} className="mx-auto rounded-lg mb-2" />
          </a>
          <div>{message.message == 'image' ? '' : message.message}</div>
        </div>
      );
    }
  }
  if (message.type === 'location') {
    return (
      <div>
        <div style={{ height: 200, width: 250 }} id={`map_${message.id}`} />
        <p className="text-sm">{message.location?.name}</p>
        <p className="text-sm">{message.location?.address}</p>
        <a className="text-sm underline text-blue-600" href={message.location?.url} target="_blank" rel="noreferrer">
          Open Map
        </a>
      </div>
    );
  }

  if (message.expand?.interactive_message) {
    const IM = message.expand?.interactive_message.message;
    return (
      <div>
        <div className="space-y-1">
          {IM?.header?.type == 'text' && <div className="font-bold text-base">{IM.header.text}</div>}
          {IM.body && <div>{IM.body.text}</div>}
          {IM.footer && <div className="text-xs">{IM.footer.text}</div>}
          {IM.type === 'list' ? (
            <div>
              {IM.action?.sections?.[0]?.rows?.map((row) => (
                <div>
                  <Button key={row.title} className="w-full bg-white border border-gray-300 text-blue-500" type="button">
                    {row.title}
                  </Button>
                </div>
              ))}
            </div>
          ) : (
            <div>
              {IM.action?.buttons?.length < 3 ? (
                <div className="flex space-x-2">
                  {IM?.action?.buttons?.map((btn) => (
                    <Button key={btn.reply.title} className="w-full bg-white border border-gray-300 text-blue-500" type="button">
                      {btn.reply.title}
                    </Button>
                  ))}
                </div>
              ) : (
                <div className="flex flex-col space-y-1">
                  {IM?.action?.buttons?.map((btn) => (
                    <Button key={btn.reply.title} variant="outline" type="button">
                      {btn.reply.title}
                    </Button>
                  ))}
                </div>
              )}
            </div>
          )}
        </div>
      </div>
    );
  }
  if (message?.expand?.template) {
    let template = message.expand.template;
    let isDeleted = template.status == 'DELETED';

    //Added .length == 0 so that if type is not defined show basic-template
    if (template.type == 'basic-template' || template.type == 'utility-template' || template.type?.length == 0) {
      return (
        <div className="w-72">
          <TemplatePreview template={template} fromLiveChat={true} />
          {isDeleted && <div>This template has been deleted</div>}
        </div>
      );
    } else {
      return (
        <div className="flex-col space-y-2 w-72">
          <TemplatePreviewCarousel template={template} fromLiveChat={true} />

          {isDeleted && <div className="text-xs text-red-500 font-bold">This template has been deleted</div>}
        </div>
      );
    }
  }
  // Function to handle sharing message content
  const handleShare = async (content: string) => {
    if (navigator.share) {
      try {
        await navigator.share({
          text: content,
        });
        toast({
          description: 'Message shared successfully',
        });
      } catch (error) {
        // User cancelled or share failed
        console.error('Error sharing:', error);
      }
    } else {
      // Fallback for browsers that don't support Web Share API
      navigator.clipboard.writeText(content);
      toast({
        description: 'Message copied to clipboard',
      });
    }
  };

  // Function to render a message with share button
  const renderMessageWithShareButton = (content: string) => {
    return (
      <div className="group relative">
        <p dangerouslySetInnerHTML={{ __html: parseMessageContent(content) }}></p>
        <button
          onClick={() => handleShare(content)}
          className="absolute -right-8 top-0 opacity-0 group-hover:opacity-100 transition-opacity p-1 rounded-full hover:bg-gray-100"
          aria-label="Share message"
        >
          <Share className="h-4 w-4 text-gray-500" />
        </button>
      </div>
    );
  };

  if (message.message) {
    if (message.referral) {
      const referral = message.referral;
      return (
        <div>
          <div className="mb-1">
            <div className="mb-1">
              Lead came from{' '}
              <Link href={referral.source_url} target="_blank" className="text-blue-500 underline font-semibold">
                Meta Ad
              </Link>
              <div className="text-muted-foreground text-xs">{referral.source_url && referral.source_type != 'ad' && 'Lead clicked ad using a computer'} </div>
            </div>
            <div className="rounded-md bg-gray-200">
              {referral.media_type == 'image' ? (
                <Link href={referral?.image_url ?? ''} className="group" target="_blank">
                  {referral?.image_url ? (
                    <Image
                      src={referral?.image_url}
                      width={370}
                      height={370}
                      alt="Image. Image gets expired after 24 hours."
                      className="rounded-t-md w-full max-w-xs sm:max-w-md md:max-w-lg h-auto"
                      style={{ objectFit: 'contain' }}
                    />
                  ) : (
                    <div className="text-xs text-gray-400">Image link has been expired</div>
                  )}
                </Link>
              ) : (
                <Link href={referral?.source_url ?? ''} className="group" target="_blank">
                  {referral?.thumbnail_url ? (
                    <Image
                      src={referral?.thumbnail_url}
                      width={370}
                      height={370}
                      alt="Image. Image gets expired after 24 hours."
                      className="rounded-t-md w-full max-w-xs sm:max-w-md md:max-w-lg h-auto"
                      style={{ objectFit: 'contain' }}
                    />
                  ) : (
                    <div className="text-xs text-gray-400">Image link has been expired</div>
                  )}
                </Link>
              )}
              <div className="p-2 text-xs">
                <div className="font-semibold">{referral?.headline}</div>
                {referral.media_type == 'image' && <div>{referral?.body}</div>}
              </div>
            </div>
          </div>
          {renderMessageWithShareButton(message.message)}
        </div>
      );
    }
    return renderMessageWithShareButton(message.message);
  }
};
// message.expand.template.status == "PENDING DELETION"
