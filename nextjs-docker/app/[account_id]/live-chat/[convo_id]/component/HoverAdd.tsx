'use client';

import { db } from '@/app/db';
import { Button } from '@/components/ui/button';
import { useToast } from '@/components/ui/use-toast';
import { saveMessage } from '@/lib/actions';
import { IExpandedMessage } from '@/lib/types';
import { Loader2Icon, Plus } from 'lucide-react';
import { ReactElement, useState, useTransition } from 'react';

const HoverAdd = ({
  leadId,
  accountId,
  children,
  message,
}: {
  leadId: string | undefined;
  accountId: string | null;
  children: ReactElement;
  message: IExpandedMessage;
}) => {
  const [isPending, transition] = useTransition();
  const [showPlus, setShowPlus] = useState(false);
  const { toast } = useToast();
  return (
    <div className="flex gap-x-4 relative" onMouseOver={() => setShowPlus(true)} onFocus={() => setShowPlus(true)} onMouseLeave={() => setShowPlus(false)}>
      {children}
      <div className="absolute top-0 right-0">
        {isPending ? (
          <Button>
            <Loader2Icon className="animate-spin" />
          </Button>
        ) : showPlus && message?.from === 'agent' && !message?.template && !message.file && !message.expand?.interactive_message?.message ? (
          <Button
            size="sm"
            variant={'secondary'}
            onClick={() => {
              transition(async () => {
                if (leadId && accountId && message.id) {
                  const res = await saveMessage(accountId, leadId, message.id);
                  if (res) {
                    db.savedMessages.put(message);
                    toast({
                      variant: 'success',
                      description: 'Message Added successfully. Open Choose message to see your saved message.',
                    });
                  }
                }
              });
            }}
          >
            <Plus />
          </Button>
        ) : null}
      </div>
    </div>
  );
};

export default HoverAdd;
