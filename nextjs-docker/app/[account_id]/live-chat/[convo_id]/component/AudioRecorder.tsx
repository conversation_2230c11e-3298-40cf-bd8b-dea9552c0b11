'use client';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { CirclePause, Loader2, Mic, Refresh<PERSON>cw, SendHorizonal, Trash, UploadIcon } from 'lucide-react';
import { RefObject, useEffect, useRef, useState } from 'react';
import { FFmpeg } from '@ffmpeg/ffmpeg';
import { toBlobURL } from '@ffmpeg/util';
import { Progress } from '@/components/ui/progress';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/components/ui/alert-dialog';
import { useFormStatus } from 'react-dom';

const mimeType = 'audio/webm';
const AudioRecorder = ({
  audio,
  setRecording,
  setAudio,
  formRef,
  fromFlows = false,
}: {
  audio: Blob | null;
  setRecording: React.Dispatch<React.SetStateAction<boolean>>;
  setAudio: React.Dispatch<React.SetStateAction<Blob | null>>;
  formRef: RefObject<HTMLFormElement>;
  fromFlows?: boolean;
}) => {
  const [permission, setPermission] = useState(false);
  const [permissionDeniedAlert, setPermissionDeniedAlert] = useState(false);
  const mediaRecorder = useRef<MediaRecorder | null>(null);
  const [recordingStatus, setRecordingStatus] = useState('inactive');
  const [audioChunks, setAudioChunks] = useState<Blob[]>([]);
  const [recordingDuration, setRecordingDuration] = useState(0);
  const recordingInterval = useRef<ReturnType<typeof setInterval> | null>(null);
  const stream = useRef<MediaStream | null>(null);
  const [loaded, setLoaded] = useState(false);
  const ffmpegRef = useRef(new FFmpeg());
  const [isAlertOpen, setIsAlertOpen] = useState(false);
  const { pending } = useFormStatus();

  const startRecording = async () => {
    setAudio(null);
    setRecording(true);
    setPermission(true);
    setRecordingStatus('recording');
    if (stream.current) {
      const media = new MediaRecorder(stream.current, { mimeType });
      //set the MediaRecorder instance to the mediaRecorder? ref
      mediaRecorder.current = media;
      //invokes the start method to start the recording process
      media.start();
      let localAudioChunks: Blob[] = [];
      media.ondataavailable = (event) => {
        if (typeof event.data === 'undefined') return;
        if (event.data.size === 0) return;
        localAudioChunks.push(event.data);
      };
      setAudioChunks(localAudioChunks);
      setRecordingDuration(0);
      recordingInterval.current = setInterval(() => {
        setRecordingDuration((prev) => prev + 1);
      }, 1000);
    }
  };

  const load = async () => {
    const baseURL = 'https://unpkg.com/@ffmpeg/core@0.12.6/dist/umd';
    const ffmpeg = ffmpegRef.current;
    ffmpeg.on('log', ({ message }: { message: string }) => {});
    // toBlobURL is used to bypass CORS issue, urls with the same
    // domain can be used directly.
    await ffmpeg.load({
      coreURL: await toBlobURL(`${baseURL}/ffmpeg-core.js`, 'text/javascript'),
      wasmURL: await toBlobURL(`${baseURL}/ffmpeg-core.wasm`, 'application/wasm'),
    });
    setLoaded(true);
  };
  const stopRecording = () => {
    if (!mediaRecorder.current) {
      return;
    }

    //stops the recording instance
    mediaRecorder.current.stop();
    mediaRecorder.current.onstop = async () => {
      //creates a blob file from the audiochunks data
      const audioBlob = new Blob(audioChunks, { type: mimeType });
      //creates a playable URL from the blob file.
      const audioBuffer = await audioBlob.arrayBuffer();
      const uint8Array = new Uint8Array(audioBuffer);
      const ffmpeg = ffmpegRef.current;
      ffmpeg.writeFile('input.webm', uint8Array);
      await ffmpeg.exec(['-i', 'input.webm', '-c:a', 'libopus', 'output.ogg']);
      const data = await ffmpeg.readFile('output.ogg');
      const audioBlobOutput = new File([data], 'output.ogg', { type: 'audio/ogg' });
      setAudio(audioBlobOutput);
      setAudioChunks([]);
      if (recordingInterval.current) {
        clearInterval(recordingInterval.current);
      }
      setRecording(false);
      setRecordingStatus('inactive');
    };
  };

  const getMicrophonePermission = async () => {
    if ('MediaRecorder' in window) {
      try {
        const streamData = await navigator.mediaDevices.getUserMedia({
          audio: true,
          video: false,
        });
        stream.current = streamData;
        startRecording();
      } catch (error) {
        setPermission(false);
        if (error instanceof DOMException) {
          if (error.message == 'Permission denied') {
            setPermissionDeniedAlert(true);
          }
        }
      }
    } else {
      alert('The MediaRecorder API is not supported in your browser.');
    }
  };

  const cancelRecording = () => {
    setIsAlertOpen(true);
  };

  const confirmCancel = () => {
    setAudio(null);
    stopRecording();
    setPermission(false);

    stream.current?.getTracks().forEach((track) => {
      track.stop();
      track.enabled = false;
    });
    setIsAlertOpen(false);
  };
  useEffect(() => {
    load();
  }, []);
  return loaded ? (
    <div className="flex gap-4">
      {audio ? <audio style={{ height: 40 }} controls src={URL.createObjectURL(audio)} /> : null}
      <main>
        <div className="audio-controls">
          {/* if no permission then get permission */}
          {/* This function will always run the first time*/}
          {!permission &&
            (fromFlows ? (
              <Button
                onClick={getMicrophonePermission}
                variant="outline"
                size="icon"
                type="button"
                className="h-12 w-12 rounded-full bg-red-50 hover:bg-red-100 border-red-200"
              >
                <Mic className="h-5 w-5 text-red-500" />
              </Button>
            ) : (
              <Button className="mb-0" variant={'outline'} onClick={getMicrophonePermission} type="button">
                <Mic />
              </Button>
            ))}
          <div className="flex space-x-2">
            {/* to rerecord */}
            {permission && recordingStatus === 'inactive' ? (
              <Button className="mb-0" variant={'outline'} onClick={startRecording} type="button">
                {audio ? <RefreshCcw className="h-5 w-5" /> : <Mic />}
              </Button>
            ) : null}
            {audio && (
              <div className="flex items-center space-x-2">
                {/* to delete recording */}
                <Button className="mb-0" variant={'outline'} onClick={cancelRecording} type="button">
                  <Trash className="h-5 w-5" />
                </Button>
                {fromFlows ? (
                  <>
                    {pending ? (
                      <Button className="bg-gradient-to-r from-emerald-500 to-emerald-500 hover:bg-gradient-to-l" disabled>
                        <Loader2 className="h-4 w-4 animate-spin mr-2" />
                        Uploading
                      </Button>
                    ) : (
                      <Button
                        className="bg-gradient-to-r from-emerald-500 to-emerald-600 hover:bg-gradient-to-l transition-all"
                        onClick={() => {
                          confirmCancel();
                          formRef?.current?.requestSubmit();
                          setTimeout(() => {
                            setAudio(null);
                            stopRecording();
                            setPermission(false);
                          }, 2000);
                        }}
                      >
                        Upload
                        <UploadIcon className="ml-2 h-4 w-4" />
                      </Button>
                    )}
                  </>
                ) : (
                  <Button
                    className="border-green-700"
                    variant={'outline'}
                    onClick={() => {
                      confirmCancel();
                      formRef?.current?.requestSubmit();
                    }}
                  >
                    Send
                    <SendHorizonal className="ml-2 h-4 w-4 text-green-700" />
                  </Button>
                )}
              </div>
            )}
          </div>
          {recordingStatus === 'recording' ? (
            <div className="w-full flex items-center space-x-2">
              <div>
                <Button className="mb-0" variant={'outline'} onClick={stopRecording} type="button">
                  <CirclePause />
                </Button>
              </div>
              <div>
                <Progress value={(recordingDuration / 60) * 100} className="w-full" />
                <p className="text-center mt-2 text-sm text-muted-foreground">Recording: {recordingDuration} seconds</p>
              </div>
            </div>
          ) : null}
        </div>
      </main>
      <AlertDialog open={isAlertOpen} onOpenChange={setIsAlertOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Are you sure you want to cancel?</AlertDialogTitle>
            <AlertDialogDescription>This action will discard your current recording. You can't undo this action.</AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>No, keep it</AlertDialogCancel>
            <AlertDialogAction onClick={confirmCancel}>Yes, discard</AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>

      <AlertDialog open={permissionDeniedAlert} onOpenChange={setPermissionDeniedAlert}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Allow Microphone</AlertDialogTitle>
            <AlertDialogDescription>
              To record Voice Messages, WhatsApp needs access to your microphone. Click allow in the URL bar and choose{' '}
              <span className="font-bold">Always allow app.wetarseel.ai </span> to access your microphone
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Ok, I got it</AlertDialogCancel>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  ) : fromFlows ? (
    <Button variant="outline" size="icon" className="h-12 w-12 rounded-full bg-red-50 hover:bg-red-100 border-red-200" disabled type="button">
      <Mic className="h-5 w-5 text-red-500" />
    </Button>
  ) : (
    <Button className="mb-0" variant={'outline'} disabled type="button">
      <Mic />
    </Button>
  );
};
export default AudioRecorder;
