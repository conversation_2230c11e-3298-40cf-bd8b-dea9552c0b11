'use client';
import { db } from '@/app/db';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Account, IExpandedConversationsWithMessages, ILead, IMessage, IMessagingLimit, ITemplateDatabase, User } from '@/lib/types';
import { getConversationExpiry } from '@/lib/utils';
import clsx from 'clsx';
import { Paperclip, SendHorizonal, SquareX } from 'lucide-react';
import { useCallback, useRef, useState } from 'react';
import AgentInputBox from '../../components/AgentInputBox';
import AudioRecorder from './AudioRecorder';
import ChooseMessageDialog from './ChooseMessageDialog';
import ImageGalleryDialog from '@/app/[account_id]/send-message/[id]/components/ImageGalleryDialog';
import { useToast } from '@/components/ui/use-toast';
import { DropdownMenuContent, DropdownMenuTrigger } from '@radix-ui/react-dropdown-menu';
import InputActionsDropDown from './InputActionsDropDown';
import { useLiveQuery } from 'dexie-react-hooks';

const OmniBox = ({
  loadMessagesOfConvo,
  account,
  currentUser,
  conversation,
  handleSubmitWithData,
  templates,
  messagingLimit,
  createdAt,
  savedMessages,
  convo_id,
}: {
  loadMessagesOfConvo: () => void;
  account: Account | undefined;
  currentUser: User;
  conversation: IExpandedConversationsWithMessages;
  handleSubmitWithData: any;
  templates: ITemplateDatabase[];
  messagingLimit: IMessagingLimit | null;
  createdAt: Date | undefined;
  savedMessages: IMessage[] | undefined;
  convo_id: string;
}) => {
  const { toast } = useToast();
  const [audio, setAudio] = useState<Blob | null>(null);
  const [recording, setRecording] = useState<boolean>(false);
  const [fileUpload, setFileUpload] = useState<boolean>(false);
  const [submitLoading, setSubmitLoading] = useState<boolean>(false);
  const [setTemplate, handleSetTemplate] = useState(null);
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const ref = useRef<HTMLFormElement>(null);
  const lead = conversation?.expand?.from;
  const isWithin24Hour = createdAt ? getConversationExpiry(createdAt).isWithin24Hour : false;
  const assignedAgent = conversation?.expand?.assigned_agent;
  const unAssigned = !conversation?.expand?.assigned_agent?.id;
  //disabled will be false if
  //user is admin
  //user has super access
  //user is same as assigned
  //disabled will be true if
  //user is null
  //user is unassigned
  const agentRoles =
    currentUser.expand?.roles?.map((role) => {
      return role.name;
    }) ?? [];
  let disabled = true;

  if (currentUser) {
    if (currentUser.type === 'admin' || agentRoles.includes('Live Chat-Super Access')) {
      disabled = false;
    } else if (!unAssigned) {
      if (assignedAgent?.id === currentUser.id) {
        disabled = false;
      } else {
        disabled = true;
      }
    } else {
      disabled = false;
    }
  }

  const handleSetImage = useCallback(
    (url: string, filename: string, file: any) => {
      const formData = new FormData();
      formData.append('message', '');
      // get the file extension and select the correct mime type
      formData.append('picture', url);
      formData.append('filename', filename);
      submitData(formData);
    },
    [lead?.id]
  );

  const submitData = async (formData: FormData) => {
    setFileUpload(false);
    setSelectedFile(null);
    if (audio) {
      const newAudio = new File([audio], 'test.aac', { type: 'audio/aac' });
      formData.append('audio', newAudio);
    }
    const picture = formData.get('picture') as string;

    const id = `${Math.ceil(Math.random() * 100)}`;
    localStorage.setItem('latestMessage', id);
    const tempMessage = {
      id,
      from: 'agent',
      convo_id: convo_id,
      created: new Date(),
      message: formData.get('message') as string,
      delivery_status: 'pending',
      expand: {
        created_by: {
          ...currentUser,
        },
        user: {
          ...lead,
        },
      },
      created_by: currentUser?.id,
      user: lead.id,
    } as any;
    if (picture) {
      let file = await (await fetch(picture as unknown as string)).blob();
      let type = file.type.split('/')[0] as string;
      if (type === 'application') {
        type = 'document';
      }
      // if (picture.size > 5242880) {
      //   toast({
      //     variant: 'destructive',
      //     description: 'File size should be less than 5MB',
      //   });
      //   return;
      // }
      // const fileUrl = URL.createObjectURL(picture);
      // tempMessage.file = picture;
      tempMessage.file = picture;
      tempMessage.url = picture;
      tempMessage.type = type;
    }

    await db.messages.put(tempMessage);
    setTimeout(() => {
      const item = document.getElementById('chat-window2');
      if (item) {
        item.scrollTo(0, item.scrollHeight);
      }
    }, 60);
    setSubmitLoading(false);
    try {
      const actualMessage = await handleSubmitWithData(formData);
      db.messages.delete(id);
      actualMessage.finalMessage.created = new Date(actualMessage.finalMessage.created);
      db.messages.put(actualMessage.finalMessage);
    } catch (error) {
      db.messages.delete(id);
      toast({
        variant: 'destructive',
        description: `${error}`,
      });
    }

    setAudio(null);
    setFileUpload(false);
    setRecording(false);
  };

  return (
    <form
      ref={ref}
      action={async (formData) => {
        await submitData(formData);
      }}
      className="flex 2xl:flex-nowrap flex-wrap md:scale-100 origin-left w-[133%] scale-75 items-end flex-col md:flex-row gap-3 md:p-4 px-1 py-1 text-sm border-t md:w-full relative border-none"
    >
      {/* //TODO: disucssion */}
      {/* {submitLoading && (
        <div className="absolute -top-8 flex items-center space-x-1 text-xs right-10">
          <Loader2 className="animate-spin" /> <span>Sending Message</span>
        </div>
      )} */}
      <div className="w-full flex items-end space-x-2">
        {!fileUpload && !recording && !audio && (
          <AgentInputBox
            currentUser={currentUser}
            isWithin24Hour={isWithin24Hour}
            unAssigned={unAssigned}
            disabled={disabled}
            formRef={ref}
            setSubmitLoading={setSubmitLoading}
            convo_id={convo_id}
            isBlocked={lead.blocked ?? false}
          />
        )}
        {fileUpload && !recording && !audio && (
          <div className="flex flex-grow w-full gap-4">
            <Input
              className="w-full"
              multiple={false}
              name="picture"
              type="file"
              onChange={(event: React.FormEvent) => {
                const files = (event.target as HTMLInputElement).files;

                if (files && files.length > 0) {
                  setSelectedFile(files[0]);
                }
              }}
            />
            {selectedFile && <Input type="text" name="message" placeholder="Optional Caption" />}
            <Button variant={'outline'} className="mb-0" type="button">
              <SquareX
                onClick={() => {
                  setFileUpload(false);
                  setSelectedFile(null);
                }}
              />
            </Button>
            {selectedFile && (
              <Button type="submit" variant={'outline'}>
                <SendHorizonal className="ml-2 h-4 w-4 text-green-700" />
              </Button>
            )}
          </div>
        )}
        {!fileUpload && !recording && !audio && (
          <ChooseMessageDialog
            templates={templates}
            loadMessagesOfConvo={loadMessagesOfConvo}
            savedMessages={savedMessages}
            handleSetTemplate={handleSetTemplate}
            lead={lead}
            currentUser={currentUser}
            handleSubmitWithData={handleSubmitWithData}
            isWithin24Hour={isWithin24Hour}
            unAssigned={unAssigned}
            disabled={disabled}
            messagingLimit={messagingLimit}
          />
        )}
        {/* { (fileUpload && account?.id) ? <ImageGalleryDialog handleSetImage={handleSetImage} accountId={account.id} /> : null } */}
        {!disabled && isWithin24Hour && !fileUpload && !recording && !audio && (
          <InputActionsDropDown handleSetImage={handleSetImage} accountId={account?.id ?? ''} />
        )}
        <div className={clsx(!disabled && isWithin24Hour && !fileUpload ? 'block' : 'hidden')}>
          <AudioRecorder setRecording={setRecording} audio={audio} setAudio={setAudio} formRef={ref} />
        </div>
      </div>
    </form>
  );
};

export default OmniBox;
