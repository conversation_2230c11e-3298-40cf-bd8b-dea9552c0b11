'use client';

import { Document } from '@/components/Document';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/components/ui/alert-dialog';
import { Button, buttonVariants } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { ScrollArea } from '@/components/ui/scroll-area';
import { useToast } from '@/components/ui/use-toast';
import { Video } from '@/components/Video';
import { uploadFileFromLiveChat } from '@/lib/pocket';
import { IImage, IImageWithUrl } from '@/lib/types';
import clsx from 'clsx';
import dayjs from 'dayjs';
import { Check, Loader2, UploadIcon } from 'lucide-react';
import Image from 'next/image';
import { memo, useEffect, useRef, useState } from 'react';
import { useFormState, useFormStatus } from 'react-dom';
import AudioRecorder from './AudioRecorder';
import { FFmpeg } from '@ffmpeg/ffmpeg';
const FileGalleryDialog = ({
  accountId,
  handleSetImage,
  dialogType,
  open,
  setIsOpen,
  files,
  filesLoading,
  setFiles,
  businessImage,
}: {
  accountId: string;
  handleSetImage: any;
  dialogType: string;
  open: boolean;
  setIsOpen: any;
  files: IImage[];
  filesLoading: boolean;
  setFiles: React.Dispatch<React.SetStateAction<IImage[]>>;
  businessImage?: string | null;
}) => {
  const [selectedFile, setSelectedFile] = useState<IImageWithUrl>();
  const [audioCodecError, setAudioCodecError] = useState(false);
  const { toast } = useToast();
  const initialState = {
    message: {
      status: 0,
      description: '',
      data: {} as any,
    },
  };
  const ref = useRef<HTMLFormElement>(null);
  const [audio, setAudio] = useState<Blob | null>(null);
  const [recording, setRecording] = useState<boolean>(false);

  const [state, formAction] = useFormState(uploadFileFromLiveChat, initialState);
  const [isFileSelected, setIsFileSelected] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const detectAudioCodec = async (file: File) => {
    const buffer = await file.slice(0, 64).arrayBuffer();
    const bytes = new Uint8Array(buffer);
    const headerStr = new TextDecoder().decode(bytes);

    if (headerStr.includes('OpusHead')) return 'opus';
    if (headerStr.includes('vorbis')) return 'vorbis';

    return 'unknown';
  };

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    let file = e.target.files?.[0];

    if (file) {
      // Check if file size is greater than 10MB (10 * 1024 * 1024 bytes)
      if (file.size > 5 * 1024 * 1024) {
        toast({
          variant: 'destructive',
          description: 'File size must be less than 5MB',
        });
        // Reset file input
        if (fileInputRef.current) {
          fileInputRef.current.value = '';
        }
        setIsFileSelected(false);
        return;
      }

      setIsFileSelected(true);
    } else {
      setIsFileSelected(false);
    }
  };

  useEffect(() => {
    if (state?.message.status == 400) {
      toast({
        variant: 'destructive',
        description: state.message.description,
      });
    } else if (state?.message.status == 200) {
      toast({
        variant: 'success',
        description: state.message.description,
      });
      setFiles((prev) => [state.message.data, ...prev]);
      // Clear the file input after successful upload
      if (fileInputRef.current) {
        fileInputRef.current.value = '';
        setIsFileSelected(false);
      }
    }
  }, [state]);

  const dialogTitle = () => {
    if (dialogType == 'images') {
      return 'Upload Image';
    }
    if (dialogType == 'videos') {
      return 'Upload Video';
    }
    if (dialogType == 'documents') {
      return 'Upload Document';
    }
    if (dialogType == 'audios') {
      return 'Upload Audio';
    }
    return 'Upload File';
  };

  const FileItems = () => {
    if (dialogType == 'images') {
      return files.map((_file, index) => (
        <div
          className={clsx('border rounded-md hover:border-gray-400', selectedFile?.url == _file.url ? 'border-blue-500' : 'border-gray-200')}
          key={_file.id}
          onClick={() => setSelectedFile(_file)}
        >
          <div className="p-2  relative hover:cursor-pointer h-32">
            <div
              className={clsx(
                'absolute z-20 h-5 w-5 border flex items-center justify-center rounded-md bg-white',
                selectedFile?.url == _file.url ? 'border-gray-100 text-blue-600' : 'border-gray-200 text-black'
              )}
            >
              {selectedFile?.url == _file.url && <Check />}
            </div>
            <Image src={_file.url} fill alt="image" className="rounded-md object-contain" sizes="(max-width: 600px) 100vw, 50vw" />
          </div>
          <div className="text-xs text-gray-500 border-t p-1">
            <div className="mb-1">Created by: {_file.expand?.created_by?.name ?? 'System'}</div>
            <div>{dayjs(_file.created).format('MMMM D, YYYY h:mm A')}</div>
          </div>
        </div>
      ));
    }
    if (dialogType == 'videos') {
      return files.map((_file, index) => (
        <div
          className={clsx('border rounded-md hover:border-gray-400', selectedFile?.url == _file.url ? 'border-blue-500' : 'border-gray-200')}
          key={_file.id}
          onClick={() => setSelectedFile(_file)}
        >
          <div className="p-2  relative hover:cursor-pointer aspect-video w-full ">
            <div
              className={clsx(
                'absolute z-20 h-5 w-5 border flex items-center justify-center rounded-md bg-white',
                selectedFile?.url == _file.url ? 'border-gray-100 text-blue-600' : 'border-gray-200 text-black'
              )}
            >
              {selectedFile?.url == _file.url && <Check />}
            </div>
            <Video file={null} src={_file.url} className="rounded-md object-contain" />
          </div>
          <div className="text-xs text-gray-500 border-t p-1">
            <div className="mb-1">Created by: {_file.expand?.created_by?.name ?? 'System'}</div>
            <div>{dayjs(_file.created).format('MMMM D, YYYY h:mm A')}</div>
          </div>
        </div>
      ));
    }
    if (dialogType == 'documents' || dialogType == 'audios') {
      return files.map((_file, index) => (
        <div
          className={clsx('border rounded-md hover:border-gray-400', selectedFile?.url == _file.url ? 'border-blue-500' : 'border-gray-200')}
          key={_file.id}
          onClick={async () => {
            setSelectedFile(_file);
            if (dialogType == 'audios') {
              const response = await fetch(_file.url);
              const blob = await response.blob();
              const file = new File([blob], _file.name, { type: blob.type });
              const codec = await detectAudioCodec(file);
              if (codec == 'opus') {
                setAudioCodecError(false);
              } else {
                setAudioCodecError(true);
              }
            } else {
              setAudioCodecError(false);
            }
          }}
        >
          <div className="p-2  relative hover:cursor-pointer aspect-document w-full">
            <div
              className={clsx(
                'absolute z-20 h-5 w-5 border flex items-center justify-center rounded-md bg-white',
                selectedFile?.url == _file.url ? 'border-gray-100 text-blue-600' : 'border-gray-200 text-black'
              )}
            >
              {selectedFile?.url == _file.url && <Check />}
            </div>
            <Document fileName={_file.file as string} url={_file.url} className="rounded-md object-contain" />
          </div>
          <div className="text-xs text-gray-500 border-t p-1">
            <div className="mb-1">Created by: {_file.expand?.created_by?.name ?? 'System'}</div>
            <div>{dayjs(_file.created).format('MMMM D, YYYY h:mm A')}</div>
          </div>
        </div>
      ));
    }
    // if (dialogType == 'audios') {
    //   return files.map((_file, index) => (
    //     <div
    //       className={clsx('border rounded-md hover:border-gray-400', selectedFile?.url == _file.url ? 'border-blue-500' : 'border-gray-200')}
    //       key={_file.id}
    //       onClick={() => setSelectedFile(_file)}
    //     >
    //       <div className="p-2  relative hover:cursor-pointer aspect-document w-full">
    //         <div
    //           className={clsx(
    //             'absolute z-20 h-5 w-5 border flex items-center justify-center rounded-md bg-white',
    //             selectedFile?.url == _file.url ? 'border-gray-100 text-blue-600' : 'border-gray-200 text-black'
    //           )}
    //         >
    //           {selectedFile?.url == _file.url && <Check />}
    //         </div>
    //         {/* <Audio fileName={_file.file as string} url={_file.url} className="rounded-md object-contain" /> */}
    //         <div className="mt-4">{_file.name}</div>
    //         <Audio src={_file.url} />
    //       </div>
    //       <div className="text-xs text-gray-500 border-t p-1">
    //         <div className="mb-1">Created by: {_file.expand?.created_by?.name ?? 'System'}</div>
    //         <div>{dayjs(_file.created).format('MMMM D, YYYY h:mm A')}</div>
    //       </div>
    //     </div>
    //   ));
    // }
  };
  let collectionName = 'images';
  if (dialogType == 'videos') {
    collectionName = 'videos';
  }
  if (dialogType == 'documents') {
    collectionName = 'documents';
  }
  if (dialogType == 'audios') {
    collectionName = 'audios';
  }
  return (
    <AlertDialog open={open} onOpenChange={setIsOpen}>
      <AlertDialogContent className="max-w-full md:w-9/12 h-[90vh] flex flex-col">
        <AlertDialogHeader>
          <AlertDialogTitle>{dialogTitle()}</AlertDialogTitle>
          <AlertDialogDescription></AlertDialogDescription>
        </AlertDialogHeader>
        <ScrollArea className="flex-grow">
          {filesLoading ? (
            <div className="flex items-center justify-center">
              <Loader2 className="animate-spin h-10 w-10" />
            </div>
          ) : (
            <div>
              <div className="max-w-2xl px-4 border-2 border-dashed rounded-lg p-4 mx-auto mb-2">
                {!recording && !audio && (
                  <form
                    action={async (formData) => {
                      try {
                        const audioFile = formData.get('file') as File;
                        //if it is an audio
                        if (audioFile && audioFile.type.includes('audio')) {
                          // Convert the file using Bun backend, send as FormData. Downsize to 64k bitrate
                          const data = await fetch(`${process.env.NEXT_PUBLIC_BUN_SERVER_URL}/convert-codec`, {
                            method: 'POST',
                            body: formData,
                          });
                          const blob = await data.blob();
                          // Replace the old file with the new one
                          formData.set('file', new File([blob], `${dayjs().format('YYYY-MM-DD_HH-mm-ss')}_wetarseel.ogg`, { type: 'audio/ogg' }));
                        }
                        formAction(formData);
                      } catch (error) {
                        console.log(error);
                        // formAction(formData);
                      }
                    }}
                  >
                    <h2 className="text-2xl font-bold mb-6 text-center">Upload File</h2>
                    <input type="text" hidden defaultValue={accountId} name="accountId" />
                    <input type="text" hidden readOnly value={collectionName} name="collectionName" />
                    <div className="flex items-center gap-2">
                      <Input
                        name="file"
                        className="block w-full text-sm text-gray-900 border border-gray-300 rounded-lg cursor-pointer bg-gray-50 dark:text-gray-400 focus:outline-none dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400"
                        aria-describedby="user_avatar_help"
                        id="user_avatar"
                        type="file"
                        accept={
                          dialogType == 'images'
                            ? 'image/png, image/jpeg'
                            : dialogType == 'videos'
                              ? 'video/mp4'
                              : dialogType == 'audios'
                                ? 'audio/*'
                                : 'application/pdf'
                        }
                        onChange={handleFileChange}
                        ref={fileInputRef}
                      />
                      <UploadImageBtn disabled={!isFileSelected} />
                    </div>
                  </form>
                )}
                {/* <div className="my-2 text-center">OR</div> */}
                {dialogType == 'audios' && (
                  <div>
                    <div className="my-4">
                      <h2 className="text-base text-center mb-2">Record Audio</h2>
                      <form
                        action={async (formData) => {
                          if (audio) {
                            const newAudio = new File([audio], `${dayjs().format('YYYY-MM-DD_HH-mm-ss')}_wetarseel.ogg`, { type: 'audio/ogg' });
                            formData.append('file', newAudio);
                          }
                          await formAction(formData);
                        }}
                        ref={ref}
                      >
                        <div className="flex items-center gap-4 justify-center">
                          <AudioRecorder setRecording={setRecording} audio={audio} setAudio={setAudio} formRef={ref} fromFlows={true} />
                          {!recording && !audio && <p className="text-sm text-muted-foreground text-center">Press the record button to start recording</p>}
                        </div>

                        <input type="text" hidden defaultValue={accountId} name="accountId" />
                        <input type="text" hidden readOnly value={collectionName} name="collectionName" />
                      </form>
                    </div>
                  </div>
                )}
              </div>
              <div className="relative">
                <div className="absolute inset-0 flex items-center">
                  <span className="w-full border-t" />
                </div>
                <div className="relative flex justify-center text-xs uppercase">
                  <span className="bg-white px-2 text-muted-foreground">Or choose from gallery</span>
                </div>
              </div>
              {audioCodecError && (
                <div className="text-sm text-orange-500 text-center">
                  <p>Warning. Audio codec not supported. Please upload a file with Opus codec.</p>
                  <p>Your audio preview will not contain business logo</p>
                </div>
              )}
              <div className="grid grid-cols-2 md:grid-cols-5 gap-4 p-4">{files.length > 0 ? <FileItems /> : <div>No Files</div>}</div>
            </div>
          )}
        </ScrollArea>
        <AlertDialogFooter>
          <AlertDialogCancel>Cancel</AlertDialogCancel>
          <AlertDialogAction
            disabled={!selectedFile}
            onClick={() => handleSetImage(selectedFile?.url ?? '', selectedFile?.name ?? 'untitled', dialogType)}
            className={buttonVariants({ variant: 'secondary' })}
          >
            Continue
          </AlertDialogAction>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  );
};

const UploadImageBtn = ({ disabled }: { disabled?: boolean }) => {
  const { pending } = useFormStatus();
  return (
    <>
      {pending ? (
        <Button className="bg-gradient-to-r from-emerald-500 to-emerald-500 hover:bg-gradient-to-l" disabled>
          <Loader2 className="h-4 w-4 animate-spin mr-2" />
          Uploading
        </Button>
      ) : (
        <Button className="bg-gradient-to-r from-emerald-500 to-emerald-600 hover:bg-gradient-to-l transition-all" type="submit" disabled={disabled}>
          Upload
          <UploadIcon className="ml-2 h-4 w-4" />
        </Button>
      )}
    </>
  );
};
export default memo(FileGalleryDialog);
