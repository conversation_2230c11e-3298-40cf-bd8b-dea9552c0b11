'use client';

import VariableParams from '@/app/[account_id]/send-message/[id]/components/VariableParams';
import VariableParamsCarousel from '@/app/[account_id]/send-message/[id]/components/VariableParamsCarousel';
import { db } from '@/app/db';
import TemplatePreview from '@/components/shared/TemplatePreview';
import TemplatePreviewCarousel from '@/components/shared/TemplatePreviewCarousel';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from '@/components/ui/alert-dialog';
import { Button } from '@/components/ui/button';
import { Checkbox } from '@/components/ui/checkbox';
import { Dialog, DialogClose, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
import { useToast } from '@/components/ui/use-toast';
import { ILead, IMessage, IMessagingLimit, ITemplateDatabase, User } from '@/lib/types';
import { FilesIcon, InfoIcon, Loader2, SearchIcon } from 'lucide-react';
import { useParams } from 'next/navigation';
import { useRef, useState } from 'react';
import Savedmessages from './Savedmessages';
import { Toaster } from '@/components/ui/toaster';
import { MobileOnly } from './MobileOnly';
import { DesktopOnly } from './DesktopOnly';

const ChooseMessageDialog = ({
  loadMessagesOfConvo,
  templates,
  savedMessages,
  handleSetTemplate,
  lead,
  currentUser,
  handleSubmitWithData,
  isWithin24Hour,
  unAssigned,
  disabled,
  messagingLimit,
}: {
  loadMessagesOfConvo: () => void;
  templates: ITemplateDatabase[];
  savedMessages: IMessage[] | undefined;
  handleSetTemplate: any;
  lead: Partial<ILead>;
  currentUser: User;
  handleSubmitWithData: any;
  disabled: boolean;
  isWithin24Hour: boolean;
  unAssigned: boolean;
  messagingLimit: IMessagingLimit | null;
}) => {
  const [selectedTemplate, setSelectedTemplate] = useState<ITemplateDatabase | null>(null);
  const [selectedMessage, setSelectedMessage] = useState<IMessage | null>(null);
  const [open, setOpen] = useState(false);
  const [showPreviewDialog, setShowPreviewDialog] = useState(false);
  const [value, setValue] = useState('');
  const [filteredTemplates, setFilteredTemplates] = useState(templates);
  const dialogContentRef = useRef<HTMLDivElement>(null);
  const [varTemplateDialog, setVarTemplateDialog] = useState(false);
  const [templateDialog, setTemplateDialog] = useState(false);
  const [loading, setLoading] = useState(false);
  const { toast } = useToast();
  const params = useParams();
  const selectedRows = [lead] as ILead[];
  const account_id = params.account_id as string;
  const handleTemplateClick = async (template: ITemplateDatabase) => {
    setLoading(true);
    setSelectedMessage(null);
    setVarTemplateDialog(true);
    // try {
    //   await sendTemplateFromLiveChat(lead, template, account_id, currentUser?.id);
    //   toast({
    //     variant: 'success',
    //     description: 'Template sent successfuly ',
    //   });
    // } catch (error) {

    //   toast({
    //     variant: 'destructive',
    //     description: 'Unable to send template',
    //   });
    // }
    setLoading(false);
  };

  const handleMessageClick = async (message: IMessage) => {
    setLoading(true);
    setSelectedTemplate(null);
    setSelectedMessage(null);

    const formData = new FormData();
    formData.append('message', message.message);
    db.messages.put({
      id: `${Math.ceil(Math.random() * 100)}`,
      from: 'agent',
      message: selectedMessage?.message,
      delivery_status: 'delivered',
      created_by: currentUser?.id,
    } as any);

    try {
      await handleSubmitWithData(formData);
      toast({
        variant: 'success',
        description: 'Message sent successfuly ',
      });
      setTimeout(() => {
        const item = document.getElementById('chat-window2');
        if (item) {
          item.scrollTo(0, item.scrollHeight);
        }
      }, 60);
    } catch (error) {
      console.log(error);
      toast({
        variant: 'destructive',
        description: 'Unable to message',
      });
    }

    setLoading(false);
    setTemplateDialog(false);
  };

  const handleCloseAllDialogs = () => {
    setVarTemplateDialog(false);
    setTemplateDialog(false);
  };
  const option = ['name', 'phone_number'];
  return (
    <div className="block">
      <AlertDialog open={templateDialog} onOpenChange={setTemplateDialog}>
        <AlertDialogTrigger asChild>
          <Button variant={'secondary'} disabled={false}>
            <FilesIcon className="h-4 w-4" />
          </Button>
        </AlertDialogTrigger>
        <AlertDialogContent className="max-w-full md:w-9/12 h-[90vh] flex flex-col" ref={dialogContentRef}>
          <AlertDialogHeader>
            <AlertDialogTitle>Choose Message</AlertDialogTitle>
            <AlertDialogDescription></AlertDialogDescription>
          </AlertDialogHeader>
          <ScrollArea className="flex-grow">
            <Tabs defaultValue="Templates" className="">
              <TabsList className="grid h-full w-full md:grid-cols-2 grid-cols-1">
                <TabsTrigger value="Templates">Templates</TabsTrigger>
                <TabsTrigger value="Saved Messages">Saved Messages</TabsTrigger>
              </TabsList>
              <TabsContent value="Templates">
                <div className="w-full">
                  {(!messagingLimit || messagingLimit.remaining_limit < 0) && (
                    <Alert variant={'destructive'} className="mb-4">
                      <InfoIcon className="h-4 w-4" />
                      <AlertTitle>You cannot send a template</AlertTitle>
                      <AlertDescription>
                        <div>{'You do not have enough quota to send a template'}</div>
                      </AlertDescription>
                    </Alert>
                  )}
                  <div className="flex w-72 items-center space-x-4">
                    <SearchIcon />
                    <Input
                      type="text"
                      placeholder="Search Templates"
                      onChange={(e) => setFilteredTemplates(templates.filter((template) => template.template_name.includes(e.target.value)))}
                    />
                  </div>
                  <DesktopOnly>
                    <Table>
                      <TableHeader>
                        <TableRow>
                          <TableHead className="w-[10px]"></TableHead>
                          <TableHead className="w-[400px]">Name</TableHead>
                          <TableHead>Type</TableHead>
                          <TableHead>Category</TableHead>
                        </TableRow>
                      </TableHeader>
                      <TableBody>
                        {filteredTemplates.map((template) => (
                          <TableRow key={template.id} onClick={() => setSelectedTemplate(template)} className="hover:cursor-pointer">
                            <TableCell className="font-medium">
                              <Checkbox id={template?.id} checked={template.id == selectedTemplate?.id} />
                            </TableCell>
                            <TableCell className="">
                              <TooltipProvider>
                                <Tooltip delayDuration={0}>
                                  <TooltipTrigger asChild>
                                    <div className="font-medium truncate">{template.template_name}</div>
                                  </TooltipTrigger>
                                  <TooltipContent>
                                    <p>{template.template_name}</p>
                                  </TooltipContent>
                                </Tooltip>
                              </TooltipProvider>
                            </TableCell>
                            <TableCell>
                              {template.type
                                ?.split('-')
                                .map((word) => word.charAt(0).toUpperCase() + word.slice(1)) // Capitalize the first letter
                                .join(' ')}
                            </TableCell>
                            <TableCell>{template.category}</TableCell>
                            <TableCell>
                              <Button variant={'outline'} size={'xl'} onClick={() => setShowPreviewDialog(!showPreviewDialog)}>
                                Preview
                              </Button>
                            </TableCell>
                          </TableRow>
                        ))}
                      </TableBody>
                    </Table>
                  </DesktopOnly>
                  <MobileOnly>
                    <Table>
                      <TableHeader>
                        <TableRow>
                          <TableHead></TableHead>
                          <TableHead>Name</TableHead>
                          <TableHead>Type</TableHead>
                        </TableRow>
                      </TableHeader>
                      <TableBody>
                        {filteredTemplates.map((template) => (
                          <TableRow key={template.id} onClick={() => setSelectedTemplate(template)} className="hover:cursor-pointer">
                            <TableCell className="font-medium">
                              <Checkbox id={template?.id} checked={template.id == selectedTemplate?.id} />
                            </TableCell>
                            <TableCell className="">
                              <TooltipProvider>
                                <Tooltip delayDuration={0}>
                                  <TooltipTrigger asChild>
                                    <div className="font-medium truncate">{template.template_name}</div>
                                  </TooltipTrigger>
                                  <TooltipContent>
                                    <p>{template.template_name}</p>
                                  </TooltipContent>
                                </Tooltip>
                              </TooltipProvider>
                            </TableCell>
                            <TableCell>
                              {template.type
                                ?.split('-')
                                .map((word) => word.charAt(0).toUpperCase() + word.slice(1)) // Capitalize the first letter
                                .join(' ')}
                            </TableCell>
                            <TableCell>
                              <Button variant={'outline'} size={'xl'} onClick={() => setShowPreviewDialog(!showPreviewDialog)}>
                                Preview
                              </Button>
                            </TableCell>
                          </TableRow>
                        ))}
                      </TableBody>
                    </Table>
                  </MobileOnly>
                </div>
                {selectedTemplate && (
                  <Dialog open={showPreviewDialog} onOpenChange={setShowPreviewDialog}>
                    <DialogContent className="flex flex-col justify-center items-center max-w-lg max-h-[90vh]">
                      <DialogHeader className="self-start">
                        <DialogTitle>Template Preview</DialogTitle>
                        <DialogDescription></DialogDescription>
                      </DialogHeader>
                      <ScrollArea className="flex-grow overflow-auto">
                        {selectedTemplate.type == 'basic-template' && <TemplatePreview template={selectedTemplate} />}
                        {selectedTemplate.type == 'utility-template' && <TemplatePreview template={selectedTemplate} />}
                        {selectedTemplate.type == 'carousel-template' && <TemplatePreviewCarousel template={selectedTemplate} />}
                      </ScrollArea>
                    </DialogContent>
                  </Dialog>
                )}
              </TabsContent>
              <TabsContent value="Saved Messages" className="h-dvh">
                {!disabled && !isWithin24Hour && (
                  <Alert variant={'destructive'} className="mb-4">
                    <InfoIcon className="h-4 w-4" />
                    <AlertTitle>You cannot send a message</AlertTitle>
                    <AlertDescription>
                      <div>{!isWithin24Hour && 'Conversation has been expired. Cannot send any message'}</div>
                      <div>{disabled && 'You are not assigned to this conversation'}</div>
                    </AlertDescription>
                  </Alert>
                )}
                <div className="grid grid-cols-3 gap-4">
                  {savedMessages &&
                    savedMessages.length > 0 &&
                    savedMessages.map((message) => (
                      <Savedmessages
                        key={message.id}
                        message={message}
                        selectedMessage={selectedMessage}
                        setSelectedMessage={setSelectedMessage}
                        setSelectedTemplate={setSelectedTemplate}
                      />
                    ))}
                </div>
              </TabsContent>
            </Tabs>
          </ScrollArea>

          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            {selectedTemplate && messagingLimit && messagingLimit.remaining_limit > 0 ? (
              loading ? (
                <Button disabled>
                  Sending
                  <Loader2 className="ml-2 animate-spin" />
                </Button>
              ) : (
                <Button onClick={() => handleTemplateClick(selectedTemplate)}>Send Template</Button>
              )
            ) : (
              <AlertDialogAction disabled={true}>Send Template</AlertDialogAction>
            )}
            {selectedMessage && !disabled && isWithin24Hour ? (
              loading ? (
                <Button disabled>
                  Sending
                  <Loader2 className="ml-2 animate-spin" />
                </Button>
              ) : (
                <Button onClick={() => handleMessageClick(selectedMessage)}>Send Message</Button>
              )
            ) : (
              <AlertDialogAction disabled={true}>Send Message</AlertDialogAction>
            )}
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
      <Dialog open={varTemplateDialog} onOpenChange={setVarTemplateDialog}>
        <DialogContent className="max-w-lg">
          <DialogHeader>
            <DialogTitle></DialogTitle>
            <DialogDescription></DialogDescription>
          </DialogHeader>
          <ScrollArea className="max-h-[70vh]">
            <div className="max-w-lg pb-20">
              {(selectedTemplate?.type == 'basic-template' || selectedTemplate?.type == 'utility-template') && (
                <VariableParams
                  accountId={account_id}
                  optionArray={option}
                  template={selectedTemplate}
                  selectedRows={selectedRows}
                  messagingLimit={messagingLimit}
                  fromLiveChat={true}
                  loadMessagesOfConvo={loadMessagesOfConvo}
                  closeAllDialogs={handleCloseAllDialogs}
                />
              )}
              {selectedTemplate?.type == 'carousel-template' && (
                <VariableParamsCarousel
                  accountId={account_id}
                  optionArray={option}
                  template={selectedTemplate}
                  selectedRows={selectedRows}
                  messagingLimit={messagingLimit}
                  fromLiveChat={true}
                  loadMessagesOfConvo={loadMessagesOfConvo}
                  closeAllDialogs={handleCloseAllDialogs}
                />
              )}
            </div>
          </ScrollArea>
          <DialogFooter>
            <DialogClose>Close</DialogClose>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default ChooseMessageDialog;
