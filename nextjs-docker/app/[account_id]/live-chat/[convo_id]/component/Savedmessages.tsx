'use client';
import { db } from '@/app/db';
import { Button } from '@/components/ui/button';
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from '@/components/ui/dropdown-menu';
import { IMessage, ITemplateDatabase } from '@/lib/types';
import { parseMessageContent } from '@/lib/utils';
import dayjs from 'dayjs';
import { Loader2, MoreHorizontal, Trash } from 'lucide-react';
import Image from 'next/image';

import { AlertDialog, AlertDialogCancel, AlertDialogContent, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle } from '@/components/ui/alert-dialog';
import { useToast } from '@/components/ui/use-toast';
import { deleteSavedMessage } from '@/lib/pocket';
import { useDialog } from '@/state/hooks/use-dialog';
import { Dispatch, SetStateAction, useState } from 'react';

interface ISavedMessagesProps {
  message: IMessage;
  selectedMessage: IMessage | null;
  setSelectedMessage: Dispatch<SetStateAction<IMessage | null>>;
  setSelectedTemplate: Dispatch<SetStateAction<ITemplateDatabase | null>>;
}

const Savedmessages = ({ message, selectedMessage, setSelectedMessage, setSelectedTemplate }: ISavedMessagesProps) => {
  const { toast } = useToast();
  const [loading, setLoading] = useState(false);
  const deleteMessageDialog = useDialog();
  const editMessageDialog = useDialog();
  const handleDeleteSaveMessage = async () => {
    try {
      setLoading(true);
      await deleteSavedMessage(message.id, message.account);
      db.savedMessages.delete(message.id);
      toast({
        variant: 'success',
        description: 'Message Deleted successfully',
      });
    } catch (error) {
      toast({
        variant: 'destructive',
        description: 'There was an error. Try again!',
      });
    }
    deleteMessageDialog.dismiss();
    setSelectedMessage(null);
    setLoading(false);
  };

  return (
    <div
      className={`border rounded-lg bg-[#d8fdd2] text-sm hover:cursor-pointer hover:border-gray-400 w-full group ${
        selectedMessage?.id === message.id ? 'border-green-500 border' : 'border-gray-200'
      }`}
      onClick={() => {
        setSelectedMessage(message);
        setSelectedTemplate(null);
      }}
    >
      <div className={`p-4  rounded-lg flex flex-col break-normal relative`}>
        <div className="absolute right-0 bg-gray-50 bg-opacity-50 rounded-md z-10 top-0 m-3">
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" className="h-8 w-8 p-0">
                <span className="sr-only">Open menu</span>
                <MoreHorizontal className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuItem {...deleteMessageDialog.triggerProps}>
                <Trash className="mr-2 h-4 w-4 text-red-600" />
                <span className="text-red-600">Delete</span>
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
        <MessageItem message={message} />
        <div className="text-xs text-gray-500">{dayjs(message.created).format('MMMM D, YYYY h:mm A')}</div>
      </div>
      <AlertDialog {...deleteMessageDialog.dialogProps}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Are you sure you want to delete the message?</AlertDialogTitle>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            {loading ? (
              <Button className="mb-0" disabled variant={'destructive'}>
                Deleting
                <Loader2 className="ml-2 animate-spin" />
              </Button>
            ) : (
              <>
                <Button onClick={() => handleDeleteSaveMessage()} variant={'destructive'} className="mb-0">
                  Yes
                </Button>
              </>
            )}
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  );
};

const MessageItem = ({ message }: { message: IMessage }) => {
  if (message.file) {
    if (message.type === 'image' && message.url) {
      return (
        <div>
          <a href={message.url} target="_blank" rel="noreferrer">
            <Image alt={message.file} src={message.url} width={200} height={200} />
          </a>
          <div>{message.message == 'image' ? '' : message.message}</div>
        </div>
      );
    }
    if (message.type === 'audio' && message.url) {
      // biome-ignore lint/a11y/useMediaCaption: <explanation>
      return <audio controls src={message.url} />;
    }
    if (message.type === 'video' && message.url) {
      // biome-ignore lint/a11y/useMediaCaption: <explanation>
      return <video controls height={200} width={250} src={message.url} />;
    }
    if (message.type === 'document' && message.url) {
      const ext = message.file.split('.').pop();
      if (ext === 'pdf') {
        return (
          <div>
            <object data={message.url} type="application/pdf" width="200" height="200">
              <embed src={message.url} type="application/pdf" />
            </object>
            <a className="text-sm underline" href={message.url} target="_blank" rel="noreferrer">
              Download File
            </a>
          </div>
        );
      } else {
        return (
          <div>
            <h2>{message.file}</h2>
            <a className="text-sm underline" href={message.url} target="_blank" rel="noreferrer">
              Download File
            </a>
          </div>
        );
      }
    }
  }
  if (message.type === 'location') {
    return (
      <div>
        <div style={{ height: 200, width: 250 }} id={`map_${message.id}`} />
        <p className="text-sm">{message.location?.name}</p>
        <p className="text-sm">{message.location?.address}</p>
        <a className="text-sm underline text-blue-600" href={message.location?.url} target="_blank" rel="noreferrer">
          Open Map
        </a>
      </div>
    );
  }
  if (message.message) {
    return <p dangerouslySetInnerHTML={{ __html: parseMessageContent(message.message) }}></p>;
  }
};

export default Savedmessages;
