import { Badge } from '@/components/ui/badge';
import { User } from '@/lib/types';
import React from 'react';
import { useFormStatus } from 'react-dom';

const AgentNameUpdating = ({ assignedAgent }: { assignedAgent: User | undefined }) => {
  const { pending } = useFormStatus();
  if (assignedAgent) {
    return (
      <div className="mr-2 font-medium text-sm">
        {pending ? (
          <div>Updating</div>
        ) : (
          <div>
            {assignedAgent.type == 'admin' ? (
              <div className="flex items-center gap-2">
                <div>{assignedAgent.name}</div>
                <Badge variant="secondary" className="ml-1">
                  Admin
                </Badge>
              </div>
            ) : (
              assignedAgent.name
            )}
          </div>
        )}
      </div>
    );
  } else {
    return <div className="mr-2">{pending ? <div>Updating</div> : <div>None</div>}</div>;
  }
};

export default AgentNameUpdating;
