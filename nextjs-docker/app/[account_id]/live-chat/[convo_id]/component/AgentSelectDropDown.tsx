import { db } from '@/app/db';
import { Select, SelectContent, SelectGroup, SelectItem, SelectLabel, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Toaster } from '@/components/ui/toaster';
import { useToast } from '@/components/ui/use-toast';
import { updateChatAgent } from '@/lib/actions';
import { Team, User } from '@/lib/types';
import { useState } from 'react';
import UpdateButton from './UpdateButton';

const AgentSelectDropDown = ({
  agentsOfAccount,
  assignedAgent,
  convo_id,
  account_id,
  currentUser,
  teams,
}: {
  convo_id: string;
  account_id: string;
  agentsOfAccount?: User[];
  assignedAgent?: Partial<User>;
  currentUser: User;
  teams: Team[];
}) => {
  const agentRoles =
    currentUser.expand?.roles?.map((role) => {
      return role.name;
    }) ?? [];
  const hasSuperAccess = currentUser?.type == 'admin' || agentRoles.includes('Live Chat-Super Access');

  const initialState = {
    message: {
      status: 0,
    },
  };

  //added this because select is hardcoded as relative
  //TODO: Change it later
  const css = `
  select {
    position: absolute !important
  }
`;
  const [valueChanged, setIsValueChanged] = useState('');
  const { toast } = useToast();

  const localAgentUpdate = () => {
    const selectedAgent = valueChanged;
    const selectedAgentObject = agentsOfAccount?.filter((item) => item.id == selectedAgent)[0];
    db.conversations.update(convo_id, {
      'expand.assigned_agent': selectedAgentObject,
      assigned_agent: selectedAgent,
    });
    const formData = new FormData();
    formData.set('agent', selectedAgent);
    updateChatAgent(convo_id, account_id, {}, formData)
      .then(() => {
        toast({
          variant: 'success',
          description: `Agent updated to ${selectedAgentObject?.name}`,
        });
      })
      .catch(() => {
        toast({
          variant: 'destructive',
          description: 'Failed to update agent',
        });
      });
  };

  return (
    <div className="flex gap-2 flex-col items-start">
      <style>{css}</style>
      {/* {convo_id} */}
      {hasSuperAccess && (
        <Select
          key={assignedAgent?.id}
          defaultValue={agentsOfAccount?.some((agent) => agent.id === assignedAgent?.id) ? assignedAgent?.id : undefined}
          disabled={!hasSuperAccess}
          name="agent"
          onValueChange={(value) => {
            setIsValueChanged(value);
          }}
        >
          <SelectTrigger className="w-[180px] mr-2">
            <SelectValue placeholder="Select an Agent" />
          </SelectTrigger>
          <SelectContent>
            <SelectGroup>
              <SelectLabel>Agent</SelectLabel>
              {agentsOfAccount?.map((agent) => (
                <SelectItem key={agent.id} value={agent.id}>
                  {agent.name} - {teams.find((t) => t.id === (agent?.team ?? ''))?.name}
                </SelectItem>
              ))}
            </SelectGroup>
          </SelectContent>
        </Select>
      )}
      {hasSuperAccess && (
        <UpdateButton label="Assign To Agent" localAgentUpdate={localAgentUpdate} hasSuperAccess={hasSuperAccess} valueChanged={valueChanged} />
      )}
    </div>
  );
};

export default AgentSelectDropDown;
