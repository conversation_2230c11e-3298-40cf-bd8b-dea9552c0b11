import { Button } from '@/components/ui/button';
import React from 'react';

const UpdateButton = ({
  hasSuperAccess,
  localAgentUpdate,
  valueChanged,
  label,
}: {
  label: string;
  localAgentUpdate: () => void;
  hasSuperAccess: boolean;
  valueChanged: string;
}) => {
  return (
    <>
      {hasSuperAccess ? (
        <Button onClick={localAgentUpdate} className="mb-0" size="sm" type="button" disabled={!valueChanged}>
          {label}
        </Button>
      ) : (
        <Button className="mb-0" size="sm" type="submit" disabled>
          {label}
        </Button>
      )}
    </>
  );
};

export default UpdateButton;
