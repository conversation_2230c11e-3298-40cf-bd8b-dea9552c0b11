'use client';

import { useEffect, useRef } from 'react';

// Create a custom event name for audio playback
const AUDIO_PLAY_EVENT = 'whatsapp-hook-audio-play';

export const Audio = ({ src }: { src: string }) => {
  const audioRef = useRef<HTMLAudioElement>(null);

  useEffect(() => {
    const audioElement = audioRef.current;
    if (!audioElement) return;

    // Function to handle play event
    const handlePlay = () => {
      // Dispatch a custom event when this audio starts playing
      const event = new CustomEvent(AUDIO_PLAY_EVENT, {
        detail: { currentAudio: audioElement }
      });
      document.dispatchEvent(event);
    };

    // Function to pause other audio elements when a new one starts playing
    const pauseOtherAudios = (e: Event) => {
      const customEvent = e as CustomEvent;
      const currentAudio = customEvent.detail.currentAudio;

      // If this is not the audio that triggered the event, pause it
      if (audioElement !== currentAudio && !audioElement.paused) {
        audioElement.pause();
      }
    };

    // Add event listeners
    audioElement.addEventListener('play', handlePlay);
    document.addEventListener(AUDIO_PLAY_EVENT, pauseOtherAudios);

    // Clean up event listeners on component unmount
    return () => {
      audioElement.removeEventListener('play', handlePlay);
      document.removeEventListener(AUDIO_PLAY_EVENT, pauseOtherAudios);
    };
  }, []);

  return <audio ref={audioRef} controls src={src} />;
};
