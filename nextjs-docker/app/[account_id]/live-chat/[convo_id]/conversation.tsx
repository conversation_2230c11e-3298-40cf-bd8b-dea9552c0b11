'use client';
import { db } from '@/app/db';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import PocketBase from 'pocketbase';
import { useEffect, useLayoutEffect, useRef } from 'react';
// import { createPortal } from 'react-dom';
// import { Badge } from "@/components/ui/badge"
// import { DropdownMenuTrigger, DropdownMenuLabel, DropdownMenuSeparator, DropdownMenuItem, DropdownMenuContent, DropdownMenu } from "@/components/ui/dropdown-menu"
import { HoverCard, HoverCardContent, HoverCardTrigger } from '@/components/ui/hover-card';
import { handleSubmit } from '@/lib/actions';
import type { IExpandedMessage, IMessagingLimit, ITemplateDatabase, User } from '@/lib/types';
import { pb_url } from '@/state/consts';
import '@uppy/core/dist/style.min.css';
import '@uppy/dashboard/dist/style.min.css';
import clsx from 'clsx';
import dayjs from 'dayjs';
import isToday from 'dayjs/plugin/isToday';
import isYesterday from 'dayjs/plugin/isYesterday';
import relativeTime from 'dayjs/plugin/relativeTime';
import weekday from 'dayjs/plugin/weekday';
import { useLiveQuery } from 'dexie-react-hooks';
import { Check, CheckCheck, CircleX, Info, Loader2 } from 'lucide-react';
import Script from 'next/script';
import HoverAdd from './component/HoverAdd';
import OmniBox from './component/OmniBox';
import { MessageItem } from './MessageItem';
import { RepliedMessageItem } from './RepliedMessageItem';
import { getMessageFromConversations } from '@/lib/pocket';

// let uppy: Uppy;

export const ConversationPage = ({
  convo_id,
  accountId: account_id,
  templates,
  messagingLimit,
  currentUser,
}: {
  convo_id: string;
  accountId: string;
  templates: ITemplateDatabase[];
  messagingLimit: IMessagingLimit | null;
  currentUser: User;
}) => {
  const conversations = useLiveQuery(() => (convo_id ? db.conversations.where({ convo_id: convo_id }).toArray() : []), [convo_id]);
  const conversation = conversations?.[0];
  const accounts = useLiveQuery(() => db.accounts.where({ id: account_id }).toArray(), [account_id]);
  const messages = useLiveQuery(
    () => {
      return convo_id
        ? db.messages
            .where({
              convo_id: convo_id,
            })
            .sortBy('created')
        : [];
    },
    [convo_id],
    null
  );
  const savedMessages = useLiveQuery(() => {
    return db.savedMessages
      .where({
        account: account_id,
      })
      .sortBy('created');
  }, [account_id]);
  const createdAt = messages?.filter((message) => message.from == 'user').reverse()[0]?.created;

  const account = accounts?.[0];
  // const accountId = account?.id ?? null;

  const phoneId = account?.phone_id ?? null;
  const wabaid = account?.waba_id ?? null;

  const convoId = convo_id ?? null;
  const lead = conversation as any;
  const chatWindowRef = useRef<HTMLDivElement>(null);
  const ref = useRef<HTMLFormElement>(null);
  // biome-ignore lint/suspicious/noExplicitAny: <explanation>
  // const uppyRef = useRef<any>(null);
  const box = useRef(null);

  dayjs.extend(relativeTime);
  dayjs.extend(weekday);
  dayjs.extend(isToday);
  dayjs.extend(isYesterday);

  const formatDate = (date: Date) => {
    const now = dayjs();
    const inputDate = dayjs(date);

    if (inputDate.isToday()) {
      return inputDate.format('hh:mm a');
    } else if (inputDate.isYesterday()) {
      return 'Yesterday';
    } else if (now.diff(inputDate, 'day') <= 5) {
      return inputDate.format('dddd');
    } else {
      return inputDate.format('DD/MM/YYYY');
    }
  };

  useLayoutEffect(() => {
    if (!messages || messages.length == 0) return;
    const item = document.getElementById('chat-window2');
    if (item) {
      item.scrollTo(0, item.scrollHeight);
    }
  }, [convo_id, messages]);

  const loadMessagesOfConvo = async () => {
    const pb = new PocketBase(pb_url);
    pb.collection<IExpandedMessage>('messages')
      .getFullList({
        filter: `convo_id="${convoId}"`,
        expand: 'template, interactive_message, created_by, replied_to',
        sort: 'created',
      })
      .then((messages) => {
        const end = performance.now();
        db.messages.bulkPut(
          messages.map((item) => {
            if (item.file) {
              return { ...item, url: pb.files.getURL(item, item.file), created: new Date(item.created) };
            }
            return { ...item, created: new Date(item.created) };
          })
        );
      });
  };
  useEffect(() => {
    loadMessagesOfConvo();
  }, [convoId]);

  const handleSubmitWithData = handleSubmit.bind(null, {
    leadId: lead?.id ?? (conversation as any)?.user ?? null,
    convoId,
    accountId: account_id,
    phoneNo: lead?.phone_number ?? (conversation as any)?.phone_number ?? null,
    phoneId,
    userId: currentUser.id,
  });

  const DeliveryStatus = ({ message }: { message: { delivery_status?: string } }) => {
    if (message.delivery_status == 'sent') {
      return <Check className="h-4 w-4 text-gray-800" />;
    }
    if (message.delivery_status == 'delivered') {
      return <CheckCheck className="h-4 w-4 text-gray-800" />;
    }
    if (message.delivery_status == 'read') {
      return <CheckCheck className="h-4 w-4 text-[#2746d1]" />;
    }
    if (message.delivery_status == 'failed') {
      return <CircleX className="h-4 w-4 text-[#d12727]" />;
    }
    return (
      <div>
        <Check className="h-4 w-4 text-gray-800" />
      </div>
    );
  };

  return (
    <main className="flex flex-col overflow-auto">
      <link
        rel="stylesheet"
        href="https://unpkg.com/leaflet@1.9.4/dist/leaflet.css"
        integrity="sha256-p4NxAoJBhIIN+hmNHrzRCf9tD/miZyoHS5obTRR9BMY="
        crossOrigin=""
      />

      <Script src="https://unpkg.com/leaflet@1.9.4/dist/leaflet.js" />
      {/* Chat Window */}

      <div
        id="chat-window2"
        className="flex
              p-0
              items-start 
              h-dvh
              bg-[#e5ddd5]
              bg-[url('/assets/whatsapp_chats_bg.png')] 
              gap-x-2 
              w-full 
              overflow-auto grow"
      >
        <div ref={chatWindowRef} className="grid gap-2 p-4 flex-grow pb-2">
          {messages &&
            messages.length > 0 &&
            messages.map((message: IExpandedMessage, idx: number) => {
              return (
                <div
                  className={`flex items-start max-w-[360px] gap-4 text-sm ${message.from === 'agent' ? 'justify-self-end' : 'justify-self-start'}`}
                  key={message.id}
                >
                  <div className="grid gap-2">
                    <HoverAdd leadId={lead?.id} accountId={account_id} message={message}>
                      <div className={clsx('rounded-lg p-4 flex flex-col items-start space-2', message.from !== 'agent' ? 'bg-gray-100' : 'bg-[#d8fdd2] ')}>
                        {message.expand?.replied_to && <RepliedMessageItem message={messages.filter((_message) => _message.id == message.replied_to)[0]} />}
                        <div className="mb-2">
                          <MessageItem message={message} />
                        </div>
                        <div className="relative flex items-center space-x-4 justify-between w-full">
                          {message.delivery_status == 'failed' ? (
                            <HoverCard openDelay={0}>
                              <HoverCardTrigger>
                                <div className="flex flex-col">
                                  {message.from == 'agent' && (
                                    <div className="text-xs italic text-gray-700">
                                      {message.created_by.length > 0 ? message.expand?.created_by.name : 'System'}
                                    </div>
                                  )}
                                  <div className="text-red-500 text-xs font-bold underline decoration-dotted ">
                                    {formatDate(message?.created ?? new Date())}
                                  </div>
                                </div>
                              </HoverCardTrigger>
                              <HoverCardContent className="space-y-1">
                                <div className="flex space-x-1 text-xs">
                                  <div className="font-bold w-14">Code:</div>
                                  <div className="flex-1">{message.error_code}</div>
                                </div>
                                <div className="flex space-x-1 text-xs">
                                  <div className="font-bold w-14">Message:</div>
                                  <div className="flex-1">{message.error}</div>
                                </div>
                                <div className="flex space-x-1 text-xs">
                                  <div className="font-bold w-14">Details:</div>
                                  <div className="flex-1">{message.error_data}</div>
                                </div>
                              </HoverCardContent>
                            </HoverCard>
                          ) : (
                            <div className="flex flex-col hover:cursor-help">
                              {message.from == 'agent' && (
                                <div className="text-xs italic text-gray-700">{message.created_by.length > 0 ? message.expand?.created_by.name : 'System'}</div>
                              )}
                              <HoverCard openDelay={0}>
                                <HoverCardTrigger>
                                  <div className="text-gray-500 text-xs">{formatDate(message?.created ?? new Date())}</div>
                                </HoverCardTrigger>
                                <HoverCardContent className="space-y-1">
                                  <div className="flex space-x-1 text-xs">
                                    <div className="flex-1">Sent at: {dayjs(message.created).format('h:mm a DD/MM/YYYY')}</div>
                                  </div>
                                </HoverCardContent>
                              </HoverCard>
                            </div>
                          )}

                          {message.from == 'agent' && <DeliveryStatus message={message} />}
                          {message.reaction?.emoji && (
                            <div className="absolute top-6 -right-7 bg-white h-5 w-5 rounded-full p-4 flex items-center justify-center">
                              {message.reaction.emoji}
                            </div>
                          )}
                        </div>
                      </div>
                    </HoverAdd>
                    {idx == messages.length - 1 && message?.from == 'agent' && message.delivery_status == 'read' && (
                      <div className="text-xs text-gray-500">Seen at {dayjs(message.updated).format('h:mm a DD/MM/YYYY ')}</div>
                    )}
                  </div>
                </div>
              );
            })}
          {messages && messages.length == 0 && (
            <div>
              <Loader2 className="ml-2 animate-spin" />
            </div>
          )}
        </div>
      </div>
      <div className="sticky bottom-0 left-0 w-full">
        {/* added a message length check so that this alert does not flash before disappearing */}
        {/* Check if a current user exists, ensure they are not an admin, and verify that no agent is assigned to the conversation */}
        {messages?.length != 0 && currentUser && currentUser.type != 'admin' && !conversation?.expand?.assigned_agent?.id && (
          <Alert variant={'destructive'} className="w-1/2 m-4">
            <Info className="h-4 w-4 text-red-500" />
            <AlertTitle>Note!</AlertTitle>
            <AlertDescription>If you start this conversation, it will automatically be assigned to you</AlertDescription>
          </Alert>
        )}
        {currentUser && conversation ? (
          <OmniBox
            loadMessagesOfConvo={loadMessagesOfConvo}
            account={account}
            currentUser={currentUser}
            conversation={conversation}
            handleSubmitWithData={handleSubmitWithData}
            templates={templates}
            messagingLimit={messagingLimit}
            savedMessages={savedMessages}
            createdAt={createdAt}
            convo_id={convoId}
          />
        ) : null}
      </div>
    </main>
  );
};
