// disable ts
// @ts-nocheck
import React, { useEffect, useRef, useState } from 'react';
import WaveSurfer from 'wavesurfer.js';
import RecordPlugin from 'wavesurfer.js/dist/plugins/record.esm.js';

const WaveSurferRecorder = () => {
  const [wavesurfer, setWaveSurfer] = useState<WaveSurfer>();
  const [record, setRecord] = useState(null);
  const [scrollingWaveform, setScrollingWaveform] = useState(false);
  const [isRecording, setIsRecording] = useState(false);
  const [isPaused, setIsPaused] = useState(false);
  const [availableDevices, setAvailableDevices] = useState<MediaDeviceInfo[]>([]);
  const micRef = useRef();
  const recordingsRef = useRef();
  const progressRef = useRef();
  const pauseButtonRef = useRef();
  const recButtonRef = useRef();
  const micSelectRef = useRef();

  useEffect(() => {
    createWaveSurfer();

    RecordPlugin.getAvailableAudioDevices().then((devices: MediaDeviceInfo[]) => {
      setAvailableDevices(devices);
    });

    return () => {
      if (wavesurfer) {
        wavesurfer.destroy();
      }
    };
  }, [scrollingWaveform]);

  const createWaveSurfer = () => {
    if (wavesurfer) {
      wavesurfer.destroy();
    }

    const ws = WaveSurfer.create({
      container: micRef?.current ?? '',
      waveColor: 'rgb(200, 0, 200)',
      progressColor: 'rgb(100, 0, 100)',
    });

    const rp = ws.registerPlugin(
      RecordPlugin.create({
        scrollingWaveform,
        renderRecordedAudio: false,
      })
    );

    rp.on('record-end', (blob) => {
      const container = recordingsRef?.current ?? '';
      const recordedUrl = URL.createObjectURL(blob);

      const recordedWavesurfer = WaveSurfer.create({
        container,
        waveColor: 'rgb(200, 100, 0)',
        progressColor: 'rgb(100, 50, 0)',
        url: recordedUrl,
      });

      const button = document.createElement('button');
      button.textContent = 'Play';
      button.onclick = () => recordedWavesurfer.playPause();
      recordedWavesurfer.on('pause', () => (button.textContent = 'Play'));
      recordedWavesurfer.on('play', () => (button.textContent = 'Pause'));

      container.appendChild(button);

      const link = document.createElement('a');
      Object.assign(link, {
        href: recordedUrl,
        download: `recording.${blob.type.split(';')[0].split('/')[1] || 'webm'}`,
        textContent: 'Download recording',
      });

      container.appendChild(link);
    });

    rp.on('record-progress', (time) => {
      updateProgress(time);
    });

    setWaveSurfer(ws);
    setRecord(rp);
    pauseButtonRef.current.style.display = 'none';
    recButtonRef.current.textContent = 'Record';
  };

  const updateProgress = (time: number) => {
    const formattedTime = [Math.floor((time % 3600000) / 60000), Math.floor((time % 60000) / 1000)].map((v) => (v < 10 ? '0' + v : v)).join(':');
    progressRef.current.textContent = formattedTime;
  };

  const handlePauseClick = () => {
    if (record.isPaused()) {
      record.resumeRecording();
      pauseButtonRef.current.textContent = 'Pause';
      setIsPaused(false);
      return;
    }

    record.pauseRecording();
    pauseButtonRef.current.textContent = 'Resume';
    setIsPaused(true);
  };

  const handleRecordClick = () => {
    if (record.isRecording() || record.isPaused()) {
      record.stopRecording();
      recButtonRef.current.textContent = 'Record';
      pauseButtonRef.current.style.display = 'none';
      setIsRecording(false);
      return;
    }

    recButtonRef.current.disabled = true;

    const deviceId = micSelectRef.current.value;
    record.startRecording({ deviceId }).then(() => {
      recButtonRef.current.textContent = 'Stop';
      recButtonRef.current.disabled = false;
      pauseButtonRef.current.style.display = 'inline';
      setIsRecording(true);
    });
  };

  const handleCheckboxClick = (e: { target: { checked: boolean | ((prevState: boolean) => boolean) } }) => {
    setScrollingWaveform(e.target.checked);
  };

  return (
    <div>
      <h1 style={{ marginTop: 0 }}>Press Record to start recording 🎙️</h1>
      <p>
        📖 <a href="https://wavesurfer.xyz/docs/classes/plugins_record.RecordPlugin">Record plugin docs</a>
      </p>
      <button ref={recButtonRef} onClick={handleRecordClick}>
        Record
      </button>
      <button ref={pauseButtonRef} onClick={handlePauseClick} style={{ display: 'none' }}>
        Pause
      </button>
      <select ref={micSelectRef}>
        <option value="" hidden>
          Select mic
        </option>
        {availableDevices.map((device) => (
          <option key={device.deviceId} value={device.deviceId}>
            {device.label || device.deviceId}
          </option>
        ))}
      </select>
      <label style={{ display: 'inline-block' }}>
        <input type="checkbox" onClick={handleCheckboxClick} /> Scrolling waveform
      </label>
      <p ref={progressRef}>00:00</p>
      <div
        ref={micRef}
        style={{
          border: '1px solid #ddd',
          borderRadius: '4px',
          marginTop: '1rem',
        }}
      ></div>
      <div ref={recordingsRef} style={{ margin: '1rem 0' }}></div>
      <style>
        {`
          button {
            min-width: 5rem;
            margin: 1rem 1rem 1rem 0;
          }
        `}
      </style>
    </div>
  );
};

export default WaveSurferRecorder;
