import { IExpandedMessage, IMessage } from '@/lib/types';
import Image from 'next/image';
import React, { useEffect } from 'react';
import { getPb } from '@/lib/pocket';
import { db } from '@/app/db';

export const RepliedMessageItem = ({ message }: { message: IMessage }) => {
  if (message?.file && message?.type == 'image' && message.url) {
    return (
      <div className="bg-white border border-l-2 border-l-green-600 py-4 pl-1 pr-2 rounded-lg w-full">
        <a href={message.url} target="_blank" rel="noreferrer">
          <Image alt={message.file} src={message.url} width={100} height={50} className="mx-auto rounded-lg mb-2 h-auto w-auto" />
        </a>
        <div>{message.message == 'image' ? 'replied with an image' : message.message}</div>
      </div>
    );
  }

  return (
    <div className="bg-white border border-l-2 border-l-green-600 py-4 pl-1 pr-2 rounded-lg w-full">
      <div className="text-xs text-gray-400">{message?.message}</div>
    </div>
  );
};
