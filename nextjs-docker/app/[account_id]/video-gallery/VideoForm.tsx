'use client';

import { Input } from '@/components/ui/input';
import { useFormState } from 'react-dom';
import { createvideo } from './action';

const VideoForm = ({ account_id }: { account_id: string }) => {
  const createvideoId = createvideo.bind(null, account_id);
  const [state, formAction] = useFormState(createvideoId, null);
  return (
    <form action={formAction} className="max-w-lg mx-auto">
      <label className="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Upload file</label>
      <Input
        name="file"
        className="block w-full text-sm text-gray-900 border border-gray-300 rounded-lg cursor-pointer bg-gray-50 dark:text-gray-400 focus:outline-none dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400"
        aria-describedby="user_avatar_help"
        id="user_avatar"
        type="file"
        // disabled={state !== 'initial'}
      />
      <div className="mt-1 mb-2 text-sm text-gray-500 dark:text-gray-300" id="user_avatar_help">
        video that will be used in templates
      </div>
      <button
        type="submit"
        // disabled={state !== 'initial'}
        className="text-white bg-gradient-to-r from-purple-500 to-pink-500 hover:bg-gradient-to-l focus:ring-4 focus:outline-none focus:ring-purple-200 dark:focus:ring-purple-800 font-medium rounded-lg text-sm px-5 py-2.5 text-center me-2 mb-2"
      >
        Add video
      </button>
      {/* { state !== 'initial' && <LoaderCircle className="animate-spin h-5 w-5 mr-2" /> } */}
    </form>
  );
};

export default VideoForm;
