'use server';

import { getPb } from '@/lib/pocket';
import { revalidatePath } from 'next/cache';
import { redirect } from 'next/navigation';

export async function createvideo(account_id: string, prevState: any, formData: FormData) {
  const rawFormData = {
    file: formData.get('file'),
    account: account_id,
    name: (formData.get('file') as File).name,
  };

  // mutate data
  const pb = await getPb();
  try {
    await pb.collection('videos').create(rawFormData);
  } catch (e) {
    console.log(e);
    return e;
  }

  // revalidate cache
  revalidatePath(`/${account_id}/video-gallery`);
  redirect(`/${account_id}/video-gallery`);
}
