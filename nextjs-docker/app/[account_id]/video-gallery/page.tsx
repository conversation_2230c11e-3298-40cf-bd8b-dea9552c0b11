import { getPb } from '@/lib/pocket';
import Link from 'next/link';
import VideoForm from './VideoForm';

const videoGallery = async ({ params }: { params: Record<string, string> }) => {
  const pb = await getPb();
  const videos = await pb.collection('videos').getFullList({ filter: `account = "${params.account_id}"` });
  // tailwind videos in a gallery format
  return (
    <div>
      <div className="flex gap-4 m-10">
        {videos.map((video) => {
          const url = pb.files.getURL(video, video.file);
          return (
            <div className="flex flex-wrap items-center max-w-sm p-6 bg-white border border-gray-200 rounded-lg shadow hover:bg-gray-100 dark:bg-gray-800 dark:border-gray-700 dark:hover:bg-gray-700">
              <video controls width={400} height={400} key={video.id} src={url} />
              <div>{url}</div>
            </div>
          );
        })}
      </div>
      <VideoForm account_id={params.account_id} />
      <Link prefetch={false} className="ml-10 text-blue-500 underline" href={`/${params.account_id}/templates`}>
        Back to templates
      </Link>
    </div>
  );
};

export default videoGallery;
