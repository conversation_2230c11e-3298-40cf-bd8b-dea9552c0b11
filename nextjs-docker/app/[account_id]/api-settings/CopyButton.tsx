'use client';

import { Button } from '@/components/ui/button';
import React from 'react';

interface CopyButtonProps {
  textToCopy: string;
}

const CopyButton: React.FC<CopyButtonProps> = ({ textToCopy }) => {
  const copyToClipboard = async () => {
    try {
      await navigator.clipboard.writeText(textToCopy);
      alert('API key copied');
    } catch (err) {
      console.error('Failed to copy text: ', err);
    }
  };

  return (
    <Button variant="outline" onClick={copyToClipboard}>
      Copy
    </Button>
  );
};

export default CopyButton;
