import { getAccount, getApiSettings } from '@/lib/pocket';
import CopyButton from './CopyButton';
import Link from 'next/link';

interface ApiSettingsPageProps {
  params: {
    account_id: string;
  };
}

const ApiSettingsPage = async ({ params }: ApiSettingsPageProps) => {
  const { account_id } = params;
  const apiSettings = await getApiSettings(account_id);
  const account = await getAccount(account_id);
  return (
    <div className="m-10">
      <h1 className="mb-4 text-2xl">API Settings for Account {account.name}</h1>
      <table className="min-w-full divide-y divide-gray-200">
        <thead className="bg-gray-50">
          <tr>
            <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              API Key
            </th>
            <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              Created At
            </th>
            <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              Expires At
            </th>
            <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              Status
            </th>
          </tr>
        </thead>
        <tbody className="bg-white divide-y divide-gray-200">
          {apiSettings.map((apiSetting) => (
            <tr>
              <td className="px-6 py-4 flex items-center gap-2 whitespace-nowrap text-sm font-medium text-gray-900">
                <code>{apiSetting.key}</code> <CopyButton textToCopy={apiSetting.key} />
              </td>
              <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{new Date(apiSetting.created).toLocaleDateString()}</td>
              <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{new Date(apiSetting.expires_at).toLocaleDateString()}</td>
              <td className="px-6 py-4 capitalize whitespace-nowrap text-sm text-gray-500">{apiSetting.status}</td>
            </tr>
          ))}
        </tbody>
      </table>

      {/* API Docs */}
      <h2 className="mt-8 mb-4 text-xl">API Documentation</h2>
      <p className="mb-2">To use the WeTarseel API, you will need to include your API key in the request headers. Here is an example using curl:</p>
      <pre className="bg-gray-100 p-4 rounded-md mb-4">curl -H "Authorization: Bearer YOUR_API_KEY" https://app.wetarseel.ai/api/v1/send-message</pre>
      <p>
        To pass parameters choose method as <code className="bg-gray-100 p-2 rounded-md">POST</code>
      </p>
      <p>
        Pass JSON parameters such as this. You will have the exact message OR the template ID, and its required parameters, you can find your list of templates
        from here:
        <Link className="ml-2 underline text-blue-600" href={`/${account_id}/templates`}>
          Templates
        </Link>
      </p>
      <pre className="bg-gray-100 p-4 rounded-md">
        {JSON.stringify(
          {
            message: 'Hello, World!',
            to: '************',
            template_name: 'template_123',
            parameters: ['<string>', '<string>'],
          },
          null,
          2
        )}
      </pre>
    </div>
  );
};

export default ApiSettingsPage;
