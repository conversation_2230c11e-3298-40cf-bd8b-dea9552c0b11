'use client';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectGroup, SelectItem, SelectLabel, SelectTrigger, SelectValue } from '@/components/ui/select';
import { useFormState } from 'react-dom';
import { createInteractiveMessage } from '@/lib/actions';
import { Account, User } from '@/lib/types';

const InteractiveMessageForm = ({ user, account }: { user: User; account: Account }) => {
  const [state, formAction] = useFormState(createInteractiveMessage, null);
  return (
    <form action={formAction}>
      <div className="mx-auto p-6 bg-gray-100 w-full">
        <div className="space-y-2">
          <div>
            <div>Type</div>
            <Select name="type">
              <SelectTrigger className="w-[180px]">
                <SelectValue placeholder="Select type" />
              </SelectTrigger>
              <SelectContent>
                <SelectGroup>
                  <SelectLabel>Type</SelectLabel>
                  <SelectItem value="list">List</SelectItem>
                  <SelectItem value="button">Button</SelectItem>
                  <SelectItem value="product">Product</SelectItem>
                  <SelectItem value="product_list">Product List</SelectItem>
                  <SelectItem value="catalog_message">Catalog Message</SelectItem>
                  <SelectItem value="flow">Flow</SelectItem>
                </SelectGroup>
              </SelectContent>
            </Select>
          </div>
          <div>
            <div>
              <div>Header Type</div>
              <Select name="header_type">
                <SelectTrigger className="w-[180px]">
                  <SelectValue placeholder="Select header type" />
                </SelectTrigger>
                <SelectContent>
                  <SelectGroup>
                    <SelectLabel>Header Type</SelectLabel>
                    <SelectItem value="text">Text</SelectItem>
                    <SelectItem value="video">Video</SelectItem>
                    <SelectItem value="image">Image</SelectItem>
                    <SelectItem value="document">Document</SelectItem>
                  </SelectGroup>
                </SelectContent>
              </Select>
            </div>
          </div>
          <div>
            <div>
              <div>Header Text</div>
              <Input type="text" name="header_text" placeholder="Header Text" max={60} />
            </div>
          </div>
          <div>
            <div>
              <div>Body Text</div>
              <Input type="text" name="body_text" placeholder="Body Text" max={1024} />
            </div>
          </div>
          <div>
            <div>
              <div>Footer Text</div>
              <Input type="text" name="footer_text" placeholder="Footer Text" max={60} />
            </div>
          </div>
          <div>
            <div>
              <div>Button 1 title</div>
              <Input type="text" name="button1" placeholder="Button1 Title" max={20} />
            </div>
          </div>
          <div>
            <div>
              <div>Button 2 title</div>
              <Input type="text" name="button2" placeholder="Button2 Title" max={20} />
            </div>
          </div>
          <div>
            <div>
              <div>Button 3 title</div>
              <Input type="text" name="button3" placeholder="Button2 Title" max={20} />
            </div>
          </div>
        </div>
        {/* <input type="text" hidden defaultValue={user.phoneNumber}/> */}
        <input type="text" hidden name="account" defaultValue={JSON.stringify(account)} />
        <Button type="submit" className="mb-0">
          Submit
        </Button>
      </div>
    </form>
  );
};

export default InteractiveMessageForm;
