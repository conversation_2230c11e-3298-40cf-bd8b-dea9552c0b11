import { getAccount } from '@/lib/pocket';
import { User } from '@/lib/types';
import InteractiveMessageForm from './components/InteractiveMessageForm';
import { server_component_pb } from '@/state/pb/server_component_pb';

export default async ({ params }: { params: Record<string, string> }) => {
  const { pb } = await server_component_pb();
  const user = pb.authStore.record as User;
  const account = await getAccount(params.account_id);
  return (
    <>
      <InteractiveMessageForm user={user} account={account} />
    </>
  );
};
