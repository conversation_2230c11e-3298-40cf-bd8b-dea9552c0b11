import { <PERSON><PERSON> } from '@/components/ui/button';
import { <PERSON>gger } from 'next-axiom';
import Link from 'next/link';

import type { Metadata } from 'next';

export const metadata: Metadata = {
  title: 'Campaign',
  description: '...',
};

export default async ({ params }: { params: Record<string, string> }) => {
  // noStore();
  const log = new Logger();
  // const campaigns = (
  //   await getCampaign(params.account_id)
  // );
  const interactiveMessages: [] = [];

  if (!interactiveMessages || interactiveMessages.length === 0) {
    await log.flush();
    return (
      <div className="h-full w-full flex flex-col justify-center items-center space-y-4">
        <div>Looks like you dont have any Interactive Messages!</div>
        <Button asChild>
          <Link href={`interactive-messages/create`}>Create Interactive Message</Link>
        </Button>
      </div>
    );
  }
  log.info('templates', interactiveMessages);
  await log.flush();

  return (
    <div className="mx-auto p-6 bg-gray-100">
      <div className="flex mb-4">
        <div className="flex-1">
          <div className="text-gray-800 text-xl font-bold sm:text-2xl">Interactive Messages List</div>
          <p className="text-gray-600 mt-2">Select the interactive message which you would like to view</p>
        </div>
        <div className="space-x-4">
          <Button asChild>
            <Link href={`/${params.account_id}/campaign/create`}>Create Interactive Message</Link>
          </Button>
        </div>
      </div>
      {/* <DataTable
        columns={columns}
        data={campaigns}
        account_id={params.account_id}
      /> */}
    </div>
  );
};
