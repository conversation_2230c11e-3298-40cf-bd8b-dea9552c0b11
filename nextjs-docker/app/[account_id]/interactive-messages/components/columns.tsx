'use client';

import { But<PERSON> } from '@/components/ui/button';
import { IExpandedCampaign } from '@/lib/types';
import { ColumnDef } from '@tanstack/react-table';
import { ArrowUpDown, ExternalLink, MoreHorizontal } from 'lucide-react';

import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuLabel, DropdownMenuTrigger } from '@/components/ui/dropdown-menu';
import dayjs from 'dayjs';
import relativeTime from 'dayjs/plugin/relativeTime';
// import utc from 'dayjs/plugin/utc'
import Link from 'next/link';
import { HoverCard, HoverCardContent, HoverCardTrigger } from '@/components/ui/hover-card';

export const columns: ColumnDef<IExpandedCampaign>[] = [
  {
    accessorKey: 'name',
    header: ({ column }) => {
      return (
        <Button variant="ghost" className="px-0 mb-0" onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}>
          Campaign Name
          <ArrowUpDown className="ml-2 h-4 w-4" />
        </Button>
      );
    },
  },
  {
    accessorKey: 'created',
    header: ({ column }) => {
      return (
        <Button variant="ghost" className="px-0 mb-0" onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}>
          Created At
          <ArrowUpDown className="ml-2 h-4 w-4" />
        </Button>
      );
    },
    cell: ({ row }) => {
      return dayjs(row.getValue('created')).format('MMMM D, YYYY h:mm A');
    },
  },

  {
    accessorKey: 'expand.leads_list',
    header: ({ column }) => {
      return (
        <Button variant="ghost" className="px-0 mb-0" onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}>
          List Name
          <ArrowUpDown className="ml-2 h-4 w-4" />
        </Button>
      );
    },
    cell: ({ row }) => {
      const leads_list = [row.original.expand?.leads_list].flat();
      return (
        <div className="flex flex-col">
          {leads_list.map((leads) => (
            <Link className="group hover:underline" href={`list/view-list?listId=${leads.id}&listName=${leads.name}`} target="_blank">
              <div className="flex gap-2 items-center">
                {leads.name}
                <ExternalLink className="h-4 w-4 text-gray-700 group-hover:text-black" />
              </div>
            </Link>
          ))}
        </div>
      );
    },
  },
  {
    accessorKey: 'type',
    header: ({ column }) => {
      return (
        <Button variant="ghost" className="px-0 mb-0" onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}>
          Type
          <ArrowUpDown className="ml-2 h-4 w-4" />
        </Button>
      );
    },
    cell: ({ row }) => {
      dayjs.extend(relativeTime);
      if (row.original.type == 'Scheduled') {
        return (
          <HoverCard openDelay={0}>
            <HoverCardTrigger>
              <div className="underline decoration-dotted decoration-emerald-600">Scheduled {dayjs().to(row.original.scheduled_time)}</div>
            </HoverCardTrigger>
            <HoverCardContent>Scheduled Time: {dayjs(row.original.scheduled_time).format('MMMM D, YYYY h:mm A')}</HoverCardContent>
          </HoverCard>
        );
      }
      return <>{row.original.type}</>;
    },
  },
  {
    accessorKey: 'expand.template.template_name',
    header: ({ column }) => {
      return (
        <Button variant="ghost" className="px-0 mb-0" onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}>
          Template
          <ArrowUpDown className="ml-2 h-4 w-4" />
        </Button>
      );
    },
  },
  {
    id: 'actions',
    size: 50,
    minSize: 50,
    maxSize: 50,
    cell: ({ row }) => {
      const leads_list_id = row.original.leads_list.map((id) => `list=${id}`).join('&');
      const template_id = row.original.template;
      return (
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="ghost" className="h-8 w-8 p-0 hover:bg-blue-500">
              <span className="sr-only">Open menu</span>
              <MoreHorizontal className="h-4 w-4" />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end">
            <DropdownMenuLabel>Actions</DropdownMenuLabel>
            <Link prefetch={false} href={`campaign/create/${template_id}/summary?${leads_list_id}`}>
              <DropdownMenuItem>Duplicate Campaign</DropdownMenuItem>
            </Link>
            <Link prefetch={false} href={`campaign/${row.id}`}>
              <DropdownMenuItem>View Campaign</DropdownMenuItem>
            </Link>
          </DropdownMenuContent>
        </DropdownMenu>
      );
    },
  },
];
