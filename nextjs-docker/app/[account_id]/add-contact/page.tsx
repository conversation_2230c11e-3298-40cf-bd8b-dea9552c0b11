import { Toaster } from '@/components/ui/toaster';
import { getAllLists, getAllTags } from '@/lib/pocket';
import { AddContact } from './component/AddContact';

export default async ({ params, searchParams }: { params: Record<string, string>; searchParams: Record<string, string> }) => {
  try {
    const tagsOptions = await getAllTags(params.account_id);
    const allList = await getAllLists(params.account_id);
    return (
      <>
        <AddContact accountId={params.account_id} tagsOptions={tagsOptions} allList={allList} />
        <Toaster />
      </>
    );
  } catch (error) {
    console.log(error);
    return <div>Lead not found</div>;
  }
};
