/**
 * This code was generated by v0 by Vercel.
 * @see https://v0.dev/t/Cs4RlbD5ER0
 * Documentation: https://v0.dev/docs#integrating-generated-code-into-your-nextjs-app
 */

'use client';

import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from '@/components/ui/alert-dialog';
import { Breadcrumb, BreadcrumbItem, BreadcrumbLink, BreadcrumbList, BreadcrumbSeparator } from '@/components/ui/breadcrumb';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { InputWithPhoneCode2 } from '@/components/ui/inputWithPhoneCode2';
import { Label } from '@/components/ui/label';
import MultipleSelector, { Option } from '@/components/ui/multiple-selector';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { useToast } from '@/components/ui/use-toast';
import { createContact } from '@/lib/pocket';
import { IList } from '@/lib/types';
import { Loader2 } from 'lucide-react';
import Link from 'next/link';
import { useRouter, useSearchParams } from 'next/navigation';
import { useRef, useState } from 'react';

interface CampaignArray {
  name: string;
  id: string;
  date_and_time: Date;
  leadReply: any;
}

[];

export function AddContact({ accountId, tagsOptions, allList }: { accountId: string; tagsOptions: Option[]; allList: IList[] }) {
  const searchParams = useSearchParams();
  const id = searchParams.get('id') || '';
  const router = useRouter();
  const [newListids, setNewListids] = useState<Option[]>([]);
  const [isSaving, setIsSaving] = useState(false);
  const [tags, setTags] = useState<Option[]>([]);
  const [numberInput, setNumberInput] = useState<string>('');
  const [numberCode, setNumberCode] = useState<{ country: string; code: string; emoji: string; phoneLength: number; iso: string }>({
    country: 'United Arab Emirates',
    code: '+971',
    emoji: '🇦🇪',
    phoneLength: 9,
    iso: 'AE',
  });
  const [error, setError] = useState<{ val: boolean; message: string }>({
    val: false,
    message: '',
  });

  const { toast } = useToast();
  const formRef = useRef<HTMLFormElement>(null);

  return (
    <div className="p-6 bg-gray-100 min-h-dvh">
      <div className="flex items-center mb-6">
        <div className="flex-1">
          <Breadcrumb>
            <BreadcrumbList>
              <BreadcrumbItem>
                <BreadcrumbLink className="font-bold text-2xl" asChild>
                  <Link href={`/${accountId}/lead-management`}>Contact List</Link>
                </BreadcrumbLink>
              </BreadcrumbItem>
              <BreadcrumbSeparator className="h-6 w-6" />

              <BreadcrumbItem className="font-bold text-2xl text-black">Add Contact</BreadcrumbItem>
            </BreadcrumbList>
          </Breadcrumb>
        </div>
        <div className="flex items-center space-x-2">
          {/* <Link prefetch={false} href={`/${accountId}/lead-management`}>
          <Button variant="default">Go to contacts list</Button>
        </Link> */}
        </div>
      </div>

      <form
        autoComplete="off"
        ref={formRef}
        action={async (formAction) => {
          setIsSaving(true);
          const addNewIds = newListids.length > 0 ? newListids.map((list) => list.value) : [];
          const country = numberCode.iso;
          const phone_number = numberCode?.code?.replace(/^\+/, '') + numberInput;
          if (numberInput && numberInput?.length != numberCode.phoneLength) {
            toast({
              variant: 'destructive',
              description: `The length of the phone number should be ${numberCode.phoneLength}`,
            });
            setError({ val: true, message: `The length of the phone number should be ${numberCode.phoneLength}` });
            setIsSaving(false);
            return;
          } else {
            setError({ val: false, message: '' });
          }
          const record = await createContact(id, accountId, formAction, country, phone_number, tags, addNewIds);
          if (record.status == 200) {
            toast({
              variant: 'success',
              description: 'The contact has been created successfully',
            });
            setIsSaving(false);
            router.push(`/${accountId}/lead-management`);
          } else if (record.status == 400) {
            toast({
              variant: 'destructive',
              description: 'The phone number is already in use.',
            });
          } else {
            toast({
              variant: 'destructive',
              description: 'There was an error in creating the contact.',
            });
          }
          setIsSaving(false);
        }}
        className="w-full"
      >
        <div className="mx-auto max-w-3xl">
          <div className="space-y-6">
            <Card>
              <CardHeader className="flex flex-col space-y-2">
                <CardTitle>Add Contact</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="name">Name</Label>
                  <span className="text-red-500 ml-1">*</span>
                  <Input id="name" name="name" placeholder="Enter name" required autoComplete="off" />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="phone">Phone</Label>
                  <span className="text-red-500 ml-1">*</span>
                  <InputWithPhoneCode2
                    numberInput={numberInput}
                    setNumberInput={setNumberInput}
                    placeholder="Whatsapp Number"
                    type="tel"
                    name="phoneNumber"
                    numberCode={numberCode}
                    setNumberCode={setNumberCode}
                    formRef={formRef}
                    className="border-gray-400"
                    required
                  />
                  {error && <div className="text-sm text-red-500">{error.message}</div>}
                </div>

                <div className="space-y-2">
                  <Label htmlFor="tags">Tags</Label>
                  <MultipleSelector
                    onChange={setTags}
                    creatable
                    badgeClassName="bg-gray-600 text-white"
                    showTagIcon
                    value={tags}
                    defaultOptions={tagsOptions}
                    placeholder="Add or remove tags"
                    emptyIndicator={<p className="text-center bg-white text-lg leading-10 text-gray-600 dark:text-gray-400">no results found.</p>}
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="phone">Add Lead to a list (Optional)</Label>
                  <MultipleSelector
                    onChange={setNewListids}
                    value={newListids}
                    defaultOptions={allList.map((list) => ({ label: list.name, value: list.id }))}
                    placeholder="Select list to add lead into"
                    emptyIndicator={<p className="text-center text-lg leading-10 text-gray-600 dark:text-gray-400">no results found.</p>}
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="status">Status</Label>
                  <Select name="status">
                    <SelectTrigger className="border-gray-400 border-2 ">
                      <SelectValue placeholder="Select status" />
                    </SelectTrigger>
                    <SelectContent position="popper">
                      <SelectItem value="New">New</SelectItem>
                      <SelectItem value="Active">Active</SelectItem>
                      <SelectItem value="Inactive">Inactive</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="flex items-center justify-center">
                  <AlertDialog>
                    <AlertDialogTrigger asChild>
                      {isSaving ? (
                        <Button disabled variant={'secondary'}>
                          Creating <Loader2 className="ml-2 animate-spin" />
                        </Button>
                      ) : (
                        <Button variant={'secondary'} type="button">
                          Create Contact
                        </Button>
                      )}
                    </AlertDialogTrigger>
                    <AlertDialogContent>
                      <AlertDialogHeader>
                        <AlertDialogTitle>Create Contact</AlertDialogTitle>
                        <AlertDialogDescription>Are you sure you want to creat the contact.</AlertDialogDescription>
                      </AlertDialogHeader>
                      <AlertDialogFooter>
                        <AlertDialogCancel>Cancel</AlertDialogCancel>
                        <AlertDialogAction
                          onClick={() => {
                            setIsSaving(true);
                            formRef.current?.requestSubmit();
                          }}
                        >
                          Yes
                        </AlertDialogAction>
                      </AlertDialogFooter>
                    </AlertDialogContent>
                  </AlertDialog>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </form>
    </div>
  );
}
