'use client';

import { IExpandedLead, Meta } from '@/lib/types';
import * as React from 'react';

import { Table } from '@tanstack/react-table';

import countrycodes from '@/lib/countrylistfiltered.json';

import { DropdownFilter } from '@/components/dropdown-filter';
import { Button } from '@/components/ui/button';
import {
  DropdownMenu,
  DropdownMenuCheckboxItem,
  DropdownMenuContent,
  DropdownMenuRadioGroup,
  DropdownMenuRadioItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import MultipleSelector, { Option } from '@/components/ui/multiple-selector';
import { Switch } from '@/components/ui/switch';
import { getAllTags } from '@/lib/pocket';
import { ChevronDown, TrashIcon, X } from 'lucide-react';
import { useParams } from 'next/navigation';
import { useUrlFilters } from '@/hooks/use-url-filters';

const LeadManagementFilters = ({ table, metaKeys }: { table: Table<IExpandedLead>; metaKeys: Meta[] }) => {
  const [filterReset, setFilterReset] = React.useState(0);
  const [options, setOptions] = React.useState<Option[]>([]);
  const { account_id } = useParams();

  const {
    getFilterValue,
    getFilterArray,
    handleNameSearch,
    handlePhoneSearch,
    handleStatusFilter,
    handleActiveFilter,
    handleOptOutFilter,
    handleTagsFilter,
    handleCountryFilter,
    clearAllFilters,
  } = useUrlFilters();

  const activeLeads = getFilterValue('active');
  const optOutLeads = getFilterValue('optout');
  const leadStatus = getFilterValue('status');

  let countryParams = getFilterArray('country', ' || country=');
  let _countryCodes = countrycodes.filter((country) => countryParams?.includes(country.iso['alpha-2']));
  let defaultCountryValues = _countryCodes.map((country) => ({ label: `${country.iso['alpha-2']} - ${country.name}`, value: country.iso['alpha-2'] }));

  let tagParams = getFilterArray('tags', ' && tags~');
  let defaultTagParamValues = tagParams?.map((tag) => ({ label: tag, value: tag })) ?? [];
  const nameInputRef = React.useRef<HTMLInputElement>(null);
  const phoneInputRef = React.useRef<HTMLInputElement>(null);

  return (
    <div className="sticky top-0 bg-white z-10">
      <div className="flex flex-col py-2 sm:py-4 space-y-3 sm:space-y-4">
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-3 sm:gap-4">
          {' '}
          <Input
            ref={nameInputRef}
            placeholder="Filter by name"
            type="search"
            defaultValue={getFilterValue('name')?.toString()}
            onChange={(e) => {
              handleNameSearch(e.target.value, () => {
                nameInputRef.current?.focus();
              });
            }}
            className="w-full"
          />
          <Input
            ref={phoneInputRef}
            placeholder="Filter by phone number"
            type="search"
            defaultValue={getFilterValue('phone_number')?.toString()}
            onChange={(e) => {
              handlePhoneSearch(e.target.value, () => {
                phoneInputRef.current?.focus();
              });
            }}
            className="w-full"
          />
          {/* Filter by tags */}
          <MultipleSelector
            className="w-full"
            commandProps={{ className: 'w-full' }}
            triggerSearchOnFocus
            delay={1000}
            inputProps={{
              onFocus: async () => {
                const res = await getAllTags(account_id as string);
                setOptions(res);
              },
              className: 'w-full',
            }}
            loadingIndicator={<p className="text-center bg-white text-lg leading-10 text-gray-600 dark:text-gray-400">Loading...</p>}
            onChange={(option) => {
              handleTagsFilter(option.map((item) => item.value));
            }}
            defaultOptions={defaultTagParamValues}
            value={defaultTagParamValues}
            options={options}
            badgeClassName="bg-gray-600 text-white"
            showTagIcon
            placeholder="Filter by tags"
            emptyIndicator={<p className="text-center bg-white text-lg leading-10 text-gray-600 dark:text-gray-400">no results found.</p>}
          />
          <MultipleSelector
            className="w-full bg-white"
            commandProps={{ className: 'w-full' }}
            defaultOptions={countrycodes.map((country) => ({ label: `${country.iso['alpha-2']} - ${country.name}`, value: country.iso['alpha-2'] }))}
            placeholder="Search for Country"
            hidePlaceholderWhenSelected
            onSearch={(term) => {
              const results = countrycodes.filter((country) => {
                return (
                  country.name.toLowerCase().includes(term.toLowerCase()) ||
                  country.iso['alpha-2'].toLowerCase().includes(term.toLowerCase()) ||
                  country.iso['alpha-3'].toLowerCase().includes(term.toLowerCase())
                );
              });
              return Promise.resolve(results.map((country) => ({ label: country.name, value: country.iso['alpha-2'] })));
            }}
            showFlagIcon
            badgeClassName="bg-slate-200 text-slate-800"
            onChange={(option) => {
              handleCountryFilter(option.map((item) => item.value));
            }}
            value={defaultCountryValues}
          />
          {/* End Filter by tags */}
        </div>
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-3 sm:gap-4">
          {/* Filter by status */}
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="outline" className="w-full">
                {leadStatus ?? 'Filter by status'}
                <ChevronDown className="ml-2 h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>{' '}
            {leadStatus && (
              <Button onClick={() => handleStatusFilter('')} variant="link" className="w-20">
                <X />
              </Button>
            )}
            <DropdownMenuContent className="w-full">
              <DropdownMenuRadioGroup value={leadStatus ?? ''} onValueChange={(value) => handleStatusFilter(value)}>
                <DropdownMenuRadioItem value="New">New</DropdownMenuRadioItem>
                <DropdownMenuRadioItem value="Prospect">Prospect</DropdownMenuRadioItem>
                <DropdownMenuRadioItem value="Opportunity">Opportunity</DropdownMenuRadioItem>
              </DropdownMenuRadioGroup>
            </DropdownMenuContent>
          </DropdownMenu>
          {/* End Filter by status */}
          {/* Meta filters */}
          <div className="relative w-full bg-white">
            <DropdownFilter key={filterReset} table={table} metaKeys={metaKeys} />
          </div>{' '}
          <div className="flex items-center space-x-2">
            <Switch
              id="airplane-mode"
              defaultChecked={activeLeads == 'true'}
              onCheckedChange={(e: boolean) => {
                handleActiveFilter(e);
              }}
            />
            <Label htmlFor="airplane-mode">Showing {activeLeads == null ? 'all' : activeLeads == 'false' ? 'Inactive' : 'Active'} Leads</Label>
          </div>
          <div className="flex items-center space-x-2">
            <Switch
              id="airplane-mode"
              defaultChecked={optOutLeads == 'true'}
              onCheckedChange={(e: boolean) => {
                handleOptOutFilter(e);
              }}
            />
            <Label htmlFor="airplane-mode">Show opted out Leads</Label>
          </div>
          <div className="flex items-center space-x-2">
            {/* View Columns Dropdown */}
            <DropdownMenu>
              <DropdownMenuTrigger asChild className="max-w-[220px]">
                <Button variant="outline">
                  View Columns <ChevronDown className="ml-2 h-4 w-4" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end">
                {table
                  .getAllColumns()
                  .filter((column) => column.getCanHide())
                  .map((column) => {
                    return (
                      <DropdownMenuCheckboxItem
                        onSelect={(e) => e.preventDefault()}
                        key={column.id}
                        className="capitalize"
                        checked={column.getIsVisible()}
                        onCheckedChange={(value) => column.toggleVisibility(!!value)}
                      >
                        {column.id}
                      </DropdownMenuCheckboxItem>
                    );
                  })}
              </DropdownMenuContent>
            </DropdownMenu>
            {/* End View Columns`` */}{' '}
            <Button variant="ghost" className="text-red-500 hover:text-red-700 hover:bg-red-50  w-full sm:w-auto" onClick={clearAllFilters}>
              <TrashIcon className="text-red-500 h-4 w-4 mr-2" />
              Clear Filters
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default React.memo(LeadManagementFilters);
