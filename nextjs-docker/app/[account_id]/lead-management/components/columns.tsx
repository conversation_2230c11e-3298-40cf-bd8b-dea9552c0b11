'use client';

import { Button } from '@/components/ui/button';
import { Country, IExpandedLead, Meta } from '@/lib/types';
import { ColumnDef } from '@tanstack/react-table';
import relativeTime from 'dayjs/plugin/relativeTime';
import { ArrowUpDown, Info } from 'lucide-react';

import dayjs from 'dayjs';
// import utc from 'dayjs/plugin/utc'
import { Badge } from '@/components/ui/badge';
import { HoverCard, HoverCardContent, HoverCardTrigger } from '@/components/ui/hover-card';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
import { convertFirstToUpperCase } from '@/lib/utils';
import Link from 'next/link';
import LeadsDropDownAction from './LeadsDropDownAction';

const getMetaColumns = (metaKeys: Meta[]) => {
  return metaKeys.map((metaKey) => {
    return {
      // accessorKey: `meta.${metaKey.key}`,
      id: metaKey.key.toString(),
      maxSize: 150,
      header: ({ column }: { column: any }) => {
        return (
          <Button variant="ghost" className="px-0 mb-0">
            {metaKey.key}
          </Button>
        );
      },
    };
  });
};

export const columns = (metaKeys: Meta[]): ColumnDef<IExpandedLead>[] => {
  dayjs.extend(relativeTime);
  return [
    {
      accessorKey: 'name',
      maxSize: 220,
      minSize: 220,
      size: 220,
      header: ({ column }) => {
        return (
          <Button variant="ghost" className="px-0 mb-0" onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}>
            Name
            <ArrowUpDown className="ml-2 h-4 w-4" />
          </Button>
        );
      },
      cell: ({ row }) => {
        return <div className="font-medium">{row.original.name}</div>;
      },
    },
    {
      accessorKey: 'phone_number',
      id: 'Phone Number',
      maxSize: 125,
      header: ({ column }) => {
        return (
          <Button variant="ghost" className="px-0 mb-0" onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}>
            Phone Number
            <ArrowUpDown className="ml-2 h-4 w-4" />
          </Button>
        );
      },
    },
    {
      accessorKey: 'status',
      maxSize: 300,
      size: 100,
      header: ({ column }) => {
        return (
          <Button variant="ghost" className="px-0 mb-0" onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}>
            Status
            <ArrowUpDown className="ml-2 h-4 w-4" />
          </Button>
        );
      },
    },
    {
      accessorKey: 'tags',
      filterFn: 'arrIncludesSome',
      maxSize: 300,
      header: ({ column }) => {
        return (
          <Button variant="ghost" className="px-0 mb-0" onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}>
            Tags
            <ArrowUpDown className="ml-2 h-4 w-4" />
          </Button>
        );
      },
      cell: ({ row }) => {
        const tags = row.original.tags;
        return (
          <HoverCard>
            <HoverCardTrigger asChild>
              <div className="flex items-center hover:cursor-help flex-wrap gap-2">
                {tags?.length > 0 ? (
                  tags.slice(0, 3).map((tag, index) => (
                    <div
                      key={index}
                      className="inline-flex items-center whitespace-nowrap border px-2 bg-secondary text-secondary-foreground rounded-lg border-solidfont-normal"
                    >
                      {convertFirstToUpperCase(tag)}
                    </div>
                  ))
                ) : (
                  <div>N/A</div>
                )}
                <div className="underline decoration-dotted decoration-green-600 font-semibold">{tags?.length > 3 && `+${tags?.length - 3}`}</div>
              </div>
            </HoverCardTrigger>
            <HoverCardContent className="w-80">
              <div className="mb-2">All Tags:</div>
              <div className="flex gap-2 flex-wrap">
                {tags?.map((tag, index) => (
                  <div
                    key={index}
                    className="inline-flex items-center whitespace-nowrap border px-2 bg-secondary text-secondary-foreground rounded-lg border-solidfont-normal"
                  >
                    {convertFirstToUpperCase(tag)}
                  </div>
                ))}
              </div>
            </HoverCardContent>
          </HoverCard>
        );
      },
    },
    ...getMetaColumns(metaKeys),
    {
      accessorKey: 'created',
      maxSize: 230,
      size: 230,
      minSize: 230,
      header: ({ column }) => {
        return (
          <Button variant="ghost" className="px-0 mb-0" onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}>
            Created At
            <ArrowUpDown className="ml-2 h-4 w-4" />
          </Button>
        );
      },
      cell: ({ row }) => {
        return (
          <TooltipProvider delayDuration={0}>
            <Tooltip>
              <TooltipTrigger asChild>
                <div className="hover:cursor-help">{dayjs(row.getValue('created')).fromNow()}</div>
              </TooltipTrigger>
              <TooltipContent>
                <p>{dayjs(row.getValue('created')).format('MMMM D, YYYY h:mm A')}</p>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>
        );
      },
    },
    {
      accessorKey: 'updated',
      maxSize: 230,
      size: 230,
      minSize: 230,
      header: ({ column }) => {
        return (
          <Button variant="ghost" className="px-0 mb-0" onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}>
            Updated At
            <ArrowUpDown className="ml-2 h-4 w-4" />
          </Button>
        );
      },
      cell: ({ row }) => {
        return (
          <TooltipProvider delayDuration={0}>
            <Tooltip>
              <TooltipTrigger asChild>
                <div className="hover:cursor-help">{dayjs(row.getValue('updated')).fromNow()}</div>
              </TooltipTrigger>
              <TooltipContent>
                <p>{dayjs(row.getValue('updated')).format('MMMM D, YYYY h:mm A')}</p>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>
        );
      },
    },
    {
      accessorKey: 'expand.created_by.name',
      id: 'Created By',
      maxSize: 300,
      size: 120,
      header: ({ column }) => {
        return (
          <Button variant="ghost" className="px-0 mb-0" onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}>
            Created By
            <ArrowUpDown className="ml-2 h-4 w-4" />
          </Button>
        );
      },
      cell: ({ row }) => {
        return row.original.expand?.created_by?.name ?? 'System';
      },
    },
    {
      accessorKey: 'country',
      id: 'Country',
      maxSize: 300,
      header: ({ column }) => {
        return (
          <Button variant="ghost" className="px-0 mb-0" onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}>
            Country
            <ArrowUpDown className="ml-2 h-4 w-4" />
          </Button>
        );
      },
      cell: ({ row }) => {
        return <div>{row.original?.country ?? 'N/A'}</div>;
      },
    },
    {
      accessorKey: 'opt_out',
      id: 'Is Opted',
      minSize: 150,
      size: 150,
      header: ({ column }) => {
        return (
          <Button variant="ghost" className="px-0 mb-0" onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}>
            Is Opted
            <ArrowUpDown className="ml-2 h-4 w-4" />
          </Button>
        );
      },
      cell: ({ row }) => {
        if (!row.original.opt_out) {
          return <>Opted In</>;
        } else {
          return (
            <HoverCard openDelay={0}>
              <HoverCardTrigger asChild>
                <div className="flex space-x-2 items-center hover:cursor-help">
                  <Badge className="rounded-full" variant={'destructive'}>
                    Opted Out
                  </Badge>
                  <Info className="h-4 w-4 text-destructive" />
                </div>
              </HoverCardTrigger>
              <HoverCardContent className="text-xs">
                <div>User has clicked on "Stop Promotions" button.</div>
                <div>User does not want to receive promotion content</div>
              </HoverCardContent>
            </HoverCard>
          );
        }
      },
    },
    {
      id: 'Blocked',
      maxSize: 120,
      header: () => {
        return <div>Is Blocked</div>;
      },
      cell: ({ row }) => {
        return (
          <div>
            {row.original.blocked ? (
              <Badge className="rounded-full" variant={'destructive'}>
                Blocked
              </Badge>
            ) : (
              <Badge className="rounded-full" variant={'secondary'}>
                Not Blocked
              </Badge>
            )}
          </div>
        );
      },
    },
    {
      id: 'live Chat Btn',
      maxSize: 120,
      header: () => {
        return <div>Live Chat Link</div>;
      },
      cell: ({ row }) => {
        return (
          <div className="flex items-center space-x-2">
            <div className="flex-1">
              <Link href={`/${row.original.account}/live-chat?user_id=${row.original.id}`}>
                <Button variant="outline" className="bg-white" size={'sm'}>
                  Live Chat
                </Button>
              </Link>
            </div>
          </div>
        );
      },
    },
    {
      accessorKey: 'active',
      enablePinning: true,
      header: ({ column }) => {
        return (
          <Button variant="ghost" className="px-0 mb-0" onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}>
            State
            <ArrowUpDown className="ml-2 h-4 w-4" />
          </Button>
        );
      },
      cell: ({ row }) => {
        return <div>{row.original.active.toString()}</div>;
      },
    },
    {
      id: 'actions',
      size: 50,
      minSize: 50,
      maxSize: 50,
      cell: ({ row, table }) => {
        return (
          <LeadsDropDownAction
            row={row}
            userType={table.options.meta?.userType ?? ''}
            agentRoles={table.options.meta?.agentRoles ?? []}
            isActive={row.original.active}
          />
        );
      },
    },
  ];
};
