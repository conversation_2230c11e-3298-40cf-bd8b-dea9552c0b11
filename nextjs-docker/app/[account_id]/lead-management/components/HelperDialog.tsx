'use client';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { ChevronLeft, ChevronRight, InfoIcon } from 'lucide-react';
import Image from 'next/image';
import { useState } from 'react';

const helpItems = [
  {
    title: 'Adding Contacts',
    description: 'Click add contact button to navigate to add contact page.',
    src: '/assets/gif/lead-management/add_contact.gif',
  },
  {
    title: 'Filter by fields',
    description: 'Type in the fields to filter contacts',
    src: '/assets/gif/lead-management/filter_by_fields.gif',
  },
  {
    title: 'Show active/inactive leads',
    description: 'Toggle between active and inactive leads',
    src: '/assets/gif/lead-management/show_active_leads.gif',
  },
  {
    title: 'Show opted out leads',
    description: 'Toggle between leads who are opted in or opted out',
    src: '/assets/gif/lead-management/show_opted_out.gif',
  },
  {
    title: 'Hover Tags',
    description: 'Hover over the tags to see all available tags assigned to the lead',
    src: '/assets/gif/lead-management/tags_hover.gif',
  },
  {
    title: 'Sort Columns',
    description: 'Click on column heads to sort the columns by ascending or descending',
    src: '/assets/gif/lead-management/toggle_column_sort.gif',
  },
  {
    title: 'Row Actions',
    description: 'Click on row actions to see more',
    src: '/assets/gif/lead-management/actions.gif',
  },
  // Add more help items as needed
];

export default function HelperDialog() {
  const [isOpen, setIsOpen] = useState(false);
  const [currentIndex, setCurrentIndex] = useState(0);

  const nextSlide = () => {
    setCurrentIndex((prevIndex) => (prevIndex + 1) % helpItems.length);
  };

  const prevSlide = () => {
    setCurrentIndex((prevIndex) => (prevIndex - 1 + helpItems.length) % helpItems.length);
  };

  return (
    <div className="">
      <Button variant="link" className="text-primary hover:text-primary/80" onClick={() => setIsOpen(true)}>
        <InfoIcon className="h-5 w-5 mr-2" />
        Show Help
      </Button>
      <Dialog
        open={isOpen}
        onOpenChange={(e) => {
          setCurrentIndex(0);
          setIsOpen(e);
        }}
      >
        <DialogContent className="max-w-full w-9/12 h-[90vh] flex flex-col overflow-auto">
          <DialogHeader>
            <DialogTitle>{helpItems[currentIndex].title}</DialogTitle>
            <DialogDescription>
              <div>
                Help {currentIndex + 1} of {helpItems.length}
              </div>
              <div>{helpItems[currentIndex].description}</div>
            </DialogDescription>
          </DialogHeader>
          <div className="flex-grow flex flex-col">
            <Image
              src={helpItems[currentIndex].src}
              alt={helpItems[currentIndex].title}
              className="w-full h-auto object-contain flex-grow"
              width={100}
              height={100}
              unoptimized={true}
            />
            <div className="flex justify-between mt-4">
              <Button variant="outline" onClick={prevSlide}>
                <ChevronLeft className="h-4 w-4 mr-2" /> Back
              </Button>
              <Button variant="outline" onClick={nextSlide} disabled={currentIndex == helpItems.length - 1}>
                Next <ChevronRight className="h-4 w-4 ml-2" />
              </Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
}
