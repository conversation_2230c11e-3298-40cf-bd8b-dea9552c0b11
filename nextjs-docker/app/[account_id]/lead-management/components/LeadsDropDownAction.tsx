'use client';

import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuLabel, DropdownMenuTrigger } from '@/components/ui/dropdown-menu';

import { AlertDialog, AlertDialogCancel, AlertDialogContent, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle } from '@/components/ui/alert-dialog';
import { Button } from '@/components/ui/button';

import { HoverCard, HoverCardContent, HoverCardTrigger } from '@/components/ui/hover-card';
import { useToast } from '@/components/ui/use-toast';
import { toggleLeadState } from '@/lib/pocket';
import { IExpandedLead } from '@/lib/types';
import { useDialog } from '@/state/hooks/use-dialog';
import { Row } from '@tanstack/react-table';
import { CheckCircle, CircleHelp, CircleOff, Eye, Loader2, MoreHorizontal, Pencil } from 'lucide-react';
import Link from 'next/link';
import { useParams } from 'next/navigation';
import { useState } from 'react';

const LeadsDropDownAction = ({
  row,
  userType,
  agentRoles,
  isActive,
}: {
  row: Row<IExpandedLead>;
  userType: string;
  agentRoles: string[];
  isActive: boolean;
}) => {
  const params = useParams<{ account_id: string }>();
  const { toast } = useToast();
  const [loading, setLoading] = useState(false);
  const toggleLead = async (id: string, active: boolean) => {
    try {
      setLoading(true);
      await toggleLeadState(id, params.account_id, active);
      if (active) {
        toast({
          variant: 'success',
          description: 'Lead activated successfully',
        });
      } else {
        toast({
          variant: 'success',
          description: 'Lead deactivated successfully',
        });
      }
    } catch (error) {
      toast({
        variant: 'destructive',
        description: 'There was an error. Try again!',
      });
    }
    setLoading(false);
  };
  const activateLeadDialog = useDialog();
  const deactivateLeadDialog = useDialog();
  const isEditAllowed = userType == 'admin' || agentRoles.includes('Lead Management-Update Contacts');
  const isDeleteAllowed = userType == 'admin' || agentRoles.includes('Lead Management-Delete Contacts');

  return (
    <>
      <DropdownMenu modal={false}>
        <DropdownMenuTrigger asChild disabled={row.original.status == 'PENDING'}>
          <Button variant="ghost" className="h-8 w-8 p-0 hover:bg-gray-100 mb-0">
            <span className="sr-only">Open menu</span>
            <MoreHorizontal className="h-4 w-4" />
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent align="end">
          <DropdownMenuLabel>Actions</DropdownMenuLabel>
          <Link href={`/${params.account_id}/view-contact?id=${row.id}`}>
            <DropdownMenuItem>
              <Eye className="mr-2 h-4 w-4 text-blue-600" />
              <span className="text-blue-600">View</span>
            </DropdownMenuItem>
          </Link>
          {isEditAllowed ? (
            !isActive ? (
              <HoverCard>
                <HoverCardTrigger>
                  <DropdownMenuItem disabled>
                    Edit Lead <CircleHelp className="ml-2 h-4 w-4" />
                  </DropdownMenuItem>
                </HoverCardTrigger>
                <HoverCardContent className="text-xs">
                  <div>Inactive Leads are not editable</div>
                </HoverCardContent>
              </HoverCard>
            ) : (
              <Link href={`/${params.account_id}/edit-contact?id=${row.id}`}>
                <DropdownMenuItem>
                  <Pencil className="mr-2 h-4 w-4 text-green-600" />
                  <span className="text-green-600">Edit</span>
                </DropdownMenuItem>
              </Link>
            )
          ) : (
            <></>
          )}

          {isDeleteAllowed ? (
            !isActive ? (
              <DropdownMenuItem {...activateLeadDialog.triggerProps}>
                <CheckCircle className="mr-2 h-4 w-4 text-green-600" />
                <span className="text-green-600">Activate</span>
              </DropdownMenuItem>
            ) : (
              <DropdownMenuItem {...deactivateLeadDialog.triggerProps}>
                <CircleOff className="mr-2 h-4 w-4 text-red-600" />
                <span className="text-red-600">Deactivate</span>
              </DropdownMenuItem>
            )
          ) : (
            <></>
          )}
        </DropdownMenuContent>
      </DropdownMenu>
      <AlertDialog {...deactivateLeadDialog.dialogProps}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Are you sure you want to deactivate the lead {row.original.name}?</AlertDialogTitle>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            {loading ? (
              <Button className="mb-0" disabled variant={'destructive'}>
                Deactivating
                <Loader2 className="ml-2 animate-spin" />
              </Button>
            ) : (
              <>
                <Button onClick={() => toggleLead(row.id, false)} variant={'destructive'} className="mb-0">
                  Yes
                </Button>
              </>
            )}
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>

      <AlertDialog {...activateLeadDialog.dialogProps}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Are you sure you want to activate the lead {row.original.name}?</AlertDialogTitle>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            {loading ? (
              <Button className="mb-0" disabled variant={'secondary'}>
                Activating
                <Loader2 className="ml-2 animate-spin" />
              </Button>
            ) : (
              <>
                <Button onClick={() => toggleLead(row.id, true)} variant={'secondary'} className="mb-0">
                  Yes
                </Button>
              </>
            )}
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </>
  );
};

export default LeadsDropDownAction;
