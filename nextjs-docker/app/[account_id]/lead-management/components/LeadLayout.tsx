'use client';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Sheet, SheetContent, SheetTrigger } from '@/components/ui/sheet';
import { Toaster } from '@/components/ui/toaster';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
import { ILog } from '@/lib/types';
import dayjs from 'dayjs';
import isToday from 'dayjs/plugin/isToday';
import isYesterday from 'dayjs/plugin/isYesterday';
import relativeTime from 'dayjs/plugin/relativeTime';
import weekday from 'dayjs/plugin/weekday';
import { Loader2Icon, MenuIcon, PlusCircle, Tag } from 'lucide-react';
import Link from 'next/link';
import { useParams } from 'next/navigation';
import { useRef } from 'react';
export const dynamic = 'force-dynamic';
export const fetchCache = 'default-no-store';

function SidebarContent({
  accountId,
  userType,
  agentRoles,
  groupedByDate,
  params,
}: {
  accountId: string;
  userType: string;
  agentRoles: string[];
  groupedByDate: Record<string, ILog[]>;
  params: any;
}) {
  return (
    <>
      <div id="upload-contact" className="flex flex-col gap-2">
        {(userType == 'admin' || agentRoles.includes('Lead Management-Create Contacts')) && (
          <Link href={`/${accountId}/add-contact`}>
            <Button variant={'secondary'} className="w-full">
              <PlusCircle className="mr-2 h-5 w-5" />
              Add Contact
            </Button>
          </Link>
        )}
        {(userType == 'admin' || agentRoles.includes('Lead Management-Import Contacts')) && (
          <Link href={`/${accountId}/upload-contact`}>
            <Button className="w-full mb-0">Bulk Import</Button>
          </Link>
        )}
        {(userType == 'admin' ||
          agentRoles.includes('Lead Management-View Lists') ||
          agentRoles.includes('Lead Management-Update Lists') ||
          agentRoles.includes('Lead Management-Delete Lists') ||
          agentRoles.includes('Lead Management-Create Lists')) && (
          <Link href={`/${accountId}/list`}>
            <Button className="w-full mb-0">Lists</Button>
          </Link>
        )}
      </div>
      <div className="mt-4 flex-1 min-h-0 flex flex-col">
        {(userType == 'admin' || agentRoles.includes('Lead Management-View Logs')) && (
          <>
            <div className="text-xl font-bold my-2">Log History</div>
            <div className="text-sm flex-1 min-h-0 overflow-y-auto flex flex-col">
              {Object.entries(groupedByDate).map(([date, logs]) => (
                <div key={date} className="p-2 bg-gray-50 rounded-md">
                  <div className="">
                    {date}{' '}
                    <span className="ml-2 rounded-full bg-primary/10 px-2 py-0.5 text-xs font-medium text-primary">
                      {logs.length} {logs.length === 1 ? 'log' : 'logs'}
                    </span>
                  </div>
                  {logs.map((log) => (
                    <div key={log.id}>
                      <TooltipProvider>
                        <Tooltip delayDuration={0}>
                          <TooltipTrigger asChild>
                            <Link
                              href={`/${accountId}/lead-management/${log.id}`}
                              className={`p-2 w-full text-sm items-center flex space-x-2 cursor-pointer rounded-md ${params.log_id === log.id ? 'bg-blue-100 text-blue-600' : 'hover:bg-gray-100'}`}
                            >
                              <Tag className="h-4 w-4 flex-shrink-0" />
                              <div className="text-xs truncate">{log.log_name}</div>
                              {!log.pending ? (
                                <Badge className="flex-shrink-0" variant={'secondary'}>
                                  {log.number_of_leads > 1000 ? (log.number_of_leads / 1000).toFixed(1) + 'k' : log.number_of_leads}
                                </Badge>
                              ) : null}
                              {log.pending ? (
                                <p>
                                  <Loader2Icon className="animate-spin" />
                                </p>
                              ) : null}
                            </Link>
                          </TooltipTrigger>
                          <TooltipContent>
                            <>
                              <p>Log Name: {log.log_name}</p>
                              <p>Number of Leads: {log.number_of_leads}</p>
                              {log.pending ? (
                                <p>
                                  <Loader2Icon className="animate-spin" />
                                </p>
                              ) : null}
                            </>
                          </TooltipContent>
                        </Tooltip>
                      </TooltipProvider>
                    </div>
                  ))}
                </div>
              ))}
            </div>
          </>
        )}
      </div>
    </>
  );
}

export function LeadLayout({
  accountId,
  logData,
  userType,
  agentRoles,
  children,
}: {
  accountId: string;
  logData: ILog[];
  userType: string;
  agentRoles: string[];
  children: any;
}) {
  dayjs.extend(relativeTime);
  dayjs.extend(weekday);
  dayjs.extend(isToday);
  dayjs.extend(isYesterday);

  const formatDate = (date: Date) => {
    const now = dayjs();
    const inputDate = dayjs(date);

    if (inputDate.isToday()) {
      return inputDate.format('hh:mm a');
    } else if (inputDate.isYesterday()) {
      return 'Yesterday';
    } else if (now.diff(inputDate, 'day') <= 5) {
      return inputDate.format('dddd');
    } else {
      return inputDate.format('DD/MM/YYYY');
    }
  };
  const groupedByDate = logData.reduce<Record<string, ILog[]>>((acc, item) => {
    const dateKey = formatDate(item.created);
    if (!acc[dateKey]) acc[dateKey] = [];
    acc[dateKey].push(item);
    return acc;
  }, {});
  const scrollRef = useRef<HTMLDivElement>(null);
  const params = useParams();
  const isSideBarViewAllowed =
    userType == 'admin' ||
    agentRoles.includes('Lead Management-Create Contacts') ||
    agentRoles.includes('Lead Management-Import Contacts') ||
    agentRoles.includes('Lead Management-View Lists') ||
    agentRoles.includes('Lead Management-Update Lists') ||
    agentRoles.includes('Lead Management-Delete Lists') ||
    agentRoles.includes('Lead Management-Create Lists') ||
    agentRoles.includes('Lead Management-View Logs');
  return (
    <div ref={scrollRef} className="bg-gray-100 min-h-full">
      <Toaster />
      <div className="flex gap-4 max-w-full">
        {/* Mobile sidebar as sheet */}
        {isSideBarViewAllowed && (
          <div className="lg:hidden bg-white h-screen p-2">
            <Sheet>
              <SheetTrigger asChild className="">
                <Button variant="outline" className="w-full p-2" size="icon">
                  <MenuIcon className="h-4 w-4" />
                </Button>
              </SheetTrigger>
              <SheetContent side="left" className="p-0 w-64 flex flex-col">
                <div className="p-4 flex flex-col h-full min-h-0">
                  <SidebarContent accountId={accountId} userType={userType} agentRoles={agentRoles} groupedByDate={groupedByDate} params={params} />
                </div>
              </SheetContent>
            </Sheet>
          </div>
        )}
        {/* Desktop sidebar */}
        {isSideBarViewAllowed && (
          <div className="hidden lg:block w-1/6 shadow-md bg-white p-2 max-w-[250px] h-screen overflow-y-auto">
            <SidebarContent accountId={accountId} userType={userType} agentRoles={agentRoles} groupedByDate={groupedByDate} params={params} />
          </div>
        )}
        <div className="flex-1 min-w-0">{children}</div>
      </div>
    </div>
  );
}
