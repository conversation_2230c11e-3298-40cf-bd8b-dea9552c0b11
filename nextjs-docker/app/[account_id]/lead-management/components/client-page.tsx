'use client';

import { IExpandedLead, Meta } from '@/lib/types';
import { columns } from './columns';
import { DataTable } from './data-table';
import { Option } from '@/components/ui/multiple-selector';

export const ClientPage = ({
  metaKeys,
  leads,
  totalpages,
  userType,
  agentRoles,
  page,
  perPage,
  totalItems,
}: {
  metaKeys: Meta[];
  leads: IExpandedLead[];
  totalpages: number;
  userType: string;
  agentRoles: string[];
  page: string;
  perPage: string;
  totalItems: number;
}) => {
  return (
    <DataTable
      key={leads.length}
      metaKeys={metaKeys}
      columns={columns(metaKeys)}
      data={leads}
      userType={userType}
      agentRoles={agentRoles}
      page={page}
      perPage={perPage}
      totalpages={totalpages}
      totalItems={totalItems}
    />
  );
};
