import { getAllLeadsFromLeadManagement, getAllMetaKeys } from '@/lib/pocket';
import { Meta, User } from '@/lib/types';
import { server_component_pb } from '@/state/pb/server_component_pb';
import { ClientPage } from './components/client-page';

export const dynamic = 'force-dynamic';

export const fetchCache = 'default-no-store';

export default async function Page({
  searchParams,
  params,
}: {
  searchParams?: {
    name?: string;
    phone_number?: string;
    country?: string;
    tags?: string;
    active?: string;
    optout?: string;
    status?: string;
    page?: string;
    perPage?: string;
    meta_filters?: string;
  };
  params: Record<string, string>;
}) {
  const page = searchParams?.page ?? '1';
  const perPage = searchParams?.perPage ?? '10';
  const active = searchParams?.active ? ` && active=${searchParams.active}` : '';
  const optout = searchParams?.optout ? ` && opt_out=${searchParams.optout}` : '';
  const phone_number = searchParams?.phone_number ? `&& phone_number~"${searchParams.phone_number}"` : '';
  const name = searchParams?.name ? `&& name~"${searchParams.name}"` : '';
  const country = searchParams?.country ? `&& (country=${searchParams?.country})` : '';
  const tags = searchParams?.tags ? `&& (tags~${searchParams?.tags})` : '';
  const status = searchParams?.status ? `&& status="${searchParams?.status}"` : '';
  const meta = searchParams?.meta_filters;
  const entries = (meta ? meta.split('&') : []).map((entry) => {
    const [key, value] = entry.split('=');
    return { key, value };
  });
  const _metaString = entries
    .map((entry) => {
      return `meta~"${entry.key}" && meta~"${entry.value}"`;
    })
    .join(' && ');
  const metaString = meta ? `&& ${_metaString}` : '';

  const { pb } = await server_component_pb();
  const user = pb.authStore.record as User;
  const startTime = new Date().getTime();

  let leads = await getAllLeadsFromLeadManagement(params.account_id, page, perPage, name, country, phone_number, tags, active, optout, status, metaString);

  const endTime = new Date().getTime();

  console.log('Time taken to fetch leads:', endTime - startTime, 'ms');

  const startTimeMeta = new Date().getTime();
  const metaKeys: Meta[] = await getAllMetaKeys(params.account_id);
  const agentRoles =
    user.expand?.roles?.map((role) => {
      return role.name;
    }) ?? [];

  const endTimeMeta = new Date().getTime();
  console.log('Time taken to fetch meta keys:', endTimeMeta - startTimeMeta, 'ms');
  return (
    <div className="w-full overflow-x-auto max-w-full">
      <ClientPage
        metaKeys={metaKeys}
        leads={leads.items}
        totalItems={leads.totalItems}
        totalpages={leads.totalPages}
        userType={user.type}
        agentRoles={agentRoles}
        page={page}
        perPage={perPage}
      />
    </div>
  );
}
