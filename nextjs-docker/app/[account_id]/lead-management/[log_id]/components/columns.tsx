'use client';

import { But<PERSON> } from '@/components/ui/button';
import { ILogData } from '@/lib/types';
import { ColumnDef } from '@tanstack/react-table';
import { ArrowUpDown, CircleCheck, CircleX } from 'lucide-react';

import dayjs from 'dayjs';
// import utc from 'dayjs/plugin/utc'
import { HoverCard, HoverCardContent, HoverCardTrigger } from '@/components/ui/hover-card';
import { convertFirstToUpperCase } from '@/lib/utils';
// import LeadsDropDownAction from "./LeadsDropDownAction"

export const columns: ColumnDef<ILogData>[] = [
  {
    accessorKey: 'name',
    size: 100,

    minSize: 75,
    header: ({ column }) => {
      return (
        <Button variant="ghost" className="px-0 mb-0" onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}>
          Name
          <ArrowUpDown className="ml-2 h-4 w-4" />
        </Button>
      );
    },
  },
  {
    accessorKey: 'phone_number',
    size: 95,

    minSize: 75,
    header: ({ column }) => {
      return (
        <Button variant="ghost" className="px-0 mb-0" onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}>
          Phone Number
        </Button>
      );
    },
  },
  {
    accessorKey: 'status',
    size: 100,
    maxSize: 300,
    minSize: 100,
    header: ({ column }) => {
      return (
        <Button variant="ghost" className="px-0 mb-0" onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}>
          Status
          <ArrowUpDown className="ml-2 h-4 w-4" />
        </Button>
      );
    },
  },
  {
    accessorKey: 'tags',
    filterFn: 'arrIncludesSome',
    size: 150,
    maxSize: 300,
    minSize: 150,
    header: ({ column }) => {
      return (
        <Button variant="ghost" className="px-0 mb-0" onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}>
          Tags
          <ArrowUpDown className="ml-2 h-4 w-4" />
        </Button>
      );
    },
    cell: ({ row }) => {
      const tags = row.original.tags;

      return (
        <HoverCard>
          <HoverCardTrigger asChild>
            <div className="flex space-x-1 items-center hover:cursor-help">
              {tags && tags?.length > 0 ? (
                tags.slice(0, 3).map((tag, index) => (
                  <div
                    key={index}
                    className="inline-flex items-center whitespace-nowrap border px-2 bg-secondary text-secondary-foreground rounded-lg border-solidfont-normal"
                  >
                    {convertFirstToUpperCase(tag)}
                  </div>
                ))
              ) : (
                <div>N/A</div>
              )}
              {tags && <div className="underline decoration-dotted decoration-green-600 font-semibold">{tags?.length > 3 && `+${tags?.length - 3}`}</div>}
            </div>
          </HoverCardTrigger>
          <HoverCardContent className="w-80">
            <div className="mb-2">All Tags:</div>
            <div className="flex gap-2 flex-wrap">
              {tags?.length &&
                tags?.map((tag, index) => (
                  <div
                    key={index}
                    className="inline-flex items-center whitespace-nowrap border px-2 bg-secondary text-secondary-foreground rounded-lg border-solidfont-normal"
                  >
                    {convertFirstToUpperCase(tag)}
                  </div>
                ))}
            </div>
          </HoverCardContent>
        </HoverCard>
      );
    },
  },
  {
    id: 'created',
    size: 100,
    minSize: 100,
    header: ({ column }) => {
      return (
        <Button variant="ghost" className="px-0 mb-0" onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}>
          Created At
          <ArrowUpDown className="ml-2 h-4 w-4" />
        </Button>
      );
    },
    cell: ({ row, table }) => {
      return dayjs(table.options.meta?.created).format('MMMM D, YYYY h:mm A');
    },
  },
  {
    id: 'created_by',
    maxSize: 300,
    minSize: 100,
    header: ({ column }) => {
      return (
        <Button variant="ghost" className="px-0 mb-0" onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}>
          Created By
          <ArrowUpDown className="ml-2 h-4 w-4" />
        </Button>
      );
    },
    cell: ({ table }) => {
      return <>{table.options.meta?.created_by?.name}</>;
    },
  },
  {
    id: 'actions',
    size: 50,
    minSize: 50,
    maxSize: 50,
    cell: ({ row, table }) => {
      return (
        <>
          {row.original.upload == 'successful' ? (
            <HoverCard>
              <HoverCardTrigger>
                <CircleCheck className="text-green-800 hover:cursor-help" />
              </HoverCardTrigger>
              <HoverCardContent>Uploaded Successful</HoverCardContent>
            </HoverCard>
          ) : (
            <HoverCard>
              <HoverCardTrigger>
                <CircleX className="text-red-800 hover:cursor-help" />
              </HoverCardTrigger>
              <HoverCardContent>{row.original.rejection_reason ?? 'Failed'}</HoverCardContent>
            </HoverCard>
          )}
        </>
      );
    },
  },
];
