'use client';

import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from '@/components/ui/card';
import { Progress } from '@/components/ui/progress';
import { IPendingContacts } from '@/lib/types';
import { pb_url } from '@/state/consts';
import PocketBase from 'pocketbase';
import { useEffect, useState } from 'react';

const LiveLoading = ({ pendingItem, pendingLogs }: { pendingItem: IPendingContacts; pendingLogs: any[] }) => {
  const [uploadResult, setUploadResult] = useState<{
    pendingItems: number;
    totalLeads: number;
    numberSuccessfull: number;
    numberFailed: number;
    failedLeads?: { phone_number: string; reason: string }[];
  }>({
    pendingItems: pendingItem.total_items - pendingLogs.length,
    totalLeads: pendingItem.total_items,
    numberSuccessfull: (pendingLogs as unknown as any[]).filter((i) => i.status === 'success').length,
    numberFailed: (pendingLogs as unknown as any[]).filter((i) => i.status === 'failure').length,
  });
  useEffect(() => {
    let i: any;
    const pb = new PocketBase(pb_url);
    pb.authStore.loadFromCookie(document.cookie);
    pb.autoCancellation(false);

    const effect = async () => {
      i = setInterval(async () => {
        try {
          let pendingLogs = await pb.collection('pending_logs').getFullList({ filter: `pending_id = "${pendingItem.id}"` });
          pendingLogs = pendingLogs.map((log: any) => log.log);
          if (pendingItem.total_items === pendingLogs.length) {
            clearInterval(i);
            window.location.reload();
          }
          setUploadResult({
            totalLeads: pendingItem.total_items,
            pendingItems: pendingItem.total_items - pendingLogs.length,
            failedLeads: (pendingLogs as unknown as any[]).filter((i) => i.status === 'failure').map((l) => ({ ...l.lead, reason: l.error })),
            numberFailed: (pendingLogs as unknown as any[]).filter((i) => i.status === 'failure').length,
            numberSuccessfull: (pendingLogs as unknown as any[]).filter((i) => i.status === 'success').length,
          });
        } catch (e) {
          console.log(e);
          return;
        }
      }, 500);
    };
    effect();
    return () => {
      clearInterval(i);
    };
  }, []);

  return (
    <Card className="mt-8 p-4">
      <CardHeader>
        <CardTitle className="text-2xl">Upload Results</CardTitle>
      </CardHeader>
      <CardContent>
        <p>Number of successful uploads: {uploadResult.numberSuccessfull}</p>
        <p>Number of failed uploads: {uploadResult.numberFailed}</p>
        {(uploadResult?.failedLeads ?? []).length > 0 && (
          <div>
            <h3 className="text-xl mt-4 text-red-600 font-medium">Failed Uploads:</h3>
            <div className="flex-col max-h-28 max-w-96 px-1 overflow-y-scroll">
              {(uploadResult?.failedLeads ?? []).map((failedLead, index) => (
                <li key={index}>
                  {failedLead.phone_number}: {failedLead.reason}
                </li>
              ))}
            </div>
          </div>
        )}
        {uploadResult.pendingItems > 0 ? (
          <div>
            <Progress value={((uploadResult.totalLeads - uploadResult.pendingItems) / uploadResult.totalLeads) * 100} />
          </div>
        ) : (
          <div>List successfully uploaded</div>
        )}
      </CardContent>
      {uploadResult.pendingItems > 0 && (
        <div>
          <div>Contacts are being uploaded, it is safe to close this window</div>
        </div>
      )}
    </Card>
  );
};

export default LiveLoading;
