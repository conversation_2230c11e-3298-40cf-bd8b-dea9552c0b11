import { getLogsById, getPb, getUser } from '@/lib/pocket';
import BreadCrumbs from './components/BreadCrumbs';
import { columns } from './components/columns';
import { DataTable } from './components/data-table';
import LiveLoading from './components/LiveLoading';
import { IPendingContacts } from '@/lib/types';

export const dynamic = 'force-dynamic';

export const fetchCache = 'default-no-store';

export default async ({ params }: { params: Record<string, string> }) => {
  const user = await getUser();
  const logs = await getLogsById(params.log_id);
  const agentRoles =
    user.expand?.roles?.map((role) => {
      return role.name;
    }) ?? [];

  const pb = await getPb();
  let pendingItem;
  let pendingLogs: any = [];
  try {
    pendingItem = await pb.collection<IPendingContacts>('pending_contacts').getFirstListItem(`logId = "${params.log_id}"`);
    pendingLogs = await pb.collection('pending_logs').getFullList({
      filter: `pending_id = "${pendingItem.id}"`,
      skipTotal: true,
    });
  } catch (e) {
    console.error(e);
  }

  return (
    <>
      <div className="flex flex-col space-y-2 my-2">
        <BreadCrumbs />
      </div>
      {logs.pending ? (
        <div>{pendingItem && pendingLogs?.length > 0 ? <LiveLoading pendingItem={pendingItem} pendingLogs={pendingLogs} /> : <p>loading</p>}</div>
      ) : (
        <div>
          <div>
            Viewing log data for: <p className="text-blue-600">{logs.log_name}</p>
          </div>
          <DataTable
            columns={columns}
            data={logs.log_data}
            userType={user.type}
            agentRoles={agentRoles}
            created={logs.created}
            updated={logs.updated}
            created_by={logs?.expand?.created_by}
            log_id={params.log_id}
            account_id={params.account_id}
          />
        </div>
      )}
    </>
  );
};
