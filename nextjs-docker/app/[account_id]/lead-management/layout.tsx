import { getLogsByAccountId, getUser } from '@/lib/pocket';
import { LeadLayout } from './components/LeadLayout';

const Layout = async ({ children, params }: { children: React.ReactNode; params: Record<string, string> }) => {
  const user = await getUser();
  const startTime = new Date().getTime();
  const logData = await getLogsByAccountId(params.account_id, user);
  const endTime = new Date().getTime();
  console.log('Time taken to fetch logs:', endTime - startTime, 'ms');
  const agentRoles =
    user.expand?.roles?.map((role) => {
      return role.name;
    }) ?? [];
  return (
    <LeadLayout accountId={params.account_id} logData={logData} userType={user.type} agentRoles={agentRoles}>
      {children}
    </LeadLayout>
  );
};

export default Layout;
