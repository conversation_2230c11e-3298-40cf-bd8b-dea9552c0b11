'use client';
import { useToast } from '@/components/ui/use-toast';
import { revalidateFullPath } from '@/lib/actions';
import { pb_url } from '@/state/consts';
import { usePathname } from 'next/navigation';
import PocketBase from 'pocketbase';
import { useEffect } from 'react';

const TemplateUpdateSubscription = ({ account_id }: { account_id: string }) => {
  const { toast } = useToast();
  const pathname = usePathname();
  const pb = new PocketBase(pb_url);
  useEffect(() => {
    const effect = () => {
      pb.collection('templates').subscribe(
        '*',
        function (e) {
          if (e.action == 'update') {
            if (e.record.status == 'APPROVED') {
              toast({
                variant: 'success',
                title: `Template ${e?.record?.template_name} status changed to ${e.record.status}`,
              });
            }
            if (e.record.status == 'REJECTED') {
              toast({
                variant: 'destructive',
                title: `Template ${e?.record?.template_name} status changed to ${e.record.status}`,
              });
            }

            revalidateFullPath(pathname);
          }
        },
        { filter: `account.id = "${account_id}"` }
      );
    };
    effect();
    return () => {
      pb.collection('templates').unsubscribe();
    };
  }, []);

  return <></>;
};

export default TemplateUpdateSubscription;
