import TemplatePreview from '@/components/shared/TemplatePreview';
import { But<PERSON> } from '@/components/ui/button';
import { Dialog, DialogClose, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTrigger } from '@/components/ui/dialog';
import { getTemplateFromDbByTemplateName } from '@/lib/pocket';
import { ITemplateDatabase } from '@/lib/types';
import { DialogTitle } from '@radix-ui/react-dialog';
import { Row } from '@tanstack/react-table';
import { useState } from 'react';
import { Eye, Loader2 } from 'lucide-react';
import TemplatePreviewCarousel from '@/components/shared/TemplatePreviewCarousel';
import { ScrollArea } from '@/components/ui/scroll-area';

const ViewTemplateDialog = ({ row }: { row: Row<ITemplateDatabase> }) => {
  const template_name = row.original.template_name;
  const [template, setTemplate] = useState<ITemplateDatabase | null>(null);

  return (
    <Dialog>
      <DialogTrigger asChild>
        <Button
          className="bg-white border text-black mb-0 hover:text-white group"
          onClick={async () => {
            const res = await getTemplateFromDbByTemplateName({ account: null, template_name });
            setTemplate(res);
          }}
        >
          View
          <Eye className="h-4 w-4 ml-2 text-black group-hover:text-white" />
        </Button>
      </DialogTrigger>
      <DialogContent className="flex flex-col justify-center items-center max-w-lg max-h-[90vh]">
        <DialogHeader className="self-start">
          <DialogTitle>Template Preview</DialogTitle>
          <DialogDescription>
            View the template <strong>{template?.template_name}</strong>
          </DialogDescription>
        </DialogHeader>
        <ScrollArea className="flex-grow overflow-auto">
          {template ? (
            <>
              {template.type == 'basic-template' && <TemplatePreview template={template} />}
              {template.type == 'utility-template' && <TemplatePreview template={template} />}
              {template.type == 'carousel-template' && <TemplatePreviewCarousel template={template} />}
            </>
          ) : (
            <div className="flex flex-1 justify-center items-center">
              <div className="h-32 w-full bg-gray-100 rounded-md"></div>
            </div>
          )}
        </ScrollArea>
        <DialogFooter className="self-end">
          <DialogClose asChild>
            <Button type="button">Close</Button>
          </DialogClose>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default ViewTemplateDialog;
