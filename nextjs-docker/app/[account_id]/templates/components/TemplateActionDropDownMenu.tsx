'use client';

import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuLabel, DropdownMenuTrigger } from '@/components/ui/dropdown-menu';
import { HoverCard, HoverCardContent, HoverCardTrigger } from '@/components/ui/hover-card';

import {
  AlertDialog,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from '@/components/ui/alert-dialog';
import { Button } from '@/components/ui/button';

import { useToast } from '@/components/ui/use-toast';
import { deleteTemplateOnFacebook } from '@/lib/pocket';
import { ITemplateDatabase } from '@/lib/types';
import { Row } from '@tanstack/react-table';
import dayjs from 'dayjs';
import { CircleHelp, Copy, Loader2, MoreHorizontal, Pencil, Trash } from 'lucide-react';
import { useParams } from 'next/navigation';
import { useState } from 'react';
import Link from 'next/link';

const TemplateActionDropDownMenu = ({ row }: { row: Row<ITemplateDatabase> }) => {
  const params = useParams<{ account_id: string }>();
  const { toast } = useToast();
  const [loading, setLoading] = useState(false);
  const [open, setOpen] = useState(false);
  const cardContent = {
    description: '',
    helperText: '',
  };
  const template_type = row.original.type;
  const editCount = row.original.edit_count;

  const isEditDisabled = (row: Row<ITemplateDatabase>) => {
    const templateEditDate = row.original.template_edit_date ?? row.original.created;
    if (row.original.edit_count >= 10) {
      cardContent.description = 'Templates cannot be edited more than 10 times in a single month';
      cardContent.helperText = '';
      return true;
    }
    if (
      24 - dayjs().diff(dayjs(templateEditDate), 'hours') > 0 &&
      dayjs(row.original.created).format('MMMM D, YYYY h:mm A') != dayjs(templateEditDate).format('MMMM D, YYYY h:mm A')
    ) {
      cardContent.description = 'Templates can only be edited once every 24 hours';
      cardContent.helperText = `${24 - dayjs().diff(dayjs(templateEditDate), 'hours')} hour(s) left`;
      return true;
    }
    if (row.original.status == 'PENDING' || row.original.status == 'PENDING DELETION') {
      cardContent.description = 'Template can only be edited when the status is APPROVED, REJECTED or PAUSED';
      cardContent.helperText = `Template is in ${row.original.status} state`;
      return true;
    }
    return false;
  };
  const handleDeleteTemplate = async () => {
    try {
      setLoading(true);
      await deleteTemplateOnFacebook(row.original.template_name, params.account_id);
      toast({
        variant: 'success',
        description: 'Template Deleted Successfully',
      });
    } catch (error) {
      toast({
        variant: 'destructive',
        description: 'There was an error in deleting the template. Try again!',
      });
    }
    setOpen(false);
    setLoading(false);
  };

  return (
    <>
      <AlertDialog open={open} onOpenChange={(isOpen) => setOpen(isOpen)}>
        <DropdownMenu modal={false}>
          <DropdownMenuTrigger asChild disabled={row.original.status == 'PENDING' || row.original.status == 'PENDING DELETION'}>
            <Button variant="ghost" className="h-8 w-8 p-0 hover:bg-gray-100 mb-0">
              <span className="sr-only">Open menu</span>
              <MoreHorizontal className="h-4 w-4" />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end">
            <DropdownMenuLabel>Actions</DropdownMenuLabel>
            {/* {isDisabled ? <HoverCard openDelay={0}>
              <HoverCardTrigger><DropdownMenuItem disabled>Edit Template <CircleHelp className="ml-2 h-4 w-4"/></DropdownMenuItem></HoverCardTrigger>
              <HoverCardContent className="text-xs">
                <div>Templates can only be edited once every 24 hours
                </div>
                <div>{24 - dayjs().diff(dayjs(row.original.updated), "hours")} hour(s) left</div>
              </HoverCardContent>
            </HoverCard> : <Link prefetch={false} href={`templates/edit/${name}`}>
              <DropdownMenuItem>
              <Pencil className="mr-2 h-4 w-4 text-green-600" />
              <span className='text-green-600'>Edit</span>
              </DropdownMenuItem>
            </Link>} */}
            {template_type != 'carousel-template' && (
              <Link href={`/${params.account_id}/templates/manage/duplicate?id=${row.original.id}`}>
                <DropdownMenuItem>
                  <Copy className="mr-2 h-4 w-4 text-orange-600" />
                  <span className="text-orange-600">Duplicate</span>
                </DropdownMenuItem>
              </Link>
            )}
            {template_type != 'carousel-template' &&
              (isEditDisabled(row) ? (
                <HoverCard>
                  <HoverCardTrigger className="hover:cursor-help">
                    <DropdownMenuItem disabled>
                      <CircleHelp className="mr-2 h-4 w-4" />
                      <span>Edit</span>
                    </DropdownMenuItem>
                  </HoverCardTrigger>
                  <HoverCardContent className="text-xs">
                    <div>{cardContent.description}</div>
                    <div>{cardContent.helperText}</div>
                  </HoverCardContent>
                </HoverCard>
              ) : (
                <Link href={`/${params.account_id}/templates/manage/edit?id=${row.original.id}`}>
                  <DropdownMenuItem>
                    <Pencil className="mr-2 h-4 w-4 text-blue-600" />
                    <span className="text-blue-600">Edit</span>
                  </DropdownMenuItem>
                </Link>
              ))}
            <AlertDialogTrigger asChild>
              <DropdownMenuItem>
                <Trash className="mr-2 h-4 w-4 text-red-600" />
                <span className="text-red-600">Delete</span>
              </DropdownMenuItem>
            </AlertDialogTrigger>
          </DropdownMenuContent>
        </DropdownMenu>

        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Are you sure you want to delete the template {row.original.template_name}?</AlertDialogTitle>
            <div>You cannot use the same template name for next 30 days</div>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            {loading ? (
              <Button className="mb-0" disabled>
                Deleting
                <Loader2 className="ml-2 animate-spin" />
              </Button>
            ) : (
              <>
                <Button onClick={() => handleDeleteTemplate()} variant={'destructive'} className="mb-0">
                  Yes Delete
                </Button>
              </>
            )}
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </>
  );
};

export default TemplateActionDropDownMenu;
