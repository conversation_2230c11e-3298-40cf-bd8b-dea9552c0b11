import { server_component_pb } from '@/state/pb/server_component_pb';
import { getAccount, getAllTemplatesFromDb, getUserAccountList, getUserById } from '@/lib/pocket';
import { MigrationTemplate } from '@/components/migration-template';
import { BaseAuthStore } from 'pocketbase';

const MigrateTemplatesPage = async ({ params, searchParams }: { params: Record<string, string>; searchParams: Record<string, string> }) => {
  const accountId = params.account_id;
  const account = await getAccount(accountId);
  const { selected_account } = searchParams;
  const selectedAccount = selected_account ? await getAccount(selected_account) : null;
  const { pb } = await server_component_pb();
  const user = pb.authStore.record;
  const userById = await getUserById((user as any).id);
  const authStore: BaseAuthStore = pb.authStore as BaseAuthStore;
  if (!user?.id) {
    return <div>Not Authenticated</div>;
  }
  const templates = await getAllTemplatesFromDb(account, userById);
  const templatesOfDestination = selectedAccount ? await getAllTemplatesFromDb(selectedAccount, userById) : [];
  const accountList = await getUserAccountList(user.id, authStore.isSuperuser);
  return <MigrationTemplate templates={templates} accountList={accountList} templatesOfDestination={templatesOfDestination} />;
};

export default MigrateTemplatesPage;
