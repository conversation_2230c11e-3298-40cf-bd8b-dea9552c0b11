import { Button } from '@/components/ui/button';
import Link from 'next/link';

import { Toaster } from '@/components/ui/toaster';
import { getAccount, getAllTemplatesFromDb } from '@/lib/pocket';
import { User } from '@/lib/types';
import { server_component_pb } from '@/state/pb/server_component_pb';
import { PlusCircle } from 'lucide-react';
import type { Metadata } from 'next';
import { columns } from './components/columns';
import { DataTable } from './components/data-table';
import TemplateUpdateSubscription from './components/TemplateUpdateSubscription';
import ConnectYourBusiness from '@/components/shared/ConnectYourBusiness';

export const metadata: Metadata = {
  title: 'Templates',
  description: '...',
};

export default async ({ params }: { params: Record<string, string> }) => {
  // noStore();
  const account = await getAccount(params.account_id);
  const { pb } = await server_component_pb();
  const user = pb.authStore.record as User;

  if (account.access_token) {
    const _templates = await getAllTemplatesFromDb(account, user);
    const templates = _templates.filter((template) => template.template_name !== 'blank');
    if (!templates || templates.length === 0) {
      return (
        <div className="h-full w-full flex flex-col justify-center items-center space-y-4">
          <div>Looks like you dont have any templates!</div>
          <Button id="create-template1" asChild>
            <Link prefetch={false} href={`/${params.account_id}/templates/manage/create-new`}>
              Create template
            </Link>
          </Button>
        </div>
      );
    }

    return (
      <div className="mx-auto p-6 bg-gray-100 min-h-full">
        <div className="flex mb-4">
          <div className="flex-1">
            <div className="text-gray-800 text-xl font-bold sm:text-2xl">Message Templates</div>
            <p className="text-gray-600 mt-2">Select the template which you would like to bulk send</p>
          </div>
          <div className="flex items-center space-x-2">
            <Link prefetch={false} href={`/${params.account_id}/templates/migrate-templates`}>
              <Button variant="link">Migrate Templates</Button>
            </Link>
            <Link prefetch={false} href={`/${params.account_id}/image-gallery`}>
              <Button>Image Gallery</Button>
            </Link>
            <Link prefetch={false} href={`/${params.account_id}/video-gallery`}>
              <Button>Video Gallery</Button>
            </Link>
            <Button id="create-template1" asChild variant={'secondary'}>
              <Link prefetch={false} href={`/${params.account_id}/templates/manage/create-new`}>
                <PlusCircle className="mr-2 h-5 w-5" />
                Create Template
              </Link>
            </Button>
          </div>
        </div>
        <TemplateUpdateSubscription account_id={params.account_id} />
        <Toaster />
        <DataTable columns={columns} data={templates} account_id={params.account_id} userType={user.type} />
      </div>
    );
  } else {
    return (
      <div className="bg-gray-100  w-full flex items-center justify-center h-full">
        <ConnectYourBusiness account={account} />
      </div>
    );
  }
};
