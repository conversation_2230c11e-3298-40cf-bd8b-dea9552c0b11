export interface IHeader {
  type: string;
  format: string;
  text: string;
  file?: File | null | FormData;
  fileUrl?: string | null;
  example?: {
    header_text?: string[];
    header_handle?: string[];
  };
}

//card header
export interface ICarouselHeader {
  type: 'HEADER';
  format: 'IMAGE' | 'VIDEO';
  file?: File | null | FormData;
  fileUrl?: string;
  example?: {
    header_handle: string[];
  };
}
//can be used for template and carousel messagebody and cardbody
export interface IBody {
  type: string;
  format?: string;
  text: string;
  example?: {
    body_text: string[];
  };
}

export interface IFooter {
  type: string;
  text: string;
  disabled?: boolean;
}

export interface IQuickReplyBtn {
  id?: string;
  btnType?: 'Marketing opt-out' | 'Custom' | string;
  type: 'QUICK_REPLY' | string;
  text: string;
}

export interface ICallToActionBtn {
  id?: string;
  type: 'PHONE_NUMBER' | 'URL' | string;
  btnType?: 'Call phone number' | 'Visit Website' | string;
  text: string;
  url?: string;
  phone_number?: string;
}

export interface ITemplateObject {
  name: string;
  category: string;
  allow_category_change: boolean;
  language: string;
  components: any;
  id: string;
}

export interface ITemplateState {
  templateName: string;
  templateLanguage: { value: string; label: string };
  templateType: 'basic-template' | 'carousel-template' | 'utility-template';
  templateId: string;
  cards: ICardComponent[];
  buttons: {
    quickReplyButtons: IQuickReplyBtn[];
    callToActionButtons: ICallToActionBtn[];
  };
  header: IHeader;
  body: IBody;
  footer: IFooter;
}

export type Action =
  | {
      type: 'SET_BUTTONS';
      payload: {
        type: 'quickReplyButtons' | 'callToActionButtons';
        buttons: IQuickReplyBtn[] | ICallToActionBtn[];
      };
    }
  | { type: 'SET_HEADER'; payload: Partial<IHeader> }
  | { type: 'SET_BODY'; payload: IBody }
  | { type: 'SET_FOOTER'; payload: Partial<IFooter> }
  | { type: 'SET_TEMPLATE_NAME'; payload: string }
  | { type: 'SET_TEMPLATE_LANGUAGE'; payload: { value: string; label: string } }
  | { type: 'SET_TEMPLATE_TYPE'; payload: 'basic-template' | 'carousel-template' | 'utility-template' }
  | { type: 'RESET_STATE'; payload: ITemplateState }
  | { type: 'SET_CARD_BODY'; payload: { index: number; body: IBody } }
  | { type: 'SET_CARD_HEADER'; payload: { index: number; header: ICarouselHeader } }
  | { type: 'REMOVE_CARD'; payload: number }
  | { type: 'ADD_CARD' }
  | {
      type: 'SET_CARD_BUTTONS';
      payload: { index: number; type: 'quickReplyButtons' | 'callToActionButtons'; buttons: IQuickReplyBtn[] | ICallToActionBtn[] };
    }
  | {
      type: 'SET_CARD_BUTTONS_FOR_ALL_CARDS';
      payload: { type: 'quickReplyButtons' | 'callToActionButtons'; buttons: IQuickReplyBtn[] | ICallToActionBtn[] };
    }
  | {
      type: 'DELETE_CARD_BUTTONS_FOR_ALL_CARDS';
      payload: { type: 'quickReplyButtons' | 'callToActionButtons'; buttons: IQuickReplyBtn[] | ICallToActionBtn[] };
    }
  | { type: 'SET_ALL_CARD_HEADERS'; payload: 'IMAGE' | 'VIDEO' };

export interface ICardComponentButtons {
  type: 'buttons';
  // buttons: [IQuickReplyBtn[], ICallToActionBtn[]] | [IQuickReplyBtn[]] | [ICallToActionBtn[]] | [];
  buttons: {
    quickReplyButtons: IQuickReplyBtn[];
    callToActionButtons: ICallToActionBtn[];
  };
}

export type ICardButtons = (IQuickReplyBtn | ICallToActionBtn)[];

export interface ICardComponent {
  components: [ICarouselHeader, IBody, ICardComponentButtons];
}

export interface ICardState {
  components: [
    ICarouselHeader,
    IBody,
    {
      type: 'buttons';
      buttons: ICardButtons;
    },
  ];
}
export interface ICarouselTemplateObject {
  name: string;
  category: 'MARKETING';
  allow_category_change: true;
  language: string;
  components: [
    IBody,
    {
      type: 'carousel';
      cards: [ICardState];
    },
  ];
  id: string;
}
