'use client';
import { v4 as uuidv4 } from 'uuid';
import { Button } from '@/components/ui/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuGroup,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { ChevronDown, Plus } from 'lucide-react';
import React from 'react';
import QuickReplyButtons from './QuickReplyButtons';
import CallToActionButtons from './CallToActionButtons';
import { ICallToActionBtn, IFooter, IQuickReplyBtn } from '../types';

interface DropDownButtonProps {
  quickReplyButtons: IQuickReplyBtn[];
  handleChangeQuickReplyButtons: (quickReplyButtons: ICallToActionBtn[]) => void;
  callToActionButtons: ICallToActionBtn[];
  handleChangeCallToActionButtons: (callToActionButtons: IQuickReplyBtn[]) => void;
  setFooter: (footer: Partial<IFooter>) => void;
  templateType: string;
}

const DropDownButtons: React.FC<DropDownButtonProps> = ({
  quickReplyButtons,
  handleChangeQuickReplyButtons,
  callToActionButtons,
  handleChangeCallToActionButtons,
  setFooter,
  templateType,
}) => {
  const callToActionAddWebsiteItem = () => {
    const id = uuidv4();
    const itemToAdd = {
      id,
      btnType: 'Visit Website',
      type: 'URL',
      text: '',
      url: '',
    };
    handleChangeCallToActionButtons([...callToActionButtons, itemToAdd]);
  };

  const callToActionAddPhoneNumber = () => {
    const id = uuidv4();
    const itemToAdd = {
      id,
      btnType: 'Call phone number',
      type: 'PHONE_NUMBER',
      text: '',
      phone_number: '',
    };
    handleChangeCallToActionButtons([...callToActionButtons, itemToAdd]);
  };

  const callToActionAddFlow = () => {
    const id = uuidv4();
    const itemToAdd = {
      id,
      type: 'CUSTOM', //need to change
      btnType: 'custom',
      text: '',
    };
    handleChangeCallToActionButtons([...callToActionButtons, itemToAdd]);
  };

  const callToActionAddOfferCode = () => {
    const id = uuidv4();
    const itemToAdd = {
      id,
      type: 'CUSTOM', //need to change
      btnType: 'custom',
      text: '',
    };
    handleChangeCallToActionButtons([...callToActionButtons, itemToAdd]);
  };

  const quickReplyaddMarketingItem = () => {
    const id = uuidv4();
    const itemToAdd = {
      id,
      btnType: 'Marketing opt-out',
      type: 'QUICK_REPLY',
      text: 'Stop promotions',
    };
    handleChangeQuickReplyButtons([...quickReplyButtons, itemToAdd]);
    if (templateType != 'carousel-template') {
      setFooter({
        text: 'Not interested? Tap Stop promotions',
      });
    }
  };

  const quickReplyaddCustomItem = () => {
    const id = uuidv4();
    const itemToAdd = {
      id,
      btnType: 'Custom',
      type: 'QUICK_REPLY',
      text: '',
    };
    const marketingOptOutIndex = quickReplyButtons.findIndex((item) => item.btnType === 'Marketing opt-out');

    if (marketingOptOutIndex !== -1) {
      // If "Marketing opt-out" exists, slice the array to add itemToAdd before it
      const beforeMarketingOptOut = quickReplyButtons.slice(0, marketingOptOutIndex);
      const afterMarketingOptOut = quickReplyButtons.slice(marketingOptOutIndex);

      handleChangeQuickReplyButtons([...beforeMarketingOptOut, itemToAdd, ...afterMarketingOptOut]);
    } else {
      // If "Marketing opt-out" does not exist, just append the new item
      handleChangeQuickReplyButtons([...quickReplyButtons, itemToAdd]);
    }
  };

  return (
    <div>
      <div className="my-4">
        <h3 className="font-semibold">
          Buttons <span className="text-gray-500">(Optional)</span>
        </h3>
        <div className="text-sm text-gray-500 mb-2">Create buttons that let customers respond to your message or take action.</div>
        <div className="bg-blue-100 p-4 rounded-lg text-sm text-blue-700 mb-2">
          <div className="font-bold">We recommend adding the marketing opt-out button</div>
          <div>
            Allow customers to request to opt out of all marketing messages. This can help reduce blocks from customers and increase your quality rating.
          </div>
        </div>
        <div id="button">
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button className="mb-0 text-sm" variant={'outline'}>
                <Plus className="mr-2 " /> Add a button <ChevronDown className="ml-2" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent className="w-56">
              <DropdownMenuLabel>
                <div className="text-lg">Quick Reply Buttons</div>
              </DropdownMenuLabel>
              <DropdownMenuGroup>
                {templateType != 'utility-template' && (
                  <DropdownMenuItem
                    disabled={quickReplyButtons.filter((item) => item.btnType == 'Marketing opt-out').length > 0}
                    className="flex-col items-start"
                    onClick={() => quickReplyaddMarketingItem()}
                  >
                    <div>Marketing Opt Out</div>
                    <div className="text-sm text-gray-800">Recommended</div>
                  </DropdownMenuItem>
                )}
                <DropdownMenuItem className="flex-col items-start" onClick={() => quickReplyaddCustomItem()}>
                  <div>Custom</div>
                </DropdownMenuItem>
              </DropdownMenuGroup>
              <DropdownMenuSeparator />
              <DropdownMenuLabel>
                <div className="text-lg">Call to action buttons</div>
              </DropdownMenuLabel>
              <DropdownMenuGroup>
                <DropdownMenuItem
                  className="flex-col items-start"
                  onClick={callToActionAddWebsiteItem}
                  disabled={callToActionButtons.filter((btn) => btn.btnType == 'Visit Website').length > 1}
                >
                  <div>Visit Website</div>
                  <div className="text-sm text-gray-800">2 buttons maximum</div>
                </DropdownMenuItem>
                <DropdownMenuItem
                  className="flex-col items-start"
                  onClick={callToActionAddPhoneNumber}
                  disabled={callToActionButtons.filter((btn) => btn.btnType == 'Call phone number').length > 0}
                >
                  <div>Call phone number</div>
                  <div className="text-sm text-gray-800">1 button maximum</div>
                </DropdownMenuItem>
                <DropdownMenuItem className="flex-col items-start" disabled>
                  <div>Complete Flow</div>
                  <div className="text-sm text-gray-800">1 button maximum</div>
                </DropdownMenuItem>
                <DropdownMenuItem className="flex-col items-start" disabled>
                  <div>Copy Offer Code</div>
                  <div className="text-sm text-gray-800">1 button maximum</div>
                </DropdownMenuItem>
              </DropdownMenuGroup>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>

        <div className="mt-2 text-gray-500 text-sm">If you add more than 3 buttons, they will appear in a list.</div>
      </div>
      {quickReplyButtons.length > 0 && (
        <div className="border border-gray-200 rounded-lg p-4 mb-4">
          <div className="font-bold pb-4">Quick Reply</div>

          {quickReplyButtons.map((btn, index) => (
            <QuickReplyButtons
              btn={btn}
              index={index}
              key={index}
              quickReplyButtons={quickReplyButtons}
              handleChangeQuickReplyButtons={handleChangeQuickReplyButtons}
              setFooter={setFooter}
            />
          ))}
        </div>
      )}

      {callToActionButtons.length > 0 && (
        <div className="border border-gray-200 rounded-lg p-4">
          <div className="font-bold pb-4">Call to action</div>

          {callToActionButtons.map((btn, index) => (
            <CallToActionButtons
              btn={btn}
              index={index}
              key={index}
              callToActionButtons={callToActionButtons}
              handleChangeCallToActionButtons={handleChangeCallToActionButtons}
              templateType={templateType}
            />
          ))}
          {/* </form> */}
        </div>
      )}
    </div>
  );
};

export default React.memo(DropDownButtons);
