'use client';
import { useImmerReducer } from 'use-immer';

import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/components/ui/alert-dialog';
import { Button } from '@/components/ui/button';
import { checkExistingName, upsertTemplate, upsertTemplateCarousel } from '@/lib/pocket';
import { AlertCircle, Loader2 } from 'lucide-react';
import { useCallback, useEffect, useMemo, useState } from 'react';
import {
  Action,
  IBody,
  ICallToActionBtn,
  ICardButtons,
  ICardComponent,
  ICardComponentButtons,
  ICarouselHeader,
  ICarouselTemplateObject,
  IFooter,
  IHeader,
  IQuickReplyBtn,
  ITemplateObject,
  ITemplateState,
} from '../types';
import Body from './Body';
import DropDownButtons from './DropDownButtons';
import Footer from './Footer';
import SubmitNameAndLanguage from './SubmitNameAndLanguage';

import { useToast } from '@/components/ui/use-toast';
import { redirectFunction } from '@/lib/actions';
import Link from 'next/link';
import { useRouter } from 'next/navigation';

import MessagePreview from '@/components/shared/MessagePreview';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { HoverCard, HoverCardContent, HoverCardTrigger } from '@/components/ui/hover-card';
import { Label } from '@/components/ui/label';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { Account } from '@/lib/types';
import { getLimit } from '@/lib/utils';
import clsx from 'clsx';
import { Draft } from 'immer';
import AllCardDropDownButtons from './AllCardDropDownButtons';
import AllCardHeaders from './AllCardHeaders';
import CardComponents from './CardComponents';
import MessagePreviewCarousel from './MessagePreviewCarousel';
import TemplateHeader from './TemplateHeader';
import { htmlToWhatsApp } from './utils';

export const CreateTemplate = ({
  account,
  template_operation,
  template_page,
  initialState,
  EACH_CAROUSEL_CARD,
}: {
  account: Account;
  template_operation: 'edit' | 'create-new' | 'duplicate';
  template_page: string;
  initialState: ITemplateState;
  EACH_CAROUSEL_CARD: ICardComponent;
}) => {
  const [goBackAlert, setGoBackAlert] = useState(false);
  const [templatePage, setTemplatePage] = useState(template_page);
  const [allCardHeaders, setAllCardHeaders] = useState<'IMAGE' | 'VIDEO'>('IMAGE');

  const reduce = (draft: Draft<ITemplateState>, action: Action) => {
    switch (action.type) {
      case 'SET_BUTTONS': {
        draft.buttons[action.payload.type] = action.payload.buttons;
        break;
      }
      case 'SET_BODY':
        draft.body = action.payload;
        break;
      case 'SET_FOOTER':
        draft.footer = {
          ...draft.footer,
          ...action.payload,
        };
        break;
      case 'SET_HEADER':
        draft.header = {
          ...draft.header,
          ...action.payload,
        };
        break;
      case 'SET_TEMPLATE_NAME':
        draft.templateName = action.payload;
        break;
      case 'SET_TEMPLATE_LANGUAGE':
        draft.templateLanguage = action.payload;
        break;
      case 'SET_TEMPLATE_TYPE':
        draft.templateType = action.payload;
        break;
      case 'RESET_STATE':
        return action.payload;
      case 'SET_CARD_BODY':
        draft.cards[action.payload.index].components[1] = action.payload.body;
        break;
      case 'SET_CARD_HEADER':
        draft.cards[action.payload.index].components[0] = action.payload.header;
        break;
      case 'ADD_CARD':
        let newCard = {
          components: [
            {
              type: 'HEADER',
              format: allCardHeaders,
              file: null,
              fileUrl: '',
            },
            {
              type: 'BODY',
              format: 'TEXT',
              text: 'Hello',
            },
            {
              type: 'buttons',
              buttons: {
                quickReplyButtons: draft.cards[0].components[2].buttons.quickReplyButtons,
                callToActionButtons: draft.cards[0].components[2].buttons.callToActionButtons,
              },
            },
          ],
        } as ICardComponent;

        draft.cards.push(newCard);
        break;
      case 'REMOVE_CARD': {
        draft.cards.splice(action.payload, 1);
        break;
      }
      case 'SET_CARD_BUTTONS':
        draft.cards[action.payload.index].components[2].buttons[action.payload.type] = action.payload.buttons;
        break;
      case 'SET_CARD_BUTTONS_FOR_ALL_CARDS':
        draft.cards.forEach((card) => {
          card.components[2].buttons[action.payload.type] = action.payload.buttons;
        });
        break;
      case 'DELETE_CARD_BUTTONS_FOR_ALL_CARDS':
        draft.cards.forEach((card) => {
          card.components[2].buttons[action.payload.type] = action.payload.buttons;
        });
        break;
      case 'SET_ALL_CARD_HEADERS':
        draft.cards.forEach((card) => {
          card.components[0].format = action.payload;
        });
        break;
      default:
        break;
    }
  };
  const [template, dispatch] = useImmerReducer(reduce, initialState);

  const router = useRouter();

  useEffect(() => {
    const unloadCallback = (event: any) => {
      event.preventDefault();
      event.returnValue = '';
      return '';
    };

    window.addEventListener('beforeunload', unloadCallback);
    return () => window.removeEventListener('beforeunload', unloadCallback);
  }, []);

  const setHeader = useCallback(
    (header: Partial<IHeader>) => {
      dispatch({
        type: 'SET_HEADER',
        payload: header,
      });
    },
    [dispatch]
  );

  const setFooter = useCallback(
    (footer: Partial<IFooter>) => {
      dispatch({
        type: 'SET_FOOTER',
        payload: footer,
      });
    },
    [dispatch]
  );

  const setBody = useCallback(
    (body: IBody) => {
      dispatch({
        type: 'SET_BODY',
        payload: body,
      });
    },
    [dispatch]
  );

  const handleChangeTemplateName = useCallback(
    (value: string) => {
      dispatch({
        type: 'SET_TEMPLATE_NAME',
        payload: value,
      });
    },
    [dispatch]
  );

  const handleChangeCallToActionButtons = useCallback(
    (callToActionButtons: ICallToActionBtn[]) => {
      dispatch({
        type: 'SET_BUTTONS',
        payload: {
          type: 'callToActionButtons',
          buttons: callToActionButtons,
        },
      });
    },
    [dispatch]
  );

  const handleChangeQuickReplyButtons = useCallback(
    (quickReplyButtons: IQuickReplyBtn[]) => {
      dispatch({
        type: 'SET_BUTTONS',
        payload: {
          type: 'quickReplyButtons',
          buttons: quickReplyButtons,
        },
      });
    },
    [dispatch]
  );

  const handleChangeCardHeader = useCallback(
    (index: number, header: ICarouselHeader) => {
      dispatch({
        type: 'SET_CARD_HEADER',
        payload: { index, header },
      });
    },
    [dispatch]
  );
  const handleChangeCardBody = useCallback(
    (index: number, body: IBody) => {
      dispatch({
        type: 'SET_CARD_BODY',
        payload: { index, body },
      });
    },
    [dispatch]
  );

  const handleChangeQuickReplyCardButtons = useCallback(
    (index: number, quickReplyButtons: IQuickReplyBtn[]) => {
      dispatch({
        type: 'SET_CARD_BUTTONS',
        payload: {
          index: index,
          type: 'quickReplyButtons',
          buttons: quickReplyButtons,
        },
      });
    },
    [dispatch]
  );

  const handleChangeCallToActionCardButtons = useCallback(
    (index: number, callToActionButtons: ICallToActionBtn[]) => {
      dispatch({
        type: 'SET_CARD_BUTTONS',
        payload: {
          index: index,
          type: 'callToActionButtons',
          buttons: callToActionButtons,
        },
      });
    },
    [dispatch]
  );
  const handleSetButtonsForAllCards = useCallback(
    (type: 'quickReplyButtons' | 'callToActionButtons', buttons: IQuickReplyBtn[] | ICallToActionBtn[]) => {
      dispatch({
        type: 'SET_CARD_BUTTONS_FOR_ALL_CARDS',
        payload: {
          type,
          buttons,
        },
      });
    },
    [dispatch]
  );

  const handleDeleteButtonsFromAllCards = useCallback(
    (type: 'quickReplyButtons' | 'callToActionButtons', buttons: IQuickReplyBtn[] | ICallToActionBtn[]) => {
      dispatch({
        type: 'DELETE_CARD_BUTTONS_FOR_ALL_CARDS',
        payload: {
          type,
          buttons,
        },
      });
    },
    [dispatch]
  );

  const addCard = useCallback(() => {
    dispatch({
      type: 'ADD_CARD',
    });
  }, [dispatch]);

  const removeCard = useCallback(
    (index: number) => {
      dispatch({
        type: 'REMOVE_CARD',
        payload: index,
      });
    },
    [dispatch]
  );

  const handleChangeAllCardHeaders = useCallback(
    (format: 'IMAGE' | 'VIDEO') => {
      setAllCardHeaders(format);
      dispatch({
        type: 'SET_ALL_CARD_HEADERS',
        payload: format,
      });
    },
    [dispatch]
  );

  const { toast } = useToast();

  type ErrorType = {
    message?: string;
    error_user_title?: string;
    error_user_msg?: string;
  };

  const [loading, setLoading] = useState(false);
  const [bodyLength, setBodyLength] = useState(0);
  const [error, setError] = useState<null | ErrorType>(null);
  let errors: { [key: string]: string } = {};
  const { templateName, templateLanguage, templateType, buttons, header, footer, body, cards, templateId } = template;
  const createOrUpdateTemplate = async (
    templateBody: ITemplateObject | ICarouselTemplateObject,
    edit: boolean,
    type: 'carousel-template' | 'normal-template',
    _form: FormData = {} as FormData
  ) => {
    try {
      setLoading(true);
      let res = null;
      if (type === 'carousel-template') {
        res = await upsertTemplateCarousel({
          accountId: account.id,
          templateObject: templateBody as ICarouselTemplateObject,
          edit,
          templateType,
          template_id: templateId,
          _form,
        });
      } else {
        res = await upsertTemplate({
          accountId: account.id,
          templateObject: templateBody,
          edit,
          templateType,
          template_id: templateId,
        });
      }

      if (res?.error as ErrorType) {
        toast({
          variant: 'destructive',
          description: 'Error in updating template',
        });
        const responseError = res?.error as ErrorType;
        const error_user_title = responseError?.error_user_title ?? 'Error';
        const error_user_msg = responseError?.error_user_msg ?? 'Error';
        setError({ error_user_title, error_user_msg });
        console.log(JSON.stringify(res?.error, null, 2), 'error in res.error');
      } else {
        toast({
          variant: 'success',
          description: 'Template updated Successfully',
        });
        router.replace(`/${account.id}/templates`);
      }
      setLoading(false);
    } catch (e) {
      toast({
        variant: 'destructive',
        description: 'Error in updating template',
      });
      setError({ error_user_title: 'Error', error_user_msg: 'An error has occured. Check you internet connection.' });
      setLoading(false);
      console.log(e, 'Error in catch');
    }
  };

  const submitTemplate = async () => {
    const templateObject = {
      name: templateName,
      category: templateType == 'utility-template' ? 'UTILITY' : 'MARKETING',
      allow_category_change: true,
      language: templateLanguage.value.split('-')[1],
    } as ITemplateObject;
    const newQuickReplyButtons = buttons.quickReplyButtons.map((item) => {
      return {
        text: item.text,
        type: item.type,
      };
    });
    const newFooter = { ...footer } as IFooter;
    delete newFooter.disabled;
    const newCallToActionButtons = buttons.callToActionButtons.map((item) => {
      return {
        text: item.text,
        type: item.type,
        ...(item.url && { url: item.url }),
        ...(item.phone_number && { phone_number: item.phone_number.replace(/\s+/g, '') }),
      };
    });
    const _text = body.text.trim();
    const replaced = htmlToWhatsApp(_text);
    // const newBody = { ...body, text: replaced };
    const newBody = { ...body, example: { body_text: [body?.example?.body_text] }, text: replaced };
    const newHeader = { ...header };
    if (newHeader.format === 'TEXT') {
      newHeader.text = newHeader.text.trim();
    }
    const _form = new FormData();
    const newHeaderFile = newHeader.file as File;
    if (newHeader.file) {
      const ext = newHeaderFile.name.split('.').pop();
      const filename = `filename.${ext}`;
      _form.append('file', newHeaderFile, filename);
      newHeader.file = _form;
    }
    delete newBody.format;
    // @ts-ignore
    // if (!newBody.example?.body_text) {
    //   delete newBody.example;
    // }
    if (newBody.example.body_text[0] == undefined || !newBody.example?.body_text?.[0][0]?.length) {
      // @ts-ignore
      delete newBody.example;
    }

    if (!newHeader.example?.header_text) {
      delete newHeader.example;
    }
    const btns = [] as any;
    if (newCallToActionButtons.length > 0) {
      newCallToActionButtons.map((btn: ICallToActionBtn) => {
        btns.push(btn);
      });
    }
    if (newQuickReplyButtons.length > 0) {
      newQuickReplyButtons.map((btn: ICallToActionBtn) => {
        btns.push(btn);
      });
    }
    // newBody.text = newBody.text
    //   .replace(/\s\s+/g, " ")
    //   .trim();
    // templateObject.components = [newHeader, newBody, newFooter];
    templateObject.components = [newHeader, newBody, newFooter];
    if (btns.length > 0) {
      templateObject.components.push({
        type: 'BUTTONS',
        buttons: btns,
      });
    }
    if (template_operation == 'create-new' || template_operation == 'duplicate') {
      if (await isExistingName()) {
        await createOrUpdateTemplate(templateObject, false, 'normal-template');
      }
    } else {
      await createOrUpdateTemplate(templateObject, true, 'normal-template');
    }
  };

  const isExistingName = async () => {
    setLoading(true);
    const res = await checkExistingName(account, templateName);
    if (res) {
      setLoading(false);
      toast({
        variant: 'destructive',
        description: 'Template Name already exists. Choose another one',
      });
    } else {
      setLoading(false);
    }
    return !res;
  };

  const submitTemplateCarousel = async () => {
    const templateObject = {
      name: templateName,
      category: 'MARKETING',
      allow_category_change: true,
      language: templateLanguage.value.split('-')[1],
    } as ICarouselTemplateObject;

    // Prepare the carousel cards
    let i = 0;
    const _form = new FormData();
    const carouselCards = cards.map((card) => {
      const newCardHeader = { ...card.components[0] } as ICarouselHeader;
      const newHeaderFile = newCardHeader.file as File;
      if (newCardHeader.file) {
        const ext = newHeaderFile.name.split('.').pop();
        const filename = `filename.${ext}`;
        _form.append(`file${i}`, newHeaderFile, filename);
        newCardHeader.file = null;
        ++i;
      }
      const _text = card.components[1].text.trim();
      const replaced = htmlToWhatsApp(_text);
      const newCardBody = { ...card.components[1], text: replaced } as IBody;
      delete newCardBody.format;
      if ((newCardBody?.example && newCardBody.example.body_text[0] == undefined) || !newCardBody.example?.body_text?.[0][0]?.length) {
        // @ts-ignore
        delete newCardBody.example;
      }

      const newQuickReplyButtons = card.components[2].buttons.quickReplyButtons.map((item) => {
        return {
          text: item.text,
          type: item.type,
        };
      });
      const newCallToActionButtons = card.components[2].buttons.callToActionButtons.map((item) => {
        return {
          text: item.text,
          type: item.type,
          ...(item.url && { url: item.url }),
          ...(item.phone_number && { phone_number: item.phone_number.replace(/\s+/g, '') }),
        };
      });

      const btns: ICardButtons = [];
      if (newQuickReplyButtons.length > 0) {
        newQuickReplyButtons.map((btn: IQuickReplyBtn) => {
          btns.push(btn);
        });
      }
      if (newCallToActionButtons.length > 0) {
        newCallToActionButtons.map((btn: ICallToActionBtn) => {
          btns.push(btn);
        });
      }

      return {
        components: [
          newCardHeader,
          newCardBody,
          {
            type: 'buttons',
            buttons: btns,
          },
        ],
      };
    });

    // Constructing the final templateObject with carousel component
    const _text = body.text.trim();
    const replaced = htmlToWhatsApp(_text);
    const newBody = { ...body, text: replaced };
    delete newBody.format;
    if (!newBody?.example?.body_text[0]) {
      delete newBody.example;
    }

    templateObject.components = [
      newBody,
      {
        type: 'carousel',
        cards: carouselCards as any,
      },
    ];

    if (carouselCards.length === 0) {
      alert('Fill all fields');
      return;
    }

    if (template_operation == 'create-new' || template_operation == 'duplicate') {
      if (await isExistingName()) {
        await createOrUpdateTemplate(templateObject, false, 'carousel-template', _form);
      }
    } else {
      // await createOrUpdateTemplate(templateObject, true, 'carousel-template');
    }
  };

  const callToActionWebsite = buttons.callToActionButtons.find((button) => button.btnType === 'Visit Website');
  const callToActionPhone = buttons.callToActionButtons.find((button) => button.btnType === 'Call phone number');
  const quickReplyCustom = buttons.quickReplyButtons.find((button) => button.btnType === 'custom');

  const submitDisabled = () => {
    if (templateName.length == 0) {
      errors.templateName = 'Template name cannot be empty';
    }
    if (templateType != 'carousel-template') {
      let headerFile = header.file as File;
      if (bodyLength == 0) {
        errors.body = 'Body cannot be empty';
      }
      if (bodyLength > 1024) {
        errors.body = 'Body cannot be more than 1024 characters';
      }
      if (header.format == 'IMAGE') {
        if (!header.fileUrl && !header.file) {
          errors.headerFile = 'Header image file is required';
        }
        if (header.file) {
          if (headerFile.type !== 'image/jpeg' && headerFile.type !== 'image/png') {
            errors.headerFile = 'Invalid Image Type. The image should be of PNG or JPEG type';
          }
          if (headerFile.size > 5242880) {
            errors.headerFile = 'Image size must not be more than 5MB';
          }
        }
      }
      if (header.format == 'VIDEO') {
        if (!header.fileUrl && !header.file) {
          errors.headerFile = 'Header video file is required';
        }
        if (header.file) {
          if (headerFile.type !== 'video/mp4' && headerFile.type !== 'video/3gp') {
            errors.headerFile = 'Invalid Video Type. The video should be of MP4 or 3GP type';
          }
          if (headerFile.size > 16777216) {
            errors.headerFile = 'Video size must not be more than 16MB';
          }
        }
      }
      if (header.format == 'DOCUMENT') {
        if (!header.fileUrl && !header.file) {
          errors.headerFile = 'Header document file is required';
        }
        if (header.file) {
          if (headerFile.size > 16777216) {
            errors.headerFile = 'Document size must not be more than 16MB';
          }
        }
      }
      if (callToActionPhone) {
        if (!callToActionPhone.phone_number) {
          errors.callToActionPhoneNumber = 'Phone number cannot be empty';
        } else {
          if (callToActionPhone.phone_number.length < 7) errors.callToActionPhoneNumber = 'Phone number invalid length';
        }
        if (!callToActionPhone.text) {
          errors.callToActionPhoneText = 'Button Text for Phone Number cannot be empty';
        }
      }

      if (callToActionWebsite) {
        if (!callToActionWebsite.url) {
          errors.callToActionWebsite = 'Website Url cannot be empty';
        }
        if (!callToActionWebsite.text) {
          errors.callToActionWebsiteText = 'Button Text for Website cannot be empty';
        }
      }
      if (quickReplyCustom) {
        if (!quickReplyCustom.text) {
          errors.callToActionWebsiteText = 'Button Text for Website cannot be empty';
        }
      }
    }

    if (templateType == 'carousel-template') {
      cards.map((card, idx) => {
        const index = idx + 1;
        const cardBodyLength = htmlToWhatsApp(card.components[1].text).length - 1;
        if (card.components[0].format == 'IMAGE') {
          if (!card.components[0].fileUrl && !card.components[0].file) {
            errors.cardHeaderFile = `Header image file is required for card ${index}`;
          }
          if (card.components[0].file) {
            const _file = card.components[0].file as File;
            if (_file.type !== 'image/jpeg' && _file.type !== 'image/png') {
              errors.cardHeaderFile = `Invalid Image Type. The image should be of PNG or JPEG type for card ${index}`;
            }
            if (_file.size > 5242880) {
              errors.cardHeaderFile = `Image size must not be more than 5MB for card ${index}`;
            }
          }
        }
        if (card.components[0].format == 'VIDEO') {
          if (!card.components[0].fileUrl && !card.components[0].file) {
            errors.cardHeaderFile = `Header video file is required for card ${index}`;
          }
          if (card.components[0].file) {
            const _file = card.components[0].file as File;
            if (_file.type !== 'video/mp4' && _file.type !== 'video/3gp') {
              errors.cardHeaderFile = `Invalid Video Type. The video should be of MP4 or 3GP type for card ${index}`;
            }
            if (_file.size > 16777216) {
              errors.cardHeaderFile = `Video size must not be more than 16MB for card ${index}`;
            }
          }
        }
        const callToActionWebsite = card.components[2].buttons.callToActionButtons.find((button) => button.btnType === 'Visit Website');
        const callToActionPhone = card.components[2].buttons.callToActionButtons.find((button) => button.btnType === 'Call phone number');
        const quickReplyCustom = card.components[2].buttons.quickReplyButtons.find((button) => button.btnType === 'custom');
        const quickReplyOptOut = card.components[2].buttons.quickReplyButtons.find((button) => button.btnType === 'Marketing opt-out');
        if (!callToActionPhone && !callToActionWebsite && !quickReplyCustom && !quickReplyOptOut) {
          errors.cardButtons = `At least one button is required for card ${index}`;
        }
        if (callToActionPhone) {
          if (!callToActionPhone.phone_number) {
            errors.callToActionPhoneNumber = `Phone number cannot be empty for card ${index}`;
          } else {
            if (callToActionPhone.phone_number.length < 7) errors.callToActionPhoneNumber = `Phone number invalid length for card ${index}`;
          }
          if (!callToActionPhone.text) {
            errors.callToActionPhoneText = `Button Text for Phone Number cannot be empty for card ${index}`;
          }
        }
        if (callToActionWebsite) {
          if (!callToActionWebsite.url) {
            errors.callToActionWebsite = `Website Url cannot be empty for card ${index}`;
          }
          if (!callToActionWebsite.text) {
            errors.callToActionWebsiteText = `Button Text for Website cannot be empty for card ${index}`;
          }
        }
        if (quickReplyCustom) {
          if (!quickReplyCustom.text) {
            errors.callToActionWebsiteText = `Button Text for Website cannot be empty for card ${index}`;
          }
        }
        if (cardBodyLength == 0) {
          errors.cardBody = `Body cannot be empty for card ${index}`;
        }
        if (cardBodyLength > getLimit('carousel-template')) {
          errors.cardBody = `Card Body cannot be more than ${getLimit('carousel-template')} characters for card ${index}`;
        }
      });
    }

    if (body.example?.body_text.some((item) => item === '')) {
      body.example?.body_text.forEach((item, index) => {
        if (item === '') {
          errors[`variables_${index}`] = `Variable ${index + 1} value cannot be empty`;
        }
      });
    }
    if (Object.keys(errors).length > 0) {
      return true;
    }
    errors = {};
    return false;
  };

  const TopBar = ({ submitFunc }: { submitFunc: () => Promise<void> }) => {
    return (
      <div className="flex flex-1 items-center bg-white py-3 px-5 shadow-lg sticky -top-6 z-10">
        <div className="flex-1 font-bold text-xl">
          {templateName.length > 80 ? `${templateName.substring(0, 80)}...` : templateName} • {templateLanguage.label}
        </div>
        <div className="flex items-center space-x-2">
          {!loading ? (
            submitDisabled() ? (
              <HoverCard defaultOpen={false} openDelay={0}>
                <HoverCardTrigger className="hover:cursor-not-allowed">
                  <Button id="submit" className="bg-blue-500" disabled={true}>
                    Submit
                  </Button>
                </HoverCardTrigger>
                <HoverCardContent className="text-sm">
                  <Alert variant="destructive">
                    <AlertCircle className="h-4 w-4" />
                    <AlertTitle>Validation Error</AlertTitle>
                    <AlertDescription>
                      <ul className="list-disc list-inside mt-2 space-y-1">
                        {Object.keys(errors).length > 0 && (
                          <div className="text-red-500">
                            {Object.values(errors).map((error, index) => (
                              <li key={index}>{error}</li>
                            ))}
                          </div>
                        )}
                      </ul>
                    </AlertDescription>
                  </Alert>
                </HoverCardContent>
              </HoverCard>
            ) : (
              <Button
                id="submit"
                className="bg-blue-500"
                onClick={() => {
                  submitFunc();
                }}
              >
                Submit
              </Button>
            )
          ) : (
            <Button className="bg-blue-500" disabled>
              <Loader2 className="h-6 w-6 animate-spin text-accent-foreground mr-2" /> Submitting
            </Button>
          )}
          <Button
            variant={'outline'}
            onClick={() => {
              setGoBackAlert(true);
            }}
          >
            Back
          </Button>
        </div>
      </div>
    );
  };

  const GoBackAlertDialog = () => {
    return (
      <AlertDialog open={goBackAlert} onOpenChange={setGoBackAlert}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Are you sure you want to leave?</AlertDialogTitle>
            <AlertDialogDescription>You have unsaved changes that will be lost if you leave this page.</AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogAction
              onClick={() => {
                setGoBackAlert(false);
                if (template_operation == 'create-new') {
                  setTemplatePage('1');
                  dispatch({ type: 'RESET_STATE', payload: initialState });
                  return;
                }
                redirectFunction(`/${account.id}/templates`, false);
              }}
            >
              OK
            </AlertDialogAction>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    );
  };

  const ErrorAlertDialog = () => {
    return (
      <AlertDialog open={!!error}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle className="tracking-tight">An error occured while creating template</AlertDialogTitle>
            <AlertDialogDescription className="w-full">
              <div className="tracking-tight">{error?.error_user_title}.</div>
              <div>Message: {error?.error_user_msg}.</div>
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogAction onClick={() => setError(null)}>Ok</AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    );
  };

  const memoizedCardButtons = useMemo(() => cards[0].components[2].buttons, [cards[0].components[2].buttons]);
  const isDisabledAddCards =
    cards[cards.length - 1].components[2].buttons.callToActionButtons.length == 0 &&
    cards[cards.length - 1].components[2].buttons.quickReplyButtons.length == 0;
  if (templatePage == '1' && template_operation === 'create-new') {
    return (
      <div>
        <div className="flex flex-1 items-center bg-white py-3 px-5 shadow-sm ">
          <div className="font-bold text-xl flex-1">New message template</div>
          <div className="space-x-4">
            <Link prefetch={false} href={`/${account.id}/templates`} className="mb-0">
              Cancel
            </Link>

            <Button type="button" variant={'secondary'} onClick={() => setTemplatePage('2')}>
              Continue
            </Button>
          </div>
        </div>
        <div className="p-5 flex flex-col space-y-2 items-center bg-gray-200">
          <div className="bg-white space-y-2 g-white p-4 w-9/12">
            <div className="text-sm font-bold mb-1">Template Type</div>
            <div className="text-sm mb-4 text-gray-600">Choose your template type</div>
            <div className="mt-2 pb-4">
              <RadioGroup
                value={templateType}
                onValueChange={(value: 'basic-template' | 'carousel-template' | 'utility-template') => {
                  dispatch({
                    type: 'SET_TEMPLATE_TYPE',
                    payload: value,
                  });
                }}
              >
                <div
                  className={clsx('flex space-x-4 hover:bg-gray-50  p-2 rounded-lg hover:cursor-pointer', templateType === 'basic-template' && 'bg-blue-50')}
                >
                  <RadioGroupItem value="basic-template" id="basic-template" />
                  <Label htmlFor="basic-template" className="flex flex-col justify-start space-y-2 font-normal text-left hover:cursor-pointer w-full">
                    <div>Marketing Template</div>
                    <div className="text-xs">Send promotional offers, announcements and more to increase awareness and engagement.</div>
                  </Label>
                </div>
                <div
                  className={clsx('flex space-x-4 hover:bg-gray-50  p-2 rounded-lg hover:cursor-pointer', templateType === 'carousel-template' && 'bg-blue-50')}
                >
                  <RadioGroupItem value="carousel-template" id="carousel-template" />
                  <Label htmlFor="carousel-template" className="flex flex-col justify-start space-y-2 font-normal text-left hover:cursor-pointer w-full">
                    <div>Carousel Template</div>
                    <div className="text-xs">Send messages about your entire catalog or multiple products from it</div>
                  </Label>
                </div>
                <div
                  className={clsx('flex space-x-4 hover:bg-gray-50  p-2 rounded-lg hover:cursor-pointer', templateType === 'utility-template' && 'bg-blue-50')}
                >
                  <RadioGroupItem value="utility-template" id="utility-template" />
                  <Label htmlFor="utility-template" className="flex flex-col justify-start space-y-2 font-normal text-left hover:cursor-pointer w-full">
                    <div>Utility Template</div>
                    <div className="text-xs">
                      Provide useful updates or important notifications about account activities, order confirmations, transactions, appointments, or system
                      alerts.
                    </div>
                  </Label>
                </div>
              </RadioGroup>
            </div>
          </div>
        </div>
      </div>
    );
  }
  if (templateType == 'basic-template' || templateType == 'utility-template') {
    return (
      <div>
        {/* go back alert component */}
        <GoBackAlertDialog />
        {/* top bar component */}
        <TopBar submitFunc={submitTemplate} />
        <div className="bg-gray-200 pt-2">
          <div className="flex space-x-2">
            <div className="flex-1 p-6 bg-white">
              {/* submit, name and lanugage select component */}
              <SubmitNameAndLanguage
                handleChangeTemplateName={handleChangeTemplateName}
                templateName={templateName}
                template_operation={template_operation}
                templateType={templateType}
                templateLanguage={templateLanguage}
                dispatch={dispatch}
              />
              <div>
                <div className="mb-6">
                  <div id="select-header">
                    <TemplateHeader header={header} setHeader={setHeader} />
                    {/* <Header header={header} setHeader={setHeader} headerError={headerError} setHeaderError={setHeaderError} type="basic-template" /> */}
                  </div>
                  <div id="enter-message">
                    {/* Body of basic create template */}
                    <Body body={body} setBody={setBody} type="basic-template" setBodyLength={setBodyLength} />
                  </div>
                  <div id="footer">
                    <Footer footer={footer} setFooter={setFooter} />
                  </div>
                  <div id="button">
                    <DropDownButtons
                      callToActionButtons={buttons.callToActionButtons}
                      quickReplyButtons={buttons.quickReplyButtons}
                      handleChangeCallToActionButtons={handleChangeCallToActionButtons}
                      handleChangeQuickReplyButtons={handleChangeQuickReplyButtons}
                      setFooter={setFooter}
                      templateType={templateType}
                    />
                  </div>
                </div>
              </div>
            </div>
            <div className="lg:w-80 w-52">
              <div className="sticky top-12">
                <div className="bg-white p-4">
                  <div className="text-lg font-semibold">Message Preview</div>
                </div>
                {/* TODO: when there are more than two /n consecutively, remove one to make it one only. */}
                <div id="message-preview">
                  <MessagePreview
                    body={body}
                    footer={footer}
                    callToActionButtons={buttons.callToActionButtons}
                    header={header}
                    quickReplyButtons={buttons.quickReplyButtons}
                    fromManageTemplate={true}
                  />
                </div>
              </div>
            </div>
          </div>
        </div>
        <ErrorAlertDialog />
      </div>
    );
  }

  return (
    <div>
      {/* go back alert component */}
      <GoBackAlertDialog />
      {/* top bar component */}
      <TopBar submitFunc={submitTemplateCarousel} />
      <div className="bg-gray-200 pt-2">
        <div className="flex space-x-2">
          <div className="flex-1 p-6 bg-white">
            {/* submit, name and lanugage select component */}
            <SubmitNameAndLanguage
              handleChangeTemplateName={handleChangeTemplateName}
              templateName={templateName}
              template_operation={template_operation}
              templateType={templateType}
              templateLanguage={templateLanguage}
              dispatch={dispatch}
            />
            <div>
              <div className="flex-col space-y-4 mb-2">
                <Body body={body} setBody={setBody} setBodyLength={setBodyLength} type="messageBody" />
                <div>
                  <div className="font-semibold text-base flex gap-2">Media Type</div>
                  <div className="text-sm text-gray-800 mb-2">
                    Choose which type of media you'll use for card(s) header. (All the headers will have the same media type)
                  </div>
                  <AllCardHeaders handleChangeAllCardHeaders={handleChangeAllCardHeaders} allCardHeaders={allCardHeaders} />

                  <AllCardDropDownButtons
                    cardButtons={memoizedCardButtons}
                    handleDeleteButtonsFromAllCards={handleDeleteButtonsFromAllCards}
                    handleSetButtonsForAllCards={handleSetButtonsForAllCards}
                  />
                </div>

                {cards.map((card, index) => (
                  <CardComponents
                    key={index}
                    card={card}
                    index={index}
                    handleChangeCardBody={handleChangeCardBody}
                    handleChangeCardHeader={handleChangeCardHeader}
                    handleChangeQuickReplyButtons={handleChangeQuickReplyCardButtons}
                    handleChangeCallToActionButtons={handleChangeCallToActionCardButtons}
                    removeCard={removeCard}
                  />
                ))}

                {cards.length < 10 &&
                  (isDisabledAddCards ? (
                    <HoverCard defaultOpen={false} openDelay={0}>
                      <HoverCardTrigger className="hover:cursor-not-allowed">
                        <Button disabled className="mt-4">
                          Add Another Card
                        </Button>
                      </HoverCardTrigger>
                      <HoverCardContent className="text-sm">
                        Please add button to the card before adding another card. To add a button scroll to the top and add a button to the card.
                      </HoverCardContent>
                    </HoverCard>
                  ) : (
                    <Button
                      disabled={isDisabledAddCards}
                      onClick={() => {
                        addCard();
                        setTimeout(() => {
                          const lastCard = document.getElementById(`card-${cards.length + 1}`);
                          if (lastCard) {
                            lastCard.scrollIntoView({ behavior: 'smooth' });
                          }
                        }, 100);
                      }}
                      className="mt-4"
                    >
                      Add Another Card
                    </Button>
                  ))}
              </div>
            </div>
          </div>
          <div className="lg:w-80 w-52">
            <div className="sticky top-12">
              <div className="bg-white p-4">
                <div className="text-lg font-semibold">Message Preview</div>
              </div>
              <div id="message-preview">
                <MessagePreviewCarousel body={body} cards={cards} />
              </div>
            </div>
          </div>
        </div>
      </div>
      <ErrorAlertDialog />
    </div>
  );
};

export default CreateTemplate;
