'use client';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import React from 'react';
import { IQuickReplyBtn } from '../types';

const CardQuickReplyButtons = ({
  btn,
  index,
  cardIndex,
  quickReplyButtons,
  handleChangeQuickReplyButtons,
}: {
  btn: IQuickReplyBtn;
  index: number;
  cardIndex: number;
  quickReplyButtons: IQuickReplyBtn[];
  handleChangeQuickReplyButtons: (index: number, quickReplyButtons: IQuickReplyBtn[]) => void;
}) => {
  const handleChangeQuickReply = (e: React.ChangeEvent<HTMLInputElement>) => {
    handleChangeQuickReplyButtons(
      cardIndex,
      quickReplyButtons.map((item: IQuickReplyBtn, idx: number) =>
        item.id == btn.id
          ? {
              ...item,
              text: e.target.value,
            }
          : item
      )
    );
  };

  const handleDropdownQuickReplyChange = (e: string) => {
    handleChangeQuickReplyButtons(
      cardIndex,
      quickReplyButtons.map((item: IQuickReplyBtn, idx: number) => {
        if (idx == index) {
          return {
            ...item,
            btnType: e,
            text: '',
          };
        } else {
          return {
            ...item,
          };
        }
      })
    );
  };

  if (btn.btnType == 'Marketing opt-out') {
    return (
      <div className="mx-8 py-2  hover:bg-gray-200 flex items-center space-x-2">
        <div className="bg-gray-100 border border-gray-300 p-4 rounded-lg mx-4 space-y-4 w-full">
          <div className="flex space-x-2">
            <div className="flex-1 space-y-1">
              <Label>Type</Label>
              <Select onValueChange={(val) => handleDropdownQuickReplyChange(val)} value={btn.btnType} disabled>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="Marketing opt-out">Marketing opt-out</SelectItem>
                  <SelectItem value="Custom">Custom</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div className="flex-1 space-y-1">
              <Label htmlFor="picture">Button Text</Label>
              <Input type="text" className="border border-gray-300 focus:outline-none" disabled readOnly value="Stop promotions" maxLength={25} />
              <div className="text-gray-500 text-sm text-right">Characters: {'stop promotions'.length}/25</div>
            </div>
            <div className="flex-1 space-y-1">
              <Label htmlFor="picture">Footer Text</Label>
              <Input
                type="text"
                id="footer-text"
                className="border border-gray-300 focus:outline-none"
                disabled
                readOnly
                value="Not interested? Tap Stop promotions"
              />
            </div>
          </div>
          <div className="flex items-center space-x-2 text-sm text-gray-900"></div>
        </div>
      </div>
    );
  } else {
    return (
      <div className="mx-8 py-2  hover:bg-gray-200 flex items-center space-x-2">
        <div className="bg-gray-100 border border-gray-300 p-4 rounded-lg mx-4 space-y-4 w-full">
          <div className="flex space-x-2 bg">
            <div className="w-52 space-y-1">
              <Label>Type</Label>
              <Select onValueChange={(val) => handleDropdownQuickReplyChange(val)} value={btn.btnType} disabled>
                <SelectTrigger>
                  <SelectValue placeholder={btn.btnType} />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="Marketing opt-out">Marketing opt-out</SelectItem>
                  <SelectItem value="Custom">Custom</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div className="flex-1 space-y-1">
              <Label htmlFor="picture">Button Text</Label>
              <Input
                type="text"
                id="footer-text"
                className="border border-gray-300 focus:outline-none"
                onChange={(val) => handleChangeQuickReply(val)}
                value={btn.text}
                name="text"
                placeholder="Add your text here"
                maxLength={25}
              />
              <div className="text-gray-500 text-sm text-right">Characters: {btn.text.length}/25</div>
            </div>
          </div>
        </div>
      </div>
    );
  }
};

export default React.memo(CardQuickReplyButtons);
