'use client';
import React from 'react';
import { ICallToActionBtn, IQuickReplyBtn } from '../types';
import CardCallToActionButtons from './CardCallToActionButtons';
import CardQuickReplyButtons from './CardQuickReplyButtons';

interface CardDropDownButtonsProps {
  quickReplyButtons: IQuickReplyBtn[];
  handleChangeQuickReplyButtons: (index: number, quickReplyButtons: ICallToActionBtn[]) => void;
  callToActionButtons: ICallToActionBtn[];
  handleChangeCallToActionButtons: (index: number, callToActionButtons: IQuickReplyBtn[]) => void;
  index: number;
}

const CardDropDownButtons: React.FC<CardDropDownButtonsProps> = ({
  quickReplyButtons,
  handleChangeQuickReplyButtons,
  callToActionButtons,
  handleChangeCallToActionButtons,
  index,
}) => {
  return (
    <div>
      {(quickReplyButtons.length > 0 || callToActionButtons.length > 0) && (
        <div>
          <h3 className="font-semibold">Buttons</h3>
          <div className="text-sm text-gray-500 mb-2">Edit buttons so that customers can respond to your message or take an action.</div>
        </div>
      )}
      {quickReplyButtons.length > 0 && (
        <div className="border border-gray-200 rounded-lg p-4 mb-4">
          <div className="font-bold pb-4">Quick Reply</div>

          {quickReplyButtons.map((btn, btnIndex) => (
            <CardQuickReplyButtons
              btn={btn}
              index={btnIndex}
              cardIndex={index}
              key={btnIndex}
              quickReplyButtons={quickReplyButtons}
              handleChangeQuickReplyButtons={handleChangeQuickReplyButtons}
            />
          ))}
        </div>
      )}

      {callToActionButtons.length > 0 && (
        <div className="border border-gray-200 rounded-lg p-4">
          <div className="font-bold pb-4">Call to action</div>

          {callToActionButtons.map((btn, btnIndex) => (
            <CardCallToActionButtons
              btn={btn}
              index={btnIndex}
              key={btnIndex}
              cardIndex={index}
              callToActionButtons={callToActionButtons}
              handleChangeCallToActionButtons={handleChangeCallToActionButtons}
            />
          ))}
          {/* </form> */}
        </div>
      )}
    </div>
  );
};

export default React.memo(CardDropDownButtons);
