'use client';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { PhoneInput } from '@/components/ui/phone-input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { CountryCode } from 'libphonenumber-js';
import React from 'react';
import { ICallToActionBtn } from '../types';

const CardCallToActionButtons = ({
  btn,
  index,
  cardIndex,
  callToActionButtons,
  handleChangeCallToActionButtons,
}: {
  btn: ICallToActionBtn;
  index: number;
  cardIndex: number;
  callToActionButtons: ICallToActionBtn[];
  handleChangeCallToActionButtons: (index: number, callToActionButtons: ICallToActionBtn[]) => void;
}) => {
  const handleChangeCallToAction = (e: React.ChangeEvent<HTMLInputElement>) => {
    handleChangeCallToActionButtons(
      cardIndex,
      callToActionButtons.map((item: any, idx: number) =>
        item.id == btn.id
          ? {
              ...item,
              [e.target.name]: e.target.value,
            }
          : item
      )
    );
  };

  const handleDropdownCallToActionChange = (e: string) => {
    handleChangeCallToActionButtons(
      cardIndex,
      callToActionButtons.map((item: any, idx: number) => {
        let type = '';
        if (e == 'Visit Website') {
          type = 'URL';
        }
        if (e == 'Call phone number') {
          type = 'PHONE_NUMBER';
        }
        if (idx == index) {
          return {
            ...item,
            btnType: e,
            type,
            text: '',
          };
        } else {
          return {
            ...item,
          };
        }
      })
    );
  };

  const popularCountryCode = [
    'AE',
    'SA',
    'US',
    'CA',
    'RU',
    'KZ',
    'EG',
    'ZA',
    'GR',
    'NL',
    'BE',
    'FR',
    'ES',
    'IT',
    'GB',
    'DE',
    'MX',
    'BR',
    'MY',
    'AU',
    'ID',
    'PH',
    'NZ',
    'SG',
    'TH',
    'JP',
    'KR',
    'VN',
    'CN',
    'TR',
    'IN',
    'PK',
    'AF',
    'LK',
    'MM',
    'IR',
    'MA',
    'DZ',
    'TN',
    'LY',
    'GM',
    'SN',
    'NG',
    'RW',
    'ET',
    'KE',
    'UG',
    'ZM',
    'ZW',
  ] as CountryCode[];

  switch (btn.btnType) {
    case 'Visit Website':
      return (
        <div className="mx-8 py-2  hover:bg-gray-200 flex items-center space-x-2">
          <div className="bg-gray-100 border border-gray-300 p-4 rounded-lg mx-4 space-y-4 w-full">
            <div className="flex space-x-2">
              <div className="w-52 space-y-1">
                <Label>Type</Label>
                <Select onValueChange={(val) => handleDropdownCallToActionChange(val)} value={btn.btnType} disabled>
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="Visit Website">Visit Website</SelectItem>
                    <SelectItem value="Call phone number">Call phone number</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div className="flex-1 space-y-1">
                <Label htmlFor="picture">Button Text</Label>
                <Input
                  type="text"
                  className="border border-gray-300 focus:outline-none"
                  onChange={handleChangeCallToAction}
                  value={btn.text}
                  placeholder="Add button text"
                  name="text"
                  maxLength={25}
                />
                <div className="text-gray-500 text-sm text-right">Characters: {btn.text.length}/25</div>
              </div>
              <div className="flex-1 space-y-1">
                <Label htmlFor="picture">Website URL</Label>
                <Input
                  type="url"
                  className="border border-gray-300 focus:outline-none"
                  onChange={handleChangeCallToAction}
                  value={btn.url?.trim()}
                  placeholder="Add website url"
                  name="url"
                  maxLength={2000}
                />
                <div className="text-gray-500 text-sm text-right">Characters: {btn.url?.trim().length}/2000</div>
              </div>
            </div>
          </div>
        </div>
      );

    case 'Call phone number':
      return (
        <div className="mx-8 py-2  hover:bg-gray-200 flex items-center space-x-2">
          <div className="bg-gray-100 border border-gray-300 p-4 rounded-lg mx-4 space-y-4 w-full">
            <div className="flex space-x-2 bg">
              <div className="w-52 space-y-1">
                <Label>Type</Label>
                <Select onValueChange={(val) => handleDropdownCallToActionChange(val)} value={btn.btnType} disabled>
                  <SelectTrigger>
                    <SelectValue placeholder={btn.btnType} />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="Visit Website">Visit Website</SelectItem>
                    <SelectItem value="Call phone number">Call phone number</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div className="space-y-1 w-1/4">
                <Label htmlFor="picture">Button Text</Label>
                <Input
                  type="text"
                  id="footer-text"
                  className="border border-gray-300 focus:outline-none"
                  onChange={handleChangeCallToAction}
                  value={btn.text}
                  name="text"
                  placeholder="Add Button Text"
                  maxLength={25}
                />
                <div className="text-gray-500 text-sm text-right">Characters: {btn.text.length}/25</div>
              </div>

              <div className="flex-1 space-y-1">
                <Label htmlFor="picture">Country and Phone Number</Label>
                <PhoneInput
                  placeholder="Enter a phone number"
                  international={true}
                  countries={popularCountryCode}
                  value={btn.phone_number}
                  maxLength={20}
                  name="phone_number"
                  onChange={(e) => {
                    handleChangeCallToActionButtons(
                      cardIndex,
                      callToActionButtons.map((item: any, idx: number) =>
                        item.id == btn.id
                          ? {
                              ...item,
                              phone_number: e,
                            }
                          : item
                      )
                    );
                  }}
                />
                <div className="flex items-center justify-between">
                  <div className="text-gray-500 text-sm text-right">Characters: {btn?.phone_number?.trim().length}/20</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      );
      break;
  }
};

export default React.memo(CardCallToActionButtons);
