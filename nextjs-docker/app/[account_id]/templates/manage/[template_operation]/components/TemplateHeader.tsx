'use client';
import { Button } from '@/components/ui/button';
import { DropdownMenu, DropdownMenuContent, DropdownMenuRadioGroup, DropdownMenuRadioItem, DropdownMenuTrigger } from '@/components/ui/dropdown-menu';
import { Input } from '@/components/ui/input';
import { Toaster } from '@/components/ui/toaster';
import { useToast } from '@/components/ui/use-toast';
import { ChevronDown, Files, PlusIcon, Trash } from 'lucide-react';
import Image from 'next/image';
import React, { useRef, useState } from 'react';
import { IHeader } from '../types';

//TODO: Enable template headers variable.
interface TemplateHeaderProps {
  setHeader: (header: Partial<IHeader>) => void;
  header: IHeader;
}

const TemplateHeader: React.FC<TemplateHeaderProps> = ({ header, setHeader }) => {
  const [file, setFile] = useState<File | null>(null);
  const { toast } = useToast();
  const inputRef = useRef<HTMLInputElement>(null);
  const handleHeaderChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const regexPattern = /\{\{\d+\}\}/g;
    const matches = [...header.text.matchAll(regexPattern)];
    if (matches.length > 0) {
      if (!header?.example?.header_text) {
        setHeader({
          text: e.target.value,
          example: {
            header_text: [''],
          },
        });
      } else {
        setHeader({ text: e.target.value });
      }
    } else {
      setHeader({ text: e.target.value, example: {} });
    }
  };

  const replaceMatchesHeader = (e: React.ChangeEvent<HTMLInputElement>, index: number) => {
    const tempExample = { ...header.example };
    const headerTextArray = tempExample.header_text || [];
    const newHeaderText = [...headerTextArray];
    newHeaderText[index] = e.target.value;
    setHeader({ example: { header_text: newHeaderText } });
  };

  const handleAddVariableToheader = () => {
    let headerCopy = { ...header };
    headerCopy.text += '{{1}}';
    if (!header?.example?.header_text) {
      setHeader({
        text: headerCopy.text,
        example: {
          header_text: [''],
        },
      });
    }
  };

  return (
    <div className="mt-4">
      <div className="font-semibold text-base flex gap-2 mb-1">
        Header
        <div className="text-gray-500">(Optional)</div>
      </div>
      <div className="text-sm text-gray-800 mb-2">Add a title or choose which type of media you'll use for this header.</div>
      <div className="flex flex-col gap-2 space-2" id="select-header">
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="outline" className="capitalize mb-0 w-80">
              {header.format.toLocaleLowerCase()}
              <ChevronDown className="ml-1" />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent className="w-56">
            <DropdownMenuRadioGroup
              value={header.format}
              onValueChange={(value) => {
                setFile(null);
                setHeader({ file: null, format: value, fileUrl: undefined, text: '' });
              }}
            >
              <>
                <DropdownMenuRadioItem value="TEXT">Text</DropdownMenuRadioItem>
                <DropdownMenuRadioItem value="IMAGE">Image</DropdownMenuRadioItem>
                <DropdownMenuRadioItem value="VIDEO">Video</DropdownMenuRadioItem>
              </>
            </DropdownMenuRadioGroup>
          </DropdownMenuContent>
        </DropdownMenu>

        {header.format === 'DOCUMENT' && (
          <div className="flex flex-col text-sm">
            <div className="mb-2">
              Allowed document type: <span className="font-bold">PDF</span>
            </div>

            {file ? (
              <div className="my-4 rounded-lg bg-gray-100 p-4 flex items-center">
                <Files />
                <span className="ml-2">{file.name}</span>
              </div>
            ) : null}
            <div className="my-2">Note: This is an example document, you can use same document or change it while sending the template</div>
            <Input
              onChange={(e) => {
                setFile(e.currentTarget?.files?.[0] ?? null);
                setHeader({
                  format: 'DOCUMENT',
                  file: e.currentTarget?.files?.[0] ?? null,
                });
              }}
              type="file"
              name="file"
              id="file"
            />
          </div>
        )}
        {header.format === 'VIDEO' && (
          <div className="flex flex-col text-sm">
            <div className="">For best video viewing:</div>
            <div>
              Dimensions: <span className="font-bold">1125px by 600px</span>
            </div>
            <div>
              Max video size: <span className="font-bold">16mb</span>
            </div>
            <div className="mb-2">
              Allowed video type: <span className="font-bold">MP4 Video and 3GPP</span>
            </div>

            {/* if header file url is present but no file is selected */}
            {header.fileUrl && !file ? (
              <video height={250} width={400} src={header.fileUrl} controls />
            ) : // if file is selected
            file ? (
              <video height={250} width={400} src={URL.createObjectURL(file)} controls />
            ) : (
              <div></div>
            )}
            <div className="my-2">Note: This is an example video, you can send the same video or change it while sending the template</div>
            <div className="flex items-center gap-2">
              <Input
                className="w-1/3"
                onChange={(e) => {
                  const selectedFile = e.currentTarget?.files?.[0] ?? null;
                  if (selectedFile && !['video/mp4', 'video/3gpp'].includes(selectedFile.type)) {
                    toast({
                      variant: 'destructive',
                      description: 'Invalid file type. Only MP4 and 3GPP are allowed.',
                    });
                    return;
                  }
                  setFile(selectedFile);
                  setHeader({
                    format: 'VIDEO',
                    file: selectedFile,
                  });
                }}
                type="file"
                accept="video/mp4, video/3gpp"
                name="file"
                id="file"
              />
              <Button
                variant="outline"
                onClick={() => {
                  setHeader({ file: null, fileUrl: undefined });
                  setFile(null);
                }}
              >
                X
              </Button>
            </div>
          </div>
        )}
        {header.format === 'IMAGE' && (
          <div className="flex flex-col gap-2 space-2">
            <div className="text-sm bg-gray-50 rounded-md border p-2">
              <div>
                For best viewing image, the recommended dimensions are <span className="font-bold">1125px by 600px</span>
              </div>
              <div></div>
              <div>
                Max Allowed size: <span className="font-bold">5mb</span>
              </div>
              <div>
                Allowed image type: <span className="font-bold">JPEG and PNG</span>
              </div>
            </div>
            {/* if header file url is present but no file is selected */}
            {header.fileUrl && !file ? (
              <Image height={0} width={0} src={header.fileUrl} alt="header" className="h-auto w-60" sizes="100vh" />
            ) : // if file is selected
            file ? (
              <div className="relative max-w-fit">
                <img alt="image" src={URL.createObjectURL(file)} className="h-auto w-60 rounded-lg" />
                <div className="absolute -top-3 -right-3">
                  <Button
                    variant="outline"
                    size={'icon'}
                    className="bg-white"
                    onClick={() => {
                      if (inputRef?.current) {
                        inputRef.current.value = '';
                      }
                      setHeader({ file: null, fileUrl: undefined });
                      setFile(null);
                    }}
                  >
                    <Trash className="text-red-500" />
                  </Button>
                </div>
              </div>
            ) : (
              <div></div>
            )}
            <div className="my-2">Note: This is an example image, you can use same image or change it while sending the template</div>
            <div className="flex items-center gap-2">
              <Input
                ref={inputRef}
                className="w-1/3"
                onChange={(e) => {
                  const selectedFile = e.currentTarget?.files?.[0] ?? null;
                  if (selectedFile && !['image/jpeg', 'image/png', 'image/jpg'].includes(selectedFile.type)) {
                    toast({
                      variant: 'destructive',
                      description: 'Invalid file type. Only JPEG and PNG are allowed.',
                    });
                    return;
                  }
                  setFile(selectedFile);
                  setHeader({
                    format: 'IMAGE',
                    file: selectedFile,
                  });
                }}
                type="file"
                accept="image/png, image/jpeg"
                name="file"
                id="file"
              />
            </div>
          </div>
        )}
        {header.format == 'TEXT' && (
          <div className="flex flex-col flex-1 space-y-2">
            <Input placeholder="Enter text" onChange={handleHeaderChange} value={header.text} maxLength={60} />
            <Button
              className="w-52 self-end"
              variant={'outline'}
              //@ts-ignore
              disabled={header?.example?.header_text?.length == 1}
              // onClick={() => handleAddVariableToheader()}
            >
              <PlusIcon /> Add Variable
            </Button>
            <div className="text-gray-500 text-sm text-right">Characters: {header?.text?.length ?? 1}/60</div>
          </div>
        )}
      </div>
      {header?.example?.header_text && (
        <div className="bg-gray-100 p-5 mt-5">
          <div className="font-semibold">Samples for header content</div>
          <div className="text-sm text-gray-700 mb-2">
            To help us review your content, provide examples of the variables or media in the header. Do not include any customer information. Cloud API hosted
            by Meta reviews templates and variable parameters to protect the security and integrity of our services.
          </div>
          {header.example.header_text.map((item, index) => (
            <div className="flex space-x-2 items-center" key={index}>
              <div>{`{{${index + 1}}}`}</div>
              <Input placeholder={`Enter content for {{${index + 1}}}`} className="flex-1" onChange={(e) => replaceMatchesHeader(e, index)} />
            </div>
          ))}
        </div>
      )}
      <Toaster />
    </div>
  );
};

export default React.memo(TemplateHeader);
