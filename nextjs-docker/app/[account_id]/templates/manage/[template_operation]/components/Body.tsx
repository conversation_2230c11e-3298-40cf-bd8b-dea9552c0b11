'use client';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import ShineBorder from '@/components/ui/shine-border';
import { getLimit } from '@/lib/utils';
import { experimental_useObject } from 'ai/react';
import clsx from 'clsx';
import { Loader2, PlusIcon } from 'lucide-react';
import { useParams } from 'next/navigation';
import Quill from 'quill';
import 'quill-paste-smart';
import 'quill/dist/quill.snow.css'; // Add css for snow theme
import React, { useCallback, useEffect, useRef, useState } from 'react';
import { useQuill } from 'react-quilljs';
import { z } from 'zod';
import { IBody } from '../types';
import QuillToolBar from './QuillToolBar';
import './styles.scss';

interface BodyProps {
  body: IBody;
  setBody: (body: IBody) => void;
  // bodyError: string;
  type: 'basic-template' | 'carousel-template' | 'input-box' | 'messageBody';
  setBodyLength: React.Dispatch<React.SetStateAction<number>>;
}

const bodyProps = {
  type: 'BODY',
  format: 'TEXT',
};

const Body: React.FC<BodyProps> = ({ body, setBody, type, setBodyLength }) => {
  const bodyRef = useRef(body);
  const paramsArrayRef = useRef<string[]>([]);
  const modules = {
    toolbar: false, // Disable default toolbar
  };
  const { quill, quillRef } = useQuill({ modules });

  // Update the ref whenever body changes
  useEffect(() => {
    bodyRef.current = body;
  }, [body]);

  const { account_id } = useParams();

  const [emojiPickerOpen, setEmojiPickerOpen] = useState(false);
  const onChange = useCallback(
    (content: string) => {
      content = content.replace(/<p>/g, '<p class="whitespace-pre-wrap">');
      setBody({ ...bodyRef.current, text: content });
      if (quill) {
        setBodyLength(quill.getLength() - 1);
      }
    },
    [setBody, setBodyLength, quill]
  );

  const handleFormat = useCallback(
    (format: string) => {
      if (!quill) return;

      const range = quill.getSelection();
      if (!range) {
        quill.focus();
        return;
      }

      const text = quill.getText(range.index, range.length);
      let prefix = '';

      switch (format) {
        case 'bold':
          prefix = '*';
          break;
        case 'italic':
          prefix = '_';
          break;
        case 'strike':
          prefix = '~~';
          break;
      }

      quill.deleteText(range.index, range.length);
      quill.insertText(range.index, `${prefix}${text}${prefix}`);
      quill.setSelection(range.index - 1 + text.length + prefix.length * 2);
      onChange(quill.root.innerHTML);
    },
    [quill, onChange]
  );

  const replaceMatchesBody = useCallback(
    (e: React.ChangeEvent<HTMLInputElement>, index: number) => {
      if (body.example) {
        const tempExample = { ...body.example };
        const newBodyText = tempExample.body_text.map((item: string, i: number) => {
          if (index === i) {
            return e.target.value;
          }
          return item;
        });
        setBody({ ...body, example: { body_text: newBodyText } });
      }
    },
    [body, setBody]
  );

  const removeVariable = useCallback(
    (index: number) => {
      if (quill) {
        const text = quill.getText();
        const variable = `{{${index + 1}}}`;
        const variableIndex = text.indexOf(variable);
        if (variableIndex !== -1) {
          quill.deleteText(variableIndex, variable.length);
        }
      }
    },
    [quill]
  );

  const handleAddVariableToQuillEditor = useCallback(() => {
    if (quill) {
      quill.focus();
      const range = quill.getSelection();
      if (range) {
        quill.insertText(range.index, ` {{${paramsArrayRef.current.length + 1}}}`);
      }
    }
  }, [quill]);

  useEffect(() => {
    if (quill) {
      quill.setText(body.text ?? ''); // Set initial value
      onChange(quill.root.innerHTML);
      quill.on('text-change', (delta, oldDelta, source) => {
        const content = quill.root.innerHTML;
        const matches = content.match(/{{\d+}}/g) ?? [];
        if (paramsArrayRef.current.length !== matches?.length) {
          paramsArrayRef.current = matches ?? [];
          if (matches.length == 0) {
            setBody({
              ...bodyProps,
              text: quill.root.innerHTML,
            });
            setBodyLength(quill.getLength() - 1);
          } else {
            setBody({
              ...bodyProps,
              text: quill.root.innerHTML,
              example: {
                body_text: matches.map((item, index) => bodyRef.current.example?.body_text?.[index] ?? ''),
              },
            });
            setBodyLength(quill.getLength() - 1);
          }
        } else {
          onChange(content);
        }
      });

      quill.clipboard.addMatcher('STRONG', function (node, delta) {
        let _node = node as HTMLElement;
        const Delta = Quill.import('delta');
        return new Delta().insert(`*${_node.innerText}*`);
      });
      quill.clipboard.addMatcher('IMG', (node, delta) => {
        const Delta = Quill.import('delta');
        return new Delta().insert('');
      });

      quill.clipboard.addMatcher(Node.ELEMENT_NODE, function (node, delta) {
        const Delta = Quill.import('delta');
        if (node.nodeName === 'P') {
          return delta;
        }
        let _node = node as HTMLElement;
        const plaintext = _node.innerText;
        return new Delta().insert(plaintext);
      });
    }
  }, [quill, onChange]);

  const { object, submit, isLoading } = experimental_useObject({
    api: '/api/ai/template-generator',
    schema: z.object({
      content: z.string().max(256),
    }),
  });

  useEffect(() => {
    if (object) {
      quill?.setText(object?.content ?? '');
    }
  }, [object?.content, quill]);

  const addEmoji = useCallback(
    (emoji: string) => {
      if (quill) {
        quill.focus();
        const range = quill.getSelection();
        if (range) {
          quill.insertText(range.index, emoji);
          quill.setSelection(range.index + emoji.length);
        }
      }
      setEmojiPickerOpen(false);
    },
    [quill]
  );

  return (
    <div className="mt-4">
      <div className="font-semibold text-base flex gap-2 mb-1">
        {type == 'basic-template' && 'Body'} {type == 'carousel-template' && 'Card Body'} {type == 'messageBody' && 'Message Body'}
      </div>
      <div className="text-sm text-gray-800 mb-2">Enter the text for your message in the language you've selected.</div>
      <div className="flex flex-col flex-1 space-y-2">
        <div className="fle flex-col flex-1">
          <QuillToolBar emojiPickerOpen={emojiPickerOpen} addEmoji={addEmoji} setEmojiPickerOpen={setEmojiPickerOpen} handleFormat={handleFormat} />
          <div className="h-48 whitespace-pre-wrap w-full" onClick={() => setEmojiPickerOpen(false)}>
            <div ref={quillRef} />
          </div>
        </div>

        <Button className="w-52 self-end" variant={'outline'} onClick={() => handleAddVariableToQuillEditor()}>
          <PlusIcon /> Add Variable
        </Button>
        <div className="flex gap-2 w-full">
          <div className="w-full grow">
            <Input placeholder="Just write one line about the template text" name="ai-prompt" />
          </div>
          <div>
            <ShineBorder className="p-0 min-h-0 min-w-0 flex items-center justify-center" color={['#A07CFE', '#FE8FB5', '#FFBE7B']}>
              <Button
                onClick={() => {
                  submit({ accountId: account_id, templatePrompt: (document?.querySelector('input[name="ai-prompt"]') as any)?.value });
                }}
                className="px-6 inline animate-gradient bg-gradient-to-r from-[#452701] via-[#38056e] to-[#71440d] bg-[length:var(--bg-size)_100%] bg-clip-text text-transparent"
                variant={'ghost'}
              >
                {!isLoading ? 'Generate Template' : <Loader2 color="black" className="h-6 w-6 animate-spin mr-2" />}
              </Button>
            </ShineBorder>
          </div>
        </div>
      </div>
      {type != 'input-box' && quill && (
        <div className={clsx('text-gray-500 text-sm text-right', quill.getLength() - 1 >= getLimit(type) && 'text-red-500 font-bold')}>
          Characters: {quill.getLength() - 1} / {getLimit(type)}
        </div>
      )}

      {body?.example?.body_text && (
        <div className="bg-gray-100 p-5 mt-5">
          <div className="font-semibold">Samples for body content</div>
          <div className="text-sm text-gray-700 mb-2">
            To help us review your message template, please add an example for each variable in your body text. Do not use real customer information. Cloud API
            hosted by Meta reviews templates and variable parameters to protect the security and integrity of our services.
          </div>
          {body?.example && body?.example?.body_text && body?.example?.body_text.length > 0 && (
            <div className="space-y-2">
              {body.example.body_text.map((item: any, index: number) => (
                <MyInput key={index} index={index} item={item} replaceMatchesBody={replaceMatchesBody} body={body} removeVariable={removeVariable} />
              ))}
            </div>
          )}
        </div>
      )}
    </div>
  );
};

interface MyInputProps {
  index: number;
  item: string;
  replaceMatchesBody: (e: React.ChangeEvent<HTMLInputElement>, index: number) => void;
  body: IBody;
  removeVariable: (index: number) => void;
}

const MyInput: React.FC<MyInputProps> = ({ index, item, replaceMatchesBody, body, removeVariable }) => {
  const inputRef = useRef<HTMLInputElement>(null);
  return (
    <div className="flex space-x-2 items-center">
      <div>{`{{${index + 1}}}`}</div>
      <Input
        value={item}
        ref={inputRef}
        placeholder={`Enter content for {{${index + 1}}}`}
        className="flex-1"
        onChange={(e) => {
          replaceMatchesBody(e, index);
          setTimeout(() => {
            inputRef.current?.focus();
          }, 10);
        }}
      />
      {index === (body?.example?.body_text.length ?? 0) - 1 ? (
        <Button variant={'outline'} onClick={() => removeVariable(index)}>
          Remove
        </Button>
      ) : null}
    </div>
  );
};

export default React.memo(Body);
