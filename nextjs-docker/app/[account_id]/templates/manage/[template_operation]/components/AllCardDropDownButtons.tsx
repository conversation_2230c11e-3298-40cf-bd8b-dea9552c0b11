'use client';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuGroup,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import React, { useCallback } from 'react';

import { Button } from '@/components/ui/button';
import { AlertCircle, ChevronDown, Plus, X } from 'lucide-react';

import { v4 as uuidv4 } from 'uuid';

import { ICallToActionBtn, IQuickReplyBtn } from '../types';

type IAllCardDropDownButtonsType = {
  cardButtons: {
    quickReplyButtons: IQuickReplyBtn[];
    callToActionButtons: ICallToActionBtn[];
  };
  handleDeleteButtonsFromAllCards: (type: 'quickReplyButtons' | 'callToActionButtons', buttons: IQuickReplyBtn[] | ICallToActionBtn[]) => void;
  handleSetButtonsForAllCards: (type: 'quickReplyButtons' | 'callToActionButtons', buttons: IQuickReplyBtn[] | ICallToActionBtn[]) => void;
};
const AllCardDropDownButtons = ({ cardButtons, handleDeleteButtonsFromAllCards, handleSetButtonsForAllCards }: IAllCardDropDownButtonsType) => {
  const callToActionAddWebsiteItem = useCallback(() => {
    const id = uuidv4();
    const itemToAdd = {
      id,
      btnType: 'Visit Website',
      type: 'URL',
      text: '',
      url: '',
    };
    handleSetButtonsForAllCards('callToActionButtons', [...cardButtons.callToActionButtons, itemToAdd]);
  }, [cardButtons]);

  const callToActionAddPhoneNumber = useCallback(() => {
    const id = uuidv4();
    const itemToAdd = {
      id,
      btnType: 'Call phone number',
      type: 'PHONE_NUMBER',
      text: '',
      phone_number: '',
    };
    handleSetButtonsForAllCards('callToActionButtons', [...cardButtons.callToActionButtons, itemToAdd]);
  }, [cardButtons]);

  const quickReplyaddMarketingItem = useCallback(() => {
    const id = uuidv4();
    const itemToAdd = {
      id,
      btnType: 'Marketing opt-out',
      type: 'QUICK_REPLY',
      text: 'Stop promotions',
    };
    handleSetButtonsForAllCards('quickReplyButtons', [...cardButtons.quickReplyButtons, itemToAdd]);
  }, [cardButtons]);

  const quickReplyaddCustomItem = useCallback(() => {
    const id = uuidv4();
    const itemToAdd = {
      id,
      btnType: 'Custom',
      type: 'QUICK_REPLY',
      text: '',
    };
    const marketingOptOutIndex = cardButtons.quickReplyButtons.findIndex((item) => item.btnType === 'Marketing opt-out');

    if (marketingOptOutIndex !== -1) {
      // If "Marketing opt-out" exists, slice the array to add itemToAdd before it
      const beforeMarketingOptOut = cardButtons.quickReplyButtons.slice(0, marketingOptOutIndex);
      const afterMarketingOptOut = cardButtons.quickReplyButtons.slice(marketingOptOutIndex);

      handleSetButtonsForAllCards('quickReplyButtons', [...beforeMarketingOptOut, itemToAdd, ...afterMarketingOptOut]);
    } else {
      // If "Marketing opt-out" does not exist, just append the new item
      handleSetButtonsForAllCards('quickReplyButtons', [...cardButtons.quickReplyButtons, itemToAdd]);
    }
  }, [cardButtons]);

  const handleDeleteQuickReply = (btn: IQuickReplyBtn) => {
    let tempItem = [...cardButtons.quickReplyButtons];
    tempItem = tempItem.filter((item) => item.id != btn.id);
    handleDeleteButtonsFromAllCards('quickReplyButtons', tempItem);
  };

  const handleDeleteCallToAction = (btn: ICallToActionBtn) => {
    let tempItem = [...cardButtons.callToActionButtons];
    tempItem = tempItem.filter((item) => item.id != btn.id);
    handleDeleteButtonsFromAllCards('callToActionButtons', tempItem);
  };

  const btnDisabled = cardButtons.quickReplyButtons.length + cardButtons.callToActionButtons.length >= 2;
  return (
    <div>
      <div className="font-semibold text-base flex gap-2 mt-4">
        Buttons <span className="text-gray-500">(Optional)</span>
      </div>
      <div className="text-sm text-gray-800 mb-2">Create buttons that let customers respond to your message or take action.</div>
      <div className="bg-blue-100 p-4 rounded-lg text-sm text-blue-700 mb-2">
        <div className="font-bold">We recommend adding the marketing opt-out button</div>
        <div>Allow customers to request to opt out of all marketing messages. This can help reduce blocks from customers and increase your quality rating.</div>
      </div>
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button className="mb-0 text-sm" variant={'outline'} disabled={btnDisabled}>
            <Plus className="mr-2 " /> Add a button <ChevronDown className="ml-2" />
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent className="w-56">
          <DropdownMenuLabel>
            <div className="text-lg">Quick Reply Buttons</div>
          </DropdownMenuLabel>
          <DropdownMenuGroup>
            <DropdownMenuItem
              disabled={cardButtons.quickReplyButtons.filter((item) => item.btnType == 'Marketing opt-out').length > 0}
              className="flex-col items-start"
              onClick={() => quickReplyaddMarketingItem()}
            >
              <div>Marketing Opt Out</div>
              <div className="text-sm text-gray-800">Recommended</div>
            </DropdownMenuItem>

            <DropdownMenuItem className="flex-col items-start" onClick={() => quickReplyaddCustomItem()}>
              <div>Custom</div>
            </DropdownMenuItem>
          </DropdownMenuGroup>
          <DropdownMenuSeparator />
          <DropdownMenuLabel>
            <div className="text-lg">Call to action buttons</div>
          </DropdownMenuLabel>
          <DropdownMenuGroup>
            <DropdownMenuItem
              className="flex-col items-start"
              onClick={() => callToActionAddWebsiteItem()}
              disabled={cardButtons.callToActionButtons.filter((btn) => btn.btnType == 'Visit Website').length > 0}
            >
              <div>Visit Website</div>
              <div className="text-sm text-gray-800">1 button maximum</div>
            </DropdownMenuItem>
            <DropdownMenuItem
              className="flex-col items-start"
              onClick={() => callToActionAddPhoneNumber()}
              disabled={cardButtons.callToActionButtons.filter((btn) => btn.btnType == 'Call phone number').length > 0}
            >
              <div>Call phone number</div>
              <div className="text-sm text-gray-800">1 button maximum</div>
            </DropdownMenuItem>
          </DropdownMenuGroup>
        </DropdownMenuContent>
      </DropdownMenu>
      <div className="mt-4">
        {cardButtons.quickReplyButtons.map((btn, index) => (
          <div key={index} className="w-72 mb-2 flex items-center space-x-1">
            <div className="bg-white border rounded-lg text-sm p-2">{btn.btnType}</div>
            <X className="text-gray-700 hover:bg-gray-100 hover:cursor-pointer h-4 w-4" onClick={() => handleDeleteQuickReply(btn)} />
          </div>
        ))}

        {cardButtons.callToActionButtons.map((btn, index) => (
          <div key={index} className="w-72 mb-2 flex items-center space-x-1">
            <div className="bg-white border rounded-lg text-sm p-2">{btn.btnType}</div>
            <X className="text-gray-700 hover:bg-gray-100 hover:cursor-pointer h-4 w-4" onClick={() => handleDeleteCallToAction(btn)} />
          </div>
        ))}
      </div>
      <div className="mt-4">
        <Alert variant="warning">
          <AlertCircle className="h-4 w-4" />
          <AlertTitle>Note:</AlertTitle>
          <AlertDescription>
            <div>At least one card is required, and you can add a maximum of ten cards.</div>
            <div>
              {' '}
              All cards in your carousel message should contain the same number of buttons and the same combination of button types (e.g., Call to Action, Quick
              Reply).
            </div>
          </AlertDescription>
        </Alert>
      </div>
    </div>
  );
};

export default React.memo(AllCardDropDownButtons);
