import React from 'react';
import Image from 'next/image';
import { ICarouselHeader, IHeader } from '../types';

interface HeaderPreviewProps {
  header: ICarouselHeader;
}

const HeaderPreview: React.FC<HeaderPreviewProps> = ({ header }) => {
  const headerFile = header.file as File;
  return (
    <div className="text-gray-800 font-bold flex-wrap text-wrap break-words">
      {header.format === 'IMAGE' && header?.file && (
        <Image
          height={600}
          width={1125}
          src={URL.createObjectURL(headerFile)}
          alt="header"
          className="rounded-lg object-cover"
          style={{ aspectRatio: '1125 / 600' }}
        />
      )}
      {/* if there is no header file but fileurl and type image */}
      {!header.file && header.fileUrl && header.format === 'IMAGE' && (
        <>
          {header.fileUrl ? (
            <Image height={600} width={1125} src={header.fileUrl} alt="header" className="rounded-lg object-cover" style={{ aspectRatio: '1125 / 600' }} />
          ) : (
            <div className="bg-slate-300 rounded-lg w-full h-40 my-4" />
          )}
        </>
      )}
      {header.format === 'VIDEO' && header?.file && <video height={250} width={400} src={URL.createObjectURL(headerFile)} />}
      {!header.file && header.fileUrl && header.format === 'VIDEO' && (
        <>{header.fileUrl ? <video height={250} width={400} src={header.fileUrl} controls /> : <div className="bg-slate-300 rounded-lg w-full h-40 my-4" />}</>
      )}
    </div>
  );
};

export default React.memo(HeaderPreview);
