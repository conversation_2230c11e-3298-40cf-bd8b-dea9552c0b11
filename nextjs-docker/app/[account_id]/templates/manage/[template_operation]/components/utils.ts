export const htmlToWhatsApp = (html: string) => {
  const replaced = html
    .replace(/<p(?: class="whitespace-pre-wrap")?>(.*?)<\/p>/g, '$1\n')

    // .replace(/<br \/>/g, '\r\n')
    .replace(/<br>/g, '')
    // .replace(/<p><br><\/p>/g, '')
    // replace bold tags with markdown
    .replace(/<strong>(.*?)<\/strong>/g, '*$1*')
    // replace italic tags with markdown
    .replace(/<em>(.*?)<\/em>/g, '_$1_')
    // replace bullet tags with markdown
    // .replace(/<li>(.*?)<\/li>/g, '- $1')
    // replace code
    .replace(/<pre class="ql-syntax" spellcheck="false">(.*?)<\/pre>/g, '`$1`')
    // blockquote
    // .replace(/<blockquote>(.*?)<\/blockquote>/g, '> $1')
    // replace strike through tags with markdown
    .replace(/<s>(.*?)<\/s>/g, '~$1~');
  // .replace(/\n(?![\s\S]*\n)/, '');
  return replaced;
};

export const whatsApptoHtml = (whatsapp: string, fromManageTemplate: boolean = false) => {
  let replaced = whatsapp
    .replace(/(?:\r\n|\r|\n)/g, '<br />') // Replace line breaks with br tags

    // replace bold tags having p tags as well with markdown
    .replace(/<p>(.*?)<\/p>/g, (match) => {
      return match.replace(/\*([^<\s][^*]*[^<\s])\*/g, '<strong>$1</strong>');
    })
    // replace bold tags with markdown if no p tags. Ussed when template is being previewed not from manage template
    .replace(/\*([^<\s][^*]*[^<\s])\*/g, '<strong>$1</strong>')

    // replace italics tags with markdown if no p tags. Ussed when template is being previewed not from manage template
    .replace(/_([^<\s][^<]*[^<\s])_/g, '<em>$1</em>')

    //TODO: add support for multiple italic tags
    // replace italic tags with markdown
    .replace(/<p>_([^<\s][^<]*[^<\s])_<\/p>/g, '<em>$1</em>')
    // replace bullet tags with markdown
    .replace(/- (.*?)\n/g, '<li>$1</li>')
    // replace code
    .replace(/`(.*?)`/g, '<pre class="ql-syntax" spellcheck="false">$1</pre>')
    // blockquote
    // .replace(/> (.*?)/g, '<blockquote>$1</blockquote>')
    // replace strike through tags with markdown
    .replace(/<p>~([^<\s][^<]*[^<\s])~<\/p>/g, '<s>$1</s>');

  if (fromManageTemplate) {
    // THEN replace spaces with &nbsp;
    // replaced = replaced.replace(/ /g, '&nbsp;'); // Replace space with non-breaking space character
  }
  return replaced;
};
