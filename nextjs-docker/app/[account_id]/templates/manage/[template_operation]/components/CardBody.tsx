'use client';
import { useQuill } from 'react-quilljs';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import ShineBorder from '@/components/ui/shine-border';
import { getLimit } from '@/lib/utils';
import { experimental_useObject } from 'ai/react';
import clsx from 'clsx';
import Quill, { EmitterSource } from 'quill';
import 'quill/dist/quill.snow.css'; // Add css for snow theme
import React, { useEffect, useRef, useState, useCallback } from 'react';
import { z } from 'zod';
import { IBody } from '../types';
import './styles.scss';
import QuillToolBar from './QuillToolBar';
import { PlusIcon } from 'lucide-react';

interface CardBodyProps {
  cardBody: IBody;
  handleChangeCardBody: (index: number, body: IBody) => void;
  index: number;
}

const CardBodyProps = {
  type: 'BODY',
  format: 'TEXT',
};

const CardBody: React.FC<CardBodyProps> = ({ cardBody, handleChangeCardBody, index }) => {
  const bodyRef = useRef(cardBody);
  const paramsArrayRef = useRef<string[]>([]);
  // Update the ref whenever body changes
  useEffect(() => {
    bodyRef.current = cardBody;
  }, [cardBody]);

  const [emojiPickerOpen, setEmojiPickerOpen] = useState(false);
  const onChange = useCallback(
    (content: string) => {
      handleChangeCardBody(index, { ...bodyRef.current, text: content });
    },
    [handleChangeCardBody, index]
  );

  const modules = {
    toolbar: false, // Disable default toolbar
  };

  const { quill, quillRef } = useQuill({ modules });

  const handleFormat = useCallback(
    (format: string) => {
      if (!quill) return;

      const range = quill.getSelection();
      if (!range) {
        quill.focus();
        return;
      }

      const text = quill.getText(range.index, range.length);
      let prefix = '';

      switch (format) {
        case 'bold':
          prefix = '*';
          break;
        case 'italic':
          prefix = '_';
          break;
        case 'strike':
          prefix = '~~';
          break;
      }

      quill.deleteText(range.index, range.length);
      quill.insertText(range.index, `${prefix}${text}${prefix}`);
      quill.setSelection(range.index - 1 + text.length + prefix.length * 2);
      onChange(quill.root.innerHTML);
    },
    [quill, onChange]
  );

  const replaceMatchesBody = useCallback(
    (e: React.ChangeEvent<HTMLInputElement>, index: number) => {
      if (cardBody.example) {
        const tempExample = { ...cardBody.example };
        const newBodyText = tempExample.body_text.map((item: string, i: number) => {
          if (index === i) {
            return e.target.value;
          }
          return item;
        });
        handleChangeCardBody(index, { ...cardBody, example: { body_text: newBodyText } });
      }
    },
    [cardBody, handleChangeCardBody, index]
  );

  const removeVariable = useCallback(
    (index: number) => {
      if (quill) {
        const text = quill.getText();
        const variable = `{{${index + 1}}}`;
        const variableIndex = text.indexOf(variable);
        if (variableIndex !== -1) {
          quill.deleteText(variableIndex, variable.length);
        }
      }
    },
    [quill]
  );

  const handleAddVariableToQuillEditor = useCallback(() => {
    if (quill) {
      quill.focus();
      const range = quill.getSelection();
      if (range) {
        quill.insertText(range.index, ` {{${paramsArrayRef.current.length + 1}}}`);
      }
    }
  }, [quill]);

  useEffect(() => {
    if (quill) {
      quill.setText(cardBody.text ?? ''); // Set initial value
      onChange(quill.root.innerHTML);
      quill.on('text-change', (delta, oldDelta, source) => {
        const content = quill.root.innerHTML;
        const matches = content.match(/{{\d+}}/g) ?? [];
        if (paramsArrayRef.current.length !== matches?.length) {
          paramsArrayRef.current = matches ?? [];
          if (matches.length == 0) {
            handleChangeCardBody(index, {
              ...CardBodyProps,
              text: quill.root.innerHTML,
            });
          } else {
            handleChangeCardBody(index, {
              ...CardBodyProps,
              text: quill.root.innerHTML,
              example: {
                body_text: matches.map((item, index) => bodyRef.current.example?.body_text?.[index] ?? ''),
              },
            });
          }
        } else {
          onChange(content);
        }
      });

      quill.clipboard.addMatcher('STRONG', function (node, delta) {
        let _node = node as HTMLElement;
        const Delta = Quill.import('delta');
        return new Delta().insert(`*${_node.innerText}*`);
      });
      quill.clipboard.addMatcher('IMG', (node, delta) => {
        const Delta = Quill.import('delta');
        return new Delta().insert('');
      });

      quill.clipboard.addMatcher(Node.ELEMENT_NODE, function (node, delta) {
        const Delta = Quill.import('delta');
        if (node.nodeName === 'P') {
          return delta;
        }
        let _node = node as HTMLElement;
        const plaintext = _node.innerText;
        return new Delta().insert(plaintext);
      });
    }
  }, [quill, onChange, handleChangeCardBody, index]);

  const { object, submit, isLoading } = experimental_useObject({
    api: '/api/ai/template-generator',
    schema: z.object({
      content: z.string().max(256),
    }),
  });

  useEffect(() => {
    if (object) {
      quill?.setText(object?.content ?? '');
    }
  }, [object?.content, quill]);

  const addEmoji = useCallback(
    (emoji: string) => {
      if (quill) {
        quill.focus();
        const range = quill.getSelection();
        if (range) {
          quill.insertText(range.index, emoji);
          quill.setSelection(range.index + emoji.length);
        }
      }
      setEmojiPickerOpen(false);
    },
    [quill]
  );

  return (
    <div className="mt-4">
      <div className="font-semibold text-base flex gap-2 mb-1">Card Body</div>
      <div className="text-sm text-gray-800 mb-2">Enter the text for card {index + 1} in the language you've selected.</div>
      <div className="flex flex-col flex-1 space-y-2">
        <div className="fle flex-col flex-1">
          <QuillToolBar emojiPickerOpen={emojiPickerOpen} addEmoji={addEmoji} setEmojiPickerOpen={setEmojiPickerOpen} handleFormat={handleFormat} />
          <div className="h-48 whitespace-pre-wrap w-full" onClick={() => setEmojiPickerOpen(false)}>
            <div ref={quillRef} />
          </div>
        </div>

        {/* <Button className="w-52 self-end" variant={'outline'} onClick={() => handleAddVariableToQuillEditor()}>
          <PlusIcon /> Add Variable
        </Button> */}
        {quill && (
          <div className={clsx('text-gray-500 text-sm text-right', quill.getLength() - 1 >= getLimit('carousel-template') && 'text-red-500 font-bold')}>
            Characters: {quill.getLength() - 1} / {getLimit('carousel-template')}
          </div>
        )}
      </div>
      {cardBody?.example?.body_text && (
        <div className="bg-gray-100 p-5 mt-5">
          <div className="font-semibold">Samples for body content</div>
          <div className="text-sm text-gray-700 mb-2">
            To help us review your message template, please add an example for each variable in your body text. Do not use real customer information. Cloud API
            hosted by Meta reviews templates and variable parameters to protect the security and integrity of our services.
          </div>
          {cardBody?.example && cardBody?.example?.body_text && cardBody?.example?.body_text.length > 0 && (
            <div className="space-y-2">
              {cardBody.example.body_text.map((item: any, index: number) => (
                <MyInput key={index} index={index} item={item} replaceMatchesBody={replaceMatchesBody} body={cardBody} removeVariable={removeVariable} />
              ))}
            </div>
          )}
        </div>
      )}
    </div>
  );
};

interface MyInputProps {
  index: number;
  item: string;
  replaceMatchesBody: (e: React.ChangeEvent<HTMLInputElement>, index: number) => void;
  body: IBody;
  removeVariable: (index: number) => void;
}

const MyInput: React.FC<MyInputProps> = ({ index, item, replaceMatchesBody, body, removeVariable }) => {
  const inputRef = useRef<HTMLInputElement>(null);
  return (
    <div className="flex space-x-2 items-center">
      <div>{`{{${index + 1}}}`}</div>
      <Input
        value={item}
        ref={inputRef}
        placeholder={`Enter content for {{${index + 1}}}`}
        className="flex-1"
        onChange={(e) => {
          replaceMatchesBody(e, index);
          setTimeout(() => {
            inputRef.current?.focus();
          }, 10);
        }}
      />
      {index === (body?.example?.body_text.length ?? 0) - 1 ? (
        <Button variant={'outline'} onClick={() => removeVariable(index)}>
          Remove
        </Button>
      ) : null}
    </div>
  );
};

export default React.memo(CardBody);
