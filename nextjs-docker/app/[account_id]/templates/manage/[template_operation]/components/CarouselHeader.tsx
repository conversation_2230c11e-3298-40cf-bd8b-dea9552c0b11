'use client';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Toaster } from '@/components/ui/toaster';
import { useToast } from '@/components/ui/use-toast';
import { Trash } from 'lucide-react';
import Image from 'next/image';
import React, { useRef, useState } from 'react';
import { ICarouselHeader } from '../types';

//TODO: Enable template headers variable.
interface CarouselHeaderProps {
  handleChangeCardHeader: (index: number, header: ICarouselHeader) => void;
  cardHeader: ICarouselHeader;
  index: number;
}

const CarouselHeader: React.FC<CarouselHeaderProps> = ({ cardHeader, handleChangeCardHeader, index }) => {
  const [file, setFile] = useState<File | null>(null);
  const { toast } = useToast();
  const inputRef = useRef<HTMLInputElement>(null);

  return (
    <div className="mt-4">
      <div className="font-semibold text-base flex gap-2 mb-1">Card Header</div>
      <div className="flex flex-col gap-2 space-2" id="select-header">
        {cardHeader.format === 'VIDEO' && (
          <div className="flex flex-col gap-2 space-2">
            <div className="text-sm">
              <div>For best video viewing:</div>
              <div>
                Dimensions: <span className="font-bold">1125px by 600px</span>
              </div>
              <div>
                Max video size: <span className="font-bold">16mb</span>
              </div>
              <div className="mb-2">
                Allowed video type: <span className="font-bold">MP4 Video and 3GPP</span>
              </div>

              {/* if header file url is present but no file is selected */}
              {cardHeader.fileUrl && !file ? (
                <video height={250} width={400} src={cardHeader.fileUrl} controls />
              ) : // if file is selected
              file ? (
                <video height={250} width={400} src={URL.createObjectURL(file)} controls />
              ) : (
                <div></div>
              )}
            </div>
            <div className="flex items-center gap-2">
              <Input
                className="w-1/3"
                onChange={(e) => {
                  const selectedFile = e.currentTarget?.files?.[0] ?? null;
                  if (selectedFile && !['video/mp4', 'video/3gpp'].includes(selectedFile.type)) {
                    toast({
                      variant: 'destructive',
                      description: 'Invalid file type. Only MP4 and 3GPP are allowed.',
                    });
                    return;
                  }
                  setFile(selectedFile);
                  handleChangeCardHeader(index, {
                    ...cardHeader,
                    format: 'VIDEO',
                    file: selectedFile,
                  });
                }}
                type="file"
                accept="video/mp4, video/3gpp"
                name="file"
                id="file"
              />
              <Button
                variant="outline"
                onClick={() => {
                  handleChangeCardHeader(index, { ...cardHeader, file: null, fileUrl: undefined });
                  setFile(null);
                }}
              >
                X
              </Button>
            </div>
          </div>
        )}
        {cardHeader.format === 'IMAGE' && (
          <div className="flex flex-col gap-2 space-2">
            <div className="text-sm bg-gray-50 rounded-md border p-2">
              <div>
                For best viewing image, the recommended dimensions are <span className="font-bold">1125px by 600px</span>
              </div>
              <div></div>
              <div>
                Max Allowed size: <span className="font-bold">5mb</span>
              </div>
              <div>
                Allowed image type: <span className="font-bold">JPEG and PNG</span>
              </div>
            </div>

            {/* if header file url is present but no file is selected */}
            {cardHeader.fileUrl && !file ? (
              <Image height={0} width={0} src={cardHeader.fileUrl} alt="header" className="h-auto w-60 rounded-lg" sizes="100vh" />
            ) : // if file is selected
            file ? (
              <div className="relative max-w-fit">
                <img alt="image" src={URL.createObjectURL(file)} className="h-auto w-60 rounded-lg" />
                <div className="absolute -top-3 -right-3">
                  <Button
                    variant="outline"
                    size={'icon'}
                    className="bg-white"
                    onClick={() => {
                      if (inputRef?.current) {
                        inputRef.current.value = '';
                      }
                      handleChangeCardHeader(index, {
                        ...cardHeader,
                        file: null,
                        fileUrl: undefined,
                      });
                      setFile(null);
                    }}
                  >
                    <Trash className="text-red-500" />
                  </Button>
                </div>
              </div>
            ) : (
              <div></div>
            )}
            <div className="flex items-center gap-2">
              <Input
                ref={inputRef}
                className="w-1/3"
                onChange={(e) => {
                  if (!e.currentTarget?.files?.[0]) {
                    return;
                  }
                  const selectedFile = e.currentTarget?.files?.[0] ?? null;
                  if (selectedFile && !['image/jpeg', 'image/png', 'image/jpg'].includes(selectedFile.type)) {
                    toast({
                      variant: 'destructive',
                      description: 'Invalid file type. Only JPEG and PNG are allowed.',
                    });
                    return;
                  }
                  setFile(selectedFile);
                  handleChangeCardHeader(index, {
                    ...cardHeader,
                    format: 'IMAGE',
                    file: selectedFile,
                  });
                }}
                type="file"
                accept="image/png, image/jpeg"
                name="file"
                id="file"
              />
            </div>
          </div>
        )}
      </div>
      <Toaster />
    </div>
  );
};

export default React.memo(CarouselHeader);
