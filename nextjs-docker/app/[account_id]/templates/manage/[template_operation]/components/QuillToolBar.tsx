import { Button } from '@/components/ui/button';
import EmojiPicker from 'emoji-picker-react';
import { Bold, Italic, Smile, Strikethrough } from 'lucide-react';
import 'quill/dist/quill.snow.css'; // Add css for snow theme
import './styles.scss';
import React from 'react';

type QuillToolBarType = {
  emojiPickerOpen: boolean;
  addEmoji: (emoji: string) => void;
  setEmojiPickerOpen: React.Dispatch<React.SetStateAction<boolean>>;
  handleFormat: (format: string) => void;
};

const QuillToolBar = ({ emojiPickerOpen, addEmoji, setEmojiPickerOpen, handleFormat }: QuillToolBarType) => {
  return (
    <div className="flex gap-2 border border-b-0 border-gray-300">
      {emojiPickerOpen && (
        <EmojiPicker
          className="z-30"
          onEmojiClick={(EmojiClickData, event) => addEmoji(EmojiClickData.emoji)}
          style={{ position: 'absolute', top: '36px' }}
          allowExpandReactions={false}
          autoFocusSearch={true}
          previewConfig={{
            showPreview: false,
          }}
        />
      )}
      <Button size="sm" variant="ghost" onClick={() => setEmojiPickerOpen(!emojiPickerOpen)}>
        <Smile className="h-4 w-4" />
      </Button>
      <Button variant="ghost" size="sm" onClick={() => handleFormat('bold')}>
        <Bold className="h-4 w-4" />
      </Button>
      <Button variant="ghost" size="sm" onClick={() => handleFormat('italic')}>
        <Italic className="h-4 w-4" />
      </Button>
      <Button variant="ghost" size="sm" onClick={() => handleFormat('strike')}>
        <Strikethrough className="h-4 w-4" />
      </Button>
    </div>
  );
};

export default React.memo(QuillToolBar);
