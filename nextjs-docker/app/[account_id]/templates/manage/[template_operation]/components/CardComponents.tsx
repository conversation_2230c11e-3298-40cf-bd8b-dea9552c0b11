import { Collapsible, CollapsibleContent, CollapsibleTrigger } from '@/components/ui/collapsible';
import { ChevronDown } from 'lucide-react';
import React from 'react';
import { IBody, ICallToActionBtn, ICardComponent, ICarouselHeader, IQuickReplyBtn } from '../types';
import CardAlertDialog from './CardAlertDialog';
import CardBody from './CardBody';
import CardDropDownButtons from './CardDropDownButtons';
import CarouselHeader from './CarouselHeader';

type CardComponentsProps = {
  index: number;
  card: ICardComponent;
  handleChangeCardBody: (index: number, body: IBody) => void;
  handleChangeCardHeader: (index: number, header: ICarouselHeader) => void;
  handleChangeQuickReplyButtons: (index: number, quickReplyButtons: IQuickReplyBtn[]) => void;
  handleChangeCallToActionButtons: (index: number, callToActionButtons: ICallToActionBtn[]) => void;
  removeCard: (index: number) => void;
};

const CardComponents = ({
  index,
  card,
  handleChangeCardBody,
  handleChangeCardHeader,
  handleChangeQuickReplyButtons,
  handleChangeCallToActionButtons,
  removeCard,
}: CardComponentsProps) => {
  return (
    <Collapsible key={index} defaultOpen={true} className=" bg-white border-gray-300 border-2 rounded-md" id={`card-${index + 1}`}>
      <CollapsibleTrigger className="flex items-center w-full p-2 bg-gray-100 rounded-md">
        <div className="space-x-2 flex-1 flex items-center">
          {/* <MessageSquare className="" /> */}
          <div className=" font-medium text-md">Card {index + 1}</div>
        </div>
        <div>
          <ChevronDown />
        </div>
      </CollapsibleTrigger>
      <CollapsibleContent className="p-4 text-sm text-gray-600 bg-white rounded-md">
        <div key={index} className="">
          {/* <div className='text-lg font-medium'>Card {index + 1}</div> */}
          <div key={index} id="select-header">
            <CarouselHeader
              cardHeader={card.components[0]}
              index={index}
              handleChangeCardHeader={handleChangeCardHeader}
              // setHeader={(value: any) => handleCardChange(index, 'header', value)}
              // carouselHeaderType={carouselHeaderType}
            />
          </div>
          <div id="enter-message">
            {/* <Body body={card.components[1]} setBody={setBody} setBodyLength={setBodyLength} type="carousel-template" /> */}
            <CardBody cardBody={card.components[1]} handleChangeCardBody={handleChangeCardBody} index={index} />
          </div>
          <div id="button">
            <CardDropDownButtons
              quickReplyButtons={card.components[2].buttons.quickReplyButtons}
              handleChangeQuickReplyButtons={handleChangeQuickReplyButtons}
              callToActionButtons={card.components[2].buttons.callToActionButtons}
              handleChangeCallToActionButtons={handleChangeCallToActionButtons}
              index={index}
            />
          </div>
          <div className="flex justify-end">{index + 1 < 9 && index + 1 > 1 && <CardAlertDialog index={index} removeCard={removeCard} />}</div>
        </div>
      </CollapsibleContent>
    </Collapsible>
  );
};

export default React.memo(CardComponents);
