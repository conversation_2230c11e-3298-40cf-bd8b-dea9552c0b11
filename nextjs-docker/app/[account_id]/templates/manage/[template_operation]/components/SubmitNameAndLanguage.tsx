import { Input } from '@/components/ui/input';
import clsx from 'clsx';
import React from 'react';
import { Select, SelectContent, SelectGroup, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Action } from '../types';
import { languagesArray } from '@/lib/utils';

const SubmitNameAndLanguage = ({
  template_operation,
  templateName,
  handleChangeTemplateName,
  templateType,
  templateLanguage,
  dispatch,
}: {
  template_operation: string;
  templateName: string;
  handleChangeTemplateName: (value: string) => void;
  templateType: string;
  templateLanguage: { value: string; label: string };
  dispatch: React.Dispatch<Action>;
}) => {
  let template_operation_type = 'Create New';
  if (template_operation == 'edit') {
    template_operation_type = 'Edit';
  }
  if (template_operation == 'duplicate') {
    template_operation_type = 'Duplicate';
  }
  let template_type = 'Marketing Template';
  if (templateType == 'utility-template') {
    template_type = 'Utility Template';
  }

  return (
    <div>
      <div className="flex justify-between items-center pb-6 mb-6 border-b border-gray-200">
        <div>
          <div className="text-2xl font-semibold">{template_type}</div>
          <div className="text-xl">{template_operation_type} Template</div>
        </div>
      </div>
      <div className="bg-white space-y-2 mb-4">
        <div className="text-sm font-bold mb-1">Name</div>
        <div className="text-sm mb-4 text-gray-600">Please put a name for your template</div>
        <div className="text-sm mb-4 text-gray-600">Only lowercase letters, numbers, and underscores allowed, up to 512 characters.</div>
        <div className="flex flex-col flex-1 space-y-2">
          <Input
            placeholder="Enter your message template name"
            type="text"
            name="templateName"
            pattern="[a-z0-9_]{1,512}"
            defaultValue={templateName}
            title="Only lowercase letters, numbers, and underscores allowed, up to 512 characters"
            maxLength={512}
            disabled={template_operation == 'edit'} // Disable input field if operation is edit
            onChange={(e) => handleChangeTemplateName(e.target.value)}
            onInput={(e) => {
              const input = e.target as HTMLInputElement;
              input.value = input.value
                .toLowerCase()
                .replace(/[^a-z0-9_\s]/g, '')
                .replace(/\s+/g, '_');
            }}
          />
          <div className={clsx('text-gray-500 text-sm text-right', templateName.length == 512 && 'font-bold text-red-500')}>
            Characters: {templateName.length}/512
          </div>
        </div>
      </div>

      <div className="bg-white space-y-2">
        <div className="text-sm font-bold mb-1">Languages</div>
        <div className="text-sm mb-4 text-gray-600">Choose languages for your message template</div>
        <Select
          name="language"
          value={templateLanguage.value}
          onValueChange={(value) => dispatch({ type: 'SET_TEMPLATE_LANGUAGE', payload: { value: value, label: value.split('-')[0] } })}
          disabled={template_operation == 'edit'} // Disable select field if operation is edit
        >
          <SelectTrigger>
            <SelectValue placeholder="Select a language" />
          </SelectTrigger>
          <SelectContent>
            <SelectGroup>
              {languagesArray.map((item, index) => (
                <SelectItem key={index} value={item.value}>
                  {item.label}
                </SelectItem>
              ))}
            </SelectGroup>
          </SelectContent>
        </Select>
      </div>
    </div>
  );
};

export default React.memo(SubmitNameAndLanguage);
