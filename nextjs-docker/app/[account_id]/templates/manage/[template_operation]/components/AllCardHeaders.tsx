import { DropdownMenu, DropdownMenuContent, DropdownMenuRadioGroup, DropdownMenuRadioItem, DropdownMenuTrigger } from '@/components/ui/dropdown-menu';

import { Button } from '@/components/ui/button';
import { ChevronDown } from 'lucide-react';
import { memo } from 'react';

type IAllCardHeaderProps = {
  allCardHeaders: 'IMAGE' | 'VIDEO';
  handleChangeAllCardHeaders: (format: 'IMAGE' | 'VIDEO') => void;
};

const AllCardHeaders = ({ allCardHeaders, handleChangeAllCardHeaders }: IAllCardHeaderProps) => {
  return (
    <div>
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button variant="outline" className="capitalize mb-0 w-80">
            {allCardHeaders.toLocaleLowerCase()}
            <ChevronDown className="ml-1" />
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent className="w-56">
          <DropdownMenuRadioGroup
            value={allCardHeaders}
            onValueChange={(value) => {
              const format = value as 'IMAGE' | 'VIDEO';
              handleChangeAllCardHeaders(format);
            }}
          >
            <>
              <DropdownMenuRadioItem value="IMAGE">Image</DropdownMenuRadioItem>
              <DropdownMenuRadioItem value="VIDEO">Video</DropdownMenuRadioItem>
            </>
          </DropdownMenuRadioGroup>
        </DropdownMenuContent>
      </DropdownMenu>
    </div>
  );
};

export default memo(AllCardHeaders);
