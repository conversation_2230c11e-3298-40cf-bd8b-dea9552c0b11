'use client';
import React from 'react';
import { IFooter, IQuickReplyBtn } from '../types';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Input } from '@/components/ui/input';
import { X } from 'lucide-react';
import { Dialog } from '@radix-ui/react-dialog';
import { DialogContent, DialogDescription, DialogFooter, DialogTitle } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';

const QuickReplyButtons = ({
  btn,
  index,
  quickReplyButtons,
  handleChangeQuickReplyButtons,
  setFooter,
}: {
  btn: IQuickReplyBtn;
  index: number;
  quickReplyButtons: IQuickReplyBtn[];
  handleChangeQuickReplyButtons: (quickReplyButtons: IQuickReplyBtn[]) => void;
  setFooter: (footer: Partial<IFooter>) => void;
}) => {
  const [confirmRemoveFooterDialog, setConfirmRemoveFooterDialog] = React.useState(false);
  const handleChangeQuickReply = (e: React.ChangeEvent<HTMLInputElement>) => {
    handleChangeQuickReplyButtons(
      quickReplyButtons.map((item: IQuickReplyBtn, idx: number) =>
        item.id == btn.id
          ? {
              ...item,
              text: e.target.value,
            }
          : item
      )
    );
  };

  const handleDropdownQuickReplyChange = (e: string) => {
    handleChangeQuickReplyButtons(
      quickReplyButtons.map((item: IQuickReplyBtn, idx: number) => {
        if (idx == index) {
          return {
            ...item,
            btnType: e,
            text: '',
          };
        } else {
          return {
            ...item,
          };
        }
      })
    );
  };

  const handleDelete = (btn: IQuickReplyBtn = { text: '', type: 'URL' }) => {
    let tempItem = [...quickReplyButtons];
    if (btn.btnType == 'Marketing opt-out') {
      setConfirmRemoveFooterDialog(true);
    } else {
      tempItem = tempItem.filter((item) => item.id != btn.id);
      handleChangeQuickReplyButtons(tempItem);
    }
  };

  if (btn.btnType == 'Marketing opt-out') {
    return (
      <>
        <Dialog open={confirmRemoveFooterDialog} onOpenChange={setConfirmRemoveFooterDialog}>
          <DialogContent>
            <DialogTitle>Remove Footer</DialogTitle>
            <DialogDescription>
              Removing stop promotions is not recommended. <br />
              This button allows customers to opt-out of the marketing message. If you decide to not add it, then the customers might block your number, causing
              your business to be marked as spam. <br />
              <br />
              A significant number of spam reports will block your business. <br />
              Are you sure you want to remove the footer?
            </DialogDescription>
            <div className="flex justify-end mt-4 space-x-2">
              <DialogFooter>
                <Button
                  variant={'destructive'}
                  onClick={() => {
                    let tempItem = [...quickReplyButtons];
                    tempItem = tempItem.filter((item) => item.id != btn.id);
                    handleChangeQuickReplyButtons(tempItem);
                    setFooter({ text: '', disabled: false });

                    setConfirmRemoveFooterDialog(false);
                  }}
                >
                  Remove
                </Button>
                <Button variant={'default'} onClick={() => setConfirmRemoveFooterDialog(false)}>
                  Cancel
                </Button>
              </DialogFooter>
            </div>
          </DialogContent>
        </Dialog>
        <div className="mx-8 py-2  hover:bg-gray-200 flex items-center space-x-2">
          <div className="bg-gray-100 border border-gray-300 p-4 rounded-lg mx-4 space-y-4 w-full">
            <div className="flex space-x-2">
              <div className="flex-1 space-y-1">
                <Label>Type</Label>
                <Select onValueChange={(val) => handleDropdownQuickReplyChange(val)} value={btn.btnType}>
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="Marketing opt-out">Marketing opt-out</SelectItem>
                    <SelectItem value="Custom">Custom</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div className="flex-1 space-y-1">
                <Label htmlFor="picture">Button Text</Label>
                <Input type="text" className="border border-gray-300 focus:outline-none" disabled readOnly value="Stop promotions" maxLength={25} />
                <div className="text-gray-500 text-sm text-right">Characters: {'stop promotions'.length}/25</div>
              </div>
              <div className="flex-1 space-y-1">
                <Label htmlFor="picture">Footer Text</Label>
                <Input
                  type="text"
                  id="footer-text"
                  className="border border-gray-300 focus:outline-none"
                  disabled
                  readOnly
                  value="Not interested? Tap Stop promotions"
                />
              </div>
            </div>
            <div className="flex items-center space-x-2 text-sm text-gray-900"></div>
          </div>
          <X className="text-gray-700 hover:bg-gray-100 hover:cursor-pointer" onClick={() => handleDelete(btn)} />
        </div>
      </>
    );
  } else {
    return (
      <div className="mx-8 py-2  hover:bg-gray-200 flex items-center space-x-2">
        <div className="bg-gray-100 border border-gray-300 p-4 rounded-lg mx-4 space-y-4 w-full">
          <div className="flex space-x-2 bg">
            <div className="w-52 space-y-1">
              <Label>Type</Label>
              <Select onValueChange={(val) => handleDropdownQuickReplyChange(val)} value={btn.btnType}>
                <SelectTrigger>
                  <SelectValue placeholder={btn.btnType} />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="Marketing opt-out">Marketing opt-out</SelectItem>
                  <SelectItem value="Custom">Custom</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div className="flex-1 space-y-1">
              <Label htmlFor="picture">Button Text</Label>
              <Input
                type="text"
                id="footer-text"
                className="border border-gray-300 focus:outline-none"
                onChange={(val) => handleChangeQuickReply(val)}
                value={btn.text}
                name="text"
                placeholder="Add your text here"
                maxLength={25}
              />
              <div className="text-gray-500 text-sm text-right">Characters: {btn.text.length}/25</div>
            </div>
          </div>
        </div>
        <X className="text-gray-700 hover:bg-gray-100 hover:cursor-pointer" onClick={() => handleDelete(btn)} />
      </div>
    );
  }
};

export default React.memo(QuickReplyButtons);
