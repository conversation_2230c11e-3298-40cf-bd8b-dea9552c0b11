'use client';
import { Input } from '@/components/ui/input';
import React from 'react';
import { IFooter } from '../types';

interface FooterProps {
  // footer: IFooter;
  footer: any;
  setFooter: (footer: Partial<IFooter>) => void;
}

const Footer: React.FC<FooterProps> = ({ footer, setFooter }) => {
  const handleFooterChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setFooter({ text: e.target.value });
  };
  return (
    <div className="mt-4">
      <div id="footer" className="font-semibold text-base flex gap-2 mb-1">
        Footer <div className="text-gray-500">(Optional)</div>
      </div>
      <div className="flex flex-col flex-1 space-y-2">
        <Input
          placeholder="Enter text in English"
          onChange={handleFooterChange}
          value={footer.text}
          disabled={footer.disabled}
          className="border border-gray-300 focus:outline-none"
          maxLength={60}
        />
      </div>
      <p className="text-gray-500 text-sm text-right">Characters: {footer.text.length}/60</p>
    </div>
  );
};

export default React.memo(Footer);
