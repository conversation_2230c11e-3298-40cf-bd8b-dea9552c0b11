'use client';
import { Icon } from '@iconify/react/dist/iconify.js';
import { MenuIcon, SquareArrowOutUpRightIcon } from 'lucide-react';
import React from 'react';
import { IBody, ICardComponent } from '../types';
// import { Video } from './Video';
import CarouselHeaderPreview from './CarouselHeaderPreview';
import { whatsApptoHtml } from './utils';
import clsx from 'clsx';

interface MessagePreviewProps {
  body: IBody;
  cards: ICardComponent[];
  fromLiveChat?: boolean;
}

const MessagePreviewCarousel: React.FC<MessagePreviewProps> = ({ body, cards, fromLiveChat }) => {
  const replaceVariablesToText = (messageBody: IBody) => {
    let str = '';
    let regexPattern = /\{\{\d+\}\}/g;
    let matches = [];
    let values = undefined;
    try {
      matches = [...messageBody.text.matchAll(regexPattern)];
      values = messageBody.example?.body_text;
      str = messageBody.text;
      // Iterate through all matches
      for (let i = 0; i < matches.length; i++) {
        // Replace the match with the corresponding value
        if (values?.[i]) {
          str = str.replace(matches[i][0], values[i]);
        }
      }
      return whatsApptoHtml(str);
    } catch (error) {
      return str;
    }
  };
  let highlight = false;
  return (
    <div id="enter-message" className={clsx(!fromLiveChat && "bg-[url('/assets/whatsapp_convo_bg.png')] p-4 rounded-lg mb-4 bg-[#e5ddd5] flex-col space-y-2")}>
      <div className={clsx(!fromLiveChat && 'bg-[#e1ffc7] rounded-lg shadow p-4')}>
        <div
          dangerouslySetInnerHTML={{ __html: replaceVariablesToText(body) }}
          key={`body-item`}
          className="text-gray-800 flex-wrap text-wrap break-words text-sm whitespace-pre-wrap"
        />
        <div className="text-right text-xs text-gray-600 my-2">2:34 PM</div>
      </div>
      <div className="flex overflow-x-scroll space-x-2 pb-2">
        {cards.map((card, index) => {
          const header = card.components[0];
          const messageBody = card.components[1];
          const buttons = card.components[2].buttons;
          const quickReplyButtons = buttons.quickReplyButtons;
          const callToActionButtons = buttons.callToActionButtons;
          return (
            <div
              key={index}
              style={{ borderStyle: highlight ? 'solid' : 'none', borderColor: '#40F8DF', borderWidth: highlight ? '4px' : '' }}
              className="bg-white rounded-lg shadow"
            >
              {/* <div className="p-4 lg:80 w-52"> */}
              <div className="p-4 lg:80 w-52">
                <CarouselHeaderPreview header={header} />
                <div
                  dangerouslySetInnerHTML={{ __html: replaceVariablesToText(messageBody) }}
                  key={`${index}-body-item`}
                  className="text-gray-800 flex-wrap text-wrap break-words text-sm whitespace-pre-wrap"
                />
              </div>
              {(quickReplyButtons.length > 0 || callToActionButtons.length > 0) && <div className="h-[1px] bg-gray-200" />}
              {Array.isArray(quickReplyButtons) &&
                quickReplyButtons.slice(0, quickReplyButtons.length + callToActionButtons.length > 3 ? 2 : 3).map((btn) => (
                  <div key={`${index} - ${btn?.btnType ?? Math.random()}`}>
                    <div className="flex items-center justify-center gap-3 text-[#00a5f4] text-sm my-2">
                      <Icon icon="mdi:share" className="text-[#00a5f4]" /> {btn.text}
                    </div>
                    <div className="h-[1px] bg-gray-200 mx-4" />
                  </div>
                ))}
              {callToActionButtons
                .slice(
                  0,
                  quickReplyButtons.length + callToActionButtons.length > 3
                    ? callToActionButtons.length > quickReplyButtons.length
                      ? Math.abs(quickReplyButtons.length - callToActionButtons.length) - 1
                      : Math.max(0, callToActionButtons.length - quickReplyButtons.length)
                    : 3
                )
                .map((btn) => (
                  <div key={`${index} - ${btn?.btnType ?? Math.random()}`}>
                    <div className="flex items-center justify-center gap-3 text-[#00a5f4] text-sm my-2">
                      {btn.type == 'PHONE_NUMBER' ? (
                        <Icon icon="ic:round-phone" className="text-[#00a5f4] " />
                      ) : btn.type == 'Visit Website' ? (
                        <SquareArrowOutUpRightIcon className="text-[#00a5f4] h-4 w-4" />
                      ) : (
                        <Icon icon="mdi:share" className="text-[#00a5f4]" />
                      )}{' '}
                      {btn.text}
                    </div>
                    <div className="h-[1px] bg-gray-200 mx-4" />
                  </div>
                ))}
              {quickReplyButtons.length + callToActionButtons.length > 3 && (
                <div className="flex items-center justify-center gap-3 text-[#00a5f4] text-sm my-2 pb-2">
                  <MenuIcon className="text-[#00a5f4] h-4 w-4" /> See all options
                </div>
              )}
            </div>
          );
        })}
      </div>
    </div>
  );
};

export default MessagePreviewCarousel;
