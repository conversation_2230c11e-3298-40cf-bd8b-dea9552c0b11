import { Toaster } from '@/components/ui/toaster';
import { getAccount, getTemplateFromDbByTemplateId } from '@/lib/pocket';
import { ITemplateDatabase } from '@/lib/types';
import { languagesArray } from '@/lib/utils';
import type { Metadata } from 'next';
import CreateTemplate from './components/CreateTemplate';
import { IBody, ICallToActionBtn, ICardComponent, ICarouselHeader, IFooter, IHeader, IQuickReplyBtn, ITemplateState } from './types';
import { init } from '@sentry/nextjs';
import { createComponentsCarousel } from '@/app/[account_id]/send-message/[id]/components/helper';

export const metadata: Metadata = {
  title: 'Create Template',
  description: '...',
};

export default async ({
  params,
  searchParams,
}: {
  params: { template_operation: 'edit' | 'duplicate' | 'create-new'; account_id: string };
  searchParams: Record<string, string>;
}) => {
  const account = await getAccount(params.account_id);
  // noStore();
  let TEMPLATE_LANGUAGE = {
    value: 'English-en',
    label: 'English',
  };

  let TEMPLATE_NAME = '';
  let TEMPLATE_ID = '';
  let TEMPLATE_TYPE: 'basic-template' | 'carousel-template' | 'utility-template' = 'basic-template';
  let HEADER: IHeader = {
    type: 'HEADER',
    format: 'TEXT',
    file: null,
    fileUrl: '',
    text: '',
  };

  let BODY: IBody = {
    type: 'BODY',
    format: 'TEXT',
    text: 'Hello',
  };

  let FOOTER: IFooter = {
    type: 'FOOTER',
    text: '',
    disabled: false,
  };

  let QUICK_REPLY_BUTTONS: IQuickReplyBtn[] = [];
  let CALL_TO_ACTION_BUTTONS: ICallToActionBtn[] = [];

  let CAROUSEL_HEADER: ICarouselHeader = {
    type: 'HEADER',
    format: 'IMAGE',
    file: null,
    fileUrl: '',
  };

  let CAROUSEL_BODY: IBody = {
    type: 'BODY',
    format: 'TEXT',
    text: 'Hello',
  };

  const EACH_CAROUSEL_CARD: ICardComponent = {
    components: [
      CAROUSEL_HEADER,
      CAROUSEL_BODY,
      {
        type: 'buttons',
        buttons: {
          quickReplyButtons: [],
          callToActionButtons: [],
        },
      },
    ],
  };

  let initialState: ITemplateState = {
    templateName: TEMPLATE_NAME,
    templateLanguage: TEMPLATE_LANGUAGE,
    templateType: TEMPLATE_TYPE,
    templateId: TEMPLATE_ID,
    cards: [EACH_CAROUSEL_CARD],
    buttons: {
      callToActionButtons: CALL_TO_ACTION_BUTTONS,
      quickReplyButtons: QUICK_REPLY_BUTTONS,
    },

    header: HEADER,
    body: BODY,
    footer: FOOTER,
  };

  if (params.template_operation === 'edit' || params.template_operation === 'duplicate') {
    let template;
    const id = searchParams?.id;
    try {
      template = (await getTemplateFromDbByTemplateId(id)) as ITemplateDatabase;
      const { template_name, type, template_body, template_id } = template;

      initialState.templateName = template_name;
      initialState.templateId = template_id;
      initialState.templateType = type ?? 'basic-template';
      initialState.templateLanguage = languagesArray.find((item) => item.value.split('-')[1] == template_body.language) ?? {
        value: 'English-en',
        label: 'English',
      };

      const header = template_body.components.find((component) => component.type === 'HEADER');
      const body = template_body.components.find((component) => component.type === 'BODY');
      const cards = body?.cards;
      const buttons = template_body.components.find((component) => component.type === 'BUTTONS');
      const footer = template_body.components.find((component) => component.type === 'FOOTER');
      if (header) {
        initialState.header = header;
      }
      if (body) {
        if (body.example) {
          //@ts-ignore
          initialState.body = { ...body, example: { body_text: body.example?.body_text[0] } };
        } else {
          initialState.body = body;
        }
      }
      if (footer) {
        initialState.footer = { ...footer, disabled: false };
      }
      if (cards) {
        initialState.cards = cards.map((eachCard) => createComponentsCarousel(eachCard));
      }
      if (buttons) {
        if (buttons.buttons.length > 0) {
          const quickReplyButtons = buttons.buttons.filter((button: any) => button.type === 'QUICK_REPLY');
          quickReplyButtons.forEach((button, index) => {
            if (button.text == 'Stop promotions') {
              FOOTER = {
                type: 'FOOTER',
                text: 'Not interested? Tap Stop promotions',
                disabled: true,
              };
              quickReplyButtons[index].btnType = 'Marketing opt-out';
            } else {
              quickReplyButtons[index].btnType = 'Custom';
            }
          });
          const callToActionButtons = buttons.buttons.filter((button: any) => button.type == 'URL' || button.type == 'PHONE_NUMBER');
          callToActionButtons.forEach((button, index) => {
            if (button.type == 'PHONE_NUMBER') {
              callToActionButtons[index].btnType = 'Call phone number';
            }
            if (button.type == 'URL') {
              callToActionButtons[index].btnType = 'Visit Website';
            }
          });
          initialState.buttons.quickReplyButtons = quickReplyButtons;
          initialState.buttons.callToActionButtons = callToActionButtons;
        }
      }
      return (
        <div className="flex h-full flex-col bg-blue-50 p-6 overflow-y-scroll">
          <Toaster />
          <CreateTemplate
            account={account}
            template_operation={params.template_operation}
            template_page="2"
            initialState={initialState}
            EACH_CAROUSEL_CARD={EACH_CAROUSEL_CARD}
          />
        </div>
      );
    } catch (error) {
      console.error(error);
      return <div className="flex h-full flex-col bg-blue-50 p-6 overflow-y-scroll"></div>;
    }
  }

  initialState.buttons.quickReplyButtons = [
    {
      text: 'Stop promotions',
      type: 'QUICK_REPLY',
      btnType: 'Marketing opt-out',
    },
  ];
  FOOTER = {
    type: 'FOOTER',
    text: 'Not interested? Tap Stop promotions',
    disabled: true,
  };

  return (
    <div className="flex h-full flex-col bg-blue-50 p-6 overflow-y-scroll">
      <Toaster />
      <CreateTemplate
        account={account}
        template_operation={params.template_operation}
        template_page="1"
        initialState={initialState}
        EACH_CAROUSEL_CARD={EACH_CAROUSEL_CARD}
      />
    </div>
  );
};
