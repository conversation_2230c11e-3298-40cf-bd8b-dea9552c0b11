import type { Metadata } from 'next';
import { Button } from '@/components/ui/button';
import { PlusIcon, SparklesIcon } from 'lucide-react';
import Link from 'next/link';

export const metadata: Metadata = {
  title: 'Create Templates',
  description: '...',
};

export default async ({ params }: { params: Record<string, string> }) => {
  // noStore();

  return (
    <div className="flex items-center justify-center h-full flex-col bg-blue-100">
      <div className="font-bold text-xl mb-2">Choose how to create a template</div>
      <div className="flex space-x-2 items-center justify-center">
        <div className="shadow-lg rounded-lg bg-white h-48 max-w-2xl p-5 flex flex-col">
          <div className="flex-1">
            <div className="font-bold">Use a blank template</div>
            <div className="text-sm">Create your template from scratch. Once you finish creating your template, it must be submitted for review.</div>
          </div>

          <Button variant="outline" className="mb-0" asChild>
            <Link prefetch={false} href={`/${params.account_id}/templates/manage/create-new`}>
              <PlusIcon className="mr-2" /> Create a new template
            </Link>
          </Button>
        </div>
        <div className="shadow-lg rounded-lg bg-white h-48 max-w-2xl p-5 flex flex-col">
          <div className="flex-1">
            <div className="font-bold">Browse the WhatsApp template library</div>
            <div className="text-sm">
              Get started faster with pre-written utility templates. Use a template as is and it will be available to send immediately, or customize the content
              to your needs.
            </div>
          </div>
          <Button variant="outline" className="mb-0">
            <SparklesIcon className="mr-2" /> Browse templates
          </Button>
        </div>
      </div>
    </div>
  );
};
