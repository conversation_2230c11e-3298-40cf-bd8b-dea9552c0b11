import { Button } from '@/components/ui/button';
import { Toaster } from '@/components/ui/toaster';
import { getAllLists } from '@/lib/pocket';
import { User } from '@/lib/types';
import { server_component_pb } from '@/state/pb/server_component_pb';
import { PlusCircle } from 'lucide-react';
import Link from 'next/link';
import BreadCrumbs from './components/BreadCrumbs';
import { columns } from './components/columns';
import { DataTable } from './components/data-table';
import { ListSelectionDialog } from './components/ListSelectionDialog';

export const dynamic = 'force-dynamic';
export const fetchCache = 'default-no-store';

export default async ({ params }: { params: Record<string, string> }) => {
  const { pb } = await server_component_pb();
  const user = pb.authStore.record as User;
  const lists = await getAllLists(params.account_id);
  const agentRoles =
    user.expand?.roles?.map((role) => {
      return role.name;
    }) ?? [];

  if (!lists || lists.length === 0) {
    return (
      <div className="h-full w-full flex flex-col justify-center items-center space-y-4">
        <div>Looks like you dont have any list!</div>
        <Link href={`/${params.account_id}/list/create-list`}>
          <Button type="button" variant={'secondary'}>
            <PlusCircle className="mr-2 h-5 w-5" />
            Create new List
          </Button>
        </Link>
      </div>
    );
  }

  return (
    <div className="mx-auto p-6 bg-gray-100 min-h-full">
      <div className="flex mb-4">
        <div className="flex-1">
          <BreadCrumbs />
          <p className="text-gray-600 mt-2">Manage Lists</p>
        </div>
        <div className="flex items-center space-x-2">
          {/* {(user.type == 'admin' || agentRoles.includes('Lead Management-Create Lists')) && (
            <Link href={`/${params.account_id}/list/create-list`}>
              <Button id="create-list" type="button" variant={'secondary'}>
                <PlusCircle className="mr-2 h-5 w-5" />
                Create new list
              </Button>
            </Link>
          )} */}
          <ListSelectionDialog accountId={params.account_id} />
        </div>
      </div>
      <DataTable columns={columns} data={lists} account_id={params.account_id} userType={user.type} agentRoles={agentRoles} />
      <Toaster />
    </div>
  );
};
