import { getAll<PERSON><PERSON>s, getAllMeta<PERSON>eys, getAllTags, getLeadsByListId, getSmartListById } from '@/lib/pocket';
import { Meta, User } from '@/lib/types';
import { server_component_pb } from '@/state/pb/server_component_pb';
import BreadCrumbs from './components/BreadCrumbs';
import { columns } from './components/columns';
import countrycodes from '@/lib/countrylistfiltered.json';
import { DataTable } from './components/data-table';
import { Option } from '@/components/ui/multiple-selector';

export const dynamic = 'force-dynamic';

export const fetchCache = 'default-no-store';

export default async ({ params, searchParams }: { params: Record<string, string>; searchParams: Record<string, string> }) => {
  const allLeads = await getAllLeads(params.account_id);
  const listData = (await getLeadsByListId(searchParams.listId)).map((item) => item.id);
  const { pb } = await server_component_pb();
  const metaKeys: Meta[] = await getAllMetaKeys(params.account_id);
  const user = pb.authStore.record as User;
  const agentRoles =
    user.expand?.roles?.map((role) => {
      return role.name;
    }) ?? [];

  const result = Object.fromEntries(
    allLeads.map((lead) => {
      if (listData.includes(lead.id)) {
        return [lead.id, true];
      } else {
        return [lead.id, false];
      }
    })
  );

  const listType = searchParams.type;
  let tags = null;
  let countriesOptions = null;
  let statusOptions = null;
  let smartList = null;
  if (listType == 'smart') {
    smartList = await getSmartListById(searchParams.listId);
    tags = await getAllTags(params.account_id);
    countriesOptions = countrycodes.map((country) => ({ label: `${country.iso['alpha-2']} - ${country.name}`, value: country.iso['alpha-2'] })) as Option[];
    statusOptions = ['New', 'Open', 'Prospect'].map((status) => ({ label: status, value: status })) as Option[];
  }

  return (
    <div className="mx-auto p-6 bg-gray-100 min-h-full">
      <div className="flex mb-4">
        <div className="flex-1">
          <BreadCrumbs />
          <p className="text-gray-600 mt-2">Select the leads from the table below and then give your list a name.</p>
        </div>
      </div>
      <DataTable
        metaKeys={metaKeys}
        columns={columns}
        data={allLeads}
        result={result}
        searchParams={searchParams}
        params={params}
        tags={tags}
        countriesOptions={countriesOptions}
        statusOptions={statusOptions}
        smartList={smartList}
      />
    </div>
  );
};
