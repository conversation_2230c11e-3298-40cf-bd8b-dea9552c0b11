'use client';
import { Bread<PERSON>rumb, BreadcrumbItem, BreadcrumbLink, BreadcrumbList, BreadcrumbPage, BreadcrumbSeparator } from '@/components/ui/breadcrumb';
import Link from 'next/link';
import { useParams } from 'next/navigation';

const BreadCrumbs = () => {
  const params = useParams();
  const account_id = params.account_id;
  return (
    <Breadcrumb>
      <BreadcrumbList>
        <BreadcrumbItem>
          <BreadcrumbLink href={`/${account_id}/lead-management`} className="font-bold text-2xl">
            Contact List
          </BreadcrumbLink>
        </BreadcrumbItem>
        <BreadcrumbSeparator className="h-6 w-6" />
        <BreadcrumbItem>
          <BreadcrumbLink href={`/${account_id}/list`} className="font-bold text-2xl">
            List
          </BreadcrumbLink>
        </BreadcrumbItem>
        <BreadcrumbSeparator className="h-6 w-6" />
        <BreadcrumbItem>
          <BreadcrumbPage className="font-bold text-2xl">Edit List</BreadcrumbPage>
        </BreadcrumbItem>
      </BreadcrumbList>
    </Breadcrumb>
  );
};

export default BreadCrumbs;
