'use client';

import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Checkbox } from '@/components/ui/checkbox';
import { HoverCard, HoverCardContent, HoverCardTrigger } from '@/components/ui/hover-card';
import { Country, IExpandedLead } from '@/lib/types';
import { convertFirstToUpperCase } from '@/lib/utils';
import { ColumnDef } from '@tanstack/react-table';
import { ArrowUpDown, Info } from 'lucide-react';

// This type is used to define the shape of our data.
// You can use a Zod schema here if you want.

export const columns: ColumnDef<IExpandedLead>[] = [
  {
    id: 'select',
    size: 50,
    maxSize: 50,
    header: ({ table }) => (
      <Checkbox
        checked={table.getIsAllPageRowsSelected() || (table.getIsSomePageRowsSelected() && 'indeterminate')}
        onCheckedChange={(value) => table.toggleAllRowsSelected(!!value)}
        aria-label="Select all"
      />
    ),
    cell: ({ row }) => {
      if (row.getIsSelected() || row.original.active) {
        return <Checkbox checked={row.getIsSelected()} onCheckedChange={(value) => row.toggleSelected(!!value)} aria-label="Select row" />;
      }
    },
    enableSorting: true,
    enableHiding: false,
    filterFn: (row, columnId, filterValue) => {
      if (filterValue === true) {
        return row.getIsSelected(); // Only show selected rows
      }
      return true; // Otherwise, show all rows
    },
  },
  {
    accessorKey: 'name',
    size: 100,
    maxSize: 100,
    header: ({ column }) => {
      return (
        <Button variant="ghost" className="px-0 mb-0" onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}>
          Name
          <ArrowUpDown className="ml-2 h-4 w-4" />
        </Button>
      );
    },
  },
  {
    accessorKey: 'phone_number',
    id: 'Phone Number',
    size: 125,
    maxSize: 125,
    header: ({ column }) => {
      return (
        <Button variant="ghost" className="px-0 mb-0" onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}>
          Phone Number
          <ArrowUpDown className="ml-2 h-4 w-4" />
        </Button>
      );
    },
  },
  {
    accessorKey: 'status',
    size: 100,
    maxSize: 100,

    header: ({ column }) => {
      return (
        <Button variant="ghost" className="px-0 mb-0">
          Status
        </Button>
      );
    },
  },
  {
    accessorKey: 'country',
    id: 'Country',
    maxSize: 300,
    filterFn: (row, columnId, filterValue: Country) => {
      const iso2 = filterValue.iso['alpha-2'].toLocaleLowerCase();
      const iso3 = filterValue.iso['alpha-3'].toLocaleLowerCase();
      const name = filterValue.name.toLocaleLowerCase();
      const country = row.original.country.toLocaleLowerCase();
      return country === iso2 || country === iso3 || country === name;
    },

    header: ({ column }) => {
      return (
        <Button variant="ghost" className="px-0 mb-0" onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}>
          Country
          <ArrowUpDown className="ml-2 h-4 w-4" />
        </Button>
      );
    },
    cell: ({ row }) => {
      return <div>{row.original?.country ?? 'N/A'}</div>;
    },
  },
  {
    accessorKey: 'tags',
    filterFn: 'arrIncludesAll',
    size: 150,
    maxSize: 300,
    minSize: 150,
    header: ({ column }) => {
      return (
        <Button variant="ghost" className="px-0 mb-0" onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}>
          Tags
          <ArrowUpDown className="ml-2 h-4 w-4" />
        </Button>
      );
    },
    cell: ({ row }) => {
      const tags = row.original.tags;
      return (
        <HoverCard>
          <HoverCardTrigger asChild>
            <div className="flex items-center hover:cursor-help flex-wrap gap-2">
              {tags.length > 0 ? (
                tags.slice(0, 3).map((tag, index) => (
                  <div
                    key={index}
                    className="inline-flex items-center whitespace-nowrap border px-2 bg-secondary text-secondary-foreground rounded-lg border-solidfont-normal"
                  >
                    {convertFirstToUpperCase(tag)}
                  </div>
                ))
              ) : (
                <div>N/A</div>
              )}
              <div className="underline decoration-dotted decoration-green-600 font-semibold">{tags.length > 3 && `+${tags.length - 3}`}</div>
            </div>
          </HoverCardTrigger>
          <HoverCardContent className="w-80">
            <div className="mb-2">All Tags:</div>
            <div className="flex gap-2 flex-wrap">
              {tags.map((tag, index) => (
                <div
                  key={index}
                  className="inline-flex items-center whitespace-nowrap border px-2 bg-secondary text-secondary-foreground rounded-lg border-solidfont-normal"
                >
                  {convertFirstToUpperCase(tag)}
                </div>
              ))}
            </div>
          </HoverCardContent>
        </HoverCard>
      );
    },
  },
  {
    accessorKey: 'opt_out',
    id: 'Is Opted',
    minSize: 50,
    header: ({ column }) => {
      return (
        <Button variant="ghost" className="px-0 mb-0" onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}>
          Is Opted
          <ArrowUpDown className="ml-2 h-4 w-4" />
        </Button>
      );
    },
    cell: ({ row }) => {
      if (!row.original.opt_out) {
        return <>Opted In</>;
      } else {
        return (
          <HoverCard openDelay={0}>
            <HoverCardTrigger asChild>
              <div className="flex space-x-2 items-center hover:cursor-help">
                <Badge className="rounded-full" variant={'destructive'}>
                  Opted Out
                </Badge>
                <Info className="h-4 w-4 text-destructive" />
              </div>
            </HoverCardTrigger>
            <HoverCardContent className="text-xs">
              <div>User has clicked on "Stop Promotions" button.</div>
              <div>User does not want to receive promotion content</div>
            </HoverCardContent>
          </HoverCard>
        );
      }
    },
  },
  {
    accessorKey: 'active',
    enablePinning: true,
    header: ({ column }) => {
      return (
        <Button variant="ghost" className="px-0 mb-0" onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}>
          Active
          <ArrowUpDown className="ml-2 h-4 w-4" />
        </Button>
      );
    },
    cell: ({ row }) => {
      return <div>{row.original.active.toString()}</div>;
    },
  },
];
