'use client';

import { <PERSON><PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Dialog, DialogClose, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import MultipleSelector, { Option } from '@/components/ui/multiple-selector';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { useToast } from '@/components/ui/use-toast';
import { updateList } from '@/lib/actions';
import { createListFilter, updateListFilter } from '@/lib/pocket';
import { IExpandedLead, IFilterCondition, IFilterGroup, ILead } from '@/lib/types';
import { Row, Table } from '@tanstack/react-table';
import { Loader2Icon, Plus, X } from 'lucide-react';
import { useRouter } from 'next/navigation';
import * as React from 'react';

interface FilterCondition {
  id: string;
  field: string;
  operator: string;
  value: string;
}

interface FilterGroup {
  id: string;
  type: 'and' | 'or';
  conditions: FilterCondition[];
}

export default function QueryBuilder({
  tags,
  countriesOptions,
  statusOptions,
  accountId,
  listId,
  listFilterId,
  smartList,
  table,
  data,
  selectedLeadIds,
}: {
  tags: Option[];
  countriesOptions: Option[];
  statusOptions: Option[];
  accountId: string;
  listId?: string;
  listFilterId?: string;
  smartList?: any;
  table: Table<IExpandedLead> | undefined;
  data: IExpandedLead[];
  selectedLeadIds: string[];
}) {
  const { toast } = useToast();
  const isEdit = listId && listFilterId;
  const router = useRouter();
  const [showListNameAlert, setShowListNameAlert] = React.useState(false);
  const [listName, setListName] = React.useState(smartList?.name ?? '');
  const [loading, setLoading] = React.useState(false);
  const [groups, setGroups] = React.useState<FilterGroup[]>(
    smartList?.expand?.list_filters?.list_filter_object ?? [
      {
        id: '1',
        type: 'and',
        conditions: [{ id: '1', field: '', operator: '', value: '' }],
      },
    ]
  );

  const fields = [
    { value: 'tags', label: 'Tags' },
    { value: 'country', label: 'Country' },
    { value: 'name', label: 'Name' },
    { value: 'status', label: 'Status' },
  ];

  const operators = [
    { value: 'equals', label: 'Equals' },
    { value: 'contains', label: 'Contains' },
  ];

  const addCondition = (groupId: string) => {
    setGroups(
      groups.map((group) => {
        if (group.id === groupId) {
          return {
            ...group,
            conditions: [...group.conditions, { id: Math.random().toString(), field: '', operator: '', value: '' }],
          };
        }
        return group;
      })
    );
  };

  const removeCondition = (groupId: string, conditionId: string) => {
    setGroups((prevGroups) => {
      const updatedGroups = prevGroups
        .map((group) => {
          if (group.id === groupId) {
            return {
              ...group,
              conditions: group.conditions.filter((c) => c.id !== conditionId),
            };
          }
          return group;
        })
        .filter((group) => group.conditions.length > 0);

      // Ensure there's always at least one group with one condition
      if (updatedGroups.length === 0) {
        return [
          {
            id: '1',
            type: 'and',
            conditions: [{ id: '1', field: '', operator: '', value: '' }],
          },
        ];
      }

      return updatedGroups;
    });
  };

  const updateCondition = (groupId: string, conditionId: string, field: keyof FilterCondition, value: string) => {
    setGroups(
      groups.map((group) => {
        if (group.id === groupId) {
          return {
            ...group,
            conditions: group.conditions.map((condition) => {
              if (condition.id === conditionId) {
                return { ...condition, [field]: value };
              }
              return condition;
            }),
          };
        }
        return group;
      })
    );
  };

  const addGroup = (type: 'and' | 'or') => {
    setGroups([
      ...groups,
      {
        id: Math.random().toString(),
        type,
        conditions: [{ id: Math.random().toString(), field: '', operator: '', value: '' }],
      },
    ]);
  };

  const buildExpression = (data: IFilterGroup[]) => {
    // Helper function to build condition strings for multiple values
    const buildCondition = (condition: IFilterCondition) => {
      const values = condition.value.split(',').map((val) => `"${val.toLowerCase().trim()}"`);
      const joinOperator = condition.operator === 'equals' && values.length > 1 ? ' && ' : ' || ';
      return `(${values.map((val) => `${condition.field} == ${val}`).join(joinOperator)})`;
    };

    // Function to build logical expressions for a group
    const buildGroup = (group: IFilterGroup) => {
      const conditions = group.conditions.map(buildCondition).join(' && ');
      return `(${conditions})`;
    };

    // Combine top-level groups based on their type
    return data.reduce((acc, group, index) => {
      const groupExpression = buildGroup(group);
      if (index === 0) {
        // Initialize with the first group's expression
        return groupExpression;
      }
      // Combine based on the type of the current group
      return `${acc} ${group.type === 'and' ? '&&' : '||'} ${groupExpression}`;
    }, '');
  };
  function isExpressionValid(expression: string): boolean {
    try {
      // Use Function to validate if the expression is syntactically correct
      new Function(`return ${expression}`);
      return true; // No error means the expression is valid
    } catch (e) {
      return false; // An error indicates invalid syntax
    }
  }

  // Generate the logical expression
  const saveFilter = () => {
    const res = isExpressionValid(buildExpression(groups));
    if (res) {
      setShowListNameAlert(true);
    } else {
      toast({
        variant: 'destructive',
        description: 'Invalid filters.',
      });
    }
  };

  const handleSaveFilter = async () => {
    setLoading(true);
    try {
      if (isEdit && table) {
        const selectedRows = table.getSelectedRowModel().rows;
        const selectedLeads = selectedRows.map((row: Row<IExpandedLead>) => row.original);
        const _addedLeadsArray = data.filter((_lead) => !_lead.checked).filter((_lead) => selectedLeads.map((lead) => lead.id).includes(_lead.id));

        const addedLeadsArray = _addedLeadsArray.filter((lead) => !selectedLeadIds.includes(lead.id));

        const removedLeadsArray = selectedLeadIds.filter((id) => !_addedLeadsArray.some((lead) => lead.id === id));

        await updateListFilter(buildExpression(groups), JSON.stringify(groups), listId, listName, listFilterId, removedLeadsArray, addedLeadsArray, accountId);
        toast({
          variant: 'success',
          description: 'List Updated.',
        });
        setLoading(false);
      } else {
        toast({
          variant: 'success',
          description: 'List Created.',
        });
        await createListFilter(buildExpression(groups), JSON.stringify(groups), listName, accountId);
        setLoading(false);
      }
      router.replace(`/${accountId}/list`);
    } catch (e) {
      toast({
        variant: 'destructive',
        description: 'There was an error.',
      });
      console.log(e);
      setLoading(false);
    }
  };
  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle>Smart Filters</CardTitle>
        <CardDescription>
          <div>
            <div>Following below are the scenerios in which the leads will be automatically added to the list when the filters match the criteria:</div>
            <div>
              <ul className="ml-6 list-disc [&>li]:mt-2">
                <li>When a new lead messages your business.</li>
                <li>When you create a new lead from the create contact page.</li>
                <li>When you edit a lead from edit contact page.</li>
                <li>When you bulk import leads from the import page.</li>
                <li>When you edit the lead details from live chat.</li>
              </ul>
            </div>
            <div className="mt-2">
              Leads coming from ads will have tag <span className="font-medium">Meta Ad</span>
            </div>
          </div>
        </CardDescription>
      </CardHeader>

      <CardContent className="space-y-6">
        {groups.map((group, groupIndex) => (
          <div key={group.id} className="space-y-4 bg-gray-100 p-4">
            {groupIndex > 0 && (
              <div className="flex items-center gap-2">
                <div className="h-px flex-1 bg-border" />
                <span className="text-sm font-medium text-muted-foreground">{group.type.toUpperCase()}</span>
                <div className="h-px flex-1 bg-border" />
              </div>
            )}
            {group.conditions.map((condition) => (
              <div key={condition.id} className="flex items-center gap-2">
                <Select value={condition.field} onValueChange={(value) => updateCondition(group.id, condition.id, 'field', value)}>
                  <SelectTrigger className="w-[400px]">
                    <SelectValue placeholder="Select a column" />
                  </SelectTrigger>
                  <SelectContent>
                    {fields.map((field) => (
                      <SelectItem key={field.value} value={field.value}>
                        {field.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>

                <Select value={condition.operator} onValueChange={(value) => updateCondition(group.id, condition.id, 'operator', value)}>
                  <SelectTrigger className="w-[400px]">
                    <SelectValue placeholder="Select an operator" />
                  </SelectTrigger>
                  <SelectContent>
                    {operators.map((operator) => (
                      <SelectItem key={operator.value} value={operator.value}>
                        {operator.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>

                {/* <Input
                  className="w-[200px]"
                  placeholder="Enter value"
                  value={condition.value}
                  onChange={(e) => updateCondition(group.id, condition.id, 'value', e.target.value)}
                /> */}
                <MultipleSelector
                  className="w-full bg-white border border-slate-200"
                  creatable
                  commandProps={{ className: 'w-full' }}
                  onChange={(e) => {
                    updateCondition(group.id, condition.id, 'value', e.map((item) => item.value).join(','));
                  }}
                  options={
                    condition.field == 'tags' ? tags : condition.field == 'country' ? countriesOptions : condition.field == 'status' ? statusOptions : []
                  }
                  value={condition.value.length > 0 ? condition.value.split(',').map((item) => ({ label: item, value: item })) : []}
                  showTagIcon={condition.field == 'tags'}
                  showStatusIcon={condition.field == 'status'}
                  showFlagIcon={condition.field == 'country'}
                  showNameIcon={condition.field == 'name'}
                  badgeClassName="bg-slate-200 text-slate-800"
                  placeholder={
                    condition.field == 'tags'
                      ? 'Select tags'
                      : condition.field == 'country'
                        ? 'Select countries'
                        : condition.field === 'status'
                          ? 'Select Status'
                          : 'Enter value'
                  }
                  emptyIndicator={<p className="text-center bg-white text-lg leading-10 text-gray-600 dark:text-gray-400">no results found.</p>}
                />

                <Button variant="ghost" size="icon" onClick={() => removeCondition(group.id, condition.id)}>
                  <X className="h-4 w-4" />
                </Button>
              </div>
            ))}
            <Button variant="default" size="sm" className="mt-2" onClick={() => addCondition(group.id)}>
              <Plus className="h-4 w-4 mr-2" />
              Add condition
            </Button>
          </div>
        ))}

        <div className="flex items-center gap-2 pt-4">
          <Button variant="outline" onClick={() => addGroup('and')}>
            AND group
          </Button>
          <Button variant="outline" onClick={() => addGroup('or')}>
            OR group
          </Button>
        </div>
        <Dialog open={showListNameAlert} onOpenChange={(e) => setShowListNameAlert(e)}>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>List name</DialogTitle>
              <DialogDescription>{isEdit ? 'Update List Name' : 'Add List Name'}</DialogDescription>
            </DialogHeader>
            <Input type="text" placeholder="Enter list name" onChange={(e) => setListName(e.target.value)} value={listName} />
            <DialogFooter>
              <DialogClose asChild>
                <Button variant={'default'} onClick={handleSaveFilter}>
                  {isEdit ? 'Update List' : 'Create List'}
                </Button>
              </DialogClose>
            </DialogFooter>
          </DialogContent>
        </Dialog>
        {loading ? (
          <Button disabled variant={'secondary'}>
            <Loader2Icon className="mr-2 h-4 w-4 animate-spin" />
            Saving
          </Button>
        ) : (
          <Button variant={'secondary'} onClick={saveFilter}>
            {isEdit ? 'Save Filter and Update List' : 'Save Filter and Create List'}
          </Button>
        )}
      </CardContent>

      {/* <div className="text-xs">{buildExpression(groups)}</div> */}
    </Card>
  );
}
