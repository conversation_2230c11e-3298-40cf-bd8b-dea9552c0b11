import { Option } from '@/components/ui/multiple-selector';
import { Toaster } from '@/components/ui/toaster';
import countrycodes from '@/lib/countrylistfiltered.json';
import { getAllTags } from '@/lib/pocket';
import BreadCrumbs from './components/BreadCrumbs';
import QueryBuilder from './components/QueryBuilder';

export default async ({ params }: { params: Record<string, string> }) => {
  const tags = await getAllTags(params.account_id);
  const countriesOptions = countrycodes.map((country) => ({ label: `${country.iso['alpha-2']} - ${country.name}`, value: country.iso['alpha-2'] })) as Option[];
  const statusOptions = ['New', 'Open', 'Prospect'].map((status) => ({ label: status, value: status })) as Option[];

  return (
    <div className="mx-auto p-6 bg-gray-100 min-h-full">
      <div className="flex mb-4">
        <div className="flex-1">
          <BreadCrumbs />
          <p className="text-gray-600 mt-2">Select the leads from the table below and then give your list a name.</p>
        </div>
      </div>
      <QueryBuilder
        tags={tags}
        countriesOptions={countriesOptions}
        statusOptions={statusOptions}
        accountId={params.account_id}
        table={undefined}
        data={[]}
        selectedLeadIds={[]}
      />
      <Toaster />
    </div>
  );
};
