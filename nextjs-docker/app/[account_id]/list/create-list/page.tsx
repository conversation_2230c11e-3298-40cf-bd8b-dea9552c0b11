import { getAllLeads, getAllMetaKeys } from '@/lib/pocket';
import Link from 'next/link';
import { Button } from '@/components/ui/button';
import { Meta } from '@/lib/types';
import { Toaster } from '@/components/ui/toaster';
import BreadCrumbs from './components/BreadCrumbs';
import Page from './client';

export default async ({ params }: { params: Record<string, string> }) => {
  const leads = await getAllLeads(params.account_id);
  const metaKeys: Meta[] = await getAllMetaKeys(params.account_id);
  const activeLeads = leads.filter((item) => {
    return item.active;
  });
  return (
    <div className="mx-auto p-6 bg-gray-100 min-h-full">
      <div className="flex mb-4">
        <div className="flex-1">
          <BreadCrumbs />
          <p className="text-gray-600 mt-2">Select the leads from the table below and then give your list a name.</p>
        </div>
      </div>
      <Page metaKeys={metaKeys} activeLeads={activeLeads} params={params} />
      <Toaster />
    </div>
  );
};
