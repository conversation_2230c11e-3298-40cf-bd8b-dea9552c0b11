'use client';

import { Button } from '@/components/ui/button';
import { Checkbox } from '@/components/ui/checkbox';
import { HoverCard, HoverCardContent, HoverCardTrigger } from '@/components/ui/hover-card';
import { Country, IExpandedLead, ILead, Meta } from '@/lib/types';
import { convertFirstToUpperCase } from '@/lib/utils';
import { ColumnDef } from '@tanstack/react-table';
import { ArrowUpDown } from 'lucide-react';

const getMetaColumns = (metaKeys: Meta[]) => {
  return metaKeys.map((metaKey) => {
    return {
      id: metaKey.key == 'phone_number' ? 'meta._phone_number' : `meta.${metaKey.key.toString()}`,
      maxSize: 150,
      header: ({ column }: { column: any }) => {
        return (
          <Button variant="ghost" className="px-0 mb-0" onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}>
            {metaKey.key}
          </Button>
        );
      },
    };
  });
};

export const listColumns = (meta: Meta[]): ColumnDef<IExpandedLead>[] => [
  {
    id: 'select',
    size: 80,
    header: ({ table, header }) => (
      <div className="flex gap-2">
        <Checkbox
          checked={table.getIsAllPageRowsSelected() || (table.getIsSomePageRowsSelected() && 'indeterminate')}
          onCheckedChange={(value) => table.toggleAllRowsSelected(!!value)}
          aria-label="Select all"
        />
        <div className="text-blue-600">Select all rows</div>
      </div>
    ),
    cell: ({ row }) => <Checkbox checked={row.getIsSelected()} onCheckedChange={(value) => row.toggleSelected(!!value)} aria-label="Select row" />,
    enableSorting: true,
    enableHiding: false,
  },
  {
    accessorKey: 'name',
    size: 100,
    header: ({ column }) => {
      return (
        <Button variant="ghost" className="px-0 mb-0" onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}>
          Name
          <ArrowUpDown className="ml-2 h-4 w-4" />
        </Button>
      );
    },
  },
  {
    accessorKey: 'phone_number',
    size: 100,
    header: ({ column }) => {
      return (
        <Button variant="ghost" className="px-0 mb-0" onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}>
          Phone Number
          <ArrowUpDown className="ml-2 h-4 w-4" />
        </Button>
      );
    },
  },
  {
    accessorKey: 'country',
    id: 'Country',
    maxSize: 300,
    filterFn: (row, columnId, filterValue: Country) => {
      const iso2 = filterValue.iso['alpha-2'].toLocaleLowerCase();
      const iso3 = filterValue.iso['alpha-3'].toLocaleLowerCase();
      const name = filterValue.name.toLocaleLowerCase();
      const country = row.original.country.toLocaleLowerCase();
      return country === iso2 || country === iso3 || country === name;
    },

    header: ({ column }) => {
      return (
        <Button variant="ghost" className="px-0 mb-0" onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}>
          Country
          <ArrowUpDown className="ml-2 h-4 w-4" />
        </Button>
      );
    },
    cell: ({ row }) => {
      return <div>{row.original?.country ?? 'N/A'}</div>;
    },
  },
  {
    accessorKey: 'status',
    size: 100,
    header: ({ column }) => {
      return (
        <Button variant="ghost" className="px-0 mb-0">
          Status
        </Button>
      );
    },
  },
  {
    accessorKey: 'tags',
    filterFn: 'arrIncludesAll',
    size: 300,
    maxSize: 300,
    minSize: 150,
    header: ({ column }) => {
      return (
        <Button variant="ghost" className="px-0 mb-0" onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}>
          Tags
          <ArrowUpDown className="ml-2 h-4 w-4" />
        </Button>
      );
    },
    cell: ({ row }) => {
      const tags = row.original.tags;
      return (
        <HoverCard>
          <HoverCardTrigger asChild>
            <div className="flex items-center hover:cursor-help flex-wrap gap-2">
              {tags.length > 0 ? (
                tags.slice(0, 5).map((tag, index) => (
                  <div
                    key={index}
                    className="inline-flex items-center whitespace-nowrap border px-2 bg-secondary text-secondary-foreground rounded-lg border-solidfont-normal"
                  >
                    {convertFirstToUpperCase(tag)}
                  </div>
                ))
              ) : (
                <div>N/A</div>
              )}
              <div className="underline decoration-dotted decoration-green-600 font-semibold">{tags.length > 5 && `+${tags.length - 5}`}</div>
            </div>
          </HoverCardTrigger>
          <HoverCardContent className="w-80">
            <div className="mb-2">All Tags:</div>
            <div className="flex gap-2 flex-wrap">
              {tags.map((tag, index) => (
                <div
                  key={index}
                  className="inline-flex items-center whitespace-nowrap border px-2 bg-secondary text-secondary-foreground rounded-lg border-solidfont-normal"
                >
                  {convertFirstToUpperCase(tag)}
                </div>
              ))}
            </div>
          </HoverCardContent>
        </HoverCard>
      );
    },
  },
  ...getMetaColumns(meta),
];
