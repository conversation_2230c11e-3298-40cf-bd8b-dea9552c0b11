'use client';

import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuLabel, DropdownMenuTrigger } from '@/components/ui/dropdown-menu';

import {
  AlertDialog,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from '@/components/ui/alert-dialog';
import { Button } from '@/components/ui/button';

import { useToast } from '@/components/ui/use-toast';
import { deleteList } from '@/lib/pocket';
import { ICampaign, IExpandedListAndLeadCount } from '@/lib/types';
import { Row } from '@tanstack/react-table';
import { Eye, Loader2, MoreHorizontal, Pencil, Trash } from 'lucide-react';
import Link from 'next/link';
import { useParams } from 'next/navigation';
import { useState } from 'react';

const ListActionDropDown = ({
  row,
  userType,
  agentRoles,
  campaigns,
}: {
  row: Row<IExpandedListAndLeadCount>;
  userType: string;
  agentRoles: string[];
  campaigns: ICampaign[];
}) => {
  const params = useParams<{ account_id: string }>();
  const { toast } = useToast();
  const [loading, setLoading] = useState(false);
  const [open, setOpen] = useState(false);
  const handleDeleteList = async () => {
    try {
      setLoading(true);
      await deleteList(row.id, params.account_id);
      toast({
        variant: 'success',
        description: 'List Deleted Successfully',
      });
    } catch (error) {
      toast({
        variant: 'destructive',
        description: 'There was an error in deleting the list. Try again!',
      });
    }
    setOpen(false);
    setLoading(false);
  };

  const isEditAllowed = userType == 'admin' || agentRoles.includes('Lead Management-Update Lists');
  const isDeleteAllowed =
    userType == 'admin' ||
    (agentRoles.includes('Lead Management-Delete Lists') && row.id != '0rpzegffdxjqiyt' && row.id != 'mzy17506lpb5e94' && row.id != 'dvoaqfxphoxazzl');
  return (
    <>
      <AlertDialog open={open} onOpenChange={(isOpen) => setOpen(isOpen)}>
        <DropdownMenu modal={false}>
          <DropdownMenuTrigger asChild>
            <Button variant="ghost" className="h-8 w-8 p-0 hover:bg-gray-100 mb-0">
              <span className="sr-only">Open menu</span>
              <MoreHorizontal className="h-4 w-4" />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end">
            <DropdownMenuLabel>Actions</DropdownMenuLabel>
            <Link
              href={`list/view-list?listId=${row.id}&listName=${row.original.name}&type=${row.original.type == 'static' || row.original.type == '' ? 'static' : 'smart'}`}
            >
              <DropdownMenuItem>
                <Eye className="mr-2 h-4 w-4 text-blue-600" />
                <span className="text-blue-600">View</span>
              </DropdownMenuItem>
            </Link>
            {isEditAllowed && (
              <Link
                href={`list/edit-list?listId=${row.id}&listName=${row.original.name}&type=${row.original.type == 'static' || row.original.type == '' ? 'static' : 'smart'}`}
              >
                <DropdownMenuItem>
                  <Pencil className="mr-2 h-4 w-4 text-green-600" />
                  <span className="text-green-600">Edit</span>
                </DropdownMenuItem>
              </Link>
            )}
            {isDeleteAllowed && !campaigns && (
              <AlertDialogTrigger asChild>
                <DropdownMenuItem>
                  <Trash className="mr-2 h-4 w-4 text-red-600" />
                  <span className="text-red-600">Delete</span>
                </DropdownMenuItem>
              </AlertDialogTrigger>
            )}
          </DropdownMenuContent>
        </DropdownMenu>

        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Are you sure you want to delete the list {row.original.name}?</AlertDialogTitle>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            {loading ? (
              <Button className="mb-0" disabled>
                Deleting
                <Loader2 className="ml-2 animate-spin" />
              </Button>
            ) : (
              <>
                <Button onClick={() => handleDeleteList()} variant={'destructive'} className="mb-0">
                  Yes Delete
                </Button>
              </>
            )}
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </>
  );
};

export default ListActionDropDown;
