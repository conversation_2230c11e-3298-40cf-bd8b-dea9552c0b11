'use client';
import { But<PERSON> } from '@/components/ui/button';
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Label } from '@/components/ui/label';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { PlusCircle } from 'lucide-react';
import { useRouter } from 'next/navigation';
import { useState } from 'react';

export function ListSelectionDialog({ accountId }: { accountId: string }) {
  const [listType, setListType] = useState('static');
  const router = useRouter();
  const handleNext = () => {
    if (listType === 'static') {
      router.replace(`/${accountId}/list/create-list`);
    } else {
      router.replace(`/${accountId}/list/create-dynamic-list`);
    }
  };
  return (
    <Dialog>
      <DialogTrigger asChild>
        <Button id="create-list" type="button" variant={'secondary'}>
          <PlusCircle className="mr-2 h-5 w-5" />
          Create new list
        </Button>
      </DialogTrigger>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>Select List Type</DialogTitle>
          <DialogDescription>Select the type of list you want to create.</DialogDescription>
        </DialogHeader>
        <div>
          <RadioGroup defaultValue="static" onValueChange={(val) => setListType(val)}>
            <div className="mb-2">
              <div className="text-sm text-gray-500 mb-2">Select leads to add to a static list</div>
              <div className="flex items-center space-x-2">
                <RadioGroupItem value="static" id="r1" />
                <Label htmlFor="r1">Static List</Label>
              </div>
            </div>
            <div>
              <div className="text-sm text-gray-500 mb-2">Automatically add leads to your list using smart filters.</div>
              <div className="flex items-center space-x-2">
                <RadioGroupItem value="smart" id="r2" />
                <Label htmlFor="r2">Smart List</Label>
              </div>
            </div>
          </RadioGroup>
        </div>
        <DialogFooter>
          <Button type="button" onClick={handleNext}>
            Next
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
