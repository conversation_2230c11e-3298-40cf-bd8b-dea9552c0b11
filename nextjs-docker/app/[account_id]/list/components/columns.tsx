'use client';

import PocketBase from 'pocketbase';
import { Button } from '@/components/ui/button';
import { ICampaign, IExpandedListAndLeadCount } from '@/lib/types';
import { ColumnDef, type Row } from '@tanstack/react-table';
import { ArrowUpDown, Loader2Icon } from 'lucide-react';

import dayjs from 'dayjs';
// import utc from 'dayjs/plugin/utc'
import { Checkbox } from '@/components/ui/checkbox';
import { HoverCard, HoverCardContent, HoverCardTrigger } from '@/components/ui/hover-card';
import ShineBorder from '@/components/ui/shine-border';
import ListActionDropDown from './ListActionDropDown';
import { Suspense, useEffect, useState } from 'react';
import { Skeleton } from '@/components/ui/skeleton';
import { pb_url } from '@/state/consts';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';

const CampaignsInList = ({ row }: { row: Row<IExpandedListAndLeadCount> }) => {
  const listId = row.original.id;
  const [campaigns, setCampaigns] = useState<ICampaign[] | undefined>();
  useEffect(() => {
    const effect = async () => {
      if (listId) {
        const cookie = document.cookie;
        const pb = new PocketBase(pb_url);
        pb.authStore.loadFromCookie(cookie);
        try {
          pb.collection<ICampaign>('campaigns')
            .getList(1, 50, {
              filter: `leads_list ~ "${listId}"`,
              expand: 'created_by',
            })
            .then((res) => {
              console.log(res.items);
              setCampaigns(res.items);
            });
        } catch (e) {}
      }
    };
    effect();
  }, [listId]);
  if (typeof campaigns === 'undefined') {
    return <Skeleton className="h-2 w-10" />;
  }
  return (
    <HoverCard>
      <HoverCardTrigger asChild>
        <div className="flex items-center hover:cursor-help flex-wrap gap-2">
          {campaigns && campaigns.length > 0 ? (
            campaigns.slice(0, 3).map((campaigns, index) => (
              <div
                key={index}
                className="inline-flex items-center whitespace-nowrap border px-2 bg-secondary text-secondary-foreground rounded-lg border-solidfont-normal"
              >
                {campaigns.name}
              </div>
            ))
          ) : (
            <div>N/A</div>
          )}
          {campaigns && (
            <div className="underline decoration-dotted decoration-green-600 font-semibold">{campaigns.length > 3 && `+${campaigns.length - 3}`}</div>
          )}
        </div>
      </HoverCardTrigger>
      <HoverCardContent className="w-80">
        <div className="mb-2">All Campaigns:</div>
        <div className="flex gap-2 flex-wrap">
          {campaigns &&
            campaigns.map((campaigns, index) => (
              <div
                key={index}
                className="inline-flex items-center whitespace-nowrap border px-2 bg-secondary text-secondary-foreground rounded-lg border-solidfont-normal"
              >
                {campaigns.name}
              </div>
            ))}
        </div>
      </HoverCardContent>
    </HoverCard>
  );
};
export const columns: ColumnDef<IExpandedListAndLeadCount>[] = [
  {
    id: 'select',
    size: 20,
    maxSize: 20,
    minSize: 20,
    header: ({ table }) => (
      <Checkbox
        checked={table.getIsAllPageRowsSelected() || (table.getIsSomePageRowsSelected() && 'indeterminate')}
        onCheckedChange={() => {
          // Get all the rows on the current page
          const pageRows = table.getRowModel().rows;

          const isSelecting = table.getIsAllPageRowsSelected() || (table.getIsSomePageRowsSelected() && 'indeterminate');
          // Toggle selection only for rows that meet the conditions
          pageRows.forEach((row) => {
            if (!row.original.expand?.campaigns_via_leads_list) {
              row.toggleSelected(!isSelecting);
            }
          });
        }}
        aria-label="Select all"
      />
    ),
    cell: ({ row }) => {
      const campaigns = row.original.expand?.campaigns_via_leads_list;
      if (!campaigns) {
        return (
          <Checkbox value={row.original.id} checked={row.getIsSelected()} onCheckedChange={(value) => row.toggleSelected(!!value)} aria-label="Select row" />
        );
      }
    },
    enableSorting: false,
    enableHiding: false,
  },
  {
    accessorKey: 'name',
    size: 150,
    maxSize: 300,
    minSize: 150,
    header: ({ column }) => {
      return (
        <Button variant="ghost" className="px-0 mb-0" onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}>
          List Name
          <ArrowUpDown className="ml-2 h-4 w-4" />
        </Button>
      );
    },
    cell: ({ row }) => {
      return (
        <TooltipProvider>
          <Tooltip delayDuration={0}>
            <TooltipTrigger asChild>
              <div className="font-medium truncate">{row.original.name}</div>
            </TooltipTrigger>
            <TooltipContent>
              <p>{row.original.name}</p>
            </TooltipContent>
          </Tooltip>
        </TooltipProvider>
      );
    },
  },
  {
    accessorKey: 'expand.created_by.name',
    size: 150,
    maxSize: 300,
    minSize: 150,
    header: ({ column }) => {
      return (
        <Button variant="ghost" className="px-0 mb-0" onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}>
          Created By
          <ArrowUpDown className="ml-2 h-4 w-4" />
        </Button>
      );
    },
  },
  {
    id: 'no_of_contacts',
    size: 150,
    maxSize: 300,
    minSize: 150,
    header: ({ column }) => {
      return (
        <Button variant="ghost" className="px-0 mb-0">
          Number of contacts
        </Button>
      );
    },
    cell: ({ row }) => {
      const totalContacts = row.original?.leads_count;
      return (
        <div className="flex gap-1">
          {totalContacts}
          {row.original?.pending ? <Loader2Icon className="animate-spin" /> : null}
        </div>
      );
    },
  },
  {
    id: 'campaigns',
    size: 150,
    maxSize: 300,
    minSize: 150,
    header: ({ column }) => {
      return (
        <Button variant="ghost" className="px-0 mb-0">
          Campaigns
        </Button>
      );
    },
    cell: ({ row }) => {
      return (
        <Suspense>
          <CampaignsInList row={row} />
        </Suspense>
      );
    },
  },
  {
    accessorKey: 'type',
    header: ({ column }) => {
      return (
        <Button variant="ghost" className="px-0 mb-0" onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}>
          List Type
          <ArrowUpDown className="ml-2 h-4 w-4" />
        </Button>
      );
    },
    cell: ({ row }) => {
      if (row.original.type === 'static' || row.original.type == '') {
        return <div className="relative w-fit place-items-center p-3 text-black ">Static List</div>;
      } else {
        return (
          <ShineBorder className="rounded-lg border min-h-[2px] min-w-[2px]" color={['#A07CFE', '#FE8FB5', '#FFBE7B']} duration={5}>
            <span className="">Smart List</span>
          </ShineBorder>
        );
      }
    },
  },
  {
    accessorKey: 'created',
    header: ({ column }) => {
      return (
        <Button variant="ghost" className="px-0 mb-0" onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}>
          Created At
          <ArrowUpDown className="ml-2 h-4 w-4" />
        </Button>
      );
    },
    cell: ({ row }) => {
      return dayjs(row.getValue('created')).format('MMMM D, YYYY h:mm A');
    },
  },
  {
    accessorKey: 'updated',
    header: ({ column }) => {
      return (
        <Button variant="ghost" className="px-0 mb-0" onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}>
          Updated At
          <ArrowUpDown className="ml-2 h-4 w-4" />
        </Button>
      );
    },
    cell: ({ row }) => {
      return dayjs(row.getValue('updated')).format('MMMM D, YYYY h:mm A');
    },
  },
  {
    id: 'actions',
    size: 50,
    minSize: 50,
    maxSize: 50,
    cell: ({ row, table }) => {
      const campaigns = row.original.expand?.campaigns_via_leads_list;
      return (
        <ListActionDropDown row={row} userType={table.options.meta?.userType ?? ''} agentRoles={table.options.meta?.agentRoles ?? []} campaigns={campaigns} />
      );
    },
  },
];
