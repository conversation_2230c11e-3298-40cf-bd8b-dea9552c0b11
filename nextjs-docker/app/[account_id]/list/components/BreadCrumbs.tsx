'use client';
import { Breadcrumb, BreadcrumbItem, BreadcrumbLink, BreadcrumbList, BreadcrumbPage, BreadcrumbSeparator } from '@/components/ui/breadcrumb';
import Link from 'next/link';
import { useParams } from 'next/navigation';

const BreadCrumbs = () => {
  const params = useParams();
  const account_id = params.account_id;
  return (
    <Breadcrumb>
      <BreadcrumbList>
        <BreadcrumbItem>
          <BreadcrumbLink className="font-bold text-2xl" asChild>
            <Link href={`/${account_id}/lead-management`}>Contact List</Link>
          </BreadcrumbLink>
        </BreadcrumbItem>
        <BreadcrumbSeparator className="h-6 w-6" />
        <BreadcrumbItem>
          <BreadcrumbPage className="font-bold text-2xl">List</BreadcrumbPage>
        </BreadcrumbItem>
      </BreadcrumbList>
    </Breadcrumb>
  );
};

export default BreadCrumbs;
