import TopHeader from '@/components/shared/TopHeader';
import { getAgentsAndUsers } from '@/lib/actions';
import { getAccount, getMessageLimitsByUser } from '@/lib/pocket';
import { User } from '@/lib/types';
import { server_component_pb } from '@/state/pb/server_component_pb';

const TopHeaderPage = async ({ params }: { params: { [key: string]: string } }) => {
  const { pb, cookies } = await server_component_pb();
  const impersonated = cookies().get('impersonate')?.value ? true : false;
  const user = pb.authStore.record as User;
  const isSuperuser = pb.authStore.isSuperuser;
  if (!user) {
    return; // User not found
  }
  try {
    const account = await getAccount(params.account_id);
    let users = undefined as User[] | undefined;
    if (isSuperuser) {
      users = await getAgentsAndUsers(account.id);
    }
    const messagingLimit = await getMessageLimitsByUser(user.id, params.account_id);
    const user_avatar = pb.files.getURL(user, user.avatar, { thumb: '100x100' });
    return (
      <TopHeader
        isSuperuser={isSuperuser}
        impersonated={impersonated}
        account={account}
        user={user}
        messagingLimit={messagingLimit}
        user_avatar={user_avatar}
        users={users}
      />
    );
  } catch (error) {
    return <div></div>;
  }
};

export default TopHeaderPage;
