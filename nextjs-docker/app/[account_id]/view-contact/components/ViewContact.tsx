'use client';

import { Badge } from '@/components/ui/badge';
import { Breadcrumb, BreadcrumbItem, BreadcrumbLink, BreadcrumbList, BreadcrumbSeparator } from '@/components/ui/breadcrumb';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from '@/components/ui/collapsible';
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from '@/components/ui/dropdown-menu';
import { Label } from '@/components/ui/label';
import { Option } from '@/components/ui/multiple-selector';
import { ScrollArea } from '@/components/ui/scroll-area';
import { useToast } from '@/components/ui/use-toast';
import { blockUnBlockLeadFromContact } from '@/lib/actions';
import { blockUnBlockLeadWeTarseel } from '@/lib/pocket';
import { Account, ILead, IList } from '@/lib/types';
import { convertFirstToUpperCase } from '@/lib/utils';
import dayjs from 'dayjs';
import { ChevronDown, ChevronDownIcon, ExternalLink, Loader2 } from 'lucide-react';
import Link from 'next/link';
import { useState } from 'react';

interface CampaignArray {
  name: string;
  id: string;
  date_and_time: Date;
  leadReply: any;
}

[];

export function ViewContact({
  account,
  lead,
  campaignsArray,
  lists,
}: {
  account: Account;
  tagsOptions: Option[];
  lead: ILead;
  campaignsArray: CampaignArray[] | undefined;
  lists: IList[];
}) {
  const { toast } = useToast();
  const [loading, setLoading] = useState(false);
  return (
    <div className=" bg-gray-100 min-h-dvh">
      {lead.blocked && <div className="bg-red-500 text-white p-4 text-center">This lead is blocked. Click the unblock button to unblock the lead.</div>}
      <div className="p-6">
        <div className="flex items-center mb-6">
          <div className="flex-1">
            <div>
              <Breadcrumb>
                <BreadcrumbList>
                  <BreadcrumbItem>
                    <BreadcrumbLink className="font-bold text-2xl" asChild>
                      <Link href={`/${account.id}/lead-management`}>Contact List</Link>
                    </BreadcrumbLink>
                  </BreadcrumbItem>
                  <BreadcrumbSeparator className="h-6 w-6" />
                  <BreadcrumbItem>
                    <DropdownMenu>
                      <DropdownMenuTrigger className="flex items-center gap-1 font-bold text-2xl text-black">
                        View Contact
                        <ChevronDownIcon />
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="start">
                        <Link href={`/${account.id}/edit-contact?id=${lead.id}`}>
                          <DropdownMenuItem>Edit Contact</DropdownMenuItem>
                        </Link>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </BreadcrumbItem>
                </BreadcrumbList>
              </Breadcrumb>
            </div>
          </div>
          <div className="flex items-center space-x-2">
            <Button
              disabled={loading}
              variant={lead.blocked ? 'secondary' : 'destructive'}
              onClick={async () => {
                setLoading(true);
                const { convo, res } = await blockUnBlockLeadFromContact({ account, lead });
                if (res?.message.status == 200) {
                  await blockUnBlockLeadWeTarseel(lead.id, !convo.chat_archive, !convo.chat_block, convo.id, account.id);
                  toast({
                    variant: 'success',
                    description: res.message.description,
                  });
                } else {
                  toast({
                    variant: 'destructive',
                    description: res?.message.description,
                  });
                }

                setLoading(false);
              }}
            >
              {loading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
              {lead.blocked ? 'Unblock' : 'Block'}
            </Button>

            <Link href={`/${account.id}/edit-contact?id=${lead.id}`}>
              <Button variant={'link'} type="button">
                Edit Contact
              </Button>
            </Link>
            <Link href={`/${account.id}/live-chat?user_id=${lead.id}`}>
              <Button variant="default">Go to live chat</Button>
            </Link>
          </div>
        </div>

        <div>
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <div className="space-y-6">
              <Card>
                <CardHeader className="flex flex-col space-y-2">
                  <CardTitle>Contact Information</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="">
                    <Label htmlFor="name">Name</Label>
                    <div className="text-md">{lead.name}</div>
                  </div>

                  <div className="">
                    <Label htmlFor="name">Phone Number</Label>
                    <div className="text-md">{lead.phone_number}</div>
                  </div>

                  <div className="">
                    <Label htmlFor="name">Tags</Label>
                    <div className="flex items-center hover:cursor-help flex-wrap gap-2">
                      {lead.tags.length > 0 ? (
                        lead.tags.map((tag, index) => (
                          <div
                            key={index}
                            className="inline-flex items-center whitespace-nowrap border px-2 bg-secondary text-secondary-foreground rounded-lg border-solidfont-normal"
                          >
                            {convertFirstToUpperCase(tag)}
                          </div>
                        ))
                      ) : (
                        <div>N/A</div>
                      )}
                      <div className="underline decoration-dotted decoration-green-600 font-semibold">{lead.tags.length > 3 && `+${lead.tags.length - 3}`}</div>
                    </div>
                  </div>

                  <div className="">
                    <Label htmlFor="status">Status</Label>
                    <div className="text-md">{lead.status}</div>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>Meta Data</CardTitle>
                  <p className="text-sm text-muted-foreground">Collected from the lead in the automation flows</p>
                </CardHeader>
                <CardContent className="space-y-2">
                  {lead.meta ? (
                    Object.keys(lead.meta).map((key) => (
                      <div key={key} className="flex justify-between">
                        <span className="text-muted-foreground">{key}</span>
                        <span>{lead.meta[key]}</span>
                      </div>
                    ))
                  ) : (
                    <div>No Meta Data</div>
                  )}
                </CardContent>
              </Card>
            </div>

            <div className="space-y-6">
              <Card>
                <CardHeader>
                  <CardTitle>Associations</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <Collapsible className="border rounded-lg" defaultOpen={true}>
                    <CollapsibleTrigger className="flex justify-between items-center w-full p-4 hover:bg-muted/50">
                      <span>Lead is part of following List(s)</span>
                      <ChevronDown className="h-4 w-4" />
                    </CollapsibleTrigger>
                    <CollapsibleContent className="p-4 pt-0">
                      <ScrollArea className="max-h-96">
                        <div className="flex flex-col space-y-1">
                          {(!lists || lists?.length == 0) && <div>No data found</div>}
                          {lists?.map((leads: any) => (
                            <Link
                              key={leads.id}
                              className="group flex items-center p-2 hover:bg-gray-200 rounded-md transition-colors duration-200"
                              href={`list/view-list?listId=${leads.id}&listName=${leads.name}&leadId=${lead.id}`}
                              target="_blank"
                            >
                              <div className="flex-1 text-gray-700">{leads.name}</div>
                              <ExternalLink className="h-5 w-5 text-gray-500 group-hover:text-gray-900 transition-colors duration-200" />
                            </Link>
                          ))}
                        </div>
                      </ScrollArea>
                    </CollapsibleContent>
                  </Collapsible>

                  <Collapsible className="border rounded-lg" defaultOpen={true}>
                    <CollapsibleTrigger className="flex justify-between items-center w-full p-4 hover:bg-muted/50">
                      <span>Lead is part of following Campaign(s)</span>
                      <ChevronDown className="h-4 w-4" />
                    </CollapsibleTrigger>
                    <CollapsibleContent className="p-4 pt-0">
                      <ScrollArea className="max-h-96 overflow-auto">
                        <div className="flex flex-col space-y-1">
                          {campaignsArray?.length == 0 && <div>No data found</div>}
                          {campaignsArray?.map((campaign: CampaignArray) => (
                            <Link
                              key={campaign.id}
                              className="group flex items-center p-3 hover:bg-gray-200 rounded-md transition-colors duration-200"
                              href={`campaign/${campaign.id}`}
                              target="_blank"
                            >
                              <div className="flex-1 text-gray-700">
                                {campaign.name}
                                <span className="block text-sm text-gray-500">{dayjs(campaign.date_and_time).format('MMMM D, YYYY h:mm A')}</span>
                                <Badge
                                  style={{ backgroundColor: campaign.leadReply == 'Not Replied' ? 'red' : 'green' }}
                                  className="text-sm mt-2"
                                  variant="destructive"
                                >
                                  {campaign.leadReply}
                                </Badge>
                              </div>
                              <ExternalLink className="h-5 w-5 text-gray-500 group-hover:text-gray-900 transition-colors duration-200 ml-2" />
                            </Link>
                          ))}
                        </div>
                      </ScrollArea>
                    </CollapsibleContent>
                  </Collapsible>
                </CardContent>
              </Card>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
