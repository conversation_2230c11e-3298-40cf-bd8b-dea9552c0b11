import { getAccount, getAllTags, getCampaignsByLists, getLeadDataById } from '@/lib/pocket';
import { ViewContact } from './components/ViewContact';

export default async ({ params, searchParams }: { params: Record<string, string>; searchParams: Record<string, string> }) => {
  const lead = await getLeadDataById(searchParams.id);
  const account = await getAccount(params.account_id);
  const campaigns_array = await getCampaignsByLists(lead.expand?.campaign_history, lead.id);
  const lists = lead.expand?.list ?? [];

  const tagsOptions = await getAllTags(params.account_id);
  return <ViewContact account={account} tagsOptions={tagsOptions} lead={lead} campaignsArray={campaigns_array} lists={lists} />;
};
