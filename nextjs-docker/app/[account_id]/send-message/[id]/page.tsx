import { Toaster } from '@/components/ui/toaster';
import { getAccount, getAllLeads, getMessageLimitsByUser, getTemplateFromDbByTemplateName, getUser } from '@/lib/pocket';
import SendTemplatePage from './components/SendTemplatePage';

export const dynamic = 'force-dynamic';
export const fetchCache = 'default-no-store';

export default async ({ params }: { params: Record<string, string> }) => {
  const user = await getUser();
  if (!user) {
    return; // User not found
  }
  const account = await getAccount(params.account_id);
  const messagingLimit = await getMessageLimitsByUser(user.id, params.account_id);
  const template = await getTemplateFromDbByTemplateName({ account, template_name: params.id });
  const leads = await getAllLeads(params.account_id);

  return (
    <div className="mx-auto p-6 bg-gray-100 min-h-full">
      <div className="flex mb-4">
        <div className="flex-1">
          <h3 className="text-gray-800 text-xl font-bold sm:text-2xl">Select Recipient</h3>
          <p className="text-gray-600 mt-2">Send the template "{template?.template_name}" to selected recipients</p>
        </div>
      </div>
      <Toaster />
      <SendTemplatePage leads={leads} template={template} params={params} messagingLimit={messagingLimit} />
    </div>
  );
};
