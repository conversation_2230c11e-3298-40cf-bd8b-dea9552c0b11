import { Loader2 } from 'lucide-react';

export default async ({ params }: { params: Record<string, string> }) => {
  return (
    <div className="mx-auto p-6 bg-white min-h-full">
      <div className="flex mb-4">
        <div className="flex-1">
          <div className="text-gray-800 text-xl font-bold sm:text-2xl">Select Recipient</div>
          <div className="flex items-center space-x-2">
            <Loader2 className="animate-spin mr-2" />
            <p className="text-gray-600 mt-2">Loading Leads</p>
          </div>
        </div>
      </div>
      {/* <DataTable metaKeys={metaKeys} columns={columns} data={activeLeads} params={params} /> */}
      <div className="flex space-x-2 items-center">
        <div className="h-64 bg-gray-100 w-full rounded-lg"></div>
        <div className="h-64 bg-gray-100 w-1/4 rounded-lg"></div>
      </div>
    </div>
  );
};
