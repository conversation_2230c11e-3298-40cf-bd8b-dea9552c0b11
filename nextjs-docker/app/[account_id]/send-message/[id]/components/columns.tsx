'use client';

import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Checkbox } from '@/components/ui/checkbox';
import { HoverCard, HoverCardContent, HoverCardTrigger } from '@/components/ui/hover-card';
import { ILead } from '@/lib/types';
import { ColumnDef } from '@tanstack/react-table';
import { ArrowUpDown, Info } from 'lucide-react';

// This type is used to define the shape of our data.
// You can use a Zod schema here if you want.

export const columns: ColumnDef<ILead>[] = [
  {
    id: 'select',
    cell: ({ row, table }) => {
      if (!row.original.opt_out || !row.original.blocked) {
        return (
          <Checkbox
            type="button"
            checked={row.getIsSelected()}
            onCheckedChange={(value) => row.toggleSelected(!!value)}
            aria-label="Select row"
            disabled={table.getSelectedRowModel().rows.length >= 5 && !row.getIsSelected()}
          />
        );
      }
    },
    enableSorting: false,
    enableHiding: false,
  },
  {
    accessorKey: 'name',
    header: ({ column }) => {
      return (
        <Button variant="ghost" className="px-0 mb-0" onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}>
          Name
          <ArrowUpDown className="ml-2 h-4 w-4" />
        </Button>
      );
    },
  },
  {
    accessorKey: 'phone_number',
    header: ({ column }) => {
      return (
        <Button variant="ghost" className="px-0 mb-0" onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}>
          Phone Number
          <ArrowUpDown className="ml-2 h-4 w-4" />
        </Button>
      );
    },
  },
  {
    accessorKey: 'opt_out',
    id: 'Is Opted',
    minSize: 50,
    header: ({ column }) => {
      return (
        <Button variant="ghost" className="px-0 mb-0" onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}>
          Is Opted
          <ArrowUpDown className="ml-2 h-4 w-4" />
        </Button>
      );
    },
    cell: ({ row }) => {
      if (!row.original.opt_out) {
        return <>Opted In</>;
      } else {
        return (
          <HoverCard openDelay={0}>
            <HoverCardTrigger asChild>
              <div className="flex space-x-2 items-center hover:cursor-help">
                <Badge className="rounded-full" variant={'destructive'}>
                  Opted Out
                </Badge>
                <Info className="h-4 w-4 text-destructive" />
              </div>
            </HoverCardTrigger>
            <HoverCardContent className="text-xs">
              <div>User has clicked on "Stop Promotions" button.</div>
              <div>User does not want to receive promotion content</div>
            </HoverCardContent>
          </HoverCard>
        );
      }
    },
  },
  {
    accessorKey: 'blocked',
    id: 'Is Blocked',
    minSize: 50,
    header: ({ column }) => {
      return (
        <Button variant="ghost" className="px-0 mb-0">
          Is Blocked
        </Button>
      );
    },
    cell: ({ row }) => {
      if (!row.original.blocked) {
        return <>Not Blocked</>;
      } else {
        return (
          <HoverCard openDelay={0}>
            <HoverCardTrigger asChild>
              <div className="flex space-x-2 items-center hover:cursor-help">
                <Badge className="rounded-full" variant={'destructive'}>
                  Blocked
                </Badge>
                <Info className="h-4 w-4 text-destructive" />
              </div>
            </HoverCardTrigger>
            <HoverCardContent className="text-xs">
              <div>Lead is blocked.</div>
              <div>View contact to unblock or go to live chat to unblock</div>
            </HoverCardContent>
          </HoverCard>
        );
      }
    },
  },
];
