'use client';
import MessagePreview from '@/components/shared/MessagePreview';
import { IBody, IHeader } from '@/app/[account_id]/templates/manage/[template_operation]/types';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import MultipleSelector, { Option } from '@/components/ui/multiple-selector-template';
import { useToast } from '@/components/ui/use-toast';
import { send } from '@/lib/actions';
import { ILead, IMessagingLimit, ITemplateDatabase } from '@/lib/types';
import { ChevronRight, Loader2 } from 'lucide-react';
import Link from 'next/link';
import { useEffect, useState } from 'react';
import { useFormState, useFormStatus } from 'react-dom';
import { createComponents } from './helper';
import { Badge } from '@/components/ui/badge';
import { uploadPdfDocument } from '@/lib/pocket';
import DocumentGalleryDialog from './DocumentGalleryDialog';
import ImageGalleryDialog from './ImageGalleryDialog';
import VideoGalleryDialog from './VideoGalleryDialog';

type IVariableParamsProps = {
  template: ITemplateDatabase;
  optionArray: string[];
  selectedRows: ILead[];
  accountId: string;
  messagingLimit: IMessagingLimit | null;
};

type IFromLiveChat =
  | { loadMessagesOfConvo?: () => void; fromLiveChat?: false; closeAllDialogs?: never }
  | { loadMessagesOfConvo: () => void; fromLiveChat: true; closeAllDialogs: () => void };

type Props = IVariableParamsProps & IFromLiveChat;

const VariableParams = ({ loadMessagesOfConvo, template, optionArray, selectedRows, accountId, messagingLimit, fromLiveChat, closeAllDialogs }: Props) => {
  const {
    header: initHeader,
    body: initBody,
    footer,
    callToActionButtons,
    quickReplyButtons,
    headerVariablesArray,
    bodyVariablesArray,
  } = createComponents({ components: template.template_body.components, template });
  let limitLeft = (messagingLimit?.remaining_limit ?? 0) - selectedRows.length;

  const isDisabled = () => {
    if (!messagingLimit || messagingLimit?.remaining_limit < selectedRows.length) {
      return true;
    }
    return false;
  };
  const [header, _setHeader] = useState(initHeader);
  const [body, _setBody] = useState(initBody);
  const [loading, setLoading] = useState(false);
  const [galleryDialog, setGalleryDialog] = useState(false);

  function updateBodyText(value: string, index: number) {
    _setBody((prevBody: any) => ({
      ...prevBody,
      example: {
        ...prevBody.example,
        body_text: prevBody.example.body_text.map((item: any, i: number) => (i === index ? value : item)),
      },
    }));
  }

  const setBody = (body: Partial<IBody>) => {
    _setBody((_body) => ({ ..._body, ...body }));
  };

  const setHeader = (header: Partial<IHeader>) => {
    _setHeader((_header) => ({ ..._header, ...header }));
  };

  const handleBodyVariableChange = (val: string, index: number) => {
    updateBodyText(val, index);
    // bodyVariablesArray[index] = val;

    // setBody({ example: { body_text: bodyVariablesArray } });
  };

  const handleHeaderVariableChange = (val: string, index: number) => {
    headerVariablesArray[index] = val;
    setHeader({ example: { header_text: headerVariablesArray } });
  };

  const handleSetFile = (url: string) => {
    setHeader({ fileUrl: url });
  };

  const OPTIONS: Option[] = optionArray.map((item) => ({
    label: item,
    value: item,
  }));

  const { toast } = useToast();

  const initialState = {
    message: {
      status: 0,
      description: '',
    },
  };

  const handleSubmit = send.bind(null, selectedRows, template, accountId, header.fileUrl ?? '', header, body);

  const [state, formAction] = useFormState(handleSubmit, initialState);

  useEffect(() => {
    if (state?.message.status == 400) {
      toast({
        variant: 'destructive',
        description: state.message.description,
      });
    } else if (state?.message.status == 200) {
      toast({
        variant: 'success',
        description: state.message.description,
      });
      if (fromLiveChat) {
        loadMessagesOfConvo();
        closeAllDialogs();
      }
    }
  }, [state]);

  const handleDocumentChange = async (event: any) => {
    setLoading(true);
    const file = event.target.files[0];
    const formData = new FormData();
    formData.append('file', file);
    const _file = await uploadPdfDocument(accountId, formData);
    handleSetFile(_file.url);
    setGalleryDialog(false);
    setLoading(false);
    try {
    } catch (error) {
      console.log(error);
      alert('some error occured');
    }
  };

  return (
    <form action={formAction}>
      <div className="flex space-x-2 items-center">
        {!fromLiveChat && <div className="font-bold flex-1">Message Preview</div>}

        {!fromLiveChat && (
          <Link href={`/${accountId}/templates`} className="text-blue-300 underline">
            <Button className="mb-0" variant={'outline'}>
              Back
            </Button>
          </Link>
        )}
      </div>
      <div className="mb-4">
        <SendTemplateButton selectedRows={selectedRows} isDisabled={isDisabled} />
      </div>
      {header.format == 'IMAGE' && (
        <div className="mb-2">
          <ImageGalleryDialog handleSetImage={handleSetFile} accountId={accountId} />
        </div>
      )}
      {header.format == 'VIDEO' && (
        <div className="mb-2">
          <VideoGalleryDialog handleSetVideo={handleSetFile} accountId={accountId} />
        </div>
      )}
      {header.format == 'DOCUMENT' && (
        <div className="mb-2">
          <DocumentGalleryDialog
            accountId={accountId}
            handleSetDocument={handleSetFile}
            handleDocumentChange={handleDocumentChange}
            loading={loading}
            setGalleryDialog={setGalleryDialog}
            galleryDialog={galleryDialog}
          />
        </div>
      )}
      {selectedRows.length > 0 && (
        <div className="mb-2">
          <div className="mb-1 text-sm">Message quota after sending the template:</div>
          <div className="flex items-center space-x-2">
            <div className="font-bold text-lg">
              {messagingLimit?.remaining_limit ?? 0} - {selectedRows.length}
            </div>
            <ChevronRight className="mx-2" />
            <Badge
              variant={
                (messagingLimit?.remaining_limit ?? 0) - selectedRows.length == 0
                  ? 'pending'
                  : (messagingLimit?.remaining_limit ?? 0) - selectedRows.length < 0
                    ? 'destructive'
                    : 'success'
              }
            >
              {(messagingLimit?.remaining_limit ?? 0) - selectedRows.length}
            </Badge>
          </div>
          {limitLeft < 0 && <div className="text-red-500 font-bold">Message quota is exceeding the limit</div>}
          {limitLeft == 0 && <div className="text-orange-500 font-bold">Message quota will finish</div>}
        </div>
      )}
      <div>
        <MessagePreview body={body} footer={footer} callToActionButtons={callToActionButtons} header={header} quickReplyButtons={quickReplyButtons} />
      </div>
      {(header.example?.header_text || body.example) && (
        <div className="bg-white p-4 rounded-md shadow-lg">
          <>
            <div>Parameters</div>
            <div>You can personalize messages. Select value from dropdown or type your own custom attribute and then press enter.</div>
          </>
          <div className="gap-2">
            <div>
              {header.example && (
                <>
                  <div className="font-bold my-2">Header</div>
                  {header.example?.header_text?.map((headerVariables, index) => {
                    return (
                      <div className="flex space-x-2 items-center">
                        <div className="self-center">{index + 1}</div>
                        <div className="flex-1">
                          <div className="text-xs mb-1">Value</div>
                          <MultipleSelector
                            defaultOptions={OPTIONS}
                            placeholder="Type here"
                            hidePlaceholderWhenSelected
                            onChange={(option) => handleHeaderVariableChange(option[0]?.value, index)}
                            creatable
                            maxSelected={1}
                            className="bg-white"
                            emptyIndicator={<p className="text-center text-lg leading-10 text-gray-600 dark:text-gray-400">no results found.</p>}
                          />
                        </div>
                        <div className="flex-1">
                          <div className="text-xs mb-1">Default value</div>
                          {/* <Input type="text" name={`header-default-${index + 1}`} placeholder="Parameter Value" onChange={(e) => handleHeaderVariableChange(e, null, index)} /> */}
                          <Input type="text" name={`header-default-${index + 1}`} placeholder="Parameter Value" required maxLength={15} />
                        </div>
                      </div>
                    );
                  })}
                </>
              )}
            </div>
            <div>
              {body.example && (
                <div>
                  <div className="font-bold my-2">Body</div>
                  {body.example?.body_text.map((bodyVariables, index) => {
                    return (
                      <div key={index} className="flex space-x-2 items-center">
                        <div className="self-center">{index + 1}</div>
                        <div className="flex-1">
                          <div className="text-xs mb-1">Value</div>
                          <MultipleSelector
                            key={index}
                            defaultOptions={OPTIONS}
                            placeholder="Type here"
                            hidePlaceholderWhenSelected
                            onChange={(option) => handleBodyVariableChange(option[0]?.value, index)}
                            creatable
                            maxSelected={1}
                            className="bg-white"
                            emptyIndicator={<p className="text-center text-lg leading-10 text-gray-600 dark:text-gray-400">no results found.</p>}
                          />
                        </div>
                        <div className="flex-1">
                          <div className="text-xs mb-1">Default value</div>
                          {/* <Input type="text" name={`body-default-${index + 1}`} placeholder="Parameter Value" onChange={(e) => handleBodyVariableChange(e, null, index)} /> */}
                          <Input type="text" name={`body-default-${index + 1}`} placeholder="Parameter Value" required maxLength={15} />
                        </div>
                      </div>
                    );
                  })}
                </div>
              )}
            </div>
          </div>
        </div>
      )}
    </form>
  );
};

const SendTemplateButton = ({ selectedRows, isDisabled }: { selectedRows: any[]; isDisabled: () => boolean }) => {
  const { pending } = useFormStatus();
  return (
    <>
      {pending ? (
        <Button className="bg-gray-900" disabled>
          <Loader2 className="h-4 w-4 animate-spin mr-2" />
          Sending messages. Please wait
        </Button>
      ) : (
        <Button variant={'secondary'} type="submit" disabled={selectedRows.length == 0 || isDisabled()}>
          Send Template
        </Button>
      )}
    </>
  );
};

export default VariableParams;
