'use client';

import { Document } from '@/components/Document';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from '@/components/ui/alert-dialog';
import { Button } from '@/components/ui/button';
import { ScrollArea } from '@/components/ui/scroll-area';
import { getFilesForDialogs } from '@/lib/pocket';
import { IImage } from '@/lib/types';
import clsx from 'clsx';
import { Check, Loader2, Upload } from 'lucide-react';
import { Dispatch, SetStateAction, useState } from 'react';

const DocumentGalleryDialog = ({
  accountId,
  handleSetDocument,
  handleDocumentChange,
  loading,
  galleryDialog,
  setGalleryDialog,
}: {
  accountId: string;
  handleSetDocument: any;
  handleDocumentChange: any;
  loading: boolean;
  galleryDialog: boolean;
  setGalleryDialog: Dispatch<SetStateAction<boolean>>;
}) => {
  const [selectedDocument, setSelectedDocument] = useState<IImage>();
  const [documents, setDocuments] = useState<IImage[]>([]);
  const [documentLoading, setDocumentLoading] = useState<boolean>(false);
  return (
    <AlertDialog open={galleryDialog} onOpenChange={setGalleryDialog}>
      <AlertDialogTrigger asChild>
        <Button
          variant={'outline'}
          onClick={async () => {
            setDocumentLoading(true);
            const docs = await getFilesForDialogs(accountId, 'documents');
            setDocuments(docs.files);
            setDocumentLoading(false);
          }}
        >
          Select Document
          <Upload className="ml-2 h-5 w-5" />
        </Button>
      </AlertDialogTrigger>
      <AlertDialogContent className="max-w-full w-9/12 h-[90vh] flex flex-col">
        <AlertDialogHeader>
          <AlertDialogTitle>Select Document</AlertDialogTitle>
        </AlertDialogHeader>
        <ScrollArea className="flex-grow">
          {documentLoading ? (
            <div className="flex items-center justify-center">
              <Loader2 className="animate-spin h-10 w-10" />
            </div>
          ) : (
            <div className="grid grid-cols-5 gap-4 p-4">
              {documents.length > 0 &&
                documents.map((document) => (
                  <div
                    key={document.id}
                    className={clsx(
                      'p-2 border rounded-md relative hover:cursor-pointer aspect-document w-full  hover:border-gray-400',
                      selectedDocument?.url == document.url ? 'border-blue-500' : 'border-gray-200 '
                    )}
                    onClick={() => setSelectedDocument(document)}
                  >
                    <div
                      className={clsx(
                        'absolute z-20 h-5 w-5 border flex items-center justify-center rounded-md bg-white',
                        selectedDocument?.url == document.url ? 'border-gray-100 text-blue-600' : 'border-gray-200 text-black'
                      )}
                    >
                      {selectedDocument?.url == document.url && <Check />}
                    </div>
                    <Document fileName={document.file as string} url={document.url} className="rounded-md object-contain w-full" />
                  </div>
                ))}
            </div>
          )}
        </ScrollArea>
        <AlertDialogFooter className="">
          <div className="mr-auto">
            <div className="mb-2">
              <div className="flex items-center">
                <input id="file-upload" type="file" name="document" className="hidden" accept="application/pdf" onChange={handleDocumentChange} />

                <label
                  htmlFor="file-upload"
                  className="px-4 border-gray-300 border flex cursor-pointer items-center text-black bg-white hover:bg-gray-100 font-medium text-sm py-2 rounded"
                >
                  Select File <Upload className="ml-2 h-5 w-5" />
                </label>
                {loading && <Loader2 className="ml-2 animate-spin" />}
              </div>
            </div>
          </div>
          <AlertDialogCancel>Cancel</AlertDialogCancel>
          <AlertDialogAction disabled={!selectedDocument} onClick={() => handleSetDocument(selectedDocument?.url ?? '')}>
            Continue
          </AlertDialogAction>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  );
};

export default DocumentGalleryDialog;
