'use client';

import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from '@/components/ui/alert-dialog';
import { Button } from '@/components/ui/button';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Video } from '@/components/Video';
import { getFilesForDialogs } from '@/lib/pocket';
import { IImage, IIVideoWithUrl } from '@/lib/types';
import clsx from 'clsx';
import { Check, Loader2, Upload } from 'lucide-react';
import { useState } from 'react';

const VideoGalleryDialog = ({ accountId, handleSetVideo }: { accountId: string; handleSetVideo: any }) => {
  const [selectedVideo, setSelectedVideo] = useState<IIVideoWithUrl>();
  const [videos, setVideos] = useState<IImage[]>([]);
  const [videoLoading, setVideoLoading] = useState<boolean>(false);
  return (
    <AlertDialog>
      <AlertDialogTrigger asChild>
        <Button
          variant={'outline'}
          onClick={async () => {
            setVideoLoading(true);
            const vids = await getFilesForDialogs(accountId, 'videos');
            setVideos(vids.files);
            setVideoLoading(false);
          }}
        >
          Select Video
          <Upload className="ml-2 h-5 w-5" />
        </Button>
      </AlertDialogTrigger>
      <AlertDialogContent className="max-w-full w-9/12 h-[90vh] flex flex-col">
        <AlertDialogHeader>
          <AlertDialogTitle>Select Video</AlertDialogTitle>
        </AlertDialogHeader>
        <ScrollArea className="flex-grow">
          {videoLoading ? (
            <div className="flex items-center justify-center">
              <Loader2 className="animate-spin h-10 w-10" />
            </div>
          ) : (
            <div className="grid grid-cols-5 gap-4 p-4">
              {videos.length > 0 &&
                videos.map((video) => (
                  <div
                    key={video.id}
                    className={clsx(
                      'p-2 border rounded-md relative hover:cursor-pointer aspect-video w-full  hover:border-gray-400',
                      selectedVideo?.url == video.url ? 'border-blue-500' : 'border-gray-200 '
                    )}
                    onClick={() => setSelectedVideo(video)}
                  >
                    <div
                      className={clsx(
                        'absolute z-20 h-5 w-5 border flex items-center justify-center rounded-md bg-white',
                        selectedVideo?.url == video.url ? 'border-gray-100 text-blue-600' : 'border-gray-200 text-black'
                      )}
                    >
                      {selectedVideo?.url == video.url && <Check />}
                    </div>
                    <Video file={null} src={video.url} className="rounded-md object-contain" />
                  </div>
                ))}
            </div>
          )}
        </ScrollArea>
        <AlertDialogFooter>
          <AlertDialogCancel>Cancel</AlertDialogCancel>
          <AlertDialogAction disabled={!selectedVideo} onClick={() => handleSetVideo(selectedVideo?.url ?? '')}>
            Continue
          </AlertDialogAction>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  );
};

export default VideoGalleryDialog;
