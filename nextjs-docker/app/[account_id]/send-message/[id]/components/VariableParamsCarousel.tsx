'use client';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import MultipleSelector, { Option } from '@/components/ui/multiple-selector-template';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { useToast } from '@/components/ui/use-toast';
import { sendCarousel } from '@/lib/actions';
import { ILead, IMessagingLimit, ITemplateDatabase } from '@/lib/types';
import { isTemplateSubmitDisabled } from '@/lib/utils';
import { ChevronRight, Loader2 } from 'lucide-react';
import Link from 'next/link';
import { useEffect, useState } from 'react';
import { useFormState, useFormStatus } from 'react-dom';
import { createComponentsCarousel } from './helper';
import { whatsApptoHtml } from '@/app/[account_id]/templates/manage/[template_operation]/components/utils';
import MessagePreviewCarousel from '@/app/[account_id]/templates/manage/[template_operation]/components/MessagePreviewCarousel';

type IVariableParamsProps = {
  template: ITemplateDatabase;
  optionArray: string[];
  selectedRows: ILead[];
  accountId: string;
  messagingLimit: IMessagingLimit | null;
  fromLiveChat?: boolean;
};

type IFromLiveChat =
  | { loadMessagesOfConvo?: () => void; fromLiveChat?: false; closeAllDialogs?: never }
  | { loadMessagesOfConvo: () => void; fromLiveChat: true; closeAllDialogs: () => void };

type Props = IVariableParamsProps & IFromLiveChat;

const VariableParamsCarousel = ({
  loadMessagesOfConvo,
  template,
  optionArray,
  selectedRows,
  accountId,
  messagingLimit,
  fromLiveChat,
  closeAllDialogs,
}: Props) => {
  const componentsArray = template.template_body.components[1]?.cards || [];
  let limitLeft = (messagingLimit?.remaining_limit ?? 0) - selectedRows.length;
  const cards = componentsArray.map((eachCard) => createComponentsCarousel(eachCard));
  const [cardsState, setCardsState] = useState(cards);
  const [selectedCardIndex, setSelectedCardIndex] = useState(0);
  const [carousel_params, setCarousel_params] = useState<any>();
  const [messageBodyError, setMessageBodyError] = useState('');
  const [messageBody, setMessageBody] = useState<any>();

  // const submitDisabled = isTemplateSubmitDisabled(template, cardsState, messageBody, carousel_params);
  const submitDisabled = false;

  const isDisabled = () => {
    if (!messagingLimit || messagingLimit?.remaining_limit < selectedRows.length) {
      return true;
    }
    return false;
  };

  const hasVariables = !!template.template_body.components[0].example || cardsState.some((card: any) => !!card?.body?.example);

  const setCardState = (index: number, key: keyof (typeof cardsState)[number], value: any) => {
    setCardsState((prev) => prev.map((card, i: number) => (i === index ? { ...card, [key]: value } : card)));
  };

  const replaceVariablesToText = () => {
    let str = template.template_body.components[0].text;
    let regexPattern = /\{\{\d+\}\}/g;
    let matches = [];
    let values = undefined;
    try {
      matches = [...str.matchAll(regexPattern)];
      values = messageBody?.body ?? str;
      // Iterate through all matches
      for (let i = 0; i < matches.length; i++) {
        // Replace the match with the corresponding value
        if (values?.[i]) {
          str = str.replace(matches[i][0], values[i]);
        }
      }

      return whatsApptoHtml(str);
    } catch (error) {
      console.log(error);
      return str;
    }
  };
  const handleBodyVariableChange = (val: string, varIndex: number) => {
    const bodyVariablesArray = [...(cardsState[selectedCardIndex]?.components?.[1]?.example?.body_text || [])];
    bodyVariablesArray[varIndex] = val;
    handleCarouselParamsChange(val, varIndex, 'body');
  };

  const handleMessageBodyVariableChange = (val: string, varIndex: number, type: string) => {
    setMessageBody((prevParams: any) => {
      let updatedParams: any = {
        defaultBody: [...(prevParams?.defaultBody || [])],
        body: [...(prevParams?.body || [])],
      };
      if (type == 'defaultBody') {
        updatedParams.defaultBody[varIndex] = val;
      } else if (type == 'body') {
        updatedParams.body[varIndex] = val;
      }
      return updatedParams;
    });
  };

  const handleCarouselParamsChange = (value: string, index: number, type: string) => {
    setCarousel_params((prevParams: any) => {
      const updatedParams = [...(prevParams || [])];

      if (!updatedParams[selectedCardIndex]) {
        updatedParams[selectedCardIndex] = { body: [], defaultBody: [], cardIndex: selectedCardIndex };
      }

      const currentCard = updatedParams[selectedCardIndex];

      currentCard.body = currentCard.body || [];
      currentCard.defaultBody = currentCard.defaultBody || [];
      currentCard.cardIndex = selectedCardIndex;

      if (type == 'defaultBody') {
        currentCard.defaultBody[index] = value;
      } else if (type == 'body') {
        currentCard.body[index] = value;
      }
      return updatedParams;
    });
  };

  const OPTIONS: Option[] = optionArray.map((item) => ({
    label: item,
    value: item,
  }));

  const { toast } = useToast();

  const messageInitialState = {
    message: {
      status: 0,
      description: '',
    },
  };

  const handleSubmit = () => sendCarousel(selectedRows, template, accountId, messageBody, carousel_params);

  const [state, formAction] = useFormState(handleSubmit, messageInitialState);

  useEffect(() => {
    if (state?.message.status === 400) {
      toast({
        variant: 'destructive',
        description: state.message.description,
      });
    } else if (state?.message.status === 200) {
      toast({
        variant: 'success',
        description: state.message.description,
      });
      if (fromLiveChat) {
        loadMessagesOfConvo();
        closeAllDialogs();
      }
    }
  }, [state]);

  return (
    <form action={formAction} className="lg:w-80 w-52">
      {/* <form> */}
      <div className="flex space-x-2 items-center mb-4">
        {!fromLiveChat && <div className="font-bold flex-1">Message Preview</div>}

        {!fromLiveChat && (
          <Link href={`/${accountId}/templates`} className="text-blue-300 underline">
            <Button className="mb-0" variant={'outline'}>
              Back
            </Button>
          </Link>
        )}
      </div>
      <div className="mb-4">
        <SendTemplateButton selectedRows={selectedRows} isDisabled={isDisabled} submitDisabled={submitDisabled} />
      </div>

      {selectedRows.length > 0 && (
        <div className="mb-2">
          <div className="mb-1 text-sm">Message quota after sending the template:</div>
          <div className="flex items-center space-x-2">
            <div className="font-bold text-lg">
              {messagingLimit?.remaining_limit ?? 0} - {selectedRows.length}
            </div>
            <ChevronRight className="mx-2" />
            <Badge
              variant={
                (messagingLimit?.remaining_limit ?? 0) - selectedRows.length === 0
                  ? 'pending'
                  : (messagingLimit?.remaining_limit ?? 0) - selectedRows.length < 0
                    ? 'destructive'
                    : 'success'
              }
            >
              {(messagingLimit?.remaining_limit ?? 0) - selectedRows.length}
            </Badge>
          </div>
          {limitLeft < 0 && <div className="text-red-500 font-bold">Message quota is exceeding the limit</div>}
          {limitLeft == 0 && <div className="text-orange-500 font-bold">Message quota will finish</div>}
        </div>
      )}

      {/* Message Preview Carousel */}
      <div>
        <MessagePreviewCarousel body={template.template_body.components[0]} cards={cards} />
      </div>

      {/* variable box starts */}
      {hasVariables && (
        <div className="bg-white p-4 rounded-md shadow-lg">
          <div>Parameters</div>
          <div>You can personalize messages. Select value from dropdown or type your own custom attribute and then press enter.</div>

          {/* Message Body */}
          {template.template_body.components[0].example && (
            <div>
              <div className="font-bold my-2">Message Body</div>
              {template.template_body.components[0].example?.body_text?.map((bodyVariables: any, index: any) => {
                return (
                  <div key={index} className="flex space-x-2 items-center">
                    <div className="self-center">{index + 1}</div>
                    <div className="flex-1">
                      <div className="text-xs mb-1">Value (required)</div>
                      <MultipleSelector
                        defaultOptions={OPTIONS}
                        placeholder="Type here"
                        hidePlaceholderWhenSelected
                        onChange={(option) => handleMessageBodyVariableChange(option[0]?.value, index, 'body')}
                        creatable
                        maxSelected={1}
                        className="bg-white"
                        emptyIndicator={<p className="text-center text-lg leading-10 text-gray-600 dark:text-gray-400">no results found.</p>}
                      />
                    </div>
                    <div className="flex-1">
                      <div className="text-xs mb-1">Default value (required)</div>
                      {/* <Input type="text" name={`body-default-${index + 1}`} placeholder="Parameter Value" onChange={(e) => handleBodyVariableChange(e, null, index)} /> */}
                      <Input
                        type="text"
                        onChange={(e) => {
                          handleMessageBodyVariableChange(e.target.value, index, 'defaultBody');
                        }}
                        name={`body-default-${index + 1}`}
                        placeholder="Parameter Value"
                        required
                        maxLength={15}
                      />
                    </div>
                  </div>
                );
              })}
            </div>
          )}

          {/* Card Selection Dropdown */}
          {cardsState.some((card: any) => !!card?.body?.example) && (
            <div className="mt-8 mb-4">
              <div className="text-sm mb-2">Select card number to add parameters to</div>
              <Select value={selectedCardIndex.toString()} onValueChange={(value) => setSelectedCardIndex(Number(value))}>
                <SelectTrigger className="w-32 border-gray-400 border-2 ml-4">
                  <SelectValue placeholder="Select Card" />
                </SelectTrigger>
                <SelectContent>
                  {cardsState.map((_: any, index: number) => (
                    <SelectItem key={index} value={index.toString()}>
                      Card {index + 1}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          )}

          {/* Card Variables (Header & Body) */}
          {cardsState[selectedCardIndex].components[1]?.example && (
            <div>
              {cardsState[selectedCardIndex]?.components[1]?.example && <div className="font-bold mb-2">Body</div>}

              {cardsState[selectedCardIndex]?.components[1]?.example?.body_text.map((bodyVariables: any, index: number) => {
                return (
                  <div key={index} className="flex space-x-2 items-center">
                    <div className="self-center">{index + 1}</div>
                    <div className="flex-1">
                      <div className="text-xs mb-1">Value (required)</div>
                      <MultipleSelector
                        value={
                          carousel_params?.[selectedCardIndex]?.body[index]
                            ? [
                                {
                                  label: carousel_params?.[selectedCardIndex]?.body[index]?.trim(),
                                  value: carousel_params?.[selectedCardIndex]?.body[index]?.trim(),
                                },
                              ]
                            : []
                        }
                        defaultOptions={OPTIONS}
                        placeholder="Type here"
                        hidePlaceholderWhenSelected
                        onChange={(option) => {
                          handleBodyVariableChange(option[0]?.value, index);
                        }}
                        creatable
                        maxSelected={1}
                        className="bg-white"
                        emptyIndicator={<p className="text-center text-lg leading-10 text-gray-600 dark:text-gray-400">no results found.</p>}
                      />
                    </div>
                    <div className="flex-1">
                      <div className="text-xs mb-1">Default value (required)</div>
                      {/* <Input type="text" name={`body-default-${index + 1}`} placeholder="Parameter Value" onChange={(e) => handleBodyVariableChange(e, null, index)} /> */}
                      <Input
                        value={carousel_params?.[selectedCardIndex]?.defaultBody?.[index] ?? ''}
                        onChange={(e) => {
                          handleCarouselParamsChange(e.target.value, index, 'defaultBody');
                        }}
                        type="text"
                        name={`body-default-${index + 1}`}
                        placeholder="Parameter Value"
                        required
                        maxLength={15}
                      />
                    </div>
                  </div>
                );
              })}
            </div>
          )}
        </div>
      )}
      {/* variable box ends */}
    </form>
  );
};

const SendTemplateButton = ({ selectedRows, isDisabled, submitDisabled }: { selectedRows: any[]; isDisabled: () => boolean; submitDisabled: boolean }) => {
  const { pending } = useFormStatus();
  return (
    <>
      {pending ? (
        <Button className="bg-gray-900" disabled>
          <Loader2 className="h-4 w-4 animate-spin mr-2" />
          Sending messages. Please wait
        </Button>
      ) : (
        <Button variant={'secondary'} type="submit" disabled={selectedRows.length === 0 || isDisabled() || submitDisabled}>
          Send Template
        </Button>
      )}
    </>
  );
};

export default VariableParamsCarousel;
