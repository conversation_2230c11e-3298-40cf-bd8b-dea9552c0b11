'use client';

import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from '@/components/ui/alert-dialog';
import { Button, buttonVariants } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { ScrollArea } from '@/components/ui/scroll-area';
import { getFilesForDialogs } from '@/lib/pocket';
import { IImage, IImageWithUrl } from '@/lib/types';
import clsx from 'clsx';
import dayjs from 'dayjs';
import { Check, Loader2, Upload, UploadIcon } from 'lucide-react';
import Image from 'next/image';
import { useState } from 'react';
import { useFormStatus } from 'react-dom';

const ImageGalleryDialog = ({ accountId, handleSetImage }: { accountId: string; handleSetImage: any }) => {
  const [selectedImage, setSelectedImage] = useState<IImageWithUrl>();
  const [images, setImages] = useState<IImage[]>([]);
  const [imageLoading, setImageLoading] = useState<boolean>(false);

  return (
    <AlertDialog>
      <AlertDialogTrigger asChild>
        <Button
          variant={'outline'}
          onClick={async () => {
            setImageLoading(true);
            const imgs = await getFilesForDialogs(accountId, 'images');
            setImages(imgs.files);
            setImageLoading(false);
          }}
        >
          Select Image
          <Upload className="ml-2 h-5 w-5" />
        </Button>
      </AlertDialogTrigger>
      <AlertDialogContent className="max-w-full w-9/12 h-[90vh] flex flex-col">
        <AlertDialogHeader>
          <AlertDialogTitle>Select Image</AlertDialogTitle>
          <AlertDialogDescription></AlertDialogDescription>
        </AlertDialogHeader>
        <ScrollArea className="flex-grow">
          <div className="grid grid-cols-5 gap-4 p-4">
            {images.map((image, index) => (
              <div
                className={clsx('border rounded-md hover:border-gray-400', selectedImage?.url == image.url ? 'border-blue-500' : 'border-gray-200')}
                key={image.id}
                onClick={() => setSelectedImage(image)}
              >
                <div className="p-2  relative hover:cursor-pointer h-32">
                  <div
                    className={clsx(
                      'absolute z-20 h-5 w-5 border flex items-center justify-center rounded-md bg-white',
                      selectedImage?.url == image.url ? 'border-gray-100 text-blue-600' : 'border-gray-200 text-black'
                    )}
                  >
                    {selectedImage?.url == image.url && <Check />}
                  </div>
                  <Image src={image.url} fill alt="image" className="rounded-md object-contain" sizes="(max-width: 600px) 100vw, 50vw" />
                </div>
                <div className="text-xs text-gray-500 border-t p-1">
                  <div className="mb-1">Created by: {image.expand?.created_by?.name ?? 'System'}</div>
                  <div>{dayjs(image.created).format('MMMM D, YYYY h:mm A')}</div>
                </div>
              </div>
            ))}
          </div>
        </ScrollArea>
        <AlertDialogFooter>
          <AlertDialogCancel>Cancel</AlertDialogCancel>
          <AlertDialogAction
            disabled={!selectedImage}
            onClick={() => handleSetImage(selectedImage?.url ?? '')}
            className={buttonVariants({ variant: 'secondary' })}
          >
            Continue
          </AlertDialogAction>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  );
};

const UploadImageBtn = ({ disabled }: { disabled?: boolean }) => {
  const { pending } = useFormStatus();
  return (
    <>
      {pending ? (
        <Button className="bg-gradient-to-r from-purple-500 to-pink-500 hover:bg-gradient-to-l" disabled>
          <Loader2 className="h-4 w-4 animate-spin mr-2" />
          Uploading
        </Button>
      ) : (
        <Button className="bg-gradient-to-r from-emerald-500 to-emerald-600 hover:bg-gradient-to-l transition-all" type="submit" disabled={disabled}>
          Upload
          <UploadIcon className="ml-2 h-4 w-4" />
        </Button>
      )}
    </>
  );
};
export default ImageGalleryDialog;
