'use client';

import { ILead, IMessagingLimit, ITemplateDatabase } from '@/lib/types';
import { columns } from './columns';
import { DataTable } from './data-table';

const SendTemplatePage = ({
  template,
  leads,
  messagingLimit,
  params,
}: {
  template: ITemplateDatabase;
  leads: ILead[];
  messagingLimit: IMessagingLimit | null;
  params: Record<string, string>;
}) => {
  return (
    <div>
      <DataTable
        columns={columns}
        data={leads}
        params={{
          account_id: params.account_id,
          id: params.id,
        }}
        template={template}
        messagingLimit={messagingLimit}
      />
    </div>
  );
};

export default SendTemplatePage;
