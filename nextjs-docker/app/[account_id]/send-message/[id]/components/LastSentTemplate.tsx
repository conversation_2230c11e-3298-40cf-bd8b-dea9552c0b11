'use client';
import { HoverCard, HoverCardContent, HoverCardTrigger } from '@/components/ui/hover-card';
import { IExpandedMessage } from '@/lib/types';
import dayjs from 'dayjs';

const LastSentTemplate = async ({ recentMessagesByTemplate }: { recentMessagesByTemplate: IExpandedMessage[] }) => {
  return (
    <div>
      <div className="font-bold mb-2">Recent Recipient:</div>
      <div className="flex flex-col space-y-2">
        {recentMessagesByTemplate?.map((message) => (
          <div className=" bg-gray-100 rounded-lg p-4">
            <div className="flex space-x-2 items-center">
              <div className="font-bold">Name:</div>
              <div>{message.expand?.user?.name}</div>
            </div>
            <div className="flex space-x-2 items-center">
              <div className="font-bold">Delivery Status:</div>
              {message.delivery_status == 'failed' ? (
                <HoverCard openDelay={0}>
                  <HoverCardTrigger>
                    <div className="text-red-500 font-bold underline decoration-dotted ">{message.delivery_status}</div>
                  </HoverCardTrigger>
                  <HoverCardContent className="space-y-1">
                    <div className="flex space-x-1 text-xs">
                      <div className="font-bold w-14">Code:</div>
                      <div className="flex-1">{message.error_code}</div>
                    </div>
                    <div className="flex space-x-1 text-xs">
                      <div className="font-bold w-14">Message:</div>
                      <div className="flex-1">{message.error}</div>
                    </div>
                    <div className="flex space-x-1 text-xs">
                      <div className="font-bold w-14">Details:</div>
                      <div className="flex-1">{message.error_data}</div>
                    </div>
                  </HoverCardContent>
                </HoverCard>
              ) : (
                <div>{message.delivery_status}</div>
              )}
            </div>
            <div className="flex space-x-2 items-center">
              <div className="font-bold">Sent at:</div>
              <div>{dayjs(message.updated).format('DD/MM/YYYY')}</div>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

export default LastSentTemplate;
