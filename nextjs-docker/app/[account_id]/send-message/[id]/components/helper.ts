import {
  IBody,
  ICallToActionBtn,
  ICardComponent,
  ICardState,
  ICarouselHeader,
  IFooter,
  IHeader,
  IQuickReplyBtn,
} from '@/app/[account_id]/templates/manage/[template_operation]/types';
import { ITemplateDatabase } from '@/lib/types';

export const createComponents = ({ components, template }: { components: any; template: ITemplateDatabase }) => {
  const updated_template_image_url = template.updated_template?.HEADER;
  function isArrayOrArrayOfArrays(value: any): boolean {
    if (Array.isArray(value)) {
      return value.length === 0 || Array.isArray(value[0]);
    }
    return false;
  }

  let header: IHeader = {
    type: 'HEADER',
    format: 'TEXT',
    file: null,
    fileUrl: '',
    text: '',
  };

  // Body configuration
  let body: IBody = {
    type: 'BODY',
    format: 'TEXT',
    text: '',
  };

  // Footer configuration
  let footer: IFooter = {
    type: 'FOOTER',
    text: '',
    disabled: false,
  };

  let headerVariablesArray: string[] = [];
  let bodyVariablesArray: string[] = [];

  // Quick reply buttons configuration
  let quickReplyButtons: IQuickReplyBtn[] = [];

  // Call-to-action buttons configuration
  let callToActionButtons: ICallToActionBtn[] = [];

  components.forEach((component: any) => {
    if (component?.type === 'HEADER') {
      if (component?.format == 'IMAGE') {
        header = {
          ...header,
          text: component.text,
          format: 'IMAGE',
          fileUrl: updated_template_image_url ?? component?.fileUrl ?? '',
        };
      } else if (component?.format == 'VIDEO') {
        header = {
          ...header,
          text: component.text,
          format: 'VIDEO',
          fileUrl: updated_template_image_url ?? component?.fileUrl ?? '',
        };
      } else if (component?.format == 'DOCUMENT') {
        header = {
          ...header,
          text: component.text,
          format: 'DOCUMENT',
          fileUrl: updated_template_image_url ?? component?.fileUrl ?? '',
        };
      } else {
        if (component.example?.header_text) {
          header = {
            ...header,
            text: component.text,
            example: { header_text: ['{{1}}'] },
          };
        } else {
          header = {
            ...header,
            text: component.text,
          };
        }
      }
    }
    if (component.type === 'BODY') {
      if (component.example?.body_text) {
        if (isArrayOrArrayOfArrays(component.example?.body_text)) {
          bodyVariablesArray = component.example?.body_text[0];
        } else {
          bodyVariablesArray = component.example?.body_text;
        }
        bodyVariablesArray = bodyVariablesArray?.map((variable, index) => `{{${index + 1}}}`);
        body = {
          ...body,
          text: component.text,
          example: { body_text: bodyVariablesArray },
        };
      } else {
        body = { ...body, text: component.text };
      }
    }
    if (component.type === 'FOOTER') {
      footer = { ...footer, text: component.text };
    }
    if (component.type === 'BUTTONS') {
      component?.buttons?.map((btn: any) => {
        const itemToAdd = {
          type: btn.type,
          text: btn.text,
          ...(btn.url && { url: btn.url }),
          ...(btn.phone_number && { phone_number: btn.phone_number }),
        };
        if (btn.type == 'PHONE_NUMBER' || btn.type == 'URL') {
          callToActionButtons = [...callToActionButtons, itemToAdd];
        } else {
          quickReplyButtons = [...quickReplyButtons, itemToAdd];
        }
      });
    }
  });

  return { header, body, footer, callToActionButtons, quickReplyButtons, headerVariablesArray, bodyVariablesArray };
};

export const createComponentsCarousel = (eachCard: ICardState) => {
  let CAROUSEL_HEADER: ICarouselHeader = {
    type: 'HEADER',
    format: 'IMAGE',
    file: null,
    fileUrl: '',
  };

  let CAROUSEL_BODY: IBody = {
    type: 'BODY',
    format: 'TEXT',
    text: 'Hello',
  };
  let QUICK_REPLY_BUTTONS: IQuickReplyBtn[] = [];
  let CALL_TO_ACTION_BUTTONS: ICallToActionBtn[] = [];

  CAROUSEL_HEADER = eachCard.components[0];
  CAROUSEL_BODY = eachCard.components[1];
  const buttons = eachCard.components[2].buttons;

  // buttons.
  buttons.map((btn: any) => {
    const itemToAdd = {
      type: btn.type,
      text: btn.text,
      ...(btn?.url && { url: btn.url }),
      ...(btn.phone_number && { phone_number: btn.phone_number }),
    };
    if (btn.type == 'PHONE_NUMBER' || btn.type == 'URL') {
      CALL_TO_ACTION_BUTTONS = [...CALL_TO_ACTION_BUTTONS, itemToAdd];
    } else {
      QUICK_REPLY_BUTTONS = [...QUICK_REPLY_BUTTONS, itemToAdd];
    }
  });

  const EACH_CAROUSEL_CARD: ICardComponent = {
    components: [
      CAROUSEL_HEADER,
      CAROUSEL_BODY,
      {
        type: 'buttons',
        buttons: {
          quickReplyButtons: QUICK_REPLY_BUTTONS,
          callToActionButtons: CALL_TO_ACTION_BUTTONS,
        },
      },
    ],
  };

  return EACH_CAROUSEL_CARD;
};
