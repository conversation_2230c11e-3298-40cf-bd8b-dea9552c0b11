import { getAllLists, getAllTags, getCampaignsByLists, getLeadDataById } from '@/lib/pocket';
import { EditContact } from './components/EditContact';
import countrycodes from '@/lib/countrylistfiltered.json';
import { Toaster } from '@/components/ui/toaster';
import phone from 'phone';

export default async ({ params, searchParams }: { params: Record<string, string>; searchParams: Record<string, string> }) => {
  function removeCode(phoneNumber: string, code: string) {
    const modifiedCode = code.replace(/^\+/, '');
    const codeLength = modifiedCode.length;
    const result = phoneNumber.slice(codeLength);
    return result;
  }

  try {
    const lead = await getLeadDataById(searchParams.id);
    const campaigns_array = await getCampaignsByLists(lead.expand?.campaign_history, lead.id);
    const lists = lead.expand?.list ?? [];
    const allList = await getAllLists(params.account_id);
    let phoneResult = phone(`+${lead.phone_number}`);
    let { countryIso2, countryIso3 } = phoneResult;
    const countryData = countrycodes.find((item) => {
      return item.name.toUpperCase() == lead.country.toUpperCase() || item.iso['alpha-2'] == countryIso2 || item.iso['alpha-3'] == countryIso3;
    });

    const numberCode = {
      country: countryData?.name ?? '',
      code: countryData?.phone[0] ?? '',
      emoji: countryData?.emoji ?? '',
      phoneLength: countryData?.phoneLength ?? 0,
      iso: countryData?.iso['alpha-2'] ?? '',
    };

    const numberInput = countryData ? removeCode(lead.phone_number, countryData.phone[0]) : '';
    const tagsOptions = await getAllTags(params.account_id);
    return (
      <>
        <EditContact
          accountId={params.account_id}
          tagsOptions={tagsOptions}
          lead={lead}
          campaignsArray={campaigns_array}
          lists={lists}
          numberCodeParam={numberCode}
          numberInputParam={numberInput}
          allList={allList}
        />
        <Toaster />
      </>
    );
  } catch (error) {
    console.log(error);
    return <div>Lead not found</div>;
  }
};
