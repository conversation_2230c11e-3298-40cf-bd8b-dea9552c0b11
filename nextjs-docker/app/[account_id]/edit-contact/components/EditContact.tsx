/**
 * This code was generated by v0 by Vercel.
 * @see https://v0.dev/t/Cs4RlbD5ER0
 * Documentation: https://v0.dev/docs#integrating-generated-code-into-your-nextjs-app
 */

'use client';

import { Alert, AlertDescription } from '@/components/ui/alert';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from '@/components/ui/alert-dialog';
import { Badge } from '@/components/ui/badge';
import { Breadcrumb, BreadcrumbItem, BreadcrumbLink, BreadcrumbList, BreadcrumbSeparator } from '@/components/ui/breadcrumb';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Checkbox } from '@/components/ui/checkbox';
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from '@/components/ui/collapsible';
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from '@/components/ui/dropdown-menu';
import { Input } from '@/components/ui/input';
import { InputWithPhoneCode2 } from '@/components/ui/inputWithPhoneCode2';
import { Label } from '@/components/ui/label';
import MultipleSelector, { Option } from '@/components/ui/multiple-selector';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { useToast } from '@/components/ui/use-toast';
import { setLeadAsOptIn, updateRecordByIdEditContact } from '@/lib/pocket';
import { ILead, IList } from '@/lib/types';
import clsx from 'clsx';
import dayjs from 'dayjs';
import { AlertCircle, ChevronDown, ChevronDownIcon, ExternalLink, Loader2 } from 'lucide-react';
import Link from 'next/link';
import { useRouter, useSearchParams } from 'next/navigation';
import { useRef, useState } from 'react';

interface CampaignArray {
  name: string;
  id: string;
  date_and_time: Date;
  leadReply: any;
}

[];

export function EditContact({
  accountId,
  tagsOptions,
  lead,
  campaignsArray,
  lists,
  numberCodeParam,
  numberInputParam,
  allList,
}: {
  accountId: string;
  tagsOptions: Option[];
  lead: ILead;
  campaignsArray: CampaignArray[] | undefined;
  lists: IList[];
  allList: IList[];
  numberCodeParam: { country: string; code: string; emoji: string; phoneLength: number; iso: string };
  numberInputParam: string;
}) {
  const searchParams = useSearchParams();
  const id = searchParams.get('id') || '';
  const router = useRouter();
  const [newListids, setNewListids] = useState<Option[]>([]);
  const [isSaving, setIsSaving] = useState(false);
  const [_lists, setLists] = useState(lists.map((list) => ({ ...list, selected: false })));
  const [isOptSaving, setIsOptSaving] = useState(false);
  const [numberInput, setNumberInput] = useState<string>(numberInputParam);
  const [tags, setTags] = useState<Option[]>(
    lead.tags.length > 0
      ? lead.tags.map((tag) => ({
          label: tag,
          value: tag,
        }))
      : []
  );
  const [error, setError] = useState<{ val: boolean; message: string }>({
    val: false,
    message: '',
  });

  const [numberCode, setNumberCode] = useState(numberCodeParam);
  const { toast } = useToast();
  const formRef = useRef<HTMLFormElement>(null);

  const handleOptSubmit = async () => {
    try {
      setIsOptSaving(true);
      await setLeadAsOptIn(accountId, lead.id);
      toast({
        variant: 'success',
        description: 'The lead status has been changed to opted in',
      });
    } catch (error) {
      toast({
        variant: 'destructive',
        description: 'There was an error in changing the lead to opt in.',
      });
      setIsOptSaving(false);
    }
  };

  const getDeletedListArray = (_lists: any) => {
    return _lists.filter((list: any) => list.selected).map((list: any) => list.id);
  };

  return (
    <div className="p-6 bg-gray-100 min-h-dvh">
      <div className="flex items-center mb-6">
        <div className="flex-1">
          <Breadcrumb>
            <BreadcrumbList>
              <BreadcrumbItem>
                <BreadcrumbLink className="font-bold text-2xl" asChild>
                  <Link href={`/${accountId}/lead-management`}>Contact List</Link>
                </BreadcrumbLink>
              </BreadcrumbItem>
              <BreadcrumbSeparator className="h-6 w-6" />

              <BreadcrumbItem>
                <DropdownMenu>
                  <DropdownMenuTrigger className="flex items-center gap-1 font-bold text-2xl text-black">
                    Edit Contact
                    <ChevronDownIcon />
                  </DropdownMenuTrigger>
                  <DropdownMenuContent align="start">
                    <Link href={`/${accountId}/view-contact?phoneNumber=${lead.phone_number}&id=${lead.id}`}>
                      <DropdownMenuItem>View Contact</DropdownMenuItem>
                    </Link>
                  </DropdownMenuContent>
                </DropdownMenu>
              </BreadcrumbItem>
            </BreadcrumbList>
          </Breadcrumb>
        </div>
        <div className="flex items-center space-x-2">
          {/* <Link prefetch={false} href={`/${accountId}/lead-management`}>
          <Button variant="default">Go to contacts list</Button>
        </Link> */}

          <AlertDialog>
            <AlertDialogTrigger asChild>
              {isSaving ? (
                <Button disabled variant={'secondary'}>
                  Saving <Loader2 className="ml-2 animate-spin" />
                </Button>
              ) : (
                <Button variant={'secondary'} type="button">
                  Save Changes
                </Button>
              )}
            </AlertDialogTrigger>
            <AlertDialogContent>
              <AlertDialogHeader>
                <AlertDialogTitle>Save Changes</AlertDialogTitle>
                <AlertDialogDescription>Are you sure you want to update the contact.</AlertDialogDescription>
              </AlertDialogHeader>
              <AlertDialogFooter>
                <AlertDialogCancel>Cancel</AlertDialogCancel>
                <AlertDialogAction
                  onClick={() => {
                    setIsSaving(true);
                    formRef.current?.requestSubmit();
                  }}
                >
                  Yes
                </AlertDialogAction>
              </AlertDialogFooter>
            </AlertDialogContent>
          </AlertDialog>
          <Link href={`/${accountId}/live-chat?user_id=${lead.id}`}>
            <Button variant="default">Go to live chat</Button>
          </Link>
        </div>
      </div>

      <form
        ref={formRef}
        action={async (formAction) => {
          setIsSaving(true);
          setNewListids([]);
          const deletedListIds = getDeletedListArray(_lists);
          const addNewIds = newListids.length > 0 ? newListids.map((list) => list.value) : [];
          const country = numberCode.iso;
          const phone_number = numberCode?.code?.replace(/^\+/, '') + numberInput;
          if (numberInput && numberInput?.length != numberCode.phoneLength) {
            toast({
              variant: 'destructive',
              description: `The length of the phone number should be ${numberCode.phoneLength}`,
            });
            setError({ val: true, message: `The length of the phone number should be ${numberCode.phoneLength}` });
            return;
          } else {
            setError({ val: false, message: '' });
          }
          const record = await updateRecordByIdEditContact(
            id,
            accountId,
            formAction,
            'edit-form',
            country,
            phone_number,
            tags,
            lead.phone_number,
            deletedListIds,
            addNewIds
          );
          if (record.status == 200) {
            toast({
              variant: 'success',
              description: 'The contact has been updated successfully',
            });
            router.push(`/${accountId}/view-contact?id=${lead.id}`);
          } else if (record.status == 400) {
            toast({
              variant: 'destructive',
              description: 'The phone number is already in use.',
            });
          } else {
            toast({
              variant: 'destructive',
              description: 'There was an error in updating the contact.',
            });
          }
        }}
      >
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <div className="space-y-6">
            <Card>
              <CardHeader className="flex flex-col space-y-2">
                <CardTitle>Contact Information</CardTitle>

                {lead.opt_out && (
                  <>
                    <Alert variant="destructive" className="w-max">
                      <AlertDescription className="text-md flex items-center gap-2">
                        <AlertCircle className="h-6 w-6" />
                        The lead has <span className="font-semibold">opted out.</span>To change the status back to opted in, please click the button below.
                      </AlertDescription>
                    </Alert>
                    <div className="flex justify-start my-4">
                      <AlertDialog>
                        <AlertDialogTrigger asChild>
                          {isOptSaving ? (
                            <Button variant={'default'} disabled={true} type="button">
                              <Loader2 className="animate-spin mr-2" />
                              Saving
                            </Button>
                          ) : (
                            <Button variant={'default'} type="button">
                              Change to Opted in
                            </Button>
                          )}
                        </AlertDialogTrigger>
                        <AlertDialogContent>
                          <AlertDialogHeader>
                            <AlertDialogTitle>Are you absolutely sure?</AlertDialogTitle>
                            <AlertDialogDescription>
                              It is not advisable to force change the status of lead who has opted out. Sending messages to such leads can harm your business
                              quality.
                            </AlertDialogDescription>
                          </AlertDialogHeader>
                          <AlertDialogFooter>
                            <AlertDialogCancel>Cancel</AlertDialogCancel>
                            <AlertDialogAction onClick={handleOptSubmit}>Yes</AlertDialogAction>
                          </AlertDialogFooter>
                        </AlertDialogContent>
                      </AlertDialog>
                    </div>
                  </>
                )}
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="name">Name</Label>
                  <Input id="name" name="name" placeholder="Enter name" defaultValue={lead.name} />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="phone">Phone</Label>
                  <InputWithPhoneCode2
                    numberInput={numberInput}
                    setNumberInput={setNumberInput}
                    placeholder="Whatsapp Number"
                    type="tel"
                    name="phoneNumber"
                    numberCode={numberCode}
                    setNumberCode={setNumberCode}
                    formRef={formRef}
                    className="border-gray-400"
                  />
                  {error && <div className="text-sm text-red-500">{error.message}</div>}
                </div>

                <div className="space-y-2">
                  <Label htmlFor="tags">Tags</Label>
                  <MultipleSelector
                    onChange={setTags}
                    value={tags}
                    defaultOptions={tagsOptions}
                    badgeClassName="bg-gray-600 text-white"
                    showTagIcon
                    placeholder="Add or remove tags"
                    emptyIndicator={<p className="text-center bg-white text-lg leading-10 text-gray-600 dark:text-gray-400">no results found.</p>}
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="status">Status</Label>
                  <Select defaultValue={lead.status} name="status">
                    <SelectTrigger className="border-gray-400 border-2 ">
                      <SelectValue placeholder="Select status" />
                    </SelectTrigger>
                    <SelectContent position="popper">
                      <SelectItem value="New">New</SelectItem>
                      <SelectItem value="Prospect">Prospect</SelectItem>
                      <SelectItem value="Opportunity">Opportunity</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Meta Data</CardTitle>
                <p className="text-sm text-muted-foreground">Collected from the lead in the automation flows</p>
              </CardHeader>
              <CardContent className="space-y-2">
                {lead.meta ? (
                  Object.keys(lead.meta).map((key) => (
                    <div key={key} className="flex justify-between">
                      <span className="text-muted-foreground">{key}</span>
                      <span>{lead.meta[key]}</span>
                    </div>
                  ))
                ) : (
                  <div>No Meta Data</div>
                )}
              </CardContent>
            </Card>
          </div>

          <div className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>Associations</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <Collapsible className="border rounded-lg" defaultOpen={true}>
                  <CollapsibleTrigger className="flex justify-between items-center w-full p-4 hover:bg-muted/50">
                    <span>Lead is part of following List(s)</span>
                    <ChevronDown className="h-4 w-4" />
                  </CollapsibleTrigger>
                  <CollapsibleContent className="p-4 pt-0">
                    <div className="flex items-center space-x-2 mb-4">
                      <MultipleSelector
                        onChange={setNewListids}
                        value={newListids}
                        defaultOptions={allList.map((list) => ({ label: list.name, value: list.id }))}
                        placeholder="Select list to add lead into"
                        emptyIndicator={<p className="text-center text-lg leading-10 text-gray-600 dark:text-gray-400">no results found.</p>}
                      />
                    </div>
                    {getDeletedListArray(_lists).length > 0 && (
                      <div className="text-red-600">{getDeletedListArray(_lists).length} list(s) marked for deletion</div>
                    )}
                    <ScrollArea className="max-h-96 overflow-auto">
                      <div className="flex flex-col space-y-1">
                        {lists.length == 0 && <div>No data found</div>}
                        {_lists.map((list) => (
                          <div key={list.id} className="flex w-full items-center space-x-2">
                            <Checkbox
                              checked={list.selected}
                              onClick={() =>
                                setLists((prev) =>
                                  prev.map((prevList) => {
                                    if (prevList.id == list.id) {
                                      return { ...prevList, selected: !prevList.selected };
                                    }
                                    return prevList;
                                  })
                                )
                              }
                            />
                            <Link
                              className="group flex flex-1 items-center p-2 hover:bg-gray-200 rounded-md transition-colors duration-200"
                              href={`list/view-list?listId=${list.id}&listName=${list.name}&leadId=${id}`}
                              target="_blank"
                            >
                              <div className={clsx('flex-1 text-gray-700', list.selected && 'line-through')}>{list.name}</div>
                              <ExternalLink className="h-5 w-5 text-gray-500 group-hover:text-gray-900 transition-colors duration-200" />
                            </Link>
                          </div>
                        ))}
                      </div>
                    </ScrollArea>
                  </CollapsibleContent>
                </Collapsible>

                <Collapsible className="border rounded-lg" defaultOpen={true}>
                  <CollapsibleTrigger className="flex justify-between items-center w-full p-4 hover:bg-muted/50">
                    <span>Lead is part of following Campaign(s)</span>
                    <ChevronDown className="h-4 w-4" />
                  </CollapsibleTrigger>
                  <CollapsibleContent className="p-4 pt-0">
                    <ScrollArea className="max-h-96 overflow-auto">
                      <div className="flex flex-col space-y-1">
                        {campaignsArray?.length == 0 && <div>No data found</div>}
                        {campaignsArray?.map((campaign: CampaignArray) => (
                          <Link
                            key={campaign.id}
                            className="group flex items-center p-3 hover:bg-gray-200 rounded-md transition-colors duration-200"
                            href={`campaign/${campaign.id}`}
                            target="_blank"
                          >
                            <div className="flex-1 text-gray-700">
                              {campaign.name}
                              <span className="block text-sm text-gray-500">{dayjs(campaign.date_and_time).format('MMMM D, YYYY h:mm A')}</span>
                              <Badge
                                style={{ backgroundColor: campaign.leadReply == 'Not Replied' ? 'red' : 'green' }}
                                className="text-sm mt-2"
                                variant="destructive"
                              >
                                {campaign.leadReply}
                              </Badge>
                            </div>
                            <ExternalLink className="h-5 w-5 text-gray-500 group-hover:text-gray-900 transition-colors duration-200 ml-2" />
                          </Link>
                        ))}
                      </div>
                    </ScrollArea>
                  </CollapsibleContent>
                </Collapsible>
              </CardContent>
            </Card>
          </div>
        </div>
      </form>
    </div>
  );
}
