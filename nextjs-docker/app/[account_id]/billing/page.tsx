import Stripe from 'stripe';
import { But<PERSON> } from '@/components/ui/button';
import { Toaster } from '@/components/ui/toaster';
import Link from 'next/link';
import {
  getAccount,
  getAllSubscriptionDetails,
  getBusinessInvoicesList,
  getLastSubscriptionDetails,
  getTransactionsList,
  getUncreatedInvoiceDocuments,
} from '@/lib/pocket';
import dayjs from 'dayjs';
import { CircleAlert, CircleCheck, CircleX } from 'lucide-react';
import { InvoiceTable } from './components/invoice-table/data-table';
import { TransactionTable } from './components/transaction-table/data-table';
import { invoiceColumns } from './components/invoice-table/column';
import { transactionColumns } from './components/transaction-table/column';
import AddPaymentMethodCard from './components/AddPaymentMethodCard';

export const dynamic = 'force-dynamic';
export const fetchCache = 'default-no-store';

export default async ({ params }: { params: Record<string, string> }) => {
  const invoices = await getBusinessInvoicesList(params.account_id);
  const transactions = await getTransactionsList(params.account_id);
  const subscriptionDetails = await getAllSubscriptionDetails(params.account_id);
  const lastSubscriptionDetails = await getLastSubscriptionDetails(params.account_id);
  const account = await getAccount(params.account_id);
  const uncreatedInvoiceDocs = await getUncreatedInvoiceDocuments(params.account_id);

  const stripe = new Stripe(process.env.STRIPE_SECRET_KEY!, {});
  if (account.stripe_customer_id) {
    // Stripe customer retrieval logic can be added here if needed in the future.
  }

  return (
    <div className="h-full w-full">
      {invoices.length > 0 &&
        (lastSubscriptionDetails?.expand?.account_id.subscription_active == false ? (
          <div className="mx-auto mt-6 p-8 border rounded-md h-fit w-[50%] bg-gradient-to-r from-red-50 to-red-100 shadow-md">
            <div className="text-center space-y-4">
              <div className="flex justify-center items-center">
                <CircleX className="text-red-700 h-10 w-10" />
              </div>
              <h1 className="text-3xl font-bold text-red-700">Your Subscription has been cancelled!</h1>
              <p className="text-lg text-red-700">If you would like to reactivate your subscription, please contact WeTarseel admins for assistance.</p>
            </div>
          </div>
        ) : subscriptionDetails?.[0]?.payment_status == 'paid' ||
          (dayjs(subscriptionDetails?.[0]?.invoice_valid_from).diff(dayjs(), 'second') > 0 && subscriptionDetails?.[1]?.payment_status == 'paid') ? (
          <div className="mx-auto mt-6 p-8 border rounded-md h-fit w-[50%] bg-gradient-to-r from-green-50 to-green-100 shadow-md">
            <div className="text-center space-y-4">
              <div className="flex justify-center items-center">
                <CircleCheck className="text-green-700 h-10 w-10" />
              </div>
              <h1 className="text-3xl font-bold text-green-700">Your Subscription is Active!</h1>
              {/* <p className="text-lg text-gray-700">Enjoy uninterrupted access to our services.</p> */}
              <div className="p-4 bg-white border border-gray-200 rounded-lg shadow-md">
                <p className="text-gray-600">
                  <strong>Subscription Duration:</strong> <span className="font-medium text-gray-800">{lastSubscriptionDetails?.billing_months} Months</span>
                </p>
                <p className="text-gray-600">
                  <strong>Subscription Ends On:</strong>{' '}
                  <span className="font-medium text-gray-800">{dayjs(lastSubscriptionDetails?.renewal_date).format('YYYY-MM-DD')}</span>
                </p>
              </div>
              {/* <footer className="text-sm text-gray-500 pt-4">Thank you for being a valued subscriber. We’re here to support you.</footer> */}
            </div>
          </div>
        ) : (
          <div className="flex justify-center items-center mt-6">
            <div className="bg-white border-2 border-gray-200 rounded-lg mb-4 p-6 max-w-md text-center">
              {/* <h2 className="text-lg font-semibold text-gray-800">
                            Payment Pending
                        </h2> */}
              <div className="mt-4 w-fit mx-auto text-red-700 text-md font-medium rounded-lg border-red-500 border bg-red-50 p-3">
                <div className="flex items-center gap-3">
                  <CircleAlert />
                  <span>Payment Pending</span>
                </div>
              </div>
              <p className="text-sm text-gray-600 mt-2">
                Your subscription payment is currently pending. To continue, please complete the payment by clicking the button below.
              </p>
              <div className="mt-4">
                <Link href={`/${params.account_id}/billing/form`}>
                  <Button className="w-full">Pay Now</Button>
                </Link>
              </div>
            </div>
          </div>
        ))}
      <div className="px-4">
        <AddPaymentMethodCard account_id={account.id} hasPaymentMethod={Boolean(account.stripe_customer_id)} />
      </div>
      <div className="p-4 mt-4 flex flex-col gap-6">
        <div className="space-y-2 mb-4 border py-2 rounded-md shadow-sm">
          <div className="px-6 text-2xl font-semibold">Invoice Table</div>
          <InvoiceTable columns={invoiceColumns} data={invoices} account_id={params.account_id} uncreatedInvoiceDocs={uncreatedInvoiceDocs} />
        </div>

        <div className="space-y-2 mb-4 border py-2 rounded-md shadow-sm">
          <div className="px-6 text-2xl font-semibold">Transaction Table</div>
          <TransactionTable columns={transactionColumns} data={transactions} />
        </div>
      </div>
      <Toaster />
    </div>
  );
};
