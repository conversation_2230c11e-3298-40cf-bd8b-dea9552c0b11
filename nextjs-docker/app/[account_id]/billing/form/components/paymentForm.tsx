'use client'; // Enable client-side rendering for this component

// Import Stripe components for payment processing
import { PaymentElement, useElements, useStripe } from '@stripe/react-stripe-js';
// React hooks and types
import { FormEvent, useEffect, useState } from 'react';
// UI components
import { <PERSON><PERSON> } from '@/components/ui/button';
import { AlertCircle, CheckCircle, CreditCard, Loader2 } from 'lucide-react';
// PDF generation library
// @ts-ignore
// API functions for payment processing and document handling
import { createPayment } from '@/lib/pocket';
// Type definitions
import { IInvoice } from '@/lib/types';
// Date handling library
import dayjs from 'dayjs';
// PDF generation utility
// Toast notification component
import { useToast } from '@/components/ui/use-toast';
// Path constants
import { PATHS } from '@/lib/utils';
// UI components
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';

/**
 * PaymentForm Component
 *
 * Handles payment processing using Stripe and displays payment information
 * Shows either an active subscription message or a payment form based on subscription status
 *
 * @param {Object} props - Component properties
 * @param {string} props.account_id - Unique identifier for the account
 * @param {string} props.customerId - Stripe customer ID for saved payment methods
 * @param {number} props.subscriptionFees - Amount due for payment
 * @param {string} props.currency - Currency code (e.g., 'USD')
 * @param {string} props.businessName - Name of the business
 * @param {any} props.businessInvoices - Invoice data for the business
 * @param {IInvoice[]} props.invoiceArray - Array of invoice objects
 * @param {IInvoice} props.subscriptionDetails - Details about the current subscription
 */
const PaymentForm = ({
  account_id,
  customerId,
  subscriptionFees,
  currency,
  invoiceArray,
  subscriptionDetails,
}: {
  account_id: string;
  customerId?: string;
  subscriptionFees: number;
  currency: string;
  businessName?: string; // Made optional since it's not used
  businessInvoices?: any; // Made optional since it's not used
  invoiceArray: IInvoice[];
  subscriptionDetails: IInvoice;
}) => {
  // Initialize toast notification hook
  const { toast } = useToast();
  // Initialize Stripe hooks
  const stripe = useStripe();
  const elements = useElements();
  // State for error handling
  const [error, setError] = useState<string | null | undefined>(null);
  // State to track payment processing status
  const [isProcessing, setIsProcessing] = useState(false);
  // State to track if the account has a saved payment method
  const [hasSavedPaymentMethod, setHasSavedPaymentMethod] = useState(false);
  // State to track if payment with saved method is successful
  const [savedMethodPaymentSuccess, setSavedMethodPaymentSuccess] = useState(false);
  // State to track if we're loading account information
  const [isLoading, setIsLoading] = useState(true);

  // Fetch account information to check for saved payment method
  useEffect(() => {
    const fetchAccountInfo = async () => {
      try {
        // If customerId is provided, we know there's a saved payment method
        if (customerId) {
          setHasSavedPaymentMethod(true);
          setIsLoading(false);
          return;
        }

        const response = await fetch(`/api/v1/account-info?account_id=${account_id}`);
        if (response.ok) {
          const data = await response.json();
          setHasSavedPaymentMethod(!!data.stripe_customer_id);
        }
      } catch (error) {
        console.error('Error fetching account info:', error);
      } finally {
        setIsLoading(false);
      }
    };

    fetchAccountInfo();
  }, [account_id, customerId]);

  /**
   * Handle form submission for payment processing with new card
   *
   * @param {FormEvent} event - Form submission event
   */
  const handleSubmit = async (event: FormEvent) => {
    // Prevent default form submission behavior
    event.preventDefault();

    // Ensure Stripe is initialized before proceeding
    if (!stripe || !elements) {
      setError('Stripe has not loaded yet. Please try again.');
      return;
    }

    // Update UI to show processing state
    setIsProcessing(true);
    setError(null);

    try {
      // Submit the payment element to create/attach the payment method
      const { error: submitError } = await elements.submit();

      if (submitError) {
        setError(submitError.message);
        setIsProcessing(false);
        return;
      }

      // Process payment through Stripe
      const result = await stripe.confirmPayment({
        elements,
        confirmParams: {
          // Redirect URL after successful payment
          return_url: `${window.location.origin}/${account_id}/billing`,
        },
        redirect: 'if_required', // Only redirect if necessary
      });

      console.log({ result });

      // Handle payment result
      if (result.error) {
        // Record failed payment attempt
        await createPayment('on-system', account_id, invoiceArray, 'failed', result.error.message);

        // Set appropriate error message based on error type
        if (result.error.type === 'card_error' || result.error.type === 'validation_error') {
          setError(result.error.message);
        } else {
          setError('An unexpected error occurred.');
        }

        // Show error notification
        toast({
          variant: 'destructive',
          description: result.error.message || 'An error occurred',
        });
      } else {
        // Payment successful
        try {
          // Record successful payment
          await createPayment('on-system', account_id, invoiceArray, 'success');

          // Show success notification
          toast({
            variant: 'success',
            description: 'The payment was done successfully',
          });
        } catch {
          // Handle API error during payment recording
          toast({
            variant: 'destructive',
            description: 'An error occurred while recording payment',
          });
        }
      }
    } catch (error) {
      console.error('Payment error:', error);
      setError('An unexpected error occurred during payment processing');
      toast({
        variant: 'destructive',
        description: 'An unexpected error occurred during payment processing',
      });
    } finally {
      // Reset processing state
      setIsProcessing(false);
    }
  };

  /**
   * Handle payment with saved payment method
   */
  const handlePayWithSavedMethod = async () => {
    try {
      // Update UI to show processing state
      setIsProcessing(true);
      setError(null);

      // Call the API to process payment with saved method
      const response = await fetch(`${PATHS[process.env.NODE_ENV]}/api/v1/pay-with-saved-method`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          account_id,
          invoices: invoiceArray,
          amount: subscriptionFees,
        }),
      });

      const data = await response.json();

      if (data.success) {
        // Payment was successful
        setSavedMethodPaymentSuccess(true);
        toast({
          variant: 'success',
          description: 'Payment was processed successfully',
        });
      } else if (data.requires_action && data.client_secret) {
        // Payment requires additional authentication
        if (!stripe) {
          setError('Unable to complete payment. Stripe is not initialized.');
          return;
        }

        // Use Stripe to handle the additional authentication
        const { error } = await stripe.confirmCardPayment(data.client_secret);

        if (error) {
          setError(error.message || 'Payment authentication failed');
          toast({
            variant: 'destructive',
            description: 'Payment authentication failed',
          });
        } else {
          // Authentication successful
          setSavedMethodPaymentSuccess(true);
          toast({
            variant: 'success',
            description: 'Payment was processed successfully',
          });
        }
      } else {
        // Payment failed
        setError(data.message || 'Payment failed');
        toast({
          variant: 'destructive',
          description: data.message || 'Payment failed',
        });
      }
    } catch (err) {
      setError('An unexpected error occurred');
      toast({
        variant: 'destructive',
        description: 'An unexpected error occurred',
      });
    } finally {
      setIsProcessing(false);
    }
  };

  return (
    <div className="flex mb-4">
      <div className="w-full flex justify-center items-start mt-10">
        {/* Conditional rendering based on subscription status */}
        {subscriptionFees === 0 ? (
          // Active subscription view - shown when no payment is due
          <div className="p-8 border rounded-xl h-fit w-[50%] bg-gradient-to-r from-green-50 to-green-100 shadow-lg">
            <div className="text-center space-y-4">
              <div className="flex justify-center items-center">
                <div className="bg-green-600 rounded-full p-3">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor" strokeWidth={2}>
                    <path strokeLinecap="round" strokeLinejoin="round" d="M5 13l4 4L19 7" />
                  </svg>
                </div>
              </div>
              <h1 className="text-3xl font-bold text-green-700">Your Subscription is Active!</h1>
              <p className="text-lg text-gray-700">Enjoy uninterrupted access to our services.</p>
              <div className="p-4 bg-white border border-gray-200 rounded-lg shadow-md">
                <p className="text-gray-600">
                  <strong>Subscription Duration:</strong> <span className="font-medium text-gray-800">{subscriptionDetails?.billing_months} Months</span>
                </p>
                <p className="text-gray-600">
                  <strong>Subscription Ends On:</strong>{' '}
                  <span className="font-medium text-gray-800">{dayjs(subscriptionDetails?.renewal_date).format('YYYY-MM-DD')}</span>
                </p>
              </div>
              <footer className="text-sm text-gray-500 pt-4">Thank you for being a valued subscriber. We’re here to support you.</footer>
            </div>
          </div>
        ) : (
          // Payment form - shown when payment is due
          <div className="p-6 border rounded-lg h-fit w-[50%] bg-white shadow-lg">
            {/* Payment Summary Section */}
            <div className="mb-8 bg-green-50 border-l-4 border-green-500 p-6 rounded-lg">
              <div className="flex items-center justify-between">
                <span className="text-green-900 text-xl font-medium">Due charges:</span>
                <span className="text-green-900 text-2xl font-bold">
                  {subscriptionFees.toFixed(2)} {currency ? currency : 'USD'}
                </span>
              </div>
              <p className="text-gray-600 mt-2">
                <strong>Due Date:</strong> {dayjs(invoiceArray[0].invoice_valid_from).add(invoiceArray[0].invoice_limit, 'day').format('YYYY-MM-DD')}
              </p>
            </div>

            {savedMethodPaymentSuccess ? (
              // Success message after payment with saved method
              <div className="text-center py-8">
                <div className="flex justify-center mb-4">
                  <div className="bg-green-100 p-3 rounded-full">
                    <CheckCircle className="h-12 w-12 text-green-600" />
                  </div>
                </div>
                <h2 className="text-2xl font-bold text-green-700 mb-2">Payment Successful!</h2>
                <p className="text-gray-600 mb-6">Your payment has been processed successfully.</p>
                <Button
                  variant="outline"
                  className="border-green-200 text-green-700 hover:bg-green-50"
                  onClick={() => (window.location.href = `/${account_id}/billing`)}
                >
                  Return to Billing
                </Button>
              </div>
            ) : (
              // Payment options tabs
              <Tabs defaultValue={hasSavedPaymentMethod ? 'saved' : 'new'} className="w-full">
                <TabsList className="grid w-full grid-cols-2 mb-6">
                  {hasSavedPaymentMethod && (
                    <TabsTrigger value="saved" className="flex items-center">
                      <CreditCard className="mr-2 h-4 w-4" />
                      Saved Card
                    </TabsTrigger>
                  )}
                  <TabsTrigger value="new" className="flex items-center">
                    <CreditCard className="mr-2 h-4 w-4" />
                    New Card
                  </TabsTrigger>
                </TabsList>

                {hasSavedPaymentMethod && (
                  <TabsContent value="saved" className="mt-0">
                    <div className="bg-gray-50 p-4 rounded-md mb-6">
                      <div className="flex items-start">
                        <CreditCard className="h-5 w-5 text-gray-500 mt-1 mr-3" />
                        <div>
                          <p className="font-medium">Pay with your saved payment method</p>
                          <p className="text-sm text-gray-500 mt-1">Your card information is securely stored with Stripe.</p>
                        </div>
                      </div>
                    </div>

                    <Button
                      variant="secondary"
                      className="w-full py-3 text-white bg-green-600 hover:bg-green-700 rounded-md transition-all duration-300 ease-in-out"
                      onClick={handlePayWithSavedMethod}
                      disabled={isProcessing}
                    >
                      {isProcessing ? (
                        <>
                          <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                          Processing...
                        </>
                      ) : (
                        'Pay with Saved Card'
                      )}
                    </Button>

                    {error && (
                      <div className="mt-4 p-3 bg-red-50 rounded-md flex items-start">
                        <AlertCircle className="h-5 w-5 text-red-500 mt-0.5 mr-2 flex-shrink-0" />
                        <p className="text-sm text-red-700">{error}</p>
                      </div>
                    )}
                  </TabsContent>
                )}

                <TabsContent value="new" className="mt-0">
                  <form onSubmit={handleSubmit}>
                    {/* Stripe Payment Element - renders the payment form */}
                    <PaymentElement options={{ layout: 'tabs' }} />

                    {/* Payment submission button with loading state */}
                    <Button
                      variant="secondary"
                      className="w-full mt-4 py-3 text-white bg-green-600 hover:bg-green-700 rounded-md transition-all duration-300 ease-in-out"
                      type="submit"
                      disabled={!stripe || isProcessing} // Disable button when Stripe isn't loaded or payment is processing
                    >
                      {isProcessing ? (
                        <>
                          <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                          Processing...
                        </>
                      ) : (
                        'Pay with New Card'
                      )}
                    </Button>

                    {/* Error message display */}
                    {error && (
                      <div className="mt-4 p-3 bg-red-50 rounded-md flex items-start">
                        <AlertCircle className="h-5 w-5 text-red-500 mt-0.5 mr-2 flex-shrink-0" />
                        <p className="text-sm text-red-700">{error}</p>
                      </div>
                    )}
                  </form>
                </TabsContent>
              </Tabs>
            )}
          </div>
        )}
      </div>
    </div>
  );
};

// Export the PaymentForm component as the default export
export default PaymentForm;
