'use client';
import getStripe from '@/lib/stripeClient';
import { Elements } from '@stripe/react-stripe-js';
import PaymentForm from './paymentForm';
import { IInvoice } from '@/lib/types';
import Link from 'next/link';
import { Button } from '@/components/ui/button';

const PaymentPage = ({
  account_id,
  customerId,
  clientSecret,
  subscriptionFees,
  currency,
  invoiceArray,
  subscriptionDetails,
}: {
  account_id: string;
  customerId?: string;
  clientSecret: any;
  subscriptionFees: number;
  currency: string;
  businessName?: string; // Made optional since it's not used
  businessInvoices?: any; // Made optional since it's not used
  invoiceArray: IInvoice[];
  subscriptionDetails: IInvoice;
}) => {
  // Enhanced options with customer configuration
  const elementsOptions = {
    clientSecret,
    appearance: { theme: 'stripe' as const },
    // If you have a customerId, you can configure Elements for that customer
    ...(customerId && {
      customer: customerId,
      // Enable saving payment methods for future use
      paymentMethodCreation: 'manual' as const,
    }),
  };

  return (
    <Elements options={elementsOptions} stripe={getStripe()}>
      <PaymentForm
        account_id={account_id}
        customerId={customerId}
        subscriptionFees={subscriptionFees}
        currency={currency}
        invoiceArray={invoiceArray}
        subscriptionDetails={subscriptionDetails}
      />
      <div className="px-4">
        {/* Go back to billing */}
        <a href={`/${account_id}/billing`}>
          <Button>Go back</Button>
        </a>
      </div>
    </Elements>
  );
};

export default PaymentPage;
