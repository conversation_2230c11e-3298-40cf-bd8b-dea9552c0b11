import Link from 'next/link';
import { getAccount, getAmountDue, getLastSubscriptionDetails } from '@/lib/pocket';
import { Converter } from 'easy-currencies';
import PaymentPage from './components/paymentPage';
import { PATHS } from '@/lib/utils';

export default async ({ params }: { params: Record<string, string> }) => {
  const account = await getAccount(params.account_id);
  const data = await getAmountDue(params.account_id);
  const converter = new Converter();
  const convertTo = account?.currency !== '' ? account?.currency : 'USD';
  const amount = data?.totalAmount ?? 0;
  const convertedValue = await converter.convert(amount, 'AED', convertTo);
  const subscriptionDetails = await getLastSubscriptionDetails(params.account_id);

  let clientSecret;
  let nCustomerId;
  try {
    const res = await fetch(`${PATHS[process.env.NODE_ENV]}/api/v1/create-payment-intent`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        amount: (amount == 0 ? 2 : amount) * 100,
        accountId: params.account_id,
      }),
    });

    const { clientSecret: clientSecretResponse, customerId } = (await res.json()) as { clientSecret: string; customerId: string };
    clientSecret = clientSecretResponse;
    nCustomerId = customerId;
  } catch (e) {
    console.log('Error creating payment intent:', e);
    return (
      <div>
        <div className="flex flex-col space-y-4 h-full w-full justify-center items-center">
          <div className="text-2xl font-bold">An error occurred</div>
          <div className="text-lg">Please try again later.</div>
          <a href={`/${params.account_id}/billing`}>
            <button className="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">Go Back to Billing</button>
          </a>
        </div>
      </div>
    );
  }

  return (
    <div className="h-full">
      <PaymentPage
        customerId={nCustomerId}
        account_id={params.account_id}
        clientSecret={clientSecret}
        subscriptionFees={convertedValue}
        currency={convertTo}
        invoiceArray={data?.pendingInvoiceArray}
        subscriptionDetails={subscriptionDetails}
      />
    </div>
  );
};
