'use client';

import { Button } from '@/components/ui/button';
import { ColumnDef } from '@tanstack/react-table';
import { ArrowUpDown } from 'lucide-react';
import dayjs from 'dayjs';
import { convertFirstToUpperCase } from '@/lib/utils';
import { HoverCard, HoverCardContent, HoverCardTrigger } from '@/components/ui/hover-card';
import { IInvoice, ITransactionLog } from '@/lib/types';
import clsx from 'clsx';

export const transactionColumns: ColumnDef<ITransactionLog>[] = [
  // {
  //     accessorKey: 'id',
  //     size: 150,
  //     maxSize: 300,
  //     minSize: 150,
  //     header: ({ column }) => {
  //         return (
  //             <Button variant="ghost" className="px-0 mb-0" onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}>
  //                 Invoice ID
  //                 <ArrowUpDown className="ml-2 h-4 w-4" />
  //             </Button>
  //         );
  //     },
  //     cell: ({ row }) => {
  //         return <div className="text-sm font-medium">{row.original.id}</div>;
  //     },
  // },
  {
    accessorKey: 'transaction_date',
    size: 150,
    maxSize: 300,
    minSize: 150,
    header: ({ column }) => {
      return (
        <Button variant="ghost" className="px-0 mb-0" onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}>
          Transaction Date
          <ArrowUpDown className="ml-2 h-4 w-4" />
        </Button>
      );
    },
    cell: ({ row }) => {
      return <div className="text-sm font-medium">{dayjs(row.getValue('transaction_date')).format('DD-MM-YYYY')}</div>;
    },
  },
  {
    accessorKey: 'created_time',
    size: 150,
    maxSize: 300,
    minSize: 150,
    header: ({ column }) => {
      return (
        <Button variant="ghost" className="px-0 mb-0" onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}>
          Transaction Time
          <ArrowUpDown className="ml-2 h-4 w-4" />
        </Button>
      );
    },
    cell: ({ row }) => {
      return <div className="text-sm font-medium">{dayjs(row.getValue('transaction_date')).format('HH:mm')}</div>;
    },
  },
  {
    accessorKey: 'paymentAmount',
    size: 150,
    maxSize: 300,
    minSize: 150,
    header: ({ column }) => {
      return (
        <Button variant="ghost" className="px-0 mb-0" onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}>
          Amount
          <ArrowUpDown className="ml-2 h-4 w-4" />
        </Button>
      );
    },
    cell: ({ row }) => {
      return <div className="text-sm font-medium">{row.original.payment_amount} AED</div>;
    },
  },
  {
    accessorKey: 'paymentRef',
    size: 150,
    maxSize: 300,
    minSize: 150,
    header: ({ column }) => {
      return (
        <Button variant="ghost" className="px-0 mb-0" onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}>
          Payment Ref
          <ArrowUpDown className="ml-2 h-4 w-4" />
        </Button>
      );
    },
    cell: ({ row }) => {
      return <div className="text-sm font-medium">{row.original.payment_ref ? row.original.payment_ref : 'N/A'}</div>;
    },
  },
  {
    accessorKey: 'paymentMethod',
    size: 150,
    maxSize: 300,
    minSize: 150,
    header: ({ column }) => {
      return (
        <Button variant="ghost" className="px-0 mb-0" onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}>
          Payment Method
          <ArrowUpDown className="ml-2 h-4 w-4" />
        </Button>
      );
    },
    cell: ({ row }) => {
      return <div className="text-sm font-medium">{row.original.payment_method}</div>;
    },
  },
  {
    accessorKey: 'error',
    size: 150,
    maxSize: 300,
    minSize: 150,
    header: ({ column }) => {
      return (
        <Button variant="ghost" className="px-0 mb-0" onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}>
          Payment Status
          <ArrowUpDown className="ml-2 h-4 w-4" />
        </Button>
      );
    },
    cell: ({ row }) => {
      return (
        <div className={clsx(row.original.error ? 'text-red-500' : 'text-green-500', 'text-sm font-medium')}>
          {row.original.error ? 'Failed' : 'Successful'}
        </div>
      );
    },
  },
  // {
  //     accessorKey: 'unit_amount',
  //     size: 150,
  //     maxSize: 300,
  //     minSize: 150,
  //     header: ({ column }) => {
  //         return (
  //             <Button variant="ghost" className="px-0 mb-0" onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}>
  //                 Unit Amount
  //                 <ArrowUpDown className="ml-2 h-4 w-4" />
  //             </Button>
  //         );
  //     },
  //     cell: ({ row }) => {
  //         return <div className="text-sm font-medium">{row.original.unitAmount} AED</div>;
  //     },
  // },
  // {
  //     accessorKey: 'tax',
  //     size: 150,
  //     maxSize: 300,
  //     minSize: 150,
  //     header: ({ column }) => {
  //         return (
  //             <Button variant="ghost" className="px-0 mb-0" onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}>
  //                 Tax
  //                 <ArrowUpDown className="ml-2 h-4 w-4" />
  //             </Button>
  //         );
  //     },
  //     cell: ({ row }) => {
  //         return <div className="text-sm font-medium">{row.original.tax} AED</div>;
  //     },
  // },
  // {
  //     accessorKey: 'charges',
  //     size: 150,
  //     maxSize: 300,
  //     minSize: 150,
  //     header: ({ column }) => {
  //         return (
  //             <Button variant="ghost" className="px-0 mb-0" onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}>
  //                 Other Charges
  //                 <ArrowUpDown className="ml-2 h-4 w-4" />
  //             </Button>
  //         );
  //     },
  //     cell: ({ row }) => {
  //         return <div className="text-sm font-medium">{row.original.charges} AED</div>;
  //     },
  // },
];
