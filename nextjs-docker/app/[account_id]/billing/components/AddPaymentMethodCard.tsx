'use client';

import { useRouter } from 'next/navigation';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { CreditCard, AlertCircle } from 'lucide-react';

interface AddPaymentMethodCardProps {
  account_id: string;
  hasPaymentMethod: boolean;
}

export default function AddPaymentMethodCard({ account_id, hasPaymentMethod }: AddPaymentMethodCardProps) {
  const router = useRouter();

  if (hasPaymentMethod) {
    return (
      <Card className="bg-green-50 border-green-200">
        <CardHeader className="pb-2">
          <CardTitle className="text-lg flex items-center text-green-700">
            <CreditCard className="mr-2 h-5 w-5" />
            Payment Method Active
          </CardTitle>
        </CardHeader>
        <CardContent>
          <p className="text-sm text-green-700">
            Your payment method is set up for automatic billing. Invoices will be automatically processed when due.
          </p>
        </CardContent>
        <CardFooter>
          <Button 
            variant="outline" 
            className="text-green-700 border-green-200 hover:bg-green-100 hover:text-green-800"
            onClick={() => router.push(`/${account_id}/billing/payment-method`)}
          >
            Update Payment Method
          </Button>
        </CardFooter>
      </Card>
    );
  }

  return (
    <Card className="bg-amber-50 border-amber-200">
      <CardHeader className="pb-2">
        <CardTitle className="text-lg flex items-center text-amber-700">
          <AlertCircle className="mr-2 h-5 w-5" />
          No Payment Method
        </CardTitle>
      </CardHeader>
      <CardContent>
        <p className="text-sm text-amber-700">
          You don't have a payment method set up for automatic billing. Add a payment method to enable automatic invoice processing.
        </p>
      </CardContent>
      <CardFooter>
        <Button 
          variant="default" 
          className="bg-amber-600 hover:bg-amber-700"
          onClick={() => router.push(`/${account_id}/billing/payment-method`)}
        >
          Add Payment Method
        </Button>
      </CardFooter>
    </Card>
  );
}
