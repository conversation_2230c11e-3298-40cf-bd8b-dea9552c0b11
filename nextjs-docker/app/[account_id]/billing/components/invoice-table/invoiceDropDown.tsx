'use client';
import { But<PERSON> } from '@/components/ui/button';
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuLabel, DropdownMenuTrigger } from '@/components/ui/dropdown-menu';
import { IInvoice } from '@/lib/types';
import { Row } from '@tanstack/react-table';
import { CircleDollarSign, CircleHelp, Eye, MoreHorizontal, Pencil } from 'lucide-react';
import Link from 'next/link';
import { useParams, useSearchParams } from 'next/navigation';

export const InvoiceDropdDown = ({ row }: { row: Row<IInvoice> }) => {
  const params = useParams<{ account_id: string }>();
  const searchParams = useSearchParams();
  return (
    <DropdownMenu modal={false}>
      <DropdownMenuTrigger asChild>
        <Button variant="ghost" className="h-8 w-8 p-0 hover:bg-gray-100 mb-0">
          <span className="sr-only">Open menu</span>
          <MoreHorizontal className="h-4 w-4" />
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end">
        <DropdownMenuLabel>Actions</DropdownMenuLabel>
        {/* <Link href={`/${params.account_id}/view-contact?phoneNumber=${row.original.phone_number}&id=${row.id}`}> */}
        <DropdownMenuItem
          onClick={() => {
            window.open(row.original.expand?.invoice_pdf.url, '_blank');
          }}
        >
          <Eye className="mr-2 h-4 w-4 text-blue-600" />
          <span className="text-blue-600">View Invoice</span>
        </DropdownMenuItem>
        {/* </Link> */}
      </DropdownMenuContent>
    </DropdownMenu>
  );
};
