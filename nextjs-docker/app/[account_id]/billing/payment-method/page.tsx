'use client';

import { useEffect, useState } from 'react';
import { useStripe, useElements, PaymentElement } from '@stripe/react-stripe-js';
import { Elements } from '@stripe/react-stripe-js';
import getStripe from '@/lib/stripeClient';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Loader2, CreditCard, CheckCircle2, AlertCircle } from 'lucide-react';
import { useToast } from '@/components/ui/use-toast';
import { PATHS } from '@/lib/utils';

// Setup form component that uses Stripe Elements
const SetupForm = ({ account_id, clientSecret }: { account_id: string; clientSecret: string }) => {
  const stripe = useStripe();
  const elements = useElements();
  const [isLoading, setIsLoading] = useState(false);
  const [message, setMessage] = useState<string | null>(null);
  const [isSuccess, setIsSuccess] = useState(false);
  const { toast } = useToast();

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!stripe || !elements) {
      // Stripe.js hasn't loaded yet
      return;
    }

    setIsLoading(true);
    setMessage(null);

    // Confirm the setup
    const result = await stripe.confirmSetup({
      elements,
      confirmParams: {
        return_url: `${window.location.origin}/${account_id}/billing/payment-method/success`,
      },
      redirect: 'if_required',
    });

    if (result.error) {
      // Show error to your customer
      setMessage(result.error.message || 'An unexpected error occurred.');
      toast({
        variant: 'destructive',
        title: 'Setup failed',
        description: result.error.message || 'An unexpected error occurred.',
      });
    } else {
      // The setup has succeeded
      setIsSuccess(true);
      setMessage('Payment method successfully saved!');
      toast({
        variant: 'success',
        title: 'Setup successful',
        description: 'Your payment method has been saved for future payments.',
      });
    }

    setIsLoading(false);
  };

  return (
    <form onSubmit={handleSubmit}>
      <PaymentElement />
      
      <div className="mt-6">
        <Button 
          type="submit" 
          disabled={!stripe || isLoading || isSuccess} 
          className="w-full"
        >
          {isLoading ? (
            <>
              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              Saving...
            </>
          ) : (
            'Save Payment Method'
          )}
        </Button>
      </div>
      
      {message && (
        <div className={`mt-4 p-4 rounded-md flex items-center ${isSuccess ? 'bg-green-50 text-green-700' : 'bg-red-50 text-red-700'}`}>
          {isSuccess ? (
            <CheckCircle2 className="h-5 w-5 mr-2 text-green-500" />
          ) : (
            <AlertCircle className="h-5 w-5 mr-2 text-red-500" />
          )}
          <p>{message}</p>
          {/* go back to billing page if success */}
          {isSuccess && (
            <Button 
              variant="outline" 
              className="ml-auto"
              onClick={() => window.location.href = `/${account_id}/billing`}
            >
              Go to Billing
            </Button>
          )}
        </div>
      )}
    </form>
  );
};

// Main page component
export default function PaymentMethodPage({ params }: { params: { account_id: string } }) {
  const [clientSecret, setClientSecret] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const { toast } = useToast();

  useEffect(() => {
    // Create a setup intent when the component mounts
    const createSetupIntent = async () => {
      try {
        const response = await fetch(`${PATHS[process.env.NODE_ENV]}/api/v1/create-setup-intent`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({ account_id: params.account_id }),
        });

        if (!response.ok) {
          throw new Error('Failed to create setup intent');
        }

        const data = await response.json();
        setClientSecret(data.clientSecret);
      } catch (err) {
        console.error('Error creating setup intent:', err);
        setError('Failed to initialize payment setup. Please try again later.');
        toast({
          variant: 'destructive',
          title: 'Setup Error',
          description: 'Failed to initialize payment setup. Please try again later.',
        });
      } finally {
        setIsLoading(false);
      }
    };

    createSetupIntent();
  }, [params.account_id, toast]);

  const appearance = {
    theme: 'stripe',
    variables: {
      colorPrimary: '#10b981',
    },
  };

  return (
    <div className="container mx-auto py-10">
      <Card className="max-w-2xl mx-auto">
        <CardHeader>
          <CardTitle className="flex items-center">
            <CreditCard className="mr-2 h-6 w-6" />
            Payment Method Setup
          </CardTitle>
          <CardDescription>
            Add a payment method to enable automatic billing for your subscription
          </CardDescription>
        </CardHeader>
        
        <CardContent>
          {isLoading ? (
            <div className="flex justify-center items-center py-10">
              <Loader2 className="h-8 w-8 animate-spin text-primary" />
            </div>
          ) : error ? (
            <div className="bg-red-50 p-4 rounded-md text-red-700">
              <AlertCircle className="h-5 w-5 text-red-500 inline mr-2" />
              {error}
            </div>
          ) : clientSecret ? (
            <Elements options={{ clientSecret, appearance: { 
              theme: 'stripe' as const,
              variables: {
                colorPrimary: '#10b981',
              },
            }}} stripe={getStripe()}>
              <SetupForm account_id={params.account_id} clientSecret={clientSecret} />
            </Elements>
          ) : null}
        </CardContent>
        
        <CardFooter className="flex flex-col items-start text-sm text-gray-500">
          <p className="mb-2">
            Your payment information is securely processed by Stripe. We don't store your full card details on our servers.
          </p>
          <p>
            This payment method will be used for automatic renewal of your subscription when invoices are due.
          </p>
        </CardFooter>
      </Card>
    </div>
  );
}
