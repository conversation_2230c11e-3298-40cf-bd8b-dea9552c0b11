import { Button } from '@/components/ui/button';
import { getAccount, getAgents } from '@/lib/pocket';
import type { Metadata } from 'next';
import Link from 'next/link';
import { columns } from './components/columns';
import { DataTable } from './components/data-table';
import { PlusCircle, PlayCircle } from 'lucide-react';
import { Toaster } from '@/components/ui/toaster';
import { HoverCard, HoverCardContent, HoverCardTrigger } from '@/components/ui/hover-card';

export const metadata: Metadata = {
  title: 'Agents',
};

export default async ({ params }: { params: Record<string, string> }) => {
  const agentData = await getAgents(params.account_id);
  const account = await getAccount(params.account_id);
  const isDisabled = account.agent_creation_limit == 0 ? false : account.agent_creation_limit < agentData.length ? true : false;
  if (!agentData || agentData.length === 0) {
    return (
      <div className="h-full w-full flex flex-col justify-center items-center space-y-4">
        <div>Looks like you dont have any agents!</div>
        <Button variant={'secondary'} asChild>
          <Link href={`/${params.account_id}/agent/create-agent`}>
            <PlusCircle className="mr-2 h-5 w-5" />
            Create Agent
          </Link>
        </Button>
        <Button variant={'secondary'} asChild>
          <Link href={`/${params.account_id}/teams/create-create-team`}>
            <PlayCircle className="mr-2 h-5 w-5" />
            Manage Teams
          </Link>
        </Button>
      </div>
    );
  }

  return (
    <div className="mx-auto p-6 bg-gray-100 min-h-full">
      <div className="flex mb-4">
        <div className="flex-1">
          <div className="text-gray-800 text-xl font-bold sm:text-2xl">Agent List</div>
          <p className="text-gray-600 mt-2">Select the agent which you would like to edit or delete</p>
        </div>
        <div className="space-x-4">
          <Button variant="default" className="bg-orange-900" asChild>
            <Link href={`/${params.account_id}/teams/create-team`}>
              <PlayCircle className="mr-2 h-5 w-5" />
              Manage Teams
            </Link>
          </Button>
          <HoverCard openDelay={0}>
            <HoverCardTrigger>
              {isDisabled ? (
                <Button variant={'secondary'} disabled={true}>
                  <PlusCircle className="mr-2 h-5 w-5" />
                  Create Agent
                </Button>
              ) : (
                <Button variant={'secondary'} asChild>
                  <Link href={`agent/create-agent`}>
                    <PlusCircle className="mr-2 h-5 w-5" />
                    Create Agent
                  </Link>
                </Button>
              )}
            </HoverCardTrigger>
            {isDisabled && <HoverCardContent className="text-xs">Agent Creation limit exceeded ({account.agent_creation_limit} allowed)</HoverCardContent>}
          </HoverCard>
        </div>
      </div>
      <DataTable columns={columns} data={agentData} account_id={params.account_id} />
      <Toaster />
    </div>
  );
};
