import { getRoles, getUserById } from '@/lib/pocket';
import { EditAgent } from './components/EditAgent';

export const dynamic = 'force-dynamic';
export const fetchCache = 'default-no-store';
export default async ({ params, searchParams }: { params: Record<string, string>; searchParams: Record<string, string> }) => {
  const user = await getUserById(searchParams.id);
  const roles = await getRoles();

  return <EditAgent accountId={params.account_id} roles={roles} userData={user} />;
};
