'use client';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/components/ui/alert-dialog';
import { Button } from '@/components/ui/button';
import { Checkbox } from '@/components/ui/checkbox';
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from '@/components/ui/collapsible';
import { useToast } from '@/components/ui/use-toast';
import { updateAgent } from '@/lib/pocket';
import { User, UserViewAll } from '@/lib/types';
import { ChevronDown, Loader2, PlusCircle } from 'lucide-react';
import Link from 'next/link';
import { useParams } from 'next/navigation';
import { useEffect, useRef, useState } from 'react';

export const dynamic = 'force-dynamic';
export const fetchCache = 'default-no-store';

export function EditAgent({ accountId, roles, userData }: { accountId: string; roles: any; userData: User }) {
  const params = useParams<{ account_id: string }>();
  const ref = useRef<HTMLFormElement>(null);

  const lead_management_roles = roles.filter((role: any) => role.parent_role === 'Lead Management');
  const live_chat_roles = roles.filter((role: any) => role.parent_role === 'Live Chat');
  const template_roles = roles.filter((role: any) => role.parent_role === 'Templates');
  const flow_roles = roles.filter((role: any) => role.parent_role === 'Flows');
  const dashboard_roles = roles.filter((role: any) => role.parent_role === 'Dashboard');
  const campaign_roles = roles.filter((role: any) => role.parent_role === 'Campaigns');
  const reviews_roles = roles.filter((role: any) => role.parent_role === 'Reviews');
  const reports_roles = roles.filter((role: any) => role.parent_role === 'Reports');
  const [viewAll_array, setViewAll_array] = useState<UserViewAll[]>([]);
  const { toast } = useToast();

  const lead_management_super_access_id = roles.find((role: any) => role.name === 'Lead Management-Super Access')?.id;
  const template_super_access_id = roles.find((role: any) => role.name === 'Templates-Super Access')?.id;
  const campaigns_super_access_id = roles.find((role: any) => role.name === 'Campaigns-Super Access')?.id;
  const live_chat_super_access_id = roles.find((role: any) => role.name === 'Live Chat-Super Access')?.id;

  interface FormData {
    userName: string;
    email: string;
    phone: string;
    password: string;
    confirmPassword: string;
  }

  const [alertOpen, setAlertOpen] = useState(false);
  const [isPending, setIsPending] = useState(false);
  const [formData, setFormData] = useState<FormData>({
    userName: '',
    email: '',
    phone: '',
    password: '',
    confirmPassword: '',
  });

  interface SelectedOptions {
    ['lead-management']: string[];
    ['live-chat']: string[];
    templates: string[];
    campaigns: string[];
    rate_reviews: string[];
    flows: string[];
    dashboard: string[];
    reports: string[];
  }

  const [selectedOptions, setSelectedOptions] = useState<SelectedOptions>({
    ['lead-management']: [],
    ['live-chat']: [],
    templates: [],
    campaigns: [],
    rate_reviews: [],
    flows: [],
    dashboard: [],
    reports: [],
  });

  const rolesArray = Object.values(selectedOptions).flat();

  const dashboardOptions: RoleType[] = (Object.keys(selectedOptions) as RoleType[]).filter((key) => selectedOptions[key].length > 0);

  const [page, setPage] = useState(1);
  const [numberCode, setNumberCode] = useState({
    code: '+971',
    emoji: '🇦🇪',
    phoneLength: 9,
  });

  const handleInputChange = (name: string, value: string) => {
    setFormData((prev) => ({
      ...prev,
      [name]: value,
    }));
  };

  const handleAgentUpdation = async () => {
    setIsPending(true);
    await updateAgent(userData.id, accountId, formData, numberCode, rolesArray, dashboardOptions, viewAll_array);
    toast({
      variant: 'success',
      description: 'The agent has been updated successfully',
    });
    setIsPending(false);
    // setAlertOpen(true);
  };

  type RoleType = 'lead-management' | 'live-chat' | 'templates' | 'campaigns' | 'rate_reviews' | 'flows' | 'dashboard' | 'reports';

  const handleCheckboxChange = (role: RoleType, value: string, checked: boolean) => {
    setSelectedOptions((prev) => {
      const newOptions = { ...prev };
      if (checked) {
        newOptions[role] = [...newOptions[role], value];
      } else {
        newOptions[role] = newOptions[role].filter((item) => item !== value);
      }
      return newOptions;
    });
  };

  const handleSuperAccessChange = (role: string, checked: boolean, roleOptions: any[]) => {
    setSelectedOptions((prev: any) => {
      const newOptions = { ...prev };
      if (checked) {
        newOptions[role] = roleOptions.map((option) => option.value);
      } else {
        newOptions[role] = [];
      }
      return newOptions;
    });
  };

  const handleViewAllChange = (role: UserViewAll, checked: boolean) => {
    let newOptions: UserViewAll[] = viewAll_array ? [...viewAll_array] : [];
    if (checked) {
      newOptions.push(role);
      setViewAll_array(newOptions);
    } else {
      newOptions = newOptions.filter((item: string) => item != role);
      setViewAll_array(newOptions);
    }
  };

  useEffect(() => {
    setFormData({
      userName: userData.username,
      email: userData.email,
      phone: '',
      password: '',
      confirmPassword: '',
    });
    const updatedSelectedOptions = {
      ['lead-management']: lead_management_roles.filter((role: any) => userData.roles?.includes(role.id)).map((role: any) => role.id),
      ['live-chat']: live_chat_roles.filter((role: any) => userData?.roles?.includes(role.id)).map((role: any) => role.id),
      templates: template_roles.filter((role: any) => userData?.roles?.includes(role.id)).map((role: any) => role.id),
      campaigns: campaign_roles.filter((role: any) => userData?.roles?.includes(role.id)).map((role: any) => role.id),
      rate_reviews: reviews_roles.filter((role: any) => userData?.roles?.includes(role.id)).map((role: any) => role.id),
      flows: flow_roles.filter((role: any) => userData?.roles?.includes(role.id)).map((role: any) => role.id),
      dashboard: dashboard_roles.filter((role: any) => userData?.roles?.includes(role.id)).map((role: any) => role.id),
      reports: reports_roles.filter((role: any) => userData?.roles?.includes(role.id)).map((role: any) => role.id),
    };

    setSelectedOptions(updatedSelectedOptions);
    setViewAll_array(userData?.view_all ?? []);
  }, []);

  return (
    <div className="mx-auto p-6 bg-gray-100 min-h-full">
      <AlertDialog open={alertOpen} onOpenChange={setAlertOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Agent created!!</AlertDialogTitle>
            <AlertDialogDescription>The agent has been updated successfully</AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <Link href={`/${params.account_id}/agent`}>
              <AlertDialogAction onClick={() => setAlertOpen(false)}>OK</AlertDialogAction>
            </Link>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
      <div className="flex mb-4">
        <div className="flex-1">
          <div className="text-gray-800 text-xl font-bold sm:text-2xl">Assign Roles to {userData.name}</div>
          <p className="text-gray-600 mt-2">Check any roles and then click on update</p>
        </div>
        <div className="flex items-center space-x-2">
          <Button variant={'link'} asChild>
            <Link href={`/${params.account_id}/agent`}>Go Back</Link>
          </Button>
        </div>
      </div>

      <div className="flex flex-row">
        <div className="flex flex-col gap-2 w-1/2 py-2">
          <Collapsible defaultOpen={true} className=" bg-white border border-gray-300 ">
            <CollapsibleTrigger className="flex items-center w-full p-2 bg-gray-200 ">
              <div className="space-x-2 flex-1 flex items-center">
                {/* <MessageSquare className="" /> */}
                <div className=" font-bold">Lead Management</div>
              </div>
              <div>
                <ChevronDown />
              </div>
            </CollapsibleTrigger>
            <CollapsibleContent className="py-5 px-2 text-sm text-gray-600 bg-white rounded-md CollapsibleContent">
              <div className="flex-col space-y-5 flex-wrap gap-5">
                <div className="flex items-center group">
                  <label
                    htmlFor="lead_management_super_access"
                    className="group-hover:cursor-pointer w-40 text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                  >
                    Super Access
                  </label>
                  <Checkbox
                    checked={selectedOptions['lead-management'].includes(lead_management_super_access_id)}
                    id="lead_management_super_access"
                    onCheckedChange={(checked) =>
                      handleSuperAccessChange(
                        'lead-management',
                        checked as boolean,
                        lead_management_roles.map((role: any) => {
                          return { value: role.id };
                        })
                      )
                    }
                  />
                </div>

                {lead_management_roles.map((role: any) => {
                  if (role.name !== 'Lead Management-Super Access') {
                    return (
                      <div key={role.id} className="flex items-center group">
                        <label
                          htmlFor={`lead_management_${role.id}`}
                          className="group-hover:cursor-pointer w-40 text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                        >
                          {role.name.split('-')[1]}
                        </label>
                        <Checkbox
                          id={`lead_management_${role.id}`}
                          checked={selectedOptions['lead-management'].includes(role.id)}
                          onCheckedChange={(checked) => handleCheckboxChange('lead-management', role.id, checked as boolean)}
                        />
                      </div>
                    );
                  }
                })}
                <div className="flex items-center group">
                  <label
                    htmlFor="lead_management_view_all"
                    className="group-hover:cursor-pointer w-40 text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                  >
                    View All
                  </label>
                  <Checkbox
                    checked={viewAll_array?.includes('lead-management')}
                    id="lead_management_view_all"
                    onCheckedChange={(checked) => handleViewAllChange('lead-management', checked as boolean)}
                  />
                </div>
              </div>
            </CollapsibleContent>
          </Collapsible>

          <Collapsible defaultOpen={true} className=" bg-white border border-gray-300 ">
            <CollapsibleTrigger className="flex items-center w-full p-2 bg-gray-200 ">
              <div className="space-x-2 flex-1 flex items-center">
                {/* <MessageSquare className="" /> */}
                <div className=" font-bold">Live Chat</div>
              </div>
              <div>
                <ChevronDown />
              </div>
            </CollapsibleTrigger>
            <CollapsibleContent className="py-5 px-2 text-sm text-gray-600 bg-white rounded-md CollapsibleContent">
              <div className="flex-col space-y-5 flex-wrap gap-5">
                <div className="flex items-center group">
                  <label
                    htmlFor="live_chat_super_access"
                    className="group-hover:cursor-pointer w-40 text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                  >
                    Super Access
                  </label>
                  <Checkbox
                    checked={selectedOptions['live-chat'].includes(live_chat_super_access_id)}
                    id="live_chat_super_access"
                    onCheckedChange={(checked) =>
                      handleSuperAccessChange(
                        'live-chat',
                        checked as boolean,
                        live_chat_roles.map((role: any) => {
                          return { value: role.id };
                        })
                      )
                    }
                  />
                </div>

                {live_chat_roles.map((role: any) => {
                  if (role.name !== 'Live Chat-Super Access') {
                    return (
                      <div key={role.id} className="flex items-center group">
                        <label
                          htmlFor={`live_chat_${role.id}`}
                          className="group-hover:cursor-pointer w-40 text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                        >
                          {role.name.split('-')[1]}
                        </label>
                        <Checkbox
                          id={`live_chat_${role.id}`}
                          checked={selectedOptions['live-chat'].includes(role.id)}
                          onCheckedChange={(checked) => handleCheckboxChange('live-chat', role.id, checked as boolean)}
                        />
                      </div>
                    );
                  }
                })}
                <div className="flex items-center group">
                  <label
                    htmlFor="live_chat_view_all"
                    className="group-hover:cursor-pointer w-40 text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                  >
                    View All
                  </label>

                  <Checkbox
                    checked={viewAll_array?.includes('live-chat')}
                    id="live_chat_view_all"
                    onCheckedChange={(checked) => handleViewAllChange('live-chat', checked as boolean)}
                  />
                </div>
              </div>
            </CollapsibleContent>
          </Collapsible>

          {/* <Collapsible defaultOpen={true} className=" bg-white border border-gray-300 ">
            <CollapsibleTrigger className="flex items-center w-full p-2 bg-gray-200 ">
              <div className="space-x-2 flex-1 flex items-center">
                <div className=" font-bold">Reports</div>
              </div>
              <div>
                <ChevronDown />
              </div>
            </CollapsibleTrigger>
            <CollapsibleContent className="py-5 px-2 text-sm text-gray-600 bg-white rounded-md CollapsibleContent">
              <div className="flex-col space-y-5 flex-wrap gap-5">
                <div className="flex items-center group">
                  <label
                    htmlFor="reports_super_access"
                    className="group-hover:cursor-pointer w-40 text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                  >
                    Super Access
                  </label>
                  <Checkbox
                    id="reports_super_access"
                    onCheckedChange={(checked) =>
                      handleSuperAccessChange(
                        'reports',
                        checked as boolean,
                        reports_roles.map((role: any) => {
                          return { value: role.id };
                        })
                      )
                    }
                  />
                </div>

                {reports_roles.map((role: any) => (
                  <div key={role.id} className="flex items-center group">
                    <label
                      htmlFor={`reports_${role.id}`}
                      className="group-hover:cursor-pointer w-40 text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                    >
                      {role.name.split('-')[1]}
                    </label>
                    <Checkbox
                      id={`reports_${role.id}`}
                      checked={selectedOptions.reports.includes(role.id)}
                      onCheckedChange={(checked) => handleCheckboxChange('reports', role.id, checked as boolean)}
                    />
                  </div>
                ))}
              </div>
            </CollapsibleContent>
          </Collapsible> */}
        </div>

        <div className="flex flex-col w-1/2 p-2 gap-2">
          <Collapsible defaultOpen={true} className=" bg-white border border-gray-300 ">
            <CollapsibleTrigger className="flex items-center w-full p-2 bg-gray-200 ">
              <div className="space-x-2 flex-1 flex items-center">
                {/* <MessageSquare className="" /> */}
                <div className=" font-bold">Templates</div>
              </div>
              <div>
                <ChevronDown />
              </div>
            </CollapsibleTrigger>
            <CollapsibleContent className="py-5 px-2 text-sm text-gray-600 bg-white rounded-md CollapsibleContent">
              <div className="flex-col space-y-5 flex-wrap gap-5">
                <div className="flex items-center group">
                  <label
                    htmlFor="templates_super_access"
                    className="group-hover:cursor-pointer w-40 text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                  >
                    Super Access
                  </label>
                  <Checkbox
                    checked={selectedOptions['templates'].includes(template_super_access_id)}
                    id="templates_super_access"
                    onCheckedChange={(checked) =>
                      handleSuperAccessChange(
                        'templates',
                        checked as boolean,
                        template_roles.map((role: any) => {
                          return { value: role.id };
                        })
                      )
                    }
                  />
                </div>

                {template_roles.map((role: any) => {
                  if (role.name !== 'Templates-Super Access') {
                    return (
                      <div key={role.id} className="flex items-center group">
                        <label
                          htmlFor={`templates_${role.id}`}
                          className="group-hover:cursor-pointer w-40 text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                        >
                          {role.name.split('-')[1]}
                        </label>
                        <Checkbox
                          id={`templates_${role.id}`}
                          checked={selectedOptions.templates.includes(role.id)}
                          onCheckedChange={(checked) => handleCheckboxChange('templates', role.id, checked as boolean)}
                        />
                      </div>
                    );
                  }
                })}
                <div className="flex items-center group">
                  <label
                    htmlFor="templates_view_all"
                    className="group-hover:cursor-pointer w-40 text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                  >
                    View All
                  </label>
                  <Checkbox
                    checked={viewAll_array?.includes('templates')}
                    id="templates_view_all"
                    onCheckedChange={(checked) => handleViewAllChange('templates', checked as boolean)}
                  />
                </div>
              </div>
            </CollapsibleContent>
          </Collapsible>

          <Collapsible defaultOpen={true} className=" bg-white border border-gray-300 ">
            <CollapsibleTrigger className="flex items-center w-full p-2 bg-gray-200 ">
              <div className="space-x-2 flex-1 flex items-center">
                {/* <MessageSquare className="" /> */}
                <div className=" font-bold">Campaigns</div>
              </div>
              <div>
                <ChevronDown />
              </div>
            </CollapsibleTrigger>
            <CollapsibleContent className="py-5 px-2 text-sm text-gray-600 bg-white rounded-md CollapsibleContent">
              <div className="flex-col space-y-5 flex-wrap gap-5">
                <div className="flex items-center group">
                  <label
                    htmlFor="campaigns_super_access"
                    className="group-hover:cursor-pointer w-40 text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                  >
                    Super Access
                  </label>
                  <Checkbox
                    checked={selectedOptions['campaigns'].includes(campaigns_super_access_id)}
                    id="campaigns_super_access"
                    onCheckedChange={(checked) =>
                      handleSuperAccessChange(
                        'campaigns',
                        checked as boolean,
                        campaign_roles.map((role: any) => {
                          return { value: role.id };
                        })
                      )
                    }
                  />
                </div>

                {campaign_roles.map((role: any) => {
                  if (role.name !== 'Campaigns-Super Access') {
                    return (
                      <div key={role.id} className="flex items-center group">
                        <label
                          htmlFor={`campaigns_${role.id}`}
                          className="group-hover:cursor-pointer w-40 text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                        >
                          {role.name.split('-')[1]}
                        </label>
                        <Checkbox
                          id={`campaigns_${role.id}`}
                          checked={selectedOptions.campaigns.includes(role.id)}
                          onCheckedChange={(checked) => handleCheckboxChange('campaigns', role.id, checked as boolean)}
                        />
                      </div>
                    );
                  }
                })}
                <div className="flex items-center group">
                  <label
                    htmlFor="campaigns_view_all"
                    className="group-hover:cursor-pointer w-40 text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                  >
                    View All
                  </label>
                  <Checkbox
                    checked={viewAll_array?.includes('campaigns')}
                    id="campaigns_view_all"
                    onCheckedChange={(checked) => handleViewAllChange('campaigns', checked as boolean)}
                  />
                </div>
              </div>
            </CollapsibleContent>
          </Collapsible>

          {/* <Collapsible defaultOpen={true} className=" bg-white border border-gray-300 ">
            <CollapsibleTrigger className="flex items-center w-full p-2 bg-gray-200 ">
              <div className="space-x-2 flex-1 flex items-center">
                <div className=" font-bold">Flows</div>
              </div>
              <div>
                <ChevronDown />
              </div>
            </CollapsibleTrigger>
            <CollapsibleContent className="py-5 px-2 text-sm text-gray-600 bg-white rounded-md CollapsibleContent">
              <div className="flex-col space-y-5 flex-wrap gap-5">
                <div className="flex items-center group">
                  <label
                    htmlFor="flows_super_access"
                    className="group-hover:cursor-pointer w-40 text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                  >
                    Super Access
                  </label>
                  <Checkbox
                    id="flows_super_access"
                    onCheckedChange={(checked) =>
                      handleSuperAccessChange(
                        'flows',
                        checked as boolean,
                        flow_roles.map((role: any) => {
                          return { value: role.id };
                        })
                      )
                    }
                  />
                </div>

                {flow_roles.map((role: any) => (
                  <div key={role.id} className="flex items-center group">
                    <label
                      htmlFor={`flows_${role.id}`}
                      className="group-hover:cursor-pointer w-40 text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                    >
                      {role.name.split('-')[1]}
                    </label>
                    <Checkbox
                      id={`flows_${role.id}`}
                      checked={selectedOptions.flows.includes(role.id)}
                      onCheckedChange={(checked) => handleCheckboxChange('flows', role.id, checked as boolean)}
                    />
                  </div>
                ))}
              </div>
            </CollapsibleContent>
          </Collapsible> */}

          {/* <Collapsible defaultOpen={true} className=" bg-white border border-gray-300 ">
            <CollapsibleTrigger className="flex items-center w-full p-2 bg-gray-200 ">
              <div className="space-x-2 flex-1 flex items-center">
                <div className=" font-bold">Rate and Review</div>
              </div>
              <div>
                <ChevronDown />
              </div>
            </CollapsibleTrigger>
            <CollapsibleContent className="py-5 px-2 text-sm text-gray-600 bg-white rounded-md CollapsibleContent">
              <div className="flex-col space-y-5 flex-wrap gap-5">
                <div className="flex items-center group">
                  <label
                    htmlFor="reviews_super_access"
                    className="group-hover:cursor-pointer w-40 text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                  >
                    Super Access
                  </label>
                  <Checkbox
                    id="reviews_super_access"
                    onCheckedChange={(checked) =>
                      handleSuperAccessChange(
                        'rate_reviews',
                        checked as boolean,
                        reviews_roles.map((role: any) => {
                          return { value: role.id };
                        })
                      )
                    }
                  />
                </div>

                {reviews_roles.map((role: any) => (
                  <div key={role.id} className="flex items-center group">
                    <label
                      htmlFor={`reviews_${role.id}`}
                      className="group-hover:cursor-pointer w-40 text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                    >
                      {role.name.split('-')[1]}
                    </label>
                    <Checkbox
                      id={`reviews_${role.id}`}
                      checked={selectedOptions.rate_reviews.includes(role.id)}
                      onCheckedChange={(checked) => handleCheckboxChange('rate_reviews', role.id, checked as boolean)}
                    />
                  </div>
                ))}
              </div>
            </CollapsibleContent>
          </Collapsible> */}
        </div>
      </div>

      <div className="flex justify-end">
        {isPending ? (
          <Button disabled>
            Saving Changes <Loader2 className="ml-2 animate-spin" />
          </Button>
        ) : (
          <Button disabled={isPending} onClick={handleAgentUpdation} variant={'secondary'}>
            Save Changes
          </Button>
        )}
      </div>
    </div>
  );
}
