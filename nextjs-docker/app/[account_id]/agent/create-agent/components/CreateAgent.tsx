'use client';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/components/ui/alert-dialog';
import { Button } from '@/components/ui/button';
import { Card, CardTitle } from '@/components/ui/card';
import { Checkbox } from '@/components/ui/checkbox';
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from '@/components/ui/collapsible';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { useToast } from '@/components/ui/use-toast';
import { createAgentAction } from '@/lib/actions';
import { ChevronDown, Loader2 } from 'lucide-react';
import Link from 'next/link';
import { useParams } from 'next/navigation';
import { useRef, useState } from 'react';
import { useFormStatus } from 'react-dom';

export function CreateAgent({ accountId, roles }: { accountId: string; roles: any }) {
  const params = useParams<{ account_id: string }>();
  const ref = useRef<HTMLFormElement>(null);

  const lead_management_roles = roles.filter((role: any) => role.parent_role === 'Lead Management');
  const live_chat_roles = roles.filter((role: any) => role.parent_role === 'Live Chat');
  const template_roles = roles.filter((role: any) => role.parent_role === 'Templates');
  const flow_roles = roles.filter((role: any) => role.parent_role === 'Flows');
  const dashboard_roles = roles.filter((role: any) => role.parent_role === 'Dashboard');
  const campaign_roles = roles.filter((role: any) => role.parent_role === 'Campaigns');
  const reviews_roles = roles.filter((role: any) => role.parent_role === 'Reviews');
  const reports_roles = roles.filter((role: any) => role.parent_role === 'Reports');

  const lead_management_super_access_id = roles.find((role: any) => role.name === 'Lead Management-Super Access')?.id;
  const template_super_access_id = roles.find((role: any) => role.name === 'Templates-Super Access')?.id;
  const campaigns_super_access_id = roles.find((role: any) => role.name === 'Campaigns-Super Access')?.id;
  const live_chat_super_access_id = roles.find((role: any) => role.name === 'Live Chat-Super Access')?.id;
  const { toast } = useToast();

  interface FormData {
    userName: string;
    email: string;
    phone: string;
    password: string;
    confirmPassword: string;
  }

  const [alertOpen, setAlertOpen] = useState(false);
  const [isPending, setIsPending] = useState(false);
  const [formData, setFormData] = useState<FormData>({
    userName: '',
    email: '',
    phone: '',
    password: '',
    confirmPassword: '',
  });

  interface SelectedOptions {
    ['lead-management']: string[];
    ['live-chat']: string[];
    templates: string[];
    campaigns: string[];
    rate_reviews: string[];
    flows: string[];
    dashboard: string[];
    reports: string[];
  }

  const [selectedOptions, setSelectedOptions] = useState<SelectedOptions>({
    ['lead-management']: [],
    ['live-chat']: [],
    templates: [],
    campaigns: [],
    rate_reviews: [],
    flows: [],
    dashboard: [],
    reports: [],
  });

  const rolesArray = Object.values(selectedOptions).flat();
  const [viewAll_array, setViewAll_array] = useState<viewAll[]>([]);
  const dashboardOptions: RoleType[] = (Object.keys(selectedOptions) as RoleType[]).filter((key) => selectedOptions[key].length > 0);

  const [page, setPage] = useState(1);
  const [numberCode, setNumberCode] = useState({
    code: '+971',
    emoji: '🇦🇪',
    phoneLength: 9,
  });

  const handleInputChange = (name: string, value: string) => {
    setFormData((prev) => ({
      ...prev,
      [name]: value,
    }));
  };

  const handleAgentCreation = async () => {
    setIsPending(true);
    try {
      await createAgentAction(accountId, formData, numberCode, rolesArray, dashboardOptions, viewAll_array);
      toast({
        variant: 'success',
        description: 'The agent has been created successfully',
      });
    } catch (error) {
      toast({
        variant: 'destructive',
        description: 'There was an error in creating agent',
      });
    }
    setIsPending(false);
    // setAlertOpen(true);
  };

  type RoleType = 'lead-management' | 'live-chat' | 'templates' | 'campaigns' | 'rate_reviews' | 'flows' | 'dashboard' | 'reports';
  type viewAll = 'lead-management' | 'live-chat' | 'templates' | 'campaigns' | 'rate_reviews' | 'flows' | 'dashboard' | 'reports';
  const handleCheckboxChange = (role: RoleType, value: string, checked: boolean) => {
    setSelectedOptions((prev) => {
      const newOptions = { ...prev };
      if (checked) {
        newOptions[role] = [...newOptions[role], value];
      } else {
        newOptions[role] = newOptions[role].filter((item) => item !== value);
      }
      return newOptions;
    });
  };

  const handleSuperAccessChange = (role: string, checked: boolean, roleOptions: any[]) => {
    setSelectedOptions((prev: any) => {
      const newOptions = { ...prev };
      if (checked) {
        newOptions[role] = roleOptions.map((option) => option.value);
      } else {
        newOptions[role] = [];
      }
      return newOptions;
    });
  };

  const handleViewAllChange = (role: viewAll, checked: boolean) => {
    let newOptions: viewAll[] = [...viewAll_array];
    if (checked) {
      newOptions.push(role);
      setViewAll_array(newOptions);
    } else {
      newOptions = newOptions.filter((item: string) => item != role);
      setViewAll_array(newOptions);
    }
  };

  const { pending } = useFormStatus();
  if (page === 1) {
    return (
      <div className="h-full bg-gray-50 w-full p-5">
        <div className="absolute right-3">
          {/* <CardTitle className="text-3xl text-center">Create Agent</CardTitle> */}
          <Link prefetch={false} href={`/${params.account_id}/agent`}>
            <Button type="button">Go to agent management</Button>
          </Link>
        </div>
        <form
          ref={ref}
          onSubmit={(e) => {
            e.preventDefault();
            setPage(2);
          }}
        >
          <div className="flex justify-center items-center mt-20">
            <Card className="w-[40%] p-4">
              <CardTitle className="text-2xl text-center">Create Agent</CardTitle>

              <div className="flex justify-center">
                <div className="flex flex-col w-full">
                  <Label htmlFor="userName" className="text-sm text-gray-600 font-semibold mb-2">
                    Username
                  </Label>
                  <Input
                    name="userName"
                    value={formData.userName}
                    placeholder="John Doe"
                    required
                    className="border border-gray-300  p-2"
                    onChange={(e) => handleInputChange(e.target.name, e.target.value)}
                  />
                  <Label htmlFor="email" className="text-sm text-gray-600 font-semibold mt-4 mb-2">
                    Email
                  </Label>
                  <Input
                    type="email"
                    name="email"
                    value={formData.email}
                    placeholder="<EMAIL>"
                    required
                    className="border border-gray-300  p-2"
                    onChange={(e) => handleInputChange(e.target.name, e.target.value)}
                  />
                </div>
              </div>
              <div className="flex justify-center mt-8">
                <Button disabled={formData.confirmPassword !== formData.password}>Next</Button>
              </div>
            </Card>
          </div>
        </form>
      </div>
    );
  } else if (page === 2) {
    return (
      <div className="p-5 bg-gray-100 h-full overflow-y-scroll">
        <AlertDialog open={alertOpen} onOpenChange={setAlertOpen}>
          <AlertDialogContent>
            <AlertDialogHeader>
              <AlertDialogTitle>Agent created!!</AlertDialogTitle>
              <AlertDialogDescription>The agent has been created successfully</AlertDialogDescription>
            </AlertDialogHeader>
            <AlertDialogFooter>
              <Link href={`/${params.account_id}/agent`}>
                <AlertDialogAction onClick={() => setAlertOpen(false)}>OK</AlertDialogAction>
              </Link>
            </AlertDialogFooter>
          </AlertDialogContent>
        </AlertDialog>
        <div className="text-2xl font-semibold px-2 mb-6">Assign Roles to Agent</div>

        <div className="flex flex-row">
          <div className="flex flex-col gap-2 w-1/2 py-2">
            <Collapsible defaultOpen={true} className=" bg-white border border-gray-300 ">
              <CollapsibleTrigger className="flex items-center w-full p-2 bg-gray-200 ">
                <div className="space-x-2 flex-1 flex items-center">
                  {/* <MessageSquare className="" /> */}
                  <div className=" font-bold">Lead Management</div>
                </div>
                <div>
                  <ChevronDown />
                </div>
              </CollapsibleTrigger>
              <CollapsibleContent className="py-5 px-2 text-sm text-gray-600 bg-white rounded-md">
                <div className="flex-col space-y-5 flex-wrap gap-5">
                  <div className="flex items-center group">
                    <label
                      htmlFor="lead_management_super_access"
                      className="group-hover:cursor-pointer w-40 text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                    >
                      Super Access
                    </label>
                    <Checkbox
                      checked={selectedOptions['lead-management'].includes(lead_management_super_access_id)}
                      id="lead_management_super_access"
                      onCheckedChange={(checked) =>
                        handleSuperAccessChange(
                          'lead-management',
                          checked as boolean,
                          lead_management_roles.map((role: any) => {
                            return { value: role.id };
                          })
                        )
                      }
                    />
                  </div>

                  {lead_management_roles.map((role: any) => {
                    if (role.name !== 'Lead Management-Super Access') {
                      return (
                        <div key={role.id} className="flex items-center group">
                          <label
                            htmlFor={`lead_management_${role.id}`}
                            className="group-hover:cursor-pointer w-40 text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                          >
                            {role.name.split('-')[1]}
                          </label>
                          <Checkbox
                            id={`lead_management_${role.id}`}
                            checked={selectedOptions['lead-management'].includes(role.id)}
                            onCheckedChange={(checked) => handleCheckboxChange('lead-management', role.id, checked as boolean)}
                          />
                        </div>
                      );
                    }
                  })}
                  <div className="flex items-center group">
                    <label
                      htmlFor="lead_management_view_all"
                      className="group-hover:cursor-pointer w-40 text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                    >
                      View All
                    </label>
                    <Checkbox id="lead_management_view_all" onCheckedChange={(checked) => handleViewAllChange('lead-management', checked as boolean)} />
                  </div>
                </div>
              </CollapsibleContent>
            </Collapsible>

            <Collapsible defaultOpen={true} className=" bg-white border border-gray-300 ">
              <CollapsibleTrigger className="flex items-center w-full p-2 bg-gray-200 ">
                <div className="space-x-2 flex-1 flex items-center">
                  {/* <MessageSquare className="" /> */}
                  <div className=" font-bold">Live Chat</div>
                </div>
                <div>
                  <ChevronDown />
                </div>
              </CollapsibleTrigger>
              <CollapsibleContent className="py-5 px-2 text-sm text-gray-600 bg-white rounded-md">
                <div className="flex-col space-y-5 flex-wrap gap-5">
                  <div className="flex items-center group">
                    <label
                      htmlFor="live_chat_super_access"
                      className="group-hover:cursor-pointer w-40 text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                    >
                      Super Access
                    </label>
                    <Checkbox
                      checked={selectedOptions['live-chat'].includes(live_chat_super_access_id)}
                      id="live_chat_super_access"
                      onCheckedChange={(checked) =>
                        handleSuperAccessChange(
                          'live-chat',
                          checked as boolean,
                          live_chat_roles.map((role: any) => {
                            return { value: role.id };
                          })
                        )
                      }
                    />
                  </div>

                  {live_chat_roles.map((role: any) => {
                    if (role.name !== 'Live Chat-Super Access') {
                      return (
                        <div key={role.id} className="flex items-center group">
                          <label
                            htmlFor={`live_chat_${role.id}`}
                            className="group-hover:cursor-pointer w-40 text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                          >
                            {role.name.split('-')[1]}
                          </label>
                          <Checkbox
                            id={`live_chat_${role.id}`}
                            checked={selectedOptions['live-chat'].includes(role.id)}
                            onCheckedChange={(checked) => handleCheckboxChange('live-chat', role.id, checked as boolean)}
                          />
                        </div>
                      );
                    }
                  })}
                  <div className="flex items-center group">
                    <label
                      htmlFor="live_chat_view_all"
                      className="group-hover:cursor-pointer w-40 text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                    >
                      View All
                    </label>
                    <Checkbox id="live_chat_view_all" onCheckedChange={(checked) => handleViewAllChange('live-chat', checked as boolean)} />
                  </div>
                </div>
              </CollapsibleContent>
            </Collapsible>

            {/* <Collapsible defaultOpen={true} className=" bg-white border border-gray-300 ">
              <CollapsibleTrigger className="flex items-center w-full p-2 bg-gray-200 ">
                <div className="space-x-2 flex-1 flex items-center">
                  <div className=" font-bold">Reports</div>
                </div>
                <div>
                  <ChevronDown />
                </div>
              </CollapsibleTrigger>
              <CollapsibleContent className="py-5 px-2 text-sm text-gray-600 bg-white rounded-md">
                <div className="flex-col space-y-5 flex-wrap gap-5">
                  <div className="flex items-center group">
                    <label
                      htmlFor="reports_super_access"
                      className="group-hover:cursor-pointer w-40 text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                    >
                      Super Access
                    </label>
                    <Checkbox
                      id="reports_super_access"
                      onCheckedChange={(checked) =>
                        handleSuperAccessChange(
                          'reports',
                          checked as boolean,
                          reports_roles.map((role: any) => {
                            return { value: role.id };
                          })
                        )
                      }
                    />
                  </div>

                  {reports_roles.map((role: any) => (
                    <div key={role.id} className="flex items-center group">
                      <label
                        htmlFor={`reports_${role.id}`}
                        className="group-hover:cursor-pointer w-40 text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                      >
                        {role.name.split('-')[1]}
                      </label>
                      <Checkbox
                        id={`reports_${role.id}`}
                        checked={selectedOptions.reports.includes(role.id)}
                        onCheckedChange={(checked) => handleCheckboxChange('reports', role.id, checked as boolean)}
                      />
                    </div>
                  ))}
                </div>
              </CollapsibleContent>
            </Collapsible> */}
          </div>

          <div className="flex flex-col w-1/2 p-2 gap-2">
            <Collapsible defaultOpen={true} className=" bg-white border border-gray-300 ">
              <CollapsibleTrigger className="flex items-center w-full p-2 bg-gray-200 ">
                <div className="space-x-2 flex-1 flex items-center">
                  {/* <MessageSquare className="" /> */}
                  <div className=" font-bold">Templates</div>
                </div>
                <div>
                  <ChevronDown />
                </div>
              </CollapsibleTrigger>
              <CollapsibleContent className="py-5 px-2 text-sm text-gray-600 bg-white rounded-md">
                <div className="flex-col space-y-5 flex-wrap gap-5">
                  <div className="flex items-center group">
                    <label
                      htmlFor="templates_super_access"
                      className="group-hover:cursor-pointer w-40 text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                    >
                      Super Access
                    </label>
                    <Checkbox
                      checked={selectedOptions['templates'].includes(template_super_access_id)}
                      id="templates_super_access"
                      onCheckedChange={(checked) =>
                        handleSuperAccessChange(
                          'templates',
                          checked as boolean,
                          template_roles.map((role: any) => {
                            return { value: role.id };
                          })
                        )
                      }
                    />
                  </div>

                  {template_roles.map((role: any) => {
                    if (role.name !== 'Templates-Super Access') {
                      return (
                        <div key={role.id} className="flex items-center group">
                          <label
                            htmlFor={`templates_${role.id}`}
                            className="group-hover:cursor-pointer w-40 text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                          >
                            {role.name.split('-')[1]}
                          </label>
                          <Checkbox
                            id={`templates_${role.id}`}
                            checked={selectedOptions.templates.includes(role.id)}
                            onCheckedChange={(checked) => handleCheckboxChange('templates', role.id, checked as boolean)}
                          />
                        </div>
                      );
                    }
                  })}
                  <div className="flex items-center group">
                    <label
                      htmlFor="templates_view_all"
                      className="group-hover:cursor-pointer w-40 text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                    >
                      View All
                    </label>
                    <Checkbox id="templates_view_all" onCheckedChange={(checked) => handleViewAllChange('templates', checked as boolean)} />
                  </div>
                </div>
              </CollapsibleContent>
            </Collapsible>

            <Collapsible defaultOpen={true} className=" bg-white border border-gray-300 ">
              <CollapsibleTrigger className="flex items-center w-full p-2 bg-gray-200 ">
                <div className="space-x-2 flex-1 flex items-center">
                  {/* <MessageSquare className="" /> */}
                  <div className=" font-bold">Campaigns</div>
                </div>
                <div>
                  <ChevronDown />
                </div>
              </CollapsibleTrigger>
              <CollapsibleContent className="py-5 px-2 text-sm text-gray-600 bg-white rounded-md">
                <div className="flex-col space-y-5 flex-wrap gap-5">
                  <div className="flex items-center group">
                    <label
                      htmlFor="campaigns_super_access"
                      className="group-hover:cursor-pointer w-40 text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                    >
                      Super Access
                    </label>
                    <Checkbox
                      checked={selectedOptions['campaigns'].includes(campaigns_super_access_id)}
                      id="campaigns_super_access"
                      onCheckedChange={(checked) =>
                        handleSuperAccessChange(
                          'campaigns',
                          checked as boolean,
                          campaign_roles.map((role: any) => {
                            return { value: role.id };
                          })
                        )
                      }
                    />
                  </div>

                  {campaign_roles.map((role: any) => {
                    if (role.name !== 'Campaigns-Super Access') {
                      return (
                        <div key={role.id} className="flex items-center group">
                          <label
                            htmlFor={`campaigns_${role.id}`}
                            className="group-hover:cursor-pointer w-40 text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                          >
                            {role.name.split('-')[1]}
                          </label>
                          <Checkbox
                            id={`campaigns_${role.id}`}
                            checked={selectedOptions.campaigns.includes(role.id)}
                            onCheckedChange={(checked) => handleCheckboxChange('campaigns', role.id, checked as boolean)}
                          />
                        </div>
                      );
                    }
                  })}
                  <div className="flex items-center group">
                    <label
                      htmlFor="campaigns_view_all"
                      className="group-hover:cursor-pointer w-40 text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                    >
                      View All
                    </label>
                    <Checkbox id="campaigns_view_all" onCheckedChange={(checked) => handleViewAllChange('campaigns', checked as boolean)} />
                  </div>
                </div>
              </CollapsibleContent>
            </Collapsible>
            {/* 
            <Collapsible defaultOpen={true} className=" bg-white border border-gray-300 ">
              <CollapsibleTrigger className="flex items-center w-full p-2 bg-gray-200 ">
                <div className="space-x-2 flex-1 flex items-center">
                  <div className=" font-bold">Flows</div>
                </div>
                <div>
                  <ChevronDown />
                </div>
              </CollapsibleTrigger>
              <CollapsibleContent className="py-5 px-2 text-sm text-gray-600 bg-white rounded-md">
                <div className="flex-col space-y-5 flex-wrap gap-5">
                  <div className="flex items-center group">
                    <label
                      htmlFor="flows_super_access"
                      className="group-hover:cursor-pointer w-40 text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                    >
                      Super Access
                    </label>
                    <Checkbox
                      id="flows_super_access"
                      onCheckedChange={(checked) =>
                        handleSuperAccessChange(
                          'flows',
                          checked as boolean,
                          flow_roles.map((role: any) => {
                            return { value: role.id };
                          })
                        )
                      }
                    />
                  </div>

                  {flow_roles.map((role: any) => (
                    <div key={role.id} className="flex items-center group">
                      <label
                        htmlFor={`flows_${role.id}`}
                        className="group-hover:cursor-pointer w-40 text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                      >
                        {role.name.split('-')[1]}
                      </label>
                      <Checkbox
                        id={`flows_${role.id}`}
                        checked={selectedOptions.flows.includes(role.id)}
                        onCheckedChange={(checked) => handleCheckboxChange('flows', role.id, checked as boolean)}
                      />
                    </div>
                  ))}
                </div>
              </CollapsibleContent>
            </Collapsible> */}

            {/* <Collapsible defaultOpen={true} className=" bg-white border border-gray-300 ">
              <CollapsibleTrigger className="flex items-center w-full p-2 bg-gray-200 ">
                <div className="space-x-2 flex-1 flex items-center">
                  <div className=" font-bold">Rate and Review</div>
                </div>
                <div>
                  <ChevronDown />
                </div>
              </CollapsibleTrigger>
              <CollapsibleContent className="py-5 px-2 text-sm text-gray-600 bg-white rounded-md">
                <div className="flex-col space-y-5 flex-wrap gap-5">
                  <div className="flex items-center group">
                    <label
                      htmlFor="reviews_super_access"
                      className="group-hover:cursor-pointer w-40 text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                    >
                      Super Access
                    </label>
                    <Checkbox
                      id="reviews_super_access"
                      onCheckedChange={(checked) =>
                        handleSuperAccessChange(
                          'rate_reviews',
                          checked as boolean,
                          reviews_roles.map((role: any) => {
                            return { value: role.id };
                          })
                        )
                      }
                    />
                  </div>

                  {reviews_roles.map((role: any) => (
                    <div key={role.id} className="flex items-center group">
                      <label
                        htmlFor={`reviews_${role.id}`}
                        className="group-hover:cursor-pointer w-40 text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                      >
                        {role.name.split('-')[1]}
                      </label>
                      <Checkbox
                        id={`reviews_${role.id}`}
                        checked={selectedOptions.rate_reviews.includes(role.id)}
                        onCheckedChange={(checked) => handleCheckboxChange('rate_reviews', role.id, checked as boolean)}
                      />
                    </div>
                  ))}
                </div>
              </CollapsibleContent>
            </Collapsible> */}
          </div>
        </div>

        <div className="flex justify-center mt-1 gap-2">
          <Button
            onClick={() => {
              setPage(1);
            }}
          >
            Go Back
          </Button>
          {pending ? (
            <Button disabled={true} variant={'secondary'}>
              <Loader2 className="mr-2 animate-spin" /> Creating{' '}
            </Button>
          ) : (
            <Button onClick={handleAgentCreation} variant={'secondary'}>
              Create Agent
            </Button>
          )}
        </div>
      </div>
    );
  }
}
