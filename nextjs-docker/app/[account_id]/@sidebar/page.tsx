import Sidebar from '@/components/Sidebar';
import { checkOverduePayment } from '@/lib/payment-utils';
import { getAccount } from '@/lib/pocket';
import { User } from '@/lib/types';
import { server_component_pb } from '@/state/pb/server_component_pb';

const SidebarPage = async ({ params }: { params: { [key: string]: string } }) => {
  const { pb, cookies } = await server_component_pb();
  const user = pb.authStore.record as User;
  if (!user) {
    return; // User not found
  }
  const { status } = (await checkOverduePayment(params.account_id)) ?? {};
  const account = await getAccount(params.account_id);
  let logo: string | null = null;
  if (account.logo) {
    logo = pb.files.getURL(account, account.logo, { thumb: '100x100' });
  }
  // return <Sidebar locked={status === 'over-due'} logo={logo} accountId={params.account_id} userType={user.type} userName={user.username} dashboard_options={user.dashboard_options} />;
  <Sidebar logo={logo} accountId={params.account_id} userType={user.type} userName={user.username} dashboard_options={user.dashboard_options} />;
};

export default SidebarPage;
