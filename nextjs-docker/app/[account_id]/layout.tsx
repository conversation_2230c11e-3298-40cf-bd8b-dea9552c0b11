import { getAccount } from '@/lib/pocket';
import { Viewport } from 'next';
import { redirect } from 'next/navigation';
import { Suspense } from 'react';
import Boot from '../_components/boot';
import { server_component_pb } from '@/state/pb/server_component_pb';
import { User } from '@/lib/types';
import { USERNAME } from '@/lib/utils';
import { BaseAuthStore } from 'pocketbase';
import { checkOverduePayment } from '@/lib/payment-utils';
import PaymentDueAlert from '../_components/PaymentDueAlert';

export const viewport: Viewport = {
  width: 'device-width',
  initialScale: 1.0,
};

const Layout = async ({
  children,
  params,
  sidebar,
  topHeader,
}: {
  children: React.ReactNode;
  sidebar: React.ReactNode;
  topHeader: React.ReactNode;
  params: Record<string, string>;
}) => {
  let account;
  let logo: string | null = null;
  try {
    account = await getAccount(params.account_id);
    const { pb, cookies } = await server_component_pb();
    const user = pb.authStore.record as User;
    const authStore: BaseAuthStore = pb.authStore as BaseAuthStore;
    if (!authStore.isSuperuser) {
      if (!account.pb_user_id.includes(user.id)) {
        throw new Error('Business does not exits');
      }
      if (user.username != USERNAME && account.lock) {
        throw new Error('Business is locked');
      }
    }
    if (account.logo) {
      logo = pb.files.getURL(account, account.logo, { thumb: '50x50' });
    }
    // const userId = user.id;
    // if (!account.pb_user_id.includes(userId)) {
    //   throw new Error("Business does not exits");
    // }
  } catch (error) {
    console.log(error);
    redirect('/business/select');
  }
  // Check for overdue payments
  const { invoice } = (await checkOverduePayment(params.account_id)) ?? {};

  return (
    <div className="flex h-dvh">
      <Boot account={account} />
      {/* <OnboardingFile account_id={params.account_id} searchParams={searchParams} /> */}
      <div className="relative md:w-[94px]">
        <Suspense>{sidebar}</Suspense>
      </div>
      <div className="w-full overflow-x-auto max-w-full">
        <div className="flex h-dvh w-full flex-col">
          {invoice && <PaymentDueAlert invoice={invoice} accountId={params.account_id} />}

          {account.name && <Suspense>{topHeader}</Suspense>}
          <div className="flex-1 h-full overflow-y-auto">
            <Suspense>{children}</Suspense>
            {/* <Footer /> */}
          </div>
        </div>
      </div>
    </div>
  );
};

export default Layout;
