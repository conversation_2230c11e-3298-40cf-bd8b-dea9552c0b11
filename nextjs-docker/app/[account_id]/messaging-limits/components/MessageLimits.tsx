'use client';
import { IExpandedAccount, IExpandedMessagingLimit } from '@/lib/types';
import { Slider } from '@/components/ui/slider';
import { useEffect, useState } from 'react';
import { Button } from '@/components/ui/button';
import { updateTableLimits } from '@/lib/pocket';
import TierType from './TierType';
import { Loader2 } from 'lucide-react';
import { useToast } from '@/components/ui/use-toast';
import { Toaster } from '@/components/ui/toaster';

type IMessageLimitsProps = {
  accountLimit: IExpandedAccount;
  userLimits: IExpandedMessagingLimit[];
  accountId: string;
};

const MessageLimits = ({ accountLimit, userLimits, accountId }: IMessageLimitsProps) => {
  const { toast } = useToast();
  const [loading, setLoading] = useState(false);
  const superUserLimit = userLimits.find((userLimit) => {
    return (userLimit.expand?.user?.type ?? '') === 'admin';
  });
  const [superUserAssignedLimit, setSuperUserAssignedLimit] = useState(superUserLimit?.assigned_limit ?? 0);
  const [businessUtilizedLimit, setBusinessUtilizedLimit] = useState<
    {
      id: string;
      assignedLimit: number;
      remainingLimit: number;
    }[]
  >([]);

  const [superUserRemainingLimit, setSuperUserRemainingLimit] = useState(superUserLimit?.remaining_limit);

  // Initialize utilized limits
  useEffect(() => {
    setBusinessUtilizedLimit(
      userLimits.map((userLimit) => ({
        id: userLimit.id,
        assignedLimit: userLimit.assigned_limit,
        remainingLimit: userLimit.remaining_limit,
      }))
    );
  }, [userLimits]);
  const calculateTotalUtilized = () => {
    return businessUtilizedLimit.reduce((total, limit) => total + limit.remainingLimit, 0);
  };

  const handleSetBusinessUtilized = (id: string, assignedLimit: number, remainingLimit: number) => {
    setBusinessUtilizedLimit((prevState) =>
      prevState.map((utilized) =>
        utilized.id === id
          ? {
              ...utilized,
              assignedLimit,
              remainingLimit,
            }
          : utilized
      )
    );
  };

  const handleSetUserLimit = (val: number[]) => {
    const newLimit = val[0];
    const otherLimits = calculateTotalUtilized() - superUserAssignedLimit;

    if (newLimit + otherLimits <= accountLimit.remaining_limit) {
      const diff = (superUserLimit?.assigned_limit ?? 0) - (superUserLimit?.remaining_limit ?? 0);
      setSuperUserRemainingLimit(Math.max(0, val[0] - diff));
      setSuperUserAssignedLimit(newLimit);
      handleSetBusinessUtilized(superUserLimit?.id || '', newLimit, Math.max(0, val[0] - diff));
    }
  };

  const updateLimits = async () => {
    setLoading(true);
    try {
      const limitDataForBusiness = {
        unutilized_limit: accountLimit.remaining_limit - calculateTotalUtilized(),
      };

      const limitDataForMessageTable = businessUtilizedLimit;

      await updateTableLimits(accountId, limitDataForBusiness, limitDataForMessageTable);
      toast({
        variant: 'success',
        description: 'Updated Successfully',
      });
    } catch (error) {
      toast({
        variant: 'destructive',
        description: 'Error in updating',
      });
    }
    setLoading(false);
  };

  return (
    <>
      <Toaster />
      <div className="mb-6">
        <div className="font-bold text-2xl mb-2">{(accountLimit.expand?.package_tier?.total_limit ?? 0) == 0 && 'You dont have any package assigned'}</div>
        <div className="flex items-center">
          <div className="flex-1">
            <div className="flex items-center space-x-2 mb-4">
              <div className="bg-gray-100 border rounded-md p-2 font-bold text-2xl text-center">{accountLimit.expand?.package_tier?.total_limit ?? 0}</div>
              <div>Total Messaging limit assigned to business</div>
            </div>
            <div className="flex items-center space-x-2 mb-4">
              <div className="bg-gray-100 border rounded-md p-2 font-bold text-2xl text-center">{accountLimit.remaining_limit - calculateTotalUtilized()}</div>
              <div>Unassigned Messages</div>
            </div>
            <div className="flex items-center space-x-2 mb-4">
              <div className="bg-gray-100 border rounded-md p-2 font-bold text-2xl text-center">{accountLimit.remaining_limit}</div>
              <div>Remaining Messages</div>
            </div>
          </div>
          <div className="flex items-center justify-center w-72">
            <TierType tier={accountLimit.expand?.package_tier?.type ?? ''} />
          </div>
        </div>
        <div>
          <div>
            <div className="font-bold text-2xl">Assigned limits to admin user</div>
            <div className="mb-8">
              <div className="flex items-center space-x-2 mb-4">
                <div className="flex-1">{superUserLimit?.expand?.user.name ?? ''}</div>
                <div className="w-40">
                  <div className="text-center font-bold">Assigned Limit</div>
                  <div className="bg-gray-100 border rounded-md p-2 font-bold text-center">{superUserAssignedLimit}</div>
                </div>
                <div className="w-40">
                  <div className="text-center font-bold">Remaining Limit</div>
                  <div className="bg-gray-100 border rounded-md p-2 font-bold text-center">{superUserRemainingLimit}</div>
                </div>
              </div>
              <Slider
                value={[superUserAssignedLimit]}
                max={accountLimit.expand?.package_tier?.total_limit ?? 0}
                step={1}
                onValueChange={(val) => handleSetUserLimit(val)}
              />
            </div>
            <div className="font-bold text-2xl">Assigned limits to agents</div>
            {userLimits
              .filter((userLimit) => (userLimit.expand?.user?.type ?? '') === 'agent')
              .map((agentLimit) => {
                const [assignedTotal, setAssignedTotal] = useState(agentLimit.assigned_limit);
                const [remaining, setRemaining] = useState(agentLimit.remaining_limit);
                const handleSetAgentLimit = (val: number[]) => {
                  const newLimit = val[0];
                  const otherLimits = calculateTotalUtilized() - assignedTotal;

                  if (newLimit + otherLimits <= accountLimit.remaining_limit) {
                    const diff = agentLimit.assigned_limit - agentLimit.remaining_limit;
                    setAssignedTotal(newLimit);
                    setRemaining(Math.max(0, val[0] - diff));
                    handleSetBusinessUtilized(agentLimit.id, newLimit, Math.max(0, val[0] - diff));
                  }
                };

                return (
                  <div key={agentLimit.id}>
                    <div className="flex items-center space-x-2 mb-4">
                      <div className="flex-1">{agentLimit.expand?.user.name}</div>
                      <div className="w-40">
                        <div className="text-center font-bold">Assigned Limit</div>
                        <div className="bg-gray-100 border rounded-md p-2 font-bold text-center">{assignedTotal}</div>
                      </div>
                      <div className="w-40">
                        <div className="text-center font-bold">Remaining Limit</div>
                        <div className="bg-gray-100 border rounded-md p-2 font-bold text-center">{remaining}</div>
                      </div>
                    </div>
                    <Slider
                      value={[assignedTotal]}
                      max={accountLimit.expand?.package_tier?.total_limit ?? 0}
                      step={1}
                      onValueChange={(val) => handleSetAgentLimit(val)}
                    />
                  </div>
                );
              })}
          </div>
        </div>
      </div>
      <div className="w-full flex justify-end">
        {!loading ? (
          <Button className="mb-0" onClick={() => updateLimits()}>
            Update
          </Button>
        ) : (
          <Button className="mb-0" disabled>
            Updating <Loader2 className="animate-spin ml-2" />
          </Button>
        )}
      </div>
    </>
  );
};

export default MessageLimits;
