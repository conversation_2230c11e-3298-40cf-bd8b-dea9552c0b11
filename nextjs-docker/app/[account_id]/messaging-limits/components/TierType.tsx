import { convertFirstToUpperCase } from '@/lib/utils';
import clsx from 'clsx';

const TierType = ({ tier }: { tier: 'platinum' | 'gold' | 'silver' | 'free' | '' }) => {
  let bgColor = 'white';
  let borderColor = 'border-gray-100';
  let textColor = 'black';
  switch (tier) {
    case 'free':
      bgColor = 'white';
      borderColor = 'border-gray-100';
      textColor = 'black';
      break;
    case 'gold':
      bgColor = 'bg-amber-200';
      borderColor = 'border-amber-400';
      textColor = 'black';
      break;
  }
  return <div className={clsx('font-bold border rounded-md px-6 py-4 text-6xl', bgColor, borderColor, textColor)}>{convertFirstToUpperCase(tier)}</div>;
};

export default TierType;
