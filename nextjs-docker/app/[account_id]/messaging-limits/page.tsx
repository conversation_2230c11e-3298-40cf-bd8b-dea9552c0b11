import { getAccountLimit, getUserLimits } from '@/lib/pocket';
import { User } from '@/lib/types';
import { server_component_pb } from '@/state/pb/server_component_pb';
import MessageLimits from './components/MessageLimits';

export default async ({ params }: { params: Record<string, string> }) => {
  const { pb } = await server_component_pb();
  const user = pb.authStore.record as User;

  if (!user) {
    return; // User not found
  }

  const accountId = params.account_id;
  let accountLimit = await getAccountLimit(accountId);
  const userLimits = await getUserLimits(accountId);

  return (
    <div className="p-5 px-10 bg-gray-100 h-full">
      <div className="font-bold text-2xl mb-2">Messaging Limit Management</div>
      <div className="bg-white p-5 shadow-md rounded-lg">
        <div>
          <MessageLimits accountLimit={accountLimit} userLimits={userLimits} accountId={accountId} />
        </div>
      </div>
    </div>
  );
};
