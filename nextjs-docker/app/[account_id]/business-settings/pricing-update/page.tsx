import { Button } from '@/components/ui/button';
import Link from 'next/link';
import { PricingUpdate } from './components/pricingUpdate';
import { Toaster } from '@/components/ui/toaster';

export default async ({ params }: { params: Record<string, string> }) => {
  return (
    <div className="h-full w-full flex items-center justify-center">
      <div className="absolute top-20 right-10">
        <Link prefetch={false} href={`/${params.account_id}/business-settings`}>
          <Button type="button">Go back</Button>
        </Link>
      </div>
      <PricingUpdate account_id={params.account_id} />
      <Toaster />
    </div>
  );
};
