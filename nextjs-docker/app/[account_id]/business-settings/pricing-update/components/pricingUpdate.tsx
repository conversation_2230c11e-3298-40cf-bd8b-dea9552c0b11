'use client';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, Card<PERSON>ooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import React, { useState } from 'react';
import <PERSON> from 'papaparse';
import { updatePricing } from '@/lib/pocket';
import { useToast } from '@/components/ui/use-toast';
import { Loader2 } from 'lucide-react';

export function PricingUpdate({ account_id }: { account_id: string }) {
  const { toast } = useToast();
  const [isPending, setIsPending] = useState(false);
  const ref = React.useRef<HTMLFormElement>(null);

  const handleSubmit = async (event: React.FormEvent<HTMLFormElement>) => {
    try {
      event.preventDefault();
      setIsPending(true);

      const formData = new FormData(ref.current as HTMLFormElement);
      const csvFile = formData.get('csv-file') as File;
      const text = await csvFile.text();
      const { data: prcing_data, meta } = Papa.parse<any>(text, {
        header: true,
        skipEmptyLines: true,
      });
      await updatePricing(prcing_data, account_id);
      toast({
        variant: 'success',
        description: 'The pricing table has been updated successfully',
      });
    } catch (err) {
      toast({
        variant: 'destructive',
        description: 'There was an error in updating the pricing table. Try again!',
      });
    }
    setIsPending(false);
  };

  return (
    <Card className="w-full max-w-md bg-white shadow-lg rounded-lg">
      <CardHeader className="p-6">
        <CardTitle className="text-2xl font-semibold text-gray-800">Upload Meta Pricing CSV</CardTitle>
        <CardDescription className="text-gray-600 mt-2 text-md">
          Please upload the official Meta pricing CSV file to update the pricing information.
        </CardDescription>
      </CardHeader>
      <form ref={ref} onSubmit={handleSubmit}>
        <CardContent className="grid gap-4 px-6 pb-4">
          <div id="choose-file" className="grid gap-2">
            <Label htmlFor="csv-file" className="text-gray-700 font-medium">
              Select CSV File
            </Label>
            <Input accept=".csv" name="csv-file" required type="file" className="border-gray-300 rounded-md p-2" />
          </div>
        </CardContent>
        <CardFooter className="px-6 py-4">
          {!isPending ? (
            <Button
              className="w-full bg-green-600 hover:bg-green-700 text-white font-semibold py-2 rounded-md transition duration-300 ease-in-out"
              type="submit"
            >
              Upload File
            </Button>
          ) : (
            <Button className="w-full bg-green-600 hover:bg-green-700 text-white font-semibold py-2 rounded-md transition duration-300 ease-in-out" disabled>
              Updating table
              <Loader2 className="ml-2 animate-spin" />
            </Button>
          )}
        </CardFooter>
      </form>
    </Card>
  );
}
