'use client';

import { Button } from '@/components/ui/button';
import { IExpandedAccount } from '@/lib/types';
import { ColumnDef } from '@tanstack/react-table';
import { ArrowUpDown } from 'lucide-react';
import dayjs from 'dayjs';

// This type is used to define the shape of our data.
// You can use a Zod schema here if you want.

export const columns: ColumnDef<IExpandedAccount>[] = [
  {
    accessorKey: 'id',
    header: ({ column }) => {
      return (
        <Button variant="ghost" className="px-0 mb-0" onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}>
          id
          <ArrowUpDown className="ml-2 h-4 w-4" />
        </Button>
      );
    },
  },
  {
    accessorKey: 'name',
    header: ({ column }) => {
      return (
        <Button variant="ghost" className="px-0 mb-0" onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}>
          Name
          <ArrowUpDown className="ml-2 h-4 w-4" />
        </Button>
      );
    },
  },
  {
    accessorKey: 'expires_at',
    header: ({ column }) => {
      return (
        <Button variant="ghost" className="px-0 mb-0" onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}>
          Token Expiry
          <ArrowUpDown className="ml-2 h-4 w-4" />
        </Button>
      );
    },
    cell: ({ row }) => {
      return <>{dayjs(row.getValue('expires_at')).format('MMMM D, YYYY h:mm A')}</>;
    },
  },
  {
    accessorKey: 'expand?.package_tier?.name',
    maxSize: 300,
    minSize: 100,
    header: ({ column }) => {
      return (
        <Button variant="ghost" className="px-0 mb-0" onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}>
          Package name
          <ArrowUpDown className="ml-2 h-4 w-4" />
        </Button>
      );
    },
    cell: ({ row }) => {
      return <>{row.original.expand?.package_tier?.name ?? 'N/A'}</>;
    },
  },
  {
    accessorKey: 'expand?.package_tier?.type',
    maxSize: 300,
    minSize: 100,
    header: ({ column }) => {
      return (
        <Button variant="ghost" className="px-0 mb-0" onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}>
          Package Type
          <ArrowUpDown className="ml-2 h-4 w-4" />
        </Button>
      );
    },
    cell: ({ row }) => {
      return <>{row.original.expand?.package_tier?.type ?? 'N/A'}</>;
    },
  },
  {
    accessorKey: 'created',
    header: ({ column }) => {
      return (
        <Button variant="ghost" className="px-0 mb-0" onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}>
          Created At
          <ArrowUpDown className="ml-2 h-4 w-4" />
        </Button>
      );
    },
    cell: ({ row }) => {
      return dayjs(row.getValue('created')).format('MMMM D, YYYY h:mm A');
    },
  },
  {
    accessorKey: 'updated',
    header: ({ column }) => {
      return (
        <Button variant="ghost" className="px-0 mb-0" onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}>
          Updated At
          <ArrowUpDown className="ml-2 h-4 w-4" />
        </Button>
      );
    },
    cell: ({ row }) => {
      return dayjs(row.getValue('updated')).format('MMMM D, YYYY h:mm A');
    },
  },
  // {
  //   id: 'actions',
  //   size: 50,
  //   minSize: 50,
  //   maxSize: 50,
  //   cell: ({ row }) => {
  //     return (
  //       <>
  //         <Link href={`business-settings/${row.original.id}`}>
  //           <Button className="mb-0" variant={'outline'}>
  //             Next
  //           </Button>
  //         </Link>
  //       </>
  //     );
  //   },
  // },
];
