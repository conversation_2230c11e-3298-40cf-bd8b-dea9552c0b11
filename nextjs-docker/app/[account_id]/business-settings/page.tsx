import { Logger } from 'next-axiom';

import { getAllAccounts } from '@/lib/pocket';
import type { Metadata } from 'next';
import { columns } from './components/columns';
import { DataTable } from './components/data-table';
import { Toaster } from '@/components/ui/toaster';

export const metadata: Metadata = {
  title: 'Business Setting',
  description: '...',
};

export default async ({ params }: { params: Record<string, string> }) => {
  // noStore();
  const log = new Logger();
  const accounts = await getAllAccounts();

  return (
    <div className="mx-auto p-6 bg-gray-100 min-h-full">
      <DataTable columns={columns} data={accounts} account_id={params.account_id} />
      <Toaster />
    </div>
  );
};
