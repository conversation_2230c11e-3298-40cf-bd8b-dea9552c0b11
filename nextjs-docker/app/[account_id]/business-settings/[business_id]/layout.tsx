import { getAccount, getUser } from '@/lib/pocket';
import dayjs from 'dayjs';
import Link from 'next/link';
import { redirect } from 'next/navigation';
import { ReactElement } from 'react';
import { Calendar, CheckCircle, Clock, DollarSign, Gauge, Image, Lock, MapPin, Package, Phone, Star, ToggleLeft, ToggleRight, Users } from 'lucide-react';
import { USERNAME } from '@/lib/utils';

export default async ({ params, children }: { params: Record<string, string>; children: ReactElement }) => {
  const user = await getUser();

  if (user.username != USERNAME) {
    redirect('/dashboard');
  }
  const account = await getAccount(params.business_id);
  return (
    <div className="mx-auto p-6 bg-gray-100 min-h-full">
      <Link href={`/${params.account_id}/business-settings/${params.business_id}`} className="text-3xl font-semibold">
        <div className="pb-4">
          {account.name}: <span className="text-gray-800 text-xl"> {account.id}</span>
        </div>
      </Link>
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 bg-white p-6 rounded-lg shadow mb-4">
        <div className="space-y-3">
          <div className="flex items-center space-x-2">
            <MapPin className="h-4 w-4 " />
            <span className="font-semibold">Business Location:</span>
            <span>{account.business_location || 'N/A'}</span>
          </div>
          <div className="flex items-center space-x-2">
            <CheckCircle className="h-4 w-4 " />
            <span className="font-semibold">Code Verification:</span>
            <span>{account.code_verification_status || 'N/A'}</span>
          </div>
          <div className="flex items-center space-x-2">
            <Calendar className="h-4 w-4 " />
            <span className="font-semibold">Created:</span>
            <span>{account.created ? dayjs(account.created).format('MMM D, YYYY') : 'N/A'}</span>
          </div>
          <div className="flex items-center space-x-2">
            <Star className="h-4 w-4 " />
            <span className="font-semibold">Quality Rating:</span>
            <span>{account.quality_rating || 'N/A'}</span>
          </div>
        </div>

        <div className="space-y-3">
          <div className="flex items-center space-x-2">
            <DollarSign className="h-4 w-4 " />
            <span className="font-semibold">Currency:</span>
            <span>{account.currency || 'N/A'}</span>
          </div>
          <div className="flex items-center space-x-2">
            <Gauge className="h-4 w-4 " />
            <span className="font-semibold">Current Limit:</span>
            <span>{account.current_limit || 'N/A'}</span>
          </div>
          <div className="flex items-center space-x-2">
            <Phone className="h-4 w-4 " />
            <span className="font-semibold">Phone:</span>
            <span>{account.display_phone_number || 'N/A'}</span>
          </div>
          <div className="flex items-center space-x-2">
            <Lock className="h-4 w-4 " />
            <span className="font-semibold">Locked:</span>
            <span>{account.lock ? 'Yes' : 'No'}</span>
          </div>
        </div>

        <div className="space-y-3">
          <div className="flex items-center space-x-2">
            <Clock className="h-4 w-4 " />
            <span className="font-semibold">Expires At:</span>
            <span>{account.expires_at ? dayjs(account.expires_at).format('MMM D, YYYY') : 'N/A'}</span>
          </div>
          <div className="flex items-center space-x-2">
            <Calendar className="h-4 w-4 " />
            <span className="font-semibold">Limit End:</span>
            <span>{account.limit_end_date ? dayjs(account.limit_end_date).format('MMM D, YYYY') : 'N/A'}</span>
          </div>
          <div className="flex items-center space-x-2">
            <Calendar className="h-4 w-4 " />
            <span className="font-semibold">Limit Start:</span>
            <span>{account.limit_start_date ? dayjs(account.limit_start_date).format('MMM D, YYYY') : 'N/A'}</span>
          </div>
          <div className="flex items-center space-x-2">
            {account.show_recent_campaigns ? <ToggleRight className="h-4 w-4 " /> : <ToggleLeft className="h-4 w-4 " />}
            <span className="font-semibold">Show recent campaigns</span>
            <span>{account.show_recent_campaigns ? 'Showing recent campaigns' : 'Not showing recent campaigns'}</span>
          </div>
        </div>

        <div className="space-y-3">
          <div className="flex items-center space-x-2">
            <Image className="h-4 w-4 " />
            <span className="font-semibold">Logo:</span>
            {account.logo ? <img src={account.logo} alt="Account Logo" className="w-10 h-10 rounded-full object-cover" /> : <span>No Logo</span>}
          </div>
          <div className="flex items-center space-x-2">
            <Package className="h-4 w-4 " />
            <span className="font-semibold">Package Tier:</span>
            <span>{account.expand?.package_tier?.name || 'N/A'}</span>
          </div>
          <div className="flex items-center space-x-2">
            <Users className="h-4 w-4 " />
            <span className="font-semibold">Users:</span>
            <span>{account.pb_user_id?.length || 0}</span>
          </div>
        </div>
      </div>
      <>{children}</>
    </div>
  );
};
