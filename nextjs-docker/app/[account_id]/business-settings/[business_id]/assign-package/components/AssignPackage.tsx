'use client';
import { Select, SelectContent, SelectGroup, SelectItem, SelectLabel, SelectTrigger, SelectValue } from '@/components/ui/select';
import { useToast } from '@/components/ui/use-toast';
import { updateAccountToPackage } from '@/lib/actions';
import { IExpandedAccount, IPackageTier } from '@/lib/types';
import { useFormState } from 'react-dom';
import SaveButton from './SaveButton';
import { useEffect } from 'react';
import { Toaster } from '@/components/ui/toaster';
import { useParams } from 'next/navigation';

const AssignPackage = ({ account, packageTiers }: { account: IExpandedAccount; packageTiers: IPackageTier[] }) => {
  const params = useParams();
  const initialState: any = {
    message: {
      status: 0,
      error: '',
    },
  };

  const css = `
  select {
    position: absolute !important
  }
`;

  const [state, formAction] = useFormState(updateAccountToPackage, initialState);
  const { toast } = useToast();
  const currentPackage = account.expand?.package_tier;

  useEffect(() => {
    if (state?.message.status === 400) {
      toast({
        variant: 'destructive',
        description: state?.message.error,
      });
    }
    if (state?.message.status == 200) {
      toast({
        variant: 'success',
        description: 'Package updated successfully',
      });
    }
  }, [state]);

  const defaultValue = `${currentPackage?.id}-${currentPackage?.total_limit}-${currentPackage?.period}`;
  return (
    <>
      <Toaster />
      <style>{css}</style>
      <div className="text-4xl mb-4">Assign Package</div>
      <div className="text-2xl mb-2">{account.name}</div>
      <form action={formAction}>
        {/* Business id */}
        <input type="text" defaultValue={account.id} hidden name="accountId" />
        {/* account id */}
        <input type="text" defaultValue={params.account_id} hidden name="account_id" />
        <Select name="packageDetails" defaultValue={defaultValue}>
          <SelectTrigger className="w-[180px]">
            <SelectValue placeholder="Select Tier" />
          </SelectTrigger>
          <SelectContent>
            <SelectGroup>
              <SelectLabel>Package Assign</SelectLabel>
              {packageTiers.map((tier) => {
                return <SelectItem value={`${tier.id}-${tier.total_limit}-${tier.period}`}>{tier.name}</SelectItem>;
              })}
            </SelectGroup>
          </SelectContent>
        </Select>
        <SaveButton />
      </form>
    </>
  );
};

export default AssignPackage;
