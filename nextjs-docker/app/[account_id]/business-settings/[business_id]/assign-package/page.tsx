import { Logger } from 'next-axiom';

import { getAccount, getPackageTier } from '@/lib/pocket';
import type { Metadata } from 'next';
import AssignPackage from './components/AssignPackage';

export const metadata: Metadata = {
  title: 'Business Account Setting',
  description: '...',
};

export default async ({ params }: { params: Record<string, string> }) => {
  // noStore();
  const log = new Logger();
  const packageTiers = await getPackageTier();
  const account = await getAccount(params.business_id);
  return (
    <>
      <div className="bg-white p-4 shadow-md rounded-lg">
        <AssignPackage packageTiers={packageTiers} account={account} />
        <div className="text-2xl">Users</div>
        <div className="flex items-center space-x-2">
          <div className="flex flex-col space-y-2">
            {account.expand?.pb_user_id?.map((user) => {
              return (
                <div key={user.id} className="flex items-center space-x-4">
                  <div className="w-72">{user.name}</div>
                  <div className="w-24">{user.type}</div>
                </div>
              );
            })}
          </div>
        </div>
      </div>
    </>
  );
};
