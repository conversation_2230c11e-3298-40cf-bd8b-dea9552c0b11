'use client';

import { Button } from '@/components/ui/button';
import { ColumnDef } from '@tanstack/react-table';
import { ArrowUpDown } from 'lucide-react';
import dayjs from 'dayjs';
import { convertFirstToUpperCase } from '@/lib/utils';
import { HoverCard, HoverCardContent, HoverCardTrigger } from '@/components/ui/hover-card';
import { IInvoice } from '@/lib/types';
import clsx from 'clsx';
import { InvoiceDropdDownAction } from './InvoiceDropDownAction';

export const columns: ColumnDef<IInvoice>[] = [
  {
    accessorKey: 'invoice_valid_from',
    size: 150,
    maxSize: 300,
    minSize: 150,
    header: ({ column }) => {
      return (
        <Button variant="ghost" className="px-0 mb-0" onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}>
          Created At
          <ArrowUpDown className="ml-2 h-4 w-4" />
        </Button>
      );
    },
    cell: ({ row }) => {
      return <div className="text-sm font-medium">{dayjs(row.getValue('invoice_valid_from')).format('DD-MM-YYYY')}</div>;
    },
  },
  {
    accessorKey: 'amount_due',
    size: 150,
    maxSize: 300,
    minSize: 150,
    header: ({ column }) => {
      return (
        <Button variant="ghost" className="px-0 mb-0" onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}>
          Total Billed Amount
          <ArrowUpDown className="ml-2 h-4 w-4" />
        </Button>
      );
    },
    cell: ({ row }) => {
      return <div className="text-sm font-medium">{row.original.amount_due} AED</div>;
    },
  },
  {
    accessorKey: 'unit_amount',
    size: 150,
    maxSize: 300,
    minSize: 150,
    header: ({ column }) => {
      return (
        <Button variant="ghost" className="px-0 mb-0" onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}>
          Unit Amount
          <ArrowUpDown className="ml-2 h-4 w-4" />
        </Button>
      );
    },
    cell: ({ row }) => {
      return <div className="text-sm font-medium">{row.original.unit_amount} AED</div>;
    },
  },
  {
    accessorKey: 'tax',
    size: 150,
    maxSize: 300,
    minSize: 150,
    header: ({ column }) => {
      return (
        <Button variant="ghost" className="px-0 mb-0" onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}>
          Tax
          <ArrowUpDown className="ml-2 h-4 w-4" />
        </Button>
      );
    },
    cell: ({ row }) => {
      return <div className="text-sm font-medium">{row.original.tax} AED</div>;
    },
  },
  {
    accessorKey: 'charges',
    size: 150,
    maxSize: 300,
    minSize: 150,
    header: ({ column }) => {
      return (
        <Button variant="ghost" className="px-0 mb-0" onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}>
          Other Charges
          <ArrowUpDown className="ml-2 h-4 w-4" />
        </Button>
      );
    },
    cell: ({ row }) => {
      return <div className="text-sm font-medium">{row.original.charges} AED</div>;
    },
  },
  {
    accessorKey: 'duration',
    size: 150,
    maxSize: 300,
    minSize: 150,
    header: ({ column }) => {
      return (
        <Button variant="ghost" className="px-0 mb-0" onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}>
          Subscription Duration
          <ArrowUpDown className="ml-2 h-4 w-4" />
        </Button>
      );
    },
    cell: ({ row }) => {
      return (
        <div className="text-sm font-medium">
          {row.original.billing_months} {row.original.billing_months > 1 ? 'Months' : 'Month'}
        </div>
      );
    },
  },
  {
    accessorKey: 'payment_status',
    size: 150,
    maxSize: 300,
    minSize: 150,
    header: ({ column }) => {
      return (
        <Button variant="ghost" className="px-0 mb-0" onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}>
          Payment Status
          <ArrowUpDown className="ml-2 h-4 w-4" />
        </Button>
      );
    },
    cell: ({ row }) => {
      return (
        <div className={clsx('text-sm font-semibold', row.original.payment_status == 'paid' ? 'text-green-400' : 'text-red-400')}>
          {row.original.payment_status.toUpperCase()}
        </div>
      );
    },
  },
  {
    id: 'actions',
    size: 50,
    minSize: 50,
    maxSize: 50,
    cell: ({ row, table }) => {
      return <InvoiceDropdDownAction row={row} />;
    },
  },
];
