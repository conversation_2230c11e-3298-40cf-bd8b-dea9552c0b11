'use client';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Label } from '@/components/ui/label';
import { useToast } from '@/components/ui/use-toast';
import { Loader2 } from 'lucide-react';
import { useRef, useState } from 'react';
import { createPayment, updateInvoice } from '@/lib/pocket';
import { IInvoice } from '@/lib/types';
import { Calendar } from '@/components/ui/calendar';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { format } from 'date-fns';
import { Input } from '@/components/ui/input';

export default function PayInvoiceManually({
  account_id,
  subscriptionDetails,
  business_id,
  invoice_id,
}: {
  account_id: string;
  subscriptionDetails: IInvoice;
  business_id: string;
  invoice_id: string;
}) {
  const { toast } = useToast();
  const [isPending, setIsPending] = useState(false);
  const [selectedDate, setSelectedDate] = useState<Date | null>(null);
  const [payRef, setPayRef] = useState('');
  const ref = useRef<HTMLFormElement>(null);

  const handleDateChange = (date: Date | null) => {
    setSelectedDate(date);
  };

  const handleSubmit = async (event: React.FormEvent<HTMLFormElement>) => {
    event.preventDefault();
    setIsPending(true);

    try {
      await createPayment('off-system', business_id, [subscriptionDetails], 'success', undefined, selectedDate ?? undefined, account_id, payRef);
      toast({
        variant: 'success',
        description: 'The payment was done successfully',
      });
    } catch (err) {
      toast({
        variant: 'destructive',
        description: 'An error occurred. Try again!',
      });
    } finally {
      setIsPending(false);
    }
  };

  return (
    <Card className="p-4 w-[40%] bg-white shadow-lg rounded-lg overflow-hidden">
      <CardHeader className="p-4 bg-white border-b">
        <CardTitle className="text-2xl font-semibold text-gray-900">Invoice Payment</CardTitle>
        <CardDescription className="text-gray-600 mt-1 text-sm">Provide the payment details</CardDescription>
      </CardHeader>

      <form ref={ref} onSubmit={handleSubmit}>
        <CardContent className="px-4 py-3 space-y-4">
          <div className="space-y-2">
            <Label className="text-gray-700 font-medium">Enter Payment Ref(check number/slip number)</Label>
            <Input
              name="pay_ref"
              placeholder="payment reference"
              className="border"
              value={payRef}
              onChange={(e) => {
                setPayRef(e.target.value);
              }}
            />
            <Label className="text-gray-700 font-medium">Select Payment Date</Label>
            <Popover>
              <PopoverTrigger asChild>
                <Button
                  variant="outline"
                  className="w-full justify-start text-left font-normal px-4 py-2 text-gray-700 border border-gray-300 rounded-lg hover:bg-gray-100 transition-all duration-200"
                >
                  {selectedDate ? format(selectedDate, 'PPP') : 'Pick a date'}
                </Button>
              </PopoverTrigger>
              <PopoverContent align="start" className="w-auto p-0 shadow-md rounded-lg">
                <Calendar
                  mode="single"
                  selected={selectedDate || undefined}
                  onSelect={(day) => handleDateChange(day || null)}
                  initialFocus
                  className="shadow-lg rounded-lg"
                />
              </PopoverContent>
            </Popover>
          </div>
        </CardContent>

        <CardFooter className="px-4 py-3 bg-white border-t flex justify-center">
          {!isPending ? (
            <Button
              disabled={!selectedDate || !payRef}
              className="w-48 py-3 bg-green-600 text-white font-semibold text-lg rounded-lg hover:bg-green-700 transition duration-200 ease-in-out"
              type="submit"
            >
              Create Payment
            </Button>
          ) : (
            <Button
              className="w-48 py-3 bg-green-600 text-white font-semibold text-lg rounded-lg hover:bg-green-700 transition duration-200 ease-in-out"
              disabled
            >
              Creating
              <Loader2 className="ml-2 animate-spin" />
            </Button>
          )}
        </CardFooter>
      </form>
    </Card>
  );
}
