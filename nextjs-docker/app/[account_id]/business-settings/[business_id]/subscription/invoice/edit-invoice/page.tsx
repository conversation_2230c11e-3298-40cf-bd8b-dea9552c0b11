import { Button } from '@/components/ui/button';
import { Toaster } from '@/components/ui/toaster';
import Link from 'next/link';
import { getAccount, getInvoiceById, getLastSubscriptionDetails } from '@/lib/pocket';
import EditSubscriptionForm from './components/EditInvoice';

export const dynamic = 'force-dynamic';
export const fetchCache = 'default-no-store';

export default async ({ params, searchParams }: { params: Record<string, string>; searchParams: Record<string, string> }) => {
  const account = await getAccount(params.account_id);

  const subscriptionDetails = await getInvoiceById(searchParams.invoice_id);
  return (
    <div className="h-full w-full flex items-center justify-center">
      <EditSubscriptionForm
        account_id={params.account_id}
        business_id={params.business_id}
        subscriptionDetails={subscriptionDetails}
        invoice_id={searchParams.invoice_id}
      />
    </div>
  );
};
