'use client';

import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { useToast } from '@/components/ui/use-toast';
import { updateInvoice } from '@/lib/pocket';
import { IInvoice } from '@/lib/types';
import { Loader2 } from 'lucide-react';
import { useRef, useState } from 'react';

export default function EditSubscriptionForm({
  account_id,
  subscriptionDetails,
  business_id,
  invoice_id,
}: {
  account_id: string;
  subscriptionDetails: IInvoice;
  business_id: string;
  invoice_id: string;
}) {
  const { toast } = useToast();
  const [isPending, setIsPending] = useState(false);
  const [billingMonths, setBillingMonths] = useState<any>(subscriptionDetails && subscriptionDetails.billing_months.toString());
  const ref = useRef<HTMLFormElement>(null);
  const handleSubmit = async (event: React.FormEvent<HTMLFormElement>) => {
    try {
      event.preventDefault();
      setIsPending(true);

      const formData = new FormData(ref.current as HTMLFormElement);
      const fees = formData.get('fee') as string;
      const tax = formData.get('tax') as string;
      const charges = formData.get('charges') as string;
      const limit = formData.get('limit') as string;

      await updateInvoice(
        account_id,
        business_id,
        invoice_id,
        parseFloat(fees),
        parseFloat(tax),
        parseFloat(charges),
        parseInt(billingMonths),
        parseInt(limit)
      );

      setIsPending(false);

      toast({
        variant: 'success',
        description: 'The invoice details have been updated successfully',
      });
    } catch (err) {
      toast({
        variant: 'destructive',
        description: 'An error occured. Try again!',
      });
    }
  };
  return (
    <Card className="p-1 box-border w-[70%] bg-white shadow-md rounded-lg overflow-hidden">
      <CardHeader className="p-4 bg-white border-b">
        <CardTitle className="text-2xl font-semibold text-gray-800">Edit Subscription Details</CardTitle>
        <CardDescription className="text-gray-600 mt-2 text-sm">Edit the subscription details for this business.</CardDescription>
      </CardHeader>

      <form ref={ref} onSubmit={handleSubmit}>
        <CardContent className="px-2 py-2 flex gap-2 ">
          <div className="flex-1 flex-col space-y-2">
            <div className="space-y-2">
              <Label className="text-gray-700 font-medium">Enter monthly subscription fee (AED)</Label>
              <Input
                defaultValue={subscriptionDetails?.unit_amount || ''}
                placeholder="monthly subscription"
                name="fee"
                required
                type="text"
                className="w-full p-4 text-md border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500 transition duration-300 ease-in-out"
              />
            </div>

            <div className="space-y-2">
              <Label className="text-gray-700 font-medium">Enter Tax amount (for the entire subscription duration)</Label>
              <Input
                defaultValue={subscriptionDetails?.tax || ''}
                placeholder="Tax amount"
                name="tax"
                required
                type="text"
                className="w-full p-4 text-md border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500 transition duration-300 ease-in-out"
              />
            </div>
            <div className="space-y-2">
              <Label className="text-gray-700 font-medium">Select subscription duration (months)</Label>
              <Select
                value={billingMonths || ''}
                onValueChange={(value) => {
                  setBillingMonths(value);
                }}
              >
                <SelectTrigger
                  name="billingMonths"
                  className="w-full p-4 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500"
                >
                  <SelectValue placeholder="Select duration" />
                </SelectTrigger>
                <SelectContent>
                  {Array.from({ length: 12 }).map((_, index: number) => (
                    <SelectItem key={index} value={(index + 1).toString()}>
                      {index + 1} {index === 0 ? 'month' : 'months'}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>

          <div className="flex-col flex-1 space-y-2">
            <div className="space-y-2">
              <Label className="text-gray-700 font-medium">Enter other charges amount (for the entire subscription duration)</Label>
              <Input
                defaultValue={subscriptionDetails?.charges || ''}
                placeholder="other charges"
                name="charges"
                required
                type="text"
                className="w-full p-4 text-md border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500 transition duration-300 ease-in-out"
              />
            </div>
            <div className="space-y-2">
              <Label className="text-gray-700 font-medium">Enter Invoice validity duration (in days)</Label>
              <Input
                defaultValue={subscriptionDetails?.invoice_limit || ''}
                placeholder="invoice duration"
                name="limit"
                required
                type="text"
                className="w-full p-4 text-md border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500 transition duration-300 ease-in-out"
              />
            </div>
          </div>
        </CardContent>

        <CardFooter className="px-2 py-4 bg-white border-t flex justify-center">
          {!isPending ? (
            <Button
              className="w-28 py-3 bg-green-600 text-white font-semibold text-lg rounded-lg hover:bg-green-700 transition duration-200 ease-in-out"
              type="submit"
            >
              Save
            </Button>
          ) : (
            <Button
              className="w-28 py-3 bg-green-600 text-white font-semibold text-lg rounded-lg hover:bg-green-700 transition duration-200 ease-in-out"
              disabled
            >
              Saving
              <Loader2 className="ml-2 animate-spin" />
            </Button>
          )}
        </CardFooter>
      </form>
    </Card>
  );
}
