'use client';

import { Button } from '@/components/ui/button';
import { Calendar } from '@/components/ui/calendar';
import { <PERSON>, CardContent, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { useToast } from '@/components/ui/use-toast';
import { generatePastInvoice } from '@/lib/pocket';
import { IInvoice } from '@/lib/types';
import { format } from 'date-fns';
import { Loader2 } from 'lucide-react';
import { useRef, useState } from 'react';

export default function PastInvoice({
  account_id,
  subscriptionDetails,
  business_id,
  isFirstInvoice,
}: {
  account_id: string;
  subscriptionDetails: IInvoice;
  business_id: string;
  isFirstInvoice: boolean;
}) {
  const { toast } = useToast();
  const [isPending, setIsPending] = useState(false);
  const [billingMonths, setBillingMonths] = useState<any>();
  const [renewal, setRenewal] = useState<any>('yes');
  const ref = useRef<HTMLFormElement>(null);
  const [selectedDate, setSelectedDate] = useState<Date | null>(null);

  const handleDateChange = (date: Date | null) => {
    setSelectedDate(date);
  };
  const handleSubmit = async (event: React.FormEvent<HTMLFormElement>) => {
    try {
      event.preventDefault();
      setIsPending(true);

      const formData = new FormData(ref.current as HTMLFormElement);
      const fees = formData.get('fee') as string;
      const tax = formData.get('tax') as string;
      const charges = formData.get('charges') as string;
      // const limit = formData.get('limit') as string;

      await generatePastInvoice(
        parseFloat(fees),
        parseFloat(tax),
        parseFloat(charges),
        business_id,
        parseInt(billingMonths),
        selectedDate,
        account_id,
        renewal,
        isFirstInvoice
      );
      // await disableAutoRenewal(subscriptionDetails.id);
      setIsPending(false);

      toast({
        variant: 'success',
        description: 'The invoice has been created successfully',
      });
    } catch (err) {
      toast({
        variant: 'destructive',
        description: 'An error occured. Try again!',
      });
    }
  };
  return (
    <Card className="p-1 box-border w-[70%] bg-white shadow-md rounded-lg overflow-hidden">
      <CardHeader className="p-4 bg-white border-b">
        <CardTitle className="text-2xl font-semibold text-gray-800">Past Invoice Details</CardTitle>
        {/* <CardDescription className="text-gray-600 mt-2 text-sm">Please enter the details.</CardDescription> */}
      </CardHeader>

      <form ref={ref} onSubmit={handleSubmit}>
        <CardContent className="px-2 py-2 flex gap-2 ">
          <div className="flex-1 flex-col space-y-2">
            <div className="space-y-2">
              <Label className="text-gray-700 font-medium">Enter monthly subscription fee (AED)</Label>
              <Input
                placeholder="monthly subscription"
                name="fee"
                required
                type="text"
                className="w-full p-4 text-md border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500 transition duration-300 ease-in-out"
              />
            </div>

            <div className="space-y-2">
              <Label className="text-gray-700 font-medium">Enter Tax amount (for the entire subscription duration)</Label>
              <Input
                placeholder="Tax amount"
                name="tax"
                required
                type="text"
                className="w-full p-4 text-md border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500 transition duration-300 ease-in-out"
              />
            </div>
            <div className="space-y-2">
              <Label className="text-gray-700 font-medium">Select subscription duration (months)</Label>
              <Select
                value={billingMonths || ''}
                onValueChange={(value) => {
                  setBillingMonths(value);
                }}
              >
                <SelectTrigger
                  name="billingMonths"
                  className="w-full p-4 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500"
                >
                  <SelectValue placeholder="Select duration" />
                </SelectTrigger>
                <SelectContent>
                  {Array.from({ length: 12 }).map((_, index: number) => (
                    <SelectItem key={index} value={(index + 1).toString()}>
                      {index + 1} {index === 0 ? 'month' : 'months'}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>

          <div className="flex-col flex-1 space-y-2">
            <div className="space-y-2">
              <Label className="text-gray-700 font-medium">Select Invoice Start Date</Label>
              <Popover>
                <PopoverTrigger asChild>
                  <Button
                    variant="outline"
                    className="w-full justify-start text-left font-normal px-4 py-2 text-gray-700 border border-gray-300 rounded-lg hover:bg-gray-100 transition-all duration-200"
                  >
                    {selectedDate ? format(selectedDate, 'PPP') : 'Pick a date'}
                  </Button>
                </PopoverTrigger>
                <PopoverContent align="start" className="w-auto p-0 shadow-md rounded-lg">
                  <Calendar
                    mode="single"
                    selected={selectedDate || undefined}
                    onSelect={(day) => handleDateChange(day || null)}
                    initialFocus
                    className="shadow-lg rounded-lg"
                  />
                </PopoverContent>
              </Popover>
            </div>

            <div className="space-y-2">
              <Label className="text-gray-700 font-medium">Enter other charges amount (for the entire subscription duration)</Label>
              <Input
                placeholder="other charges"
                name="charges"
                required
                type="text"
                className="w-full p-4 text-md border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500 transition duration-300 ease-in-out"
              />
            </div>
            <div className="space-y-2">
              <Label className="text-gray-700 font-medium">Select whether this invoice should be recreated or not</Label>
              <Select
                value={renewal}
                onValueChange={(value) => {
                  setRenewal(value);
                }}
              >
                <SelectTrigger
                  name="billingMonths"
                  className="w-full p-4 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500"
                >
                  <SelectValue placeholder="Yes/No" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="yes">Yes</SelectItem>
                  <SelectItem value="no">No</SelectItem>
                </SelectContent>
              </Select>
              {/* <Label htmlFor="airplane-mode">Show opted out Leads</Label> */}
            </div>
          </div>
        </CardContent>

        <CardFooter className="px-2 py-4 bg-white border-t flex justify-center">
          {!isPending ? (
            <Button
              className="w-28 py-3 bg-green-600 text-white font-semibold text-lg rounded-lg hover:bg-green-700 transition duration-200 ease-in-out"
              type="submit"
            >
              Save
            </Button>
          ) : (
            <Button
              className="w-28 py-3 bg-green-600 text-white font-semibold text-lg rounded-lg hover:bg-green-700 transition duration-200 ease-in-out"
              disabled
            >
              Saving
              <Loader2 className="ml-2 animate-spin" />
            </Button>
          )}
        </CardFooter>
      </form>
    </Card>
  );
}
