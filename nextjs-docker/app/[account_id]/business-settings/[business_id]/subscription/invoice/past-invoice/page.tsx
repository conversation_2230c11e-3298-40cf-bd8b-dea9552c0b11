import { Button } from '@/components/ui/button';
import { Toaster } from '@/components/ui/toaster';
import Link from 'next/link';
import { getAccount, getAllSubscriptionDetails, getLastSubscriptionDetails } from '@/lib/pocket';
import PastInvoice from './components/PastInvoice';

export const dynamic = 'force-dynamic';
export const fetchCache = 'default-no-store';

export default async ({ params }: { params: Record<string, string> }) => {
  const account = await getAccount(params.account_id);
  const subscriptionDetails = await getAllSubscriptionDetails(params.business_id);
  const isFirstInvoice = subscriptionDetails?.[0] ? false : true;
  return (
    <div className="h-full w-full flex items-center justify-center">
      <PastInvoice
        account_id={params.account_id}
        business_id={params.business_id}
        subscriptionDetails={subscriptionDetails?.[0]}
        isFirstInvoice={isFirstInvoice}
      />
    </div>
  );
};
