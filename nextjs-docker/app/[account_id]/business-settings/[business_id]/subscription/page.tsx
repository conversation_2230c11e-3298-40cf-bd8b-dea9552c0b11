import { Button } from '@/components/ui/button';
import { Toaster } from '@/components/ui/toaster';
import Link from 'next/link';
import { getAccount, getAllSubscriptionDetails, getBusinessInvoicesList, getUncreatedInvoiceDocuments } from '@/lib/pocket';
import { DataTable } from './components/data-table';
import { columns } from './components/columns';

export const dynamic = 'force-dynamic';
export const fetchCache = 'default-no-store';

export default async ({ params }: { params: Record<string, string> }) => {
  const account = await getAccount(params.account_id);
  const invoices = await getBusinessInvoicesList(params.business_id);
  const subscriptionDetails = await getAllSubscriptionDetails(params.business_id);
  const uncreatedInvoiceDocs = await getUncreatedInvoiceDocuments(params.business_id);

  return (
    <>
      <DataTable
        columns={columns}
        data={invoices}
        account_id={params.account_id}
        business_id={params.business_id}
        subscriptionDetails={subscriptionDetails?.[0]}
        uncreatedInvoiceDocs={uncreatedInvoiceDocs}
      />
      <Toaster />
    </>
  );
};
