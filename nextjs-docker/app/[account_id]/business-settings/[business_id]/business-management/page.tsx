import { Logger } from 'next-axiom';

import { getAccount, getPackageTier } from '@/lib/pocket';
import type { Metadata } from 'next';
import { ManageBusinessForm } from './ManageBusinessForm';

export const metadata: Metadata = {
  title: 'Business Account Setting',
  description: '...',
};

export default async ({ params }: { params: Record<string, string> }) => {
  // noStore();
  const log = new Logger();
  const packageTiers = await getPackageTier();
  const account = await getAccount(params.business_id);
  return (
    <>
      <div className="bg-white p-4 shadow-md rounded-lg">
        <div className="text-2xl mb-4">Manage Business</div>
        <ManageBusinessForm account={account} businessId={params.business_id} />
      </div>
    </>
  );
};
