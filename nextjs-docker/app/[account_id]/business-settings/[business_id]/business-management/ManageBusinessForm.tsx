'use client';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Switch } from '@/components/ui/switch';
import { Toaster } from '@/components/ui/toaster';
import { useToast } from '@/components/ui/use-toast';
import { updateBusinessByAdmin } from '@/lib/actions';
import { Account } from '@/lib/types';
import clsx from 'clsx';
import { Loader2 } from 'lucide-react';
import { useEffect } from 'react';
import { useFormState, useFormStatus } from 'react-dom';

const SaveButton = () => {
  const { pending } = useFormStatus();

  if (pending) {
    return (
      <Button disabled>
        <Loader2 className="animate-spin mr-2 h-4 w-4" />
        Saving
      </Button>
    );
  }
  return <Button type="submit">Save</Button>;
};
export const ManageBusinessForm = ({ account, businessId }: { account: Account; businessId: string }) => {
  const { toast } = useToast();

  const [state, formAction] = useFormState(updateBusinessByAdmin, null);
  useEffect(() => {
    if (state?.message.status == 400) {
      toast({
        variant: 'destructive',
        description: `Error updating agent`,
      });
    } else if (state?.message.status == 200) {
      toast({
        variant: 'success',
        description: `Agent updated successfully`,
      });
    }
  }, [state]);

  return (
    <form className="flex flex-col space-y-4 max-w-xl" action={formAction}>
      <div className="flex space-x-2 items-center">
        <div>Name:</div>
        <Input type="text" name="name" defaultValue={account.name} />
      </div>
      <div className="flex space-x-2 items-center">
        <div className={clsx(!account.lock && 'font-bold')}>Not Locked</div>
        <Switch name="lock" defaultChecked={account.lock} />
        <div className={clsx(account.lock && 'font-bold')}>Locked</div>
      </div>
      <input type="text" defaultValue={businessId} name="businessId" hidden />
      <input type="text" defaultValue={account.id} name="accountId" hidden />
      <SaveButton />
      <Toaster />
    </form>
  );
};
