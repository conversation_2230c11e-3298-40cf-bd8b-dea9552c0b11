import { Button } from '@/components/ui/button';
import { Briefcase, DollarSign, Package, Users } from 'lucide-react';
import Link from 'next/link';

import type { Metadata } from 'next';
import { Toaster } from '@/components/ui/toaster';

export const metadata: Metadata = {
  title: 'Business Account Setting',
  description: '...',
};

export default async ({ params }: { params: Record<string, string> }) => {
  return (
    <>
      <nav className="flex flex-col space-y-2 p-4 bg-white rounded-lg shadow">
        <Button variant="ghost" className="justify-start hover:bg-gray-100" asChild>
          <Link href={`${params.business_id}/assign-package`}>
            <Package className="mr-2 h-4 w-4" />
            Assign package
          </Link>
        </Button>
        <Button variant="ghost" className="justify-start hover:bg-gray-100" asChild>
          <Link href={`${params.business_id}/user-management`}>
            <Users className="mr-2 h-4 w-4" />
            User Management
          </Link>
        </Button>
        <Button variant="ghost" className="justify-start hover:bg-gray-100" asChild>
          <Link href={`${params.business_id}/business-management`}>
            <Briefcase className="mr-2 h-4 w-4" />
            Manage Business
          </Link>
        </Button>
        <Button variant="ghost" className="justify-start hover:bg-gray-100" asChild>
          <Link href={`${params.business_id}/subscription`}>
            <DollarSign className="mr-2 h-4 w-4" />
            Subscription
          </Link>
        </Button>
      </nav>
      <Toaster />
    </>
  );
};
