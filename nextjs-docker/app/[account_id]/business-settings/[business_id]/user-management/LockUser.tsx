'use client';

import { Switch } from '@/components/ui/switch';
import { toggleUserState } from '@/lib/actions';
import { User } from '@/lib/types';
import clsx from 'clsx';

const LockUser = ({ user, accountId }: { user: User; accountId: string }) => {
  return (
    <>
      <div className={clsx(!user.lock && 'font-bold')}>Not Locked</div>
      <Switch name="lock" defaultChecked={user.lock} onClick={() => toggleUserState(user.id, accountId, user.lock)} />
      <div className={clsx(user.lock && 'font-bold')}>Locked</div>
    </>
  );
};

export default LockUser;
