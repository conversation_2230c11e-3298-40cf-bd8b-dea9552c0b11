import { getAccount } from '@/lib/pocket';
import type { Metadata } from 'next';
import LockUser from './LockUser';

export const metadata: Metadata = {
  title: 'Business Account Setting',
  description: '...',
};

export default async ({ params }: { params: Record<string, string> }) => {
  // noStore();
  const account = await getAccount(params.business_id);
  return (
    <>
      <div className="bg-white p-4 shadow-md rounded-lg">
        <div className="text-2xl mb-4">Users</div>
        <div className="flex flex-col space-y-2">
          {account.expand?.pb_user_id?.map((user) => {
            return (
              <>
                <div className="flex items-center space-x-4">
                  <div className="w-72">{user.name}</div>
                  <div className="w-24">{user.type}</div>
                  <LockUser user={user} accountId={params.account_id} />
                </div>
              </>
            );
          })}
        </div>
      </div>
    </>
  );
};
