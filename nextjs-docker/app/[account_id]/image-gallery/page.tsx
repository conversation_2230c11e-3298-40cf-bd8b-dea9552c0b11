'use server';
import { getPb } from '@/lib/pocket';
import { IImage } from '@/lib/types';
import ImageGallery from './ImageGallery';

export default async ({ params }: { params: Record<string, string> }) => {
  const pb = await getPb();
  const images = await pb.collection<IImage>('images').getFullList({ filter: `account = "${params.account_id}"`, sort: '-created' });
  // tailwind images in a gallery format

  const _images = [];
  for (const image of images) {
    if (!image.url) {
      image.url = pb.files.getURL(image, image.file as string);
    }
    _images.push({ ...image });
  }

  // console.log(_images.forEach((image) => console.log(image.url)));

  return (
    <div>
      <ImageGallery images={_images} accountId={params.account_id} />
    </div>
  );
};
