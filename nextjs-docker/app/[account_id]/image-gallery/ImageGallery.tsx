'use client';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Toaster } from '@/components/ui/toaster';
import { useToast } from '@/components/ui/use-toast';
import { uploadImage } from '@/lib/pocket';
import { IImage } from '@/lib/types';
import { Loader2 } from 'lucide-react';
import Image from 'next/image';
import Link from 'next/link';
import React, { useEffect, useRef, useState } from 'react';
import { useFormState, useFormStatus } from 'react-dom';

const ImageGallery = ({ images, accountId }: { images: IImage[]; accountId: string }) => {
  const { toast } = useToast();

  const initialState = {
    message: {
      status: 0,
      description: '',
    },
  };

  const [state, formAction] = useFormState(uploadImage, initialState);
  const [isFileSelected, setIsFileSelected] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setIsFileSelected(!!(e.target.files && e.target.files.length > 0));
  };
  useEffect(() => {
    if (state?.message.status == 400) {
      toast({
        variant: 'destructive',
        description: state.message.description,
      });
    } else if (state?.message.status == 200) {
      toast({
        variant: 'success',
        description: state.message.description,
      });
      // Clear the file input after successful upload
      if (fileInputRef.current) {
        fileInputRef.current.value = '';
        setIsFileSelected(false);
      }
    }
  }, [state]);

  return (
    <div className="p-6">
      <form action={formAction} className="max-w-lg mx-auto">
        <input type="text" hidden defaultValue={accountId} name="accountId" />
        <label className="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Upload file</label>
        <Input
          name="file"
          className="block w-full text-sm text-gray-900 border border-gray-300 rounded-lg cursor-pointer bg-gray-50 dark:text-gray-400 focus:outline-none dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400"
          aria-describedby="user_avatar_help"
          id="user_avatar"
          type="file"
          onChange={handleFileChange}
          ref={fileInputRef}
        />
        <div className="mt-1 mb-2 text-sm text-gray-500 dark:text-gray-300" id="user_avatar_help"></div>
        <UploadImageBtn disabled={!isFileSelected} />
      </form>
      <div className="flex gap-4 m-10 flex-wrap">
        {images.map((image) => {
          return (
            <div
              className="flex items-center max-w-sm p-6 bg-white border border-gray-200 rounded-lg shadow hover:bg-gray-100 dark:bg-gray-800 dark:border-gray-700 dark:hover:bg-gray-700"
              key={image.id}
            >
              <Image width={400} height={400} key={image.id} src={image.url as unknown as string} alt={image.name} />
            </div>
          );
        })}
      </div>

      <Link className="ml-10 text-blue-500 underline" href={`/${accountId}/templates`}>
        Back to templates
      </Link>
      <Toaster />
    </div>
  );
};

const UploadImageBtn = ({ disabled }: { disabled?: boolean }) => {
  const { pending } = useFormStatus();
  return (
    <>
      {pending ? (
        <Button className="bg-gradient-to-r from-purple-500 to-pink-500 hover:bg-gradient-to-l" disabled>
          <Loader2 className="h-4 w-4 animate-spin mr-2" />
          Uploading
        </Button>
      ) : (
        <Button className="bg-gradient-to-r from-purple-500 to-pink-500 hover:bg-gradient-to-l" type="submit" disabled={disabled}>
          Upload
        </Button>
      )}
    </>
  );
};

export default ImageGallery;
