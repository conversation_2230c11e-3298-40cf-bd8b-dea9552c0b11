'use client';

import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Button } from '@/components/ui/button';
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from '@/components/ui/collapsible';
import { Command, CommandEmpty, CommandGroup, CommandInput, CommandItem, CommandList } from '@/components/ui/command';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Toaster } from '@/components/ui/toaster';
import { useToast } from '@/components/ui/use-toast';
import { removeLogo, updateBusiness, updateLogo } from '@/lib/actions';
import { Account, IRole, User } from '@/lib/types';
import { cn, countryCurrencyMap } from '@/lib/utils';
import { Check, ChevronDown, MinusCircle, PlusCircle } from 'lucide-react';
import { useRef, useState } from 'react';
import SaveButton from './SaveButton';
import Image from 'next/image';
import { Input } from '@/components/ui/input';
import dayjs from 'dayjs';
import { Switch } from '@/components/ui/switch';

type IProfileProps = {
  params: Record<string, string>;
  user: User;
  roles: IRole[];
  account: Account;
  logo: string | null;
  avatar: string | null;
};

export function Profile({ params, user, roles, account, logo, avatar }: IProfileProps) {
  const ref = useRef<HTMLFormElement>(null);
  const [selectedCountry, setSelectedCountry] = useState<string>(account.business_location ?? 'Pakistan');
  const [selectedCurrency, setSelectedCurrency] = useState<string>(account.currency ?? 'USD');
  const [city, setCity] = useState<string>(account.city ?? '');
  const [address, setAddress] = useState<string>(account.address ?? '');
  const [TRN, setTRN] = useState<string>(account.TRN ?? '');
  const [open, setOpen] = useState(false);
  const [value, setValue] = useState(account.business_location ?? '');
  const [preview, setPreview] = useState(false);

  console.log('preview is ' + preview);

  const { toast } = useToast();
  const countryOptions = Object.keys(countryCurrencyMap).map((country) => ({
    value: country,
    label: country,
  }));

  const css = `
    select {
      position: absolute !important
    }
  `;

  const handleBussinessLogoChange = async (event: any) => {
    const file = event.target.files[0];
    if (file) {
      const formData = new FormData();
      formData.append('file', file);
      try {
        await updateLogo(user.id, params.account_id, formData, 'business');
      } catch (error) {
        alert('some error occured');
      }
    }
  };

  const handleUserImageChange = async (event: any) => {
    const file = event.target.files[0];
    if (file) {
      const formData = new FormData();
      formData.append('file', file);
      try {
        await updateLogo(user.id, params.account_id, formData, 'user');
      } catch (error) {
        alert('some error occured');
      }
    }
  };

  const handleBussinessLogoDelete = async (event: any) => {
    try {
      await removeLogo(user.id, params.account_id, 'business');
    } catch (error) {
      alert('some error occured');
    }
  };

  const handleUserImageDelete = async (event: any) => {
    try {
      await removeLogo(user.id, params.account_id, 'user');
    } catch (error) {
      alert('some error occured');
    }
  };

  return (
    <div className="p-5 px-10 bg-gray-100">
      <Toaster />
      {/* <style>{css}</style> */}
      <div className="font-bold text-2xl mb-4">Your Profile</div>
      <div className="bg-white p-6 rounded-lg border border-gray-200">
        <form
          ref={ref}
          action={async (formData) => {
            await updateBusiness(params.account_id, formData, selectedCountry, city, address, TRN);
            toast({
              variant: 'success',
              description: 'The profile has been updated successfully',
            });
          }}
        >
          <div className="space-y-4">
            <div>
              <div className="font-bold text-gray-700">Name:</div>
              <div>{user.name}</div>
            </div>
            <div>
              <div className="font-bold text-gray-700">Email:</div>
              <div>{user.email}</div>
            </div>
            <div>
              <div className="font-bold text-gray-700">Username:</div>
              <div>{user.username}</div>
            </div>
            <div>
              <div className="font-bold text-gray-700">Type:</div>
              <div>{user.type}</div>
            </div>
            <div>
              <div className="font-bold text-gray-700">Roles:</div>
              {user.type == 'admin' ? (
                <div>All roles (User type is admin)</div>
              ) : (
                <Collapsible defaultOpen={false} className="bg-white border border-gray-200 mt-2 rounded-lg shadow-sm w-1/4">
                  <CollapsibleTrigger className="flex items-center w-full p-3 bg-gray-100 rounded-t-lg hover:bg-gray-200 transition-colors duration-300">
                    <div className="flex-1 flex items-center space-x-2">
                      <div className="font-medium text-gray-700">List of roles assigned to you</div>
                    </div>
                    <div>
                      <ChevronDown className="text-gray-600 transition-transform duration-300" />
                    </div>
                  </CollapsibleTrigger>
                  <CollapsibleContent className="p-4 bg-gray-50 rounded-b-lg">
                    <div className="flex flex-col space-y-2">
                      {roles.map((role) => (
                        <div key={role.name} className="flex items-center p-2 bg-white border border-gray-300 rounded-md shadow-sm">
                          <div className="font-medium text-gray-800">{role.name}</div>
                        </div>
                      ))}
                    </div>
                  </CollapsibleContent>
                </Collapsible>
              )}
            </div>
            <div>
              <div className="font-bold text-gray-700">User Image:</div>
              <Avatar className="hover:cursor-pointer h-16 w-16">
                <AvatarImage src={avatar ?? user.avatar} alt="@shadcn" />
                <AvatarFallback className="font-bold">
                  {user.name
                    .toUpperCase()
                    .split(' ')
                    .map((chars) => chars.charAt(0))
                    .join('')}
                </AvatarFallback>
              </Avatar>
              <div className="flex items-center gap-2 mt-2">
                <div className="flex items-center">
                  <input id="file-upload" type="file" name="business_logo" className="hidden" onChange={handleUserImageChange} />

                  <label
                    htmlFor="file-upload"
                    className="flex cursor-pointer items-center bg-green-600 hover:bg-green-500 text-white font-medium py-2 px-2 rounded"
                  >
                    <PlusCircle className="mr-1 h-5 w-5" /> Upload
                  </label>
                </div>
                <div onClick={handleUserImageDelete} className="flex items-center">
                  <label className="flex cursor-pointer items-center bg-red-600 hover:bg-red-500 text-white font-medium py-2 px-2 rounded">
                    <MinusCircle className="mr-1 h-5 w-5" /> Remove
                  </label>
                </div>
              </div>
              {/* <Input className="w-1/4 mt-2" name="user_avatar" type="file" /> */}
            </div>
            {user.type === 'admin' && (
              <>
                <div className="flex">
                  <div className="space-y-2" style={{ flex: 0.5 }}>
                    <div>
                      <div className="font-bold text-gray-700">Business Location:</div>
                      <div className="w-1/2">
                        <Popover open={open} onOpenChange={setOpen}>
                          <PopoverTrigger asChild>
                            <Button variant="outline" role="combobox" aria-expanded={open} className="w-full justify-between">
                              {selectedCountry ?? 'Select Business location'}
                              <ChevronDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
                            </Button>
                          </PopoverTrigger>
                          <PopoverContent className="w-full p-0">
                            <Command>
                              <CommandInput placeholder="Search country" />
                              <CommandList>
                                <CommandEmpty>No country found.</CommandEmpty>
                                <CommandGroup>
                                  {countryOptions.map((country) => (
                                    <CommandItem
                                      key={country.value}
                                      value={country.value}
                                      onSelect={(currentValue) => {
                                        setSelectedCountry(currentValue);
                                        setSelectedCurrency('USD');
                                        setOpen(false);
                                        setValue(currentValue);
                                      }}
                                    >
                                      <Check className={cn('mr-2 h-4 w-4', value === country.value ? 'opacity-100' : 'opacity-0')} />
                                      {country.label}
                                    </CommandItem>
                                  ))}
                                </CommandGroup>
                              </CommandList>
                            </Command>
                          </PopoverContent>
                        </Popover>
                      </div>
                    </div>
                    <div className="font-bold text-gray-700">Preferred Currency:</div>
                    <div className="w-1/2">
                      <Select name="currency" onValueChange={(value) => setSelectedCurrency(value)} value={selectedCurrency}>
                        <SelectTrigger>
                          <SelectValue placeholder="Select a currency" />
                        </SelectTrigger>
                        <SelectContent>
                          {countryCurrencyMap[selectedCountry]?.map((currency) => (
                            <SelectItem key={currency} value={currency}>
                              {currency}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>
                    <div>
                      <div className="font-bold text-gray-700">Address:</div>
                      <div className="w-1/2">
                        <Input
                          value={address}
                          onChange={(e) => {
                            setAddress(e.target.value);
                          }}
                          className="border border-gray-200"
                        />
                      </div>
                    </div>
                    <div>
                      <div className="font-bold text-gray-700">City:</div>
                      <div className="w-1/2">
                        <Input
                          value={city}
                          onChange={(e) => {
                            setCity(e.target.value);
                          }}
                          className="border border-gray-200"
                        />
                      </div>
                    </div>
                    <div>
                      <div className="font-bold text-gray-700">Transaction Reference Number:</div>
                      <div className="w-1/2">
                        <Input
                          value={TRN}
                          onChange={(e) => {
                            setTRN(e.target.value);
                          }}
                          className="border border-gray-200"
                        />
                      </div>
                    </div>

                    <div className="flex items-center gap-3 border rounded-md w-fit p-2">
                      <Switch
                        checked={preview}
                        onCheckedChange={() => {
                          setPreview(!preview);
                        }}
                      />
                      <div className="font-bold text-gray-700">View Invoice Preview</div>
                    </div>
                  </div>

                  <div className="border" style={{ flex: 0.5, boxSizing: 'border-box', padding: '10px', display: preview ? 'block' : 'none' }}>
                    <div style={{ display: 'flex', justifyContent: 'space-between', marginBottom: '30px' }}>
                      <h1 style={{ fontSize: '24px', margin: 0 }}>Tax Invoice</h1>
                      <div style={{ textAlign: 'right' }}>
                        <p style={{ margin: 0 }}>Order 1253627</p>
                        <p style={{ margin: 0 }}>{dayjs().format('MMMM DD, YYYY')}</p>
                      </div>
                    </div>

                    <div style={{ display: 'grid', gridTemplateColumns: 'repeat(3, 1fr)', gap: '20px' }}>
                      <div>
                        <h3 style={{ fontSize: '14px', color: '#666', marginBottom: '8px' }}>From</h3>
                        <p style={{ margin: 0 }}>{account.name}</p>
                        <p style={{ margin: 0 }}>
                          {address}, {city}
                        </p>
                        <p style={{ margin: 0 }}>{account.business_location}</p>
                        <p style={{ margin: 0 }}>{account.display_phone_number}</p>
                        <p style={{ margin: 0 }}>TRN: {TRN}</p>
                      </div>

                      <div>
                        <h3 style={{ fontSize: '14px', color: '#666', marginBottom: '8px' }}>Bill to</h3>
                        <p style={{ margin: 0 }}>Ali Ahmed</p>
                        <p style={{ margin: 0 }}>Next Innovations</p>
                        <p style={{ margin: 0 }}>Dubai Silicon Oasis</p>
                        <p style={{ margin: 0 }}>Dubai 341111</p>
                        <p style={{ margin: 0 }}>United Arab Emirates</p>
                      </div>

                      <div>
                        <h3 style={{ fontSize: '14px', color: '#666', marginBottom: '8px' }}>Ship to</h3>
                        <p style={{ margin: 0 }}>Sara Malik</p>
                        <p style={{ margin: 0 }}>Innovative Solutions</p>
                        <p style={{ margin: 0 }}>Business Bay</p>
                        <p style={{ margin: 0 }}>Dubai 344211</p>
                        <p style={{ margin: 0 }}>United Arab Emirates</p>
                        <p style={{ margin: 0 }}>+971523456789</p>
                      </div>
                    </div>
                  </div>
                </div>
                <div>
                  <div className="font-bold text-gray-700">Business Logo:</div>
                  {logo ? (
                    <Avatar className="hover:cursor-pointer h-16 w-16">
                      <AvatarImage src={logo ?? '/assets/WT_Logo.png'} alt="business logo" />
                      <AvatarFallback className="font-bold">
                        {user.name
                          .toUpperCase()
                          .split(' ')
                          .map((chars) => chars.charAt(0))
                          .join('')}
                      </AvatarFallback>
                    </Avatar>
                  ) : (
                    <Image src={'/assets/WT_Logo.png'} width={50} height={50} alt="logo" className="mt-2 w-auto" />
                  )}
                  <div className="flex items-center gap-2 mt-2">
                    <div className="flex items-center">
                      <input id="file-upload2" type="file" name="business_logo" className="hidden" onChange={handleBussinessLogoChange} />

                      <label
                        htmlFor="file-upload2"
                        className="flex cursor-pointer items-center bg-green-600 hover:bg-green-500 text-white font-medium py-2 px-2 rounded"
                      >
                        <PlusCircle className="mr-1 h-5 w-5" /> Upload
                      </label>
                    </div>
                    <div onClick={handleBussinessLogoDelete} className="flex items-center">
                      <label className="flex cursor-pointer items-center bg-red-600 hover:bg-red-500 text-white font-medium py-2 px-2 rounded">
                        <MinusCircle className="mr-1 h-5 w-5" /> Remove
                      </label>
                    </div>
                  </div>
                  {/* <Input className="w-1/4 mt-2" name="business_logo" type="file" onChange={handleBussinessLogoChange} /> */}
                </div>
              </>
            )}
          </div>
          {user.type == 'admin' && <SaveButton />}
        </form>
      </div>
    </div>
  );
}
