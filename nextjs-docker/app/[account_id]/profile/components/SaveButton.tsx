import { Button } from '@/components/ui/button';
import { Loader2 } from 'lucide-react';
import React from 'react';
import { useFormStatus } from 'react-dom';

const SaveButton = () => {
  const { pending } = useFormStatus();
  return (
    <>
      {pending ? (
        <Button disabled className="mt-8">
          Saving <Loader2 className="ml-2 animate-spin" />
        </Button>
      ) : (
        <Button type="submit" className="mt-8">
          Save
        </Button>
      )}
    </>
  );
};

export default SaveButton;
