import { getAccount, getPb } from '@/lib/pocket';
import { User } from '@/lib/types';
import { server_component_pb } from '@/state/pb/server_component_pb';
import { Profile } from './components/Profile';

export default async ({ params }: { params: Record<string, string> }) => {
  const { pb, cookies } = await server_component_pb();
  const pb2 = await getPb();
  const user = pb.authStore.record as User;
  const account = await getAccount(params.account_id);
  let logo: string | null = null;
  let user_avatar: string | null = null;

  if (user.avatar) {
    user_avatar = pb.files.getURL(user, user.avatar, { thumb: '50x50' });
  }
  if (account.logo) {
    logo = pb2.files.getUrl(account, account.logo, { thumb: '50x50' });
  }

  if (!user) {
    return;
  }

  const roles = user.expand?.roles ?? [];
  return <Profile params={params} user={user} roles={roles} account={account} logo={logo} avatar={user_avatar} />;
};
