import { GeneralBusinessHoursComponent } from '@/components/general-business-hours';
import { getAccount, getApiSettings } from '@/lib/pocket';

interface ApiSettingsPageProps {
  params: {
    account_id: string;
  };
}

const OfficeSettingsPage = async ({ params }: ApiSettingsPageProps) => {
  const { account_id } = params;
  const apiSettings = await getApiSettings(account_id);
  const account = await getAccount(account_id);
  return (
    <div className="m-10">
      <h1 className="mb-4 text-2xl">Office Settings for Account {account.name}</h1>
      <GeneralBusinessHoursComponent />
    </div>
  );
};

export default OfficeSettingsPage;
