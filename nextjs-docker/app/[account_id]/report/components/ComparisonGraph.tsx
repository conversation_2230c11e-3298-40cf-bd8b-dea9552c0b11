'use client';

import { CalendarI<PERSON>, Check, ChevronDown, ChevronsDown, ChevronsUpDown } from 'lucide-react';
import { CartesianGrid, LabelList, Line, LineChart, Tooltip, XAxis } from 'recharts';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { ChartConfig, ChartContainer } from '@/components/ui/chart';
import { z } from 'zod';
import { endOfDay, format, isWithinInterval, startOfDay } from 'date-fns';
import { useEffect, useState } from 'react';
import { zodResolver } from '@hookform/resolvers/zod';
import { useForm } from 'react-hook-form';
import { Form, FormControl, FormDescription, FormField, FormItem, FormLabel } from '@/components/ui/form';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { Button } from '@/components/ui/button';
import { cn, extractUniqueAdIds, extractUniqueTags } from '@/lib/utils';
import { Calendar } from '@/components/ui/calendar';
import dayjs from 'dayjs';
import MultipleSelector from '@/components/ui/multiple-selector';
import countrycodes from '@/lib/countrylistfiltered.json';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Command, CommandEmpty, CommandGroup, CommandInput, CommandItem, CommandList } from '@/components/ui/command';
import { IExpandedLead, IReferral } from '@/lib/types';

export const description = 'A line chart with multiple datasets';

const chartConfig = {
  desktop: {
    label: 'leads',
    color: 'hsl(var(--chart-1))',
  },
} satisfies ChartConfig;

const FormSchema = z.object({
  startDate: z.date({
    required_error: 'Start date is required.',
  }),
  endDate: z.date({
    required_error: 'End date is required.',
  }),
  country: z.string().optional(),
});

export function ComparisonGraph({
  leadsData,
  adIdArray,
}: {
  leadsData: IExpandedLead[];
  adIdArray: {
    label: string;
    value: string;
  }[];
}) {
  const [filteredData, setFilteredData] = useState<any[]>([]);
  const [selectedCountry, setSelectedCountry] = useState<string | null>(null);
  const [selectedTags, setSelectedTags] = useState<string[]>([]);
  const [tempAdId1, setTempAdId1] = useState<string | undefined>();
  const [tempAdId2, setTempAdId2] = useState<string | undefined>();
  const [open1, setOpen1] = useState(false);
  const [open2, setOpen2] = useState(false);
  const [selectedDate, setSelectedDate] = useState<string | null>(null);
  const [selectedCounts, setSelectedCounts] = useState<{ count1?: number; count2?: number }>({});

  const form = useForm<z.infer<typeof FormSchema>>({
    resolver: zodResolver(FormSchema),
    defaultValues: {
      startDate: new Date(new Date().getFullYear(), 0, 1),
      endDate: new Date(),
    },
  });

  useEffect(() => {
    const { startDate, endDate } = form.getValues();

    let newFilteredData = leadsData.filter((lead: any) =>
      isWithinInterval(new Date(lead.created), {
        start: startOfDay(startDate),
        end: endOfDay(endDate),
      })
    );

    if (selectedCountry) {
      const selectedCountryObj = countrycodes.find((country: any) => country.iso['alpha-2'] === selectedCountry || country.name === selectedCountry);
      if (selectedCountryObj) {
        newFilteredData = newFilteredData.filter((lead: any) => lead.country === selectedCountryObj.iso['alpha-2'] || lead.country === selectedCountryObj.name);
      }
    }

    if (selectedTags.length > 0) {
      newFilteredData = newFilteredData.filter((lead: any) => lead.tags?.some((tag: string) => selectedTags.includes(tag)));
    }

    const aggregatedData = newFilteredData.reduce((acc: any, lead: any) => {
      const dateKey = dayjs(lead.created).format('MMMM D, YYYY');
      acc[dateKey] = acc[dateKey] || { created: dateKey, count1: 0, count2: 0 };
      if (lead?.referral?.source_id === tempAdId1) {
        acc[dateKey].count1 += 1;
      }
      if (lead?.referral?.source_id === tempAdId2) {
        acc[dateKey].count2 += 1;
      }
      return acc;
    }, {});

    const chartData = Object.values(aggregatedData);

    setFilteredData(chartData);
  }, [form.watch('startDate'), form.watch('endDate'), selectedCountry, selectedTags, tempAdId1, tempAdId2]);

  return (
    <Card>
      <CardHeader className="flex justify-center items-center">
        <CardTitle className="mb-2 text-3xl">Leads Comparison - AD ID</CardTitle>
        <CardDescription>
          <Form {...form}>
            <form className="space-y-8">
              <div className="flex items-center space-x-4">
                <FormField
                  control={form.control}
                  name="startDate"
                  render={({ field }) => (
                    <FormItem className="flex flex-col">
                      <FormLabel>Start Date</FormLabel>
                      <Popover>
                        <PopoverTrigger className="border-2 border-gray-400" asChild>
                          <FormControl>
                            <Button variant={'outline'} className={cn('w-[240px] pl-3 text-left font-normal', !field.value && 'text-muted-foreground')}>
                              {field.value ? format(field.value, 'PPP') : <span>Pick a date</span>}
                              <CalendarIcon className="ml-auto h-4 w-4 opacity-50" />
                            </Button>
                          </FormControl>
                        </PopoverTrigger>
                        <PopoverContent className="w-auto p-0" align="start">
                          <Calendar
                            mode="single"
                            selected={field.value}
                            onSelect={field.onChange}
                            disabled={(date) => date > new Date() || date < new Date('1900-01-01')}
                            initialFocus
                          />
                        </PopoverContent>
                      </Popover>
                      <FormDescription>Pick the start date for the range.</FormDescription>
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="endDate"
                  render={({ field }) => (
                    <FormItem className="flex flex-col">
                      <FormLabel>End Date</FormLabel>
                      <Popover>
                        <PopoverTrigger className="border-2 border-gray-400" asChild>
                          <FormControl>
                            <Button variant={'outline'} className={cn('w-[240px] pl-3 text-left font-normal', !field.value && 'text-muted-foreground')}>
                              {field.value ? format(field.value, 'PPP') : <span>Pick a date</span>}
                              <CalendarIcon className="ml-auto h-4 w-4 opacity-50" />
                            </Button>
                          </FormControl>
                        </PopoverTrigger>
                        <PopoverContent className="w-auto p-0" align="start">
                          <Calendar
                            mode="single"
                            selected={field.value}
                            onSelect={field.onChange}
                            disabled={(date) => date > new Date() || date < new Date('1900-01-01')}
                            initialFocus
                          />
                        </PopoverContent>
                      </Popover>
                      <FormDescription>Pick the end date for the range.</FormDescription>
                    </FormItem>
                  )}
                />
                <MultipleSelector
                  className="max-w-[220px] mb-2"
                  defaultOptions={countrycodes.map((country: any) => ({
                    label: country.name,
                    value: country.iso['alpha-2'],
                  }))}
                  badgeClassName="bg-gray-600 text-white"
                  placeholder="Filter by country"
                  hidePlaceholderWhenSelected
                  maxSelected={1}
                  onChange={(option) => setSelectedCountry(option.length > 0 ? option[0].value : null)}
                />
                <MultipleSelector
                  badgeClassName="bg-gray-600 text-white"
                  showTagIcon
                  className="max-w-[220px] mb-2"
                  onChange={(selected) => setSelectedTags(selected?.map((item) => item.value) || [])}
                  defaultOptions={extractUniqueTags(leadsData)}
                  placeholder="Filter by tags"
                />
              </div>
              <div className="flex flex-col items-center">
                <Label className=" text-center text-black font-bold text-lg mb-2">Enter the service/Ad ids to compare</Label>
                <div className="flex items-center justify-center gap-2">
                  <Popover open={open1} onOpenChange={setOpen1}>
                    <PopoverTrigger asChild>
                      <Button
                        style={{ borderColor: chartConfig.desktop.color }}
                        variant="outline"
                        role="combobox"
                        className="w-[200px] justify-between border-2"
                      >
                        {tempAdId1 ? adIdArray.find((item: any) => item.value === tempAdId1)?.label?.toString() : 'Select ad id 1'}
                        <ChevronDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
                      </Button>
                    </PopoverTrigger>
                    <PopoverContent className="w-[200px] p-0">
                      <Command>
                        <CommandInput placeholder="Search source..." />
                        <CommandList>
                          <CommandEmpty>No source found.</CommandEmpty>
                          <CommandGroup>
                            {adIdArray.map((item: any) => (
                              <CommandItem
                                key={item.value}
                                value={item.value}
                                onSelect={(currentValue) => {
                                  setTempAdId1(currentValue === tempAdId1 ? '' : currentValue);
                                  setOpen1(false);
                                }}
                              >
                                <Check className={cn('mr-2 h-4 w-4', tempAdId1 === item.value ? 'opacity-100' : 'opacity-0')} />
                                {item.label}
                              </CommandItem>
                            ))}
                          </CommandGroup>
                        </CommandList>
                      </Command>
                    </PopoverContent>
                  </Popover>
                  <Popover open={open2} onOpenChange={setOpen2}>
                    <PopoverTrigger asChild>
                      <Button style={{ borderColor: '#8884d8' }} variant="outline" role="combobox" className="w-[200px] justify-between border-2">
                        {tempAdId2 ? adIdArray.find((item: any) => item.value === tempAdId2)?.label?.toString() : 'Select ad id 2'}
                        <ChevronDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
                      </Button>
                    </PopoverTrigger>
                    <PopoverContent className="w-[200px] p-0">
                      <Command>
                        <CommandInput placeholder="Search source..." />
                        <CommandList>
                          <CommandEmpty>No source found.</CommandEmpty>
                          <CommandGroup>
                            {adIdArray.map((item: any) => (
                              <CommandItem
                                key={item.value}
                                value={item.value}
                                onSelect={(currentValue) => {
                                  setTempAdId2(currentValue === tempAdId2 ? '' : currentValue);
                                  setOpen2(false);
                                }}
                              >
                                <Check className={cn('mr-2 h-4 w-4', tempAdId2 === item.value ? 'opacity-100' : 'opacity-0')} />
                                {item.label}
                              </CommandItem>
                            ))}
                          </CommandGroup>
                        </CommandList>
                      </Command>
                    </PopoverContent>
                  </Popover>
                </div>
              </div>
            </form>
          </Form>
        </CardDescription>
      </CardHeader>
      <CardContent>
        <ChartContainer className="aspect-[3/1]" config={chartConfig}>
          <LineChart
            accessibilityLayer
            data={filteredData}
            margin={{
              top: 20,
              right: 50,
              left: 50,
              bottom: 50,
            }}
            onMouseMove={(e) => {
              if (e.activePayload && e.activePayload.length > 0) {
                const { created, count1, count2 } = e.activePayload[0].payload;
                setSelectedDate(created);
                setSelectedCounts({ count1, count2 });
              }
            }}
            onMouseLeave={() => {
              setSelectedDate(null);
              setSelectedCounts({});
            }}
          >
            <CartesianGrid strokeDasharray="3 3" />
            <XAxis dataKey="created" tick={false} />
            <Tooltip formatter={(value, name) => [`${value}`, `${name}`]} cursor={{ strokeDasharray: '3 3' }} />
            <Line type="monotone" dataKey="count1" stroke={chartConfig.desktop.color} name="Ad ID 1" strokeWidth={3} dot={{ r: 5 }}>
              <LabelList position="top" offset={10} className="fill-foreground" fontSize={12} />
            </Line>
            <Line type="monotone" dataKey="count2" stroke="#8884d8" name="Ad ID 2" strokeWidth={3} dot={{ r: 5 }}>
              <LabelList position="top" offset={10} className="fill-foreground" fontSize={12} />
            </Line>
          </LineChart>
        </ChartContainer>
      </CardContent>
    </Card>
  );
}
