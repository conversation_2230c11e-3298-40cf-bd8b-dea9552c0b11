'use client';

import React, { useEffect, useState } from 'react';
import { CalendarIcon } from 'lucide-react';
import { <PERSON>, Bar<PERSON>hart, CartesianGrid, LabelList, XAxis, YAxis } from 'recharts';
import { endOfDay, format, isWithinInterval, startOfDay } from 'date-fns';
import { zodResolver } from '@hookform/resolvers/zod';
import { useForm } from 'react-hook-form';
import { z } from 'zod';

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { ChartConfig, ChartContainer, ChartTooltip, ChartTooltipContent } from '@/components/ui/chart';
import { Form, FormControl, FormDescription, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { But<PERSON> } from '@/components/ui/button';
import { cn } from '@/lib/utils';
import { Calendar } from '@/components/ui/calendar';
import { ICampaignReport } from '@/lib/types';
import { TotalCampaigns } from './totalCampaigns';

interface IChartData {
  name: string;
  count: any;
}

const chartConfig = {
  desktop: {
    label: 'Campaigns',
    color: 'hsl(var(--chart-1))',
  },
} satisfies ChartConfig;

const FormSchema = z.object({
  startDate: z.date({
    required_error: 'Start date is required.',
  }),
  endDate: z.date({
    required_error: 'End date is required.',
  }),
});

export function CampaignsAgent({ campaignsData }: { campaignsData: ICampaignReport[] }) {
  const [filteredData, setFilteredData] = useState<IChartData[]>([]);
  const form = useForm<z.infer<typeof FormSchema>>({
    resolver: zodResolver(FormSchema),
    defaultValues: {
      startDate: new Date(new Date().getFullYear(), 0, 1),
      endDate: new Date(),
    },
  });

  useEffect(() => {
    const { startDate, endDate } = form.getValues();

    const newFilteredData = campaignsData.filter((campaign: any) =>
      isWithinInterval(new Date(campaign.createdAt), {
        start: startOfDay(startDate),
        end: endOfDay(endDate),
      })
    );

    const aggregatedData = newFilteredData.reduce((acc: any, campaign: any) => {
      acc[campaign.createdBy] = (acc[campaign.createdBy] || 0) + 1;
      return acc;
    }, {});

    const chartData = Object.entries(aggregatedData).map(([name, count]) => ({
      name,
      count,
    }));
    setFilteredData(chartData);
  }, [form.watch('startDate'), form.watch('endDate')]);

  return (
    <Card className="p-5 h-1/2">
      <TotalCampaigns campaignsData={campaignsData} />
      <CardHeader className="flex justify-center items-center">
        <CardTitle className="mb-2 text-3xl">Campaigns Created - Agent wise</CardTitle>
        <CardDescription>
          <Form {...form}>
            <form className="space-y-8">
              <div className="flex items-center space-x-4">
                <FormField
                  control={form.control}
                  name="startDate"
                  render={({ field }) => (
                    <FormItem className="flex flex-col">
                      <FormLabel>Start Date</FormLabel>
                      <Popover>
                        <PopoverTrigger className="border-2 border-gray-400" asChild>
                          <FormControl>
                            <Button variant={'outline'} className={cn('w-[240px] pl-3 text-left font-normal', !field.value && 'text-muted-foreground')}>
                              {field.value ? format(field.value, 'PPP') : <span>Pick a date</span>}
                              <CalendarIcon className="ml-auto h-4 w-4 opacity-50" />
                            </Button>
                          </FormControl>
                        </PopoverTrigger>
                        <PopoverContent className="w-auto p-0" align="start">
                          <Calendar
                            mode="single"
                            selected={field.value}
                            onSelect={field.onChange}
                            disabled={(date) => date > new Date() || date < new Date('1900-01-01')}
                            initialFocus
                          />
                        </PopoverContent>
                      </Popover>
                      <FormDescription>Pick the start date for the range.</FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="endDate"
                  render={({ field }) => (
                    <FormItem className="flex flex-col">
                      <FormLabel>End Date</FormLabel>
                      <Popover>
                        <PopoverTrigger className="border-2 border-gray-400" asChild>
                          <FormControl>
                            <Button variant={'outline'} className={cn('w-[240px] pl-3 text-left font-normal', !field.value && 'text-muted-foreground')}>
                              {field.value ? format(field.value, 'PPP') : <span>Pick a date</span>}
                              <CalendarIcon className="ml-auto h-4 w-4 opacity-50" />
                            </Button>
                          </FormControl>
                        </PopoverTrigger>
                        <PopoverContent className="w-auto p-0" align="start">
                          <Calendar
                            mode="single"
                            selected={field.value}
                            onSelect={field.onChange}
                            disabled={(date) => date > new Date() || date < new Date('1900-01-01')}
                            initialFocus
                          />
                        </PopoverContent>
                      </Popover>
                      <FormDescription>Pick the end date for the range.</FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
            </form>
          </Form>
        </CardDescription>
      </CardHeader>
      <CardContent className=" p-0">
        {filteredData.length > 0 ? (
          <ChartContainer className="p-0 space-y-0 aspect-[4/1]" config={chartConfig}>
            <BarChart
              className=" p-1"
              accessibilityLayer
              data={filteredData}
              layout="vertical"
              barCategoryGap={1}
              barGap={2}
              margin={{ right: 15 }}
              // height={200}
            >
              <CartesianGrid strokeDasharray="3 3" stroke="#ccc" vertical={true} />
              <XAxis type="number" dataKey="count" hide />
              <YAxis dataKey="name" type="category" tickLine={false} tickMargin={10} axisLine={false} width={100} />
              <ChartTooltip cursor={false} content={<ChartTooltipContent hideLabel />} />
              <Bar
                dataKey="count"
                fill="var(--color-desktop)"
                radius={5}
                barSize={filteredData.length >= 7 ? (filteredData.length >= 10 ? 10 : 15) : filteredData.length >= 5 ? 30 : 40}
              >
                <LabelList dataKey="count" position="right" className="font-semibold text-md" />
              </Bar>
            </BarChart>
          </ChartContainer>
        ) : (
          <div className="text-center text-3xl font-bold text-foreground my-5">0 Campaign</div>
        )}
      </CardContent>
      {/* <CardFooter className="flex-col items-start gap-2 text-sm">
                <div className="flex gap-2 font-medium leading-none">
                    Trending up by 5.2% this month <TrendingUp className="h-4 w-4" />
                </div>
                <div className="leading-none text-muted-foreground">
                    Showing total campaigns created within the selected date range.
                </div>
            </CardFooter> */}
    </Card>
    // </div>
  );
}
