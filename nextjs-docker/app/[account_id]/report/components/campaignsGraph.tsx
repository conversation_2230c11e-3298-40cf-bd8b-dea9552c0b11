'use client';

import { CalendarIcon, TrendingUp } from 'lucide-react';
import { CartesianGrid, LabelList, Line, LineChart, XAxis } from 'recharts';

import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { ChartConfig, ChartContainer, ChartTooltip, ChartTooltipContent } from '@/components/ui/chart';
import { z } from 'zod';
import { endOfDay, format, isWithinInterval, startOfDay } from 'date-fns';
import { useEffect, useState } from 'react';
import { zodResolver } from '@hookform/resolvers/zod';
import { useForm } from 'react-hook-form';
import { Form, FormControl, FormDescription, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { Button } from '@/components/ui/button';
import { cn } from '@/lib/utils';
import { Calendar } from '@/components/ui/calendar';

export const description = 'A line chart with a label';

interface IChartData {
  name: string;
  count: any;
}

const chartConfig = {
  desktop: {
    label: 'Campaigns',
    color: 'hsl(var(--chart-1))',
  },
} satisfies ChartConfig;

const FormSchema = z.object({
  startDate: z.date({
    required_error: 'Start date is required.',
  }),
  endDate: z.date({
    required_error: 'End date is required.',
  }),
});

export function CampaignsGraph({
  campaignsData,
  currency,
}: {
  campaignsData: {
    date: string;
    cost: Number;
  }[];
  currency: string;
}) {
  const [filteredData, setFilteredData] = useState<any>([]);
  const form = useForm<z.infer<typeof FormSchema>>({
    resolver: zodResolver(FormSchema),
    defaultValues: {
      startDate: new Date(new Date().getFullYear(), 0, 1),
      endDate: new Date(),
    },
  });

  useEffect(() => {
    const { startDate, endDate } = form.getValues();

    const newFilteredData = campaignsData?.filter((campaign: any) =>
      isWithinInterval(new Date(campaign.date), {
        start: startOfDay(startDate),
        end: endOfDay(endDate),
      })
    );

    const _chartData = Object.entries(
      newFilteredData?.reduce((acc: any, { date, cost }: { date: any; cost: any }) => {
        if (acc[date]) {
          acc[date] += parseFloat(cost.toFixed(2));
        } else {
          acc[date] = parseFloat(cost.toFixed(2));
        }
        return acc;
      }, {})
    ).map(([date, cost]) => ({ date, cost }));

    setFilteredData(
      _chartData.map((item: any) => {
        return {
          date: item.date,
          cost: parseFloat(item.cost.toFixed(2)),
        };
      })
    );
  }, [form.watch('startDate'), form.watch('endDate')]);
  return (
    <Card>
      <CardHeader className="flex justify-center items-center">
        <CardTitle className="mb-2 text-3xl">Per day cost of messages sent - In {currency}</CardTitle>
        <CardDescription>
          <Form {...form}>
            <form className="space-y-8">
              <div className="flex items-center space-x-4">
                <FormField
                  control={form.control}
                  name="startDate"
                  render={({ field }) => (
                    <FormItem className="flex flex-col">
                      <FormLabel>Start Date</FormLabel>
                      <Popover>
                        <PopoverTrigger className="border-2 border-gray-400" asChild>
                          <FormControl>
                            <Button variant={'outline'} className={cn('w-[240px] pl-3 text-left font-normal', !field.value && 'text-muted-foreground')}>
                              {field.value ? format(field.value, 'PPP') : <span>Pick a date</span>}
                              <CalendarIcon className="ml-auto h-4 w-4 opacity-50" />
                            </Button>
                          </FormControl>
                        </PopoverTrigger>
                        <PopoverContent className="w-auto p-0" align="start">
                          <Calendar
                            mode="single"
                            selected={field.value}
                            onSelect={field.onChange}
                            disabled={(date) => date > new Date() || date < new Date('1900-01-01')}
                            initialFocus
                          />
                        </PopoverContent>
                      </Popover>
                      <FormDescription>Pick the start date for the range.</FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="endDate"
                  render={({ field }) => (
                    <FormItem className="flex flex-col">
                      <FormLabel>End Date</FormLabel>
                      <Popover>
                        <PopoverTrigger className="border-2 border-gray-400" asChild>
                          <FormControl>
                            <Button variant={'outline'} className={cn('w-[240px] pl-3 text-left font-normal', !field.value && 'text-muted-foreground')}>
                              {field.value ? format(field.value, 'PPP') : <span>Pick a date</span>}
                              <CalendarIcon className="ml-auto h-4 w-4 opacity-50" />
                            </Button>
                          </FormControl>
                        </PopoverTrigger>
                        <PopoverContent className="w-auto p-0" align="start">
                          <Calendar
                            mode="single"
                            selected={field.value}
                            onSelect={field.onChange}
                            disabled={(date) => date > new Date() || date < new Date('1900-01-01')}
                            initialFocus
                          />
                        </PopoverContent>
                      </Popover>
                      <FormDescription>Pick the end date for the range.</FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
            </form>
          </Form>
        </CardDescription>
      </CardHeader>
      <CardContent>
        <ChartContainer className="aspect-[3/1]" config={chartConfig}>
          <LineChart
            accessibilityLayer
            data={filteredData}
            margin={{
              top: 20,
              right: 20,
              left: 20,
              bottom: 20,
            }}
          >
            <CartesianGrid vertical={true} />
            <XAxis display="none" dataKey="date" tickLine={false} axisLine={false} tickMargin={8} tickFormatter={(value) => value.slice(0, 3)} />
            <ChartTooltip cursor={false} content={<ChartTooltipContent indicator="line" />} />
            <Line
              dataKey="cost"
              type="natural"
              stroke="#40b86a"
              strokeWidth={2}
              dot={{
                fill: '#40b86a',
              }}
              activeDot={{
                r: 6,
              }}
            >
              <LabelList position="top" offset={10} className="fill-foreground" fontSize={12} />
            </Line>
          </LineChart>
        </ChartContainer>
      </CardContent>
      <CardFooter className="flex-col items-start gap-2 text-sm">
        <div className="flex gap-2 font-medium leading-none self-center">
          Showing daily costing <TrendingUp className="h-4 w-4" />
        </div>
      </CardFooter>
    </Card>
  );
}
