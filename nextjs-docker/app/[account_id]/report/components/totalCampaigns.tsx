'use client';

import * as React from 'react';
import { TrendingUp } from 'lucide-react';
import { Label, Pie, Pie<PERSON><PERSON> } from 'recharts';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { ChartConfig, ChartContainer, ChartTooltip, ChartTooltipContent } from '@/components/ui/chart';
import { useForm } from 'react-hook-form';
import { Select, SelectContent, SelectGroup, SelectItem, SelectLabel, SelectTrigger, SelectValue } from '@/components/ui/select';
import { ICampaignReport } from '@/lib/types';

const years = [(new Date().getFullYear() - 1).toString(), new Date().getFullYear().toString()];

const colors = ['#FF6384', '#36A2EB', '#FFCE56', '#4BC0C0', '#9966FF', '#FF9F40', '#FF6384', '#36A2EB', '#FFCE56', '#4BC0C0', '#9966FF', '#FF9F40'];
const months = [
  { label: 'January', key: 'January' },
  { label: 'February', key: 'February' },
  { label: 'March', key: 'March' },
  { label: 'April', key: 'April' },
  { label: 'May', key: 'May' },
  { label: 'June', key: 'June' },
  { label: 'July', key: 'July' },
  { label: 'August', key: 'August' },
  { label: 'September', key: 'September' },
  { label: 'October', key: 'October' },
  { label: 'November', key: 'November' },
  { label: 'December', key: 'December' },
];
const chartConfig = {
  campaigns: {
    label: 'Campaigns',
    color: '#FFFFFF',
  },
} satisfies ChartConfig;

export function TotalCampaigns({ campaignsData }: { campaignsData: ICampaignReport[] }) {
  const [selectedYear, setSelectedYear] = React.useState(new Date().getFullYear());
  const form = useForm({
    defaultValues: {
      year: new Date(),
    },
  });

  const filteredData = React.useMemo(() => {
    const monthlyData = Array(12)
      .fill(0)
      .map((_, index) => ({
        month: months[index].label,
        campaigns: 0,
        fill: colors[index],
      }));

    campaignsData.forEach((campaign) => {
      const campaignYear = new Date(campaign.createdAt).getFullYear();
      if (campaignYear === selectedYear) {
        const month = new Date(campaign.createdAt).getMonth();
        monthlyData[month].campaigns += 1;
      }
    });

    return monthlyData;
  }, [selectedYear]);

  const totalCampaigns = React.useMemo(() => {
    return filteredData.reduce((acc, curr) => acc + curr.campaigns, 0);
  }, [filteredData]);

  const onSubmit = (values: any) => {
    setSelectedYear(new Date(values.year).getFullYear());
  };

  return (
    <Card className="flex flex-col">
      <CardHeader className="items-center pb-0">
        <CardTitle className="mb-2 text-3xl">Total Campaigns created</CardTitle>
        <CardDescription>
          <span className="font-medium">Year</span>
          <Select
            defaultValue={new Date().getFullYear().toString()}
            name="year"
            onValueChange={(val) => {
              setSelectedYear(parseInt(val));
            }}
          >
            <SelectTrigger className="border border-gray-400 w-48">
              <SelectValue placeholder="Select Year" />
            </SelectTrigger>
            <SelectContent>
              <SelectGroup>
                <SelectLabel>Year</SelectLabel>
                {years.map((year) => {
                  return (
                    <SelectItem key={year} value={year}>
                      {year}
                    </SelectItem>
                  );
                })}
              </SelectGroup>
            </SelectContent>
          </Select>
        </CardDescription>
      </CardHeader>
      <CardContent className="flex-1 pb-0">
        {totalCampaigns > 0 ? (
          <ChartContainer config={chartConfig} className="mx-auto aspect-square max-h-[250px]">
            <PieChart>
              <ChartTooltip cursor={false} content={<ChartTooltipContent hideLabel />} />
              <Pie data={filteredData} dataKey="campaigns" nameKey="month" innerRadius={60} strokeWidth={5}>
                <Label
                  content={({ viewBox }) => {
                    if (viewBox && 'cx' in viewBox && 'cy' in viewBox) {
                      return (
                        <text x={viewBox.cx} y={viewBox.cy} textAnchor="middle" dominantBaseline="middle">
                          <tspan x={viewBox.cx} y={viewBox.cy} className="fill-foreground text-3xl font-bold">
                            {totalCampaigns}
                          </tspan>
                          <tspan x={viewBox.cx} y={(viewBox.cy || 0) + 24} className="fill-muted-foreground">
                            Campaigns
                          </tspan>
                        </text>
                      );
                    }
                  }}
                />
              </Pie>
            </PieChart>
          </ChartContainer>
        ) : (
          <div className="text-center text-3xl font-bold text-foreground my-5">0 Campaign</div>
        )}
      </CardContent>
      <CardFooter className="flex-col gap-2 text-sm">
        <div className="flex items-center gap-2 font-medium leading-none">
          Showing total campaigns sent for the selected year <TrendingUp className="h-4 w-4" />
        </div>
      </CardFooter>
    </Card>
  );
}
