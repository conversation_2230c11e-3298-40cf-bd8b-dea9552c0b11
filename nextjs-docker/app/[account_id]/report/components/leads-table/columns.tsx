'use client';

import { Button } from '@/components/ui/button';
import { Country, IExpandedLead, Meta } from '@/lib/types';
import { ColumnDef } from '@tanstack/react-table';
import { ArrowUpDown } from 'lucide-react';
import dayjs from 'dayjs';
import { convertFirstToUpperCase } from '@/lib/utils';
import { HoverCard, HoverCardContent, HoverCardTrigger } from '@/components/ui/hover-card';

//fixed duplicate key erros and accesor errors. NO sorting needed.
const getMetaColumns = (metaKeys: Meta[]) => {
  return metaKeys.map((metaKey) => {
    return {
      id: metaKey.key == 'phone_number' ? 'meta._phone_number' : `meta.${metaKey.key.toString()}`,
      maxSize: 150,
      header: ({ column }: { column: any }) => {
        return (
          <Button variant="ghost" className="px-0 mb-0" onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}>
            {metaKey.key}
          </Button>
        );
      },
      cell: ({ row }: { row: any }) => {
        if (row.original.meta) {
          const meta = JSON.parse(row.original.meta);
          return <div>{meta[metaKey.key]}</div>;
        }
        return <div>-</div>;
      },
    };
  });
};

export const leadColumns = (meta: Meta[]): ColumnDef<IExpandedLead>[] => [
  {
    accessorKey: 'name',
    size: 150,
    maxSize: 300,
    minSize: 150,
    header: ({ column }) => {
      return (
        <Button variant="ghost" className="px-0 mb-0" onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}>
          Name
          <ArrowUpDown className="ml-2 h-4 w-4" />
        </Button>
      );
    },
    cell: ({ row }) => {
      return <div className="text-sm font-medium">{row.original.name}</div>;
    },
  },
  {
    accessorKey: 'phone_number',
    size: 150,
    maxSize: 300,
    minSize: 150,
    header: ({ column }) => {
      return (
        <Button variant="ghost" className="px-0 mb-0" onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}>
          Phone Number
          <ArrowUpDown className="ml-2 h-4 w-4" />
        </Button>
      );
    },
    cell: ({ row }) => {
      return <div className="text-sm font-medium">{row.original.phone_number}</div>;
    },
  },
  {
    accessorKey: 'tags',
    filterFn: 'arrIncludesSome',
    maxSize: 300,
    header: ({ column }) => {
      return (
        <Button variant="ghost" className="px-0 mb-0" onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}>
          Tags
          <ArrowUpDown className="ml-2 h-4 w-4" />
        </Button>
      );
    },
    cell: ({ row }) => {
      const tags = row.original.tags;
      return (
        <HoverCard>
          <HoverCardTrigger asChild>
            <div className="flex items-center hover:cursor-help flex-wrap gap-2">
              {tags?.length > 0 ? (
                tags?.slice(0, 3).map((tag, index) => (
                  <div
                    key={index}
                    className="inline-flex items-center whitespace-nowrap border px-2 bg-secondary text-secondary-foreground rounded-lg border-solidfont-normal"
                  >
                    {convertFirstToUpperCase(tag)}
                  </div>
                ))
              ) : (
                <div>N/A</div>
              )}
              <div className="underline decoration-dotted decoration-green-600 font-semibold">{tags?.length > 3 && `+${tags.length - 3}`}</div>
            </div>
          </HoverCardTrigger>
          <HoverCardContent className="w-80">
            <div className="mb-2">All Tags:</div>
            <div className="flex gap-2 flex-wrap">
              {tags?.map((tag, index) => (
                <div
                  key={index}
                  className="inline-flex items-center whitespace-nowrap border px-2 bg-secondary text-secondary-foreground rounded-lg border-solidfont-normal"
                >
                  {convertFirstToUpperCase(tag)}
                </div>
              ))}
            </div>
          </HoverCardContent>
        </HoverCard>
      );
    },
  },
  {
    accessorKey: 'country',
    id: 'Country',
    maxSize: 300,
    filterFn: (row, columnId, filterValue: Country) => {
      const iso2 = filterValue.iso['alpha-2'].toLocaleLowerCase();
      const iso3 = filterValue.iso['alpha-3'].toLocaleLowerCase();
      const name = filterValue.name.toLocaleLowerCase();
      const country = row.original.country.toLocaleLowerCase();
      return country === iso2 || country === iso3 || country === name;
    },

    header: ({ column }) => {
      return (
        <Button variant="ghost" className="px-0 mb-0" onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}>
          Country
          <ArrowUpDown className="ml-2 h-4 w-4" />
        </Button>
      );
    },
    cell: ({ row }) => {
      return <div>{row.original?.country ?? 'N/A'}</div>;
    },
  },
  ...getMetaColumns(meta),
  {
    accessorKey: 'created',
    header: ({ column }) => {
      return (
        <Button variant="ghost" className="px-0 mb-0" onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}>
          Created At
          <ArrowUpDown className="ml-2 h-4 w-4" />
        </Button>
      );
    },
    cell: ({ row }) => {
      return <div className="text-sm">{dayjs(row.getValue('created')).format('MMMM D, YYYY h:mm A')}</div>;
    },
  },

  {
    accessorKey: 'referral.source_id',
    id: 'ad',
    size: 150,
    maxSize: 300,
    minSize: 150,
    header: ({ column }) => {
      return (
        <Button variant="ghost" className="px-0 mb-0" onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}>
          Ad ID
          <ArrowUpDown className="ml-2 h-4 w-4" />
        </Button>
      );
    },
    cell: ({ row }) => {
      return <div className="text-sm font-medium">{row.original?.referral?.source_id ?? 'N/A'}</div>;
    },
  },
];
