'use client';

import { CalendarI<PERSON>, Check, ChevronDown, X } from 'lucide-react';
import { CartesianGrid, LabelList, Line, LineChart, XAxis } from 'recharts';

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { ChartConfig, ChartContainer, ChartTooltip, ChartTooltipContent } from '@/components/ui/chart';
import { z } from 'zod';
import { endOfDay, format, isWithinInterval, startOfDay } from 'date-fns';
import { useEffect, useState } from 'react';
import { zodResolver } from '@hookform/resolvers/zod';
import { useForm } from 'react-hook-form';
import { Form, FormControl, FormDescription, FormField, FormItem, FormLabel } from '@/components/ui/form';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { Button } from '@/components/ui/button';
import { cn, extractUniqueTags } from '@/lib/utils';
import { Calendar } from '@/components/ui/calendar';
import dayjs from 'dayjs';
import MultipleSelector from '@/components/ui/multiple-selector';
import countrycodes from '@/lib/countrylistfiltered.json';
import { Input } from '@/components/ui/input';
import { Command, CommandEmpty, CommandGroup, CommandInput, CommandItem, CommandList } from '@/components/ui/command';
import { IExpandedLead, IReferral } from '@/lib/types';

export const description = 'A line chart with a label';

const chartConfig = {
  desktop: {
    label: 'leads',
    color: 'hsl(var(--chart-1))',
  },
} satisfies ChartConfig;

const FormSchema = z.object({
  startDate: z.date({
    required_error: 'Start date is required.',
  }),
  endDate: z.date({
    required_error: 'End date is required.',
  }),
  country: z.string().optional(),
});

export function LeadsGraph({
  leadsData,
  adIdArray,
}: {
  leadsData: IExpandedLead[];
  adIdArray: {
    label: string;
    value: string;
  }[];
}) {
  const [filteredData, setFilteredData] = useState<any[]>([]);
  const [selectedCountry, setSelectedCountry] = useState<string | null>(null);
  const [selectedTags, setSelectedTags] = useState<string[]>([]);
  const [tempAdId, setTempAdId] = useState<string | undefined>();
  const [open, setOpen] = useState(false);

  const form = useForm<z.infer<typeof FormSchema>>({
    resolver: zodResolver(FormSchema),
    defaultValues: {
      startDate: new Date(new Date().getFullYear(), 0, 1),
      endDate: new Date(),
    },
  });

  useEffect(() => {
    const { startDate, endDate } = form.getValues();

    let newFilteredData = leadsData.filter((lead: any) =>
      isWithinInterval(new Date(lead.created), {
        start: startOfDay(startDate),
        end: endOfDay(endDate),
      })
    );

    if (selectedCountry) {
      const selectedCountryObj = countrycodes.find((country: any) => country.iso['alpha-2'] === selectedCountry || country.name === selectedCountry);
      if (selectedCountryObj) {
        newFilteredData = newFilteredData.filter((lead: any) => lead.country === selectedCountryObj.iso['alpha-2'] || lead.country === selectedCountryObj.name);
      }
    }

    if (selectedTags.length > 0) {
      newFilteredData = newFilteredData.filter((lead: any) => lead.tags?.some((tag: string) => selectedTags.includes(tag)));
    }

    if (tempAdId) {
      newFilteredData = newFilteredData.filter((lead: any) => lead?.referral?.source_id === tempAdId);
    }

    const aggregatedData = newFilteredData.reduce((acc: any, lead: any) => {
      acc[dayjs(lead.created).format('MMMM D, YYYY')] = (acc[dayjs(lead.created).format('MMMM D, YYYY')] || 0) + 1;
      return acc;
    }, {});

    const chartData = Object.entries(aggregatedData).map(([created, count]) => ({
      created,
      count,
    }));

    setFilteredData(chartData);
  }, [form.watch('startDate'), form.watch('endDate'), selectedCountry, selectedTags, tempAdId]);

  return (
    <Card>
      <CardHeader className="flex justify-center items-center">
        <CardTitle className="mb-2 text-3xl">Leads created per day</CardTitle>
        <CardDescription>
          <Form {...form}>
            <form className="space-y-8">
              <div className="flex items-center space-x-4">
                <FormField
                  control={form.control}
                  name="startDate"
                  render={({ field }) => (
                    <FormItem className="flex flex-col">
                      <FormLabel>Start Date</FormLabel>
                      <Popover>
                        <PopoverTrigger className="border-2 border-gray-400" asChild>
                          <FormControl>
                            <Button variant={'outline'} className={cn('w-[240px] pl-3 text-left font-normal', !field.value && 'text-muted-foreground')}>
                              {field.value ? format(field.value, 'PPP') : <span>Pick a date</span>}
                              <CalendarIcon className="ml-auto h-4 w-4 opacity-50" />
                            </Button>
                          </FormControl>
                        </PopoverTrigger>
                        <PopoverContent className="w-auto p-0" align="start">
                          <Calendar
                            mode="single"
                            selected={field.value}
                            onSelect={field.onChange}
                            disabled={(date) => date > new Date() || date < new Date('1900-01-01')}
                            initialFocus
                          />
                        </PopoverContent>
                      </Popover>
                      <FormDescription>Pick the start date for the range.</FormDescription>
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="endDate"
                  render={({ field }) => (
                    <FormItem className="flex flex-col">
                      <FormLabel>End Date</FormLabel>
                      <Popover>
                        <PopoverTrigger className="border-2 border-gray-400" asChild>
                          <FormControl>
                            <Button variant={'outline'} className={cn('w-[240px] pl-3 text-left font-normal', !field.value && 'text-muted-foreground')}>
                              {field.value ? format(field.value, 'PPP') : <span>Pick a date</span>}
                              <CalendarIcon className="ml-auto h-4 w-4 opacity-50" />
                            </Button>
                          </FormControl>
                        </PopoverTrigger>
                        <PopoverContent className="w-auto p-0" align="start">
                          <Calendar
                            mode="single"
                            selected={field.value}
                            onSelect={field.onChange}
                            disabled={(date) => date > new Date() || date < new Date('1900-01-01')}
                            initialFocus
                          />
                        </PopoverContent>
                      </Popover>
                      <FormDescription>Pick the end date for the range.</FormDescription>
                    </FormItem>
                  )}
                />
                <MultipleSelector
                  defaultOptions={countrycodes.map((country: any) => ({
                    label: country.name,
                    value: country.iso['alpha-2'],
                  }))}
                  placeholder="Filter by country"
                  hidePlaceholderWhenSelected
                  maxSelected={1}
                  onChange={(option) => setSelectedCountry(option.length > 0 ? option[0].value : null)}
                  className="bg-white mb-2"
                  badgeClassName="bg-gray-600 text-white"
                />
                <MultipleSelector
                  badgeClassName="bg-gray-600 text-white"
                  showTagIcon
                  className="max-w-[220px] mb-2"
                  onChange={(selected) => setSelectedTags(selected?.map((item) => item.value) || [])}
                  defaultOptions={extractUniqueTags(leadsData)}
                  placeholder="Filter by tags"
                />
                <Popover open={open} onOpenChange={setOpen}>
                  <PopoverTrigger className="max-w-[220px] mb-2" asChild>
                    <Button variant="outline" role="combobox" className="max-w-[220px] justify-between border-2 border-gray-400">
                      {tempAdId ? adIdArray.find((item: any) => item.value === tempAdId)?.label?.toString() : 'Select ad id'}
                      <ChevronDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
                    </Button>
                  </PopoverTrigger>
                  <PopoverContent className="w-[200px] p-0">
                    <Command>
                      <CommandInput placeholder="Search source..." />
                      <CommandList>
                        <CommandEmpty>No source found.</CommandEmpty>
                        <CommandGroup>
                          {adIdArray.map((item) => (
                            <CommandItem
                              key={item.value}
                              value={item.value}
                              onSelect={(currentValue) => {
                                setTempAdId(currentValue === tempAdId ? '' : currentValue);
                                setOpen(false);
                              }}
                            >
                              <Check className={cn('mr-2 h-4 w-4', tempAdId === item.value ? 'opacity-100' : 'opacity-0')} />
                              {item.label}
                            </CommandItem>
                          ))}
                        </CommandGroup>
                      </CommandList>
                    </Command>
                  </PopoverContent>
                </Popover>
                {tempAdId && (
                  <Button
                    className="bg-blue-100 hover:bg-blue-200 mb-2"
                    onClick={() => {
                      setTempAdId('');
                    }}
                  >
                    <X className="text-blue-500 " />
                  </Button>
                )}
              </div>
            </form>
          </Form>
        </CardDescription>
      </CardHeader>
      <CardContent>
        <ChartContainer className="aspect-[3/1]" config={chartConfig}>
          <LineChart
            accessibilityLayer
            data={filteredData}
            margin={{
              top: 20,
              right: 50,
              left: 50,
              bottom: 50,
            }}
          >
            <CartesianGrid strokeDasharray="3 3" />
            <XAxis dataKey="created" tick={false} />
            <ChartTooltip cursor={false} content={<ChartTooltipContent indicator="line" />} />
            <Line
              dataKey="count"
              type="natural"
              stroke="#40b86a"
              strokeWidth={2}
              dot={{
                fill: '#40b86a',
              }}
              activeDot={{
                stroke: '#40b86a',
                strokeWidth: 2,
                fill: '#fff',
                r: 4,
              }}
            >
              <LabelList position="top" offset={10} className="fill-foreground" fontSize={12} />
            </Line>
          </LineChart>
        </ChartContainer>
      </CardContent>
    </Card>
  );
}
