import { getAccount, getReport, getUser } from '@/lib/pocket';
import Report from './report';

export default async ({ params }: { params: Record<string, string> }) => {
  const [account] = await Promise.all([getAccount(params.account_id)]);
  if (account.access_token) {
    const [user, report] = await Promise.all([getUser(), getReport(params.account_id)]);
    const {
      accountMessages,
      convertTo,
      publishedCampaigns,
      messagesLiveChatArray,
      messagesCampaignsArrayAgent,
      messagesLiveChatArrayAgent,
      account,
      campaignsArray,
      messagesCampaignsArray,
      publishedCampaignsAgent,
      leadData,
      metaKeys,
    } = report;
    return (
      <div>
        <Report
          user={user}
          accountMessages={accountMessages}
          currency={convertTo}
          publishedCampaigns={publishedCampaigns}
          params={params}
          messagesLiveChatArray={messagesLiveChatArray}
          messagesCampaignsArrayAgent={messagesCampaignsArrayAgent}
          messagesLiveChatArrayAgent={messagesLiveChatArrayAgent}
          account={account}
          campaignsArray={campaignsArray}
          messagesCampaignsArray={messagesCampaignsArray}
          publishedCampaignsAgent={publishedCampaignsAgent}
          leadData={leadData}
          metaKeys={metaKeys}
        />
      </div>
    );
  }
};
