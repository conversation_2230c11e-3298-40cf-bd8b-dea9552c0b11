'use client';

import { But<PERSON> } from "@/components/ui/button";
import type { IExpandedMessageAndRepliedList } from "@/lib/types";
import { ColumnDef } from "@tanstack/react-table";
import { ArrowUpDown, Check, CheckCheck, CircleX } from "lucide-react";
import dayjs from "dayjs";
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";

export const columns: ColumnDef<IExpandedMessageAndRepliedList>[] = [
  {
    accessorKey: 'message',
    header: ({ column }) => {
      return (
        <Button variant="ghost" className="px-0 mb-0" onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}>
          Message
          <ArrowUpDown className="ml-2 h-4 w-4" />
        </Button>
      );
    },
    cell: ({ row }) => {
      return (
        <TooltipProvider>
          <Tooltip delayDuration={0}>
            <TooltipTrigger asChild>
              <div className="truncate max-w-[200px]">{row.getValue('message')}</div>
            </TooltipTrigger>
            <TooltipContent>
              <p>{row.getValue('message')}</p>
            </TooltipContent>
          </Tooltip>
        </TooltipProvider>
      );
    },
  },
  {
    accessorKey: 'user',
    header: ({ column }) => {
      return (
        <Button variant="ghost" className="px-0 mb-0" onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}>
          Recipient
          <ArrowUpDown className="ml-2 h-4 w-4" />
        </Button>
      );
    },
    cell: ({ row }) => {
      return <div>{row.original.expand?.user?.phone_number ?? 'N/A'}</div>;
    },
  },
  {
    accessorKey: 'delivery_status',
    header: ({ column }) => {
      return (
        <Button variant="ghost" className="px-0 mb-0" onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}>
          Delivery Status
          <ArrowUpDown className="ml-2 h-4 w-4" />
        </Button>
      );
    },
    cell: ({ row }) => {
      const status = row.getValue('delivery_status') as string;
      return (
        <div className="flex items-center gap-2">
          <StatusIcon status={status} />
          <span>{status}</span>
        </div>
      );
    },
  },
  {
    accessorKey: 'created',
    header: ({ column }) => {
      return (
        <Button variant="ghost" className="px-0 mb-0" onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}>
          Created At
          <ArrowUpDown className="ml-2 h-4 w-4" />
        </Button>
      );
    },
    cell: ({ row }) => {
      const date = row.getValue('created') as string;
      return <div>{dayjs(date).format('MMMM D, YYYY h:mm A')}</div>;
    },
  },
];

function StatusIcon({ status }: { status: string }) {
  if (status === 'sent') {
    return <Check className="h-4 w-4 text-gray-800" />;
  }
  if (status === 'delivered') {
    return <CheckCheck className="h-4 w-4 text-gray-800" />;
  }
  if (status === 'read') {
    return <CheckCheck className="h-4 w-4 text-[#2746d1]" />;
  }
  if (status === 'failed') {
    return <CircleX className="h-4 w-4 text-[#d12727]" />;
  }
  return <Check className="h-4 w-4 text-gray-800" />;
}
