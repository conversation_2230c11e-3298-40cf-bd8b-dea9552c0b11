import { getAccountMessages } from './api';
import { columns } from './components/columns';
import { DataTable } from './components/data-table';

const ApiDeliveryPage = async ({ params }: { params: Record<string, string> }) => {
  const accountId = params.account_id;
  const messages = await getAccountMessages(accountId);

  return (
    <div className="px-6 py-4">
      <DataTable columns={columns} data={messages} />
    </div>
  );
};

export default ApiDeliveryPage;
