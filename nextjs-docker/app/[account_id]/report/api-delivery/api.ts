'use server';

import { getPb } from '@/lib/pocket';
import { IExpandedMessage, IMessage } from '@/lib/types';

/**
 * Fetches all messages for a specific account
 * @param accountId The account ID to fetch messages for
 * @returns Array of messages with their delivery status
 */
export async function getAccountMessages(accountId: string): Promise<IMessage[]> {
  try {
    const pb = await getPb();
    const messages = await pb.collection<IExpandedMessage>('messages').getFullList({
      filter: `account = "${accountId}" && system = "API"`,
      sort: '-created',
      expand: 'created_by,template,user',
    });
    
    // Convert file URLs if needed
    return messages.map((message) => {
      if (message.file) {
        return { 
          ...message, 
          url: pb.files.getURL(message, message.file),
          created: new Date(message.created)
        };
      }
      return { 
        ...message,
        created: new Date(message.created)
      };
    });
  } catch (error) {
    console.error('Error fetching account messages:', error);
    return [];
  }
}
