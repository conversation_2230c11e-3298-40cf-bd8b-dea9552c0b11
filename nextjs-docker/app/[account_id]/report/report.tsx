'use client';
import ConnectYourBusiness from '@/components/shared/ConnectYourBusiness';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { ICampaignReport, IExpandedAccount, IExpandedCampaign, IExpandedLead, Meta, User } from '@/lib/types';
import { extractUniqueAdIds } from '@/lib/utils';
import { CampaignsAgent } from './components/CampaignsAgent';
import { ComparisonGraph } from './components/ComparisonGraph';
import { columns } from './components/cost-table/columns';
import { DataTable } from './components/cost-table/data-table';
import { leadColumns } from './components/leads-table/columns';
import { LeadTable } from './components/leads-table/data-table';
import { LeadsGraph } from './components/LeadsGraphs';
import { MessagesAgent } from './components/MessagesAgent';
import { TotalCampaigns } from './components/totalCampaigns';
import { TotalMessages } from './components/TotalMessages';
import { CampaignsGraph } from './components/campaignsGraph';
import Link from 'next/link';

export default function Report({
  user,
  accountMessages,
  currency,
  publishedCampaigns,
  params,
  messagesLiveChatArray,
  messagesCampaignsArrayAgent,
  messagesLiveChatArrayAgent,
  account,
  campaignsArray,
  messagesCampaignsArray,
  publishedCampaignsAgent,
  leadData,
  metaKeys,
}: {
  user: User;
  accountMessages: { date: string; cost: Number }[];
  currency: string;
  publishedCampaigns: (IExpandedCampaign & { cost?: number })[];
  params: Record<string, string>;
  messagesLiveChatArray: ICampaignReport[];
  messagesCampaignsArrayAgent: ICampaignReport[];
  messagesLiveChatArrayAgent: ICampaignReport[];
  account: IExpandedAccount;
  campaignsArray: ICampaignReport[];
  messagesCampaignsArray: ICampaignReport[];
  publishedCampaignsAgent: (IExpandedCampaign & { cost?: number })[];
  leadData: IExpandedLead[];
  metaKeys: Meta[];
}) {
  const adIdArray = extractUniqueAdIds(leadData);

  if (user.type == 'admin' || (user.type == 'agent' && user.view_all?.includes('campaigns'))) {
    return (
      <div className="bg-gray-100 pt-4">
        <div className='mx-6 pt-2 bg-slate-300 rounded-md p-2 w-32 text-blue-600 underline'>
          <Link href={`/${params.account_id}/report/api-delivery`}>API reports</Link>
        </div>
        <Tabs defaultValue="general" className="w-full p-6">
          <TabsList className="grid w-full grid-cols-3 bg-gray-200">
            <TabsTrigger value="general">General</TabsTrigger>
            <TabsTrigger value="costing">Costing</TabsTrigger>
            <TabsTrigger value="leads">Leads</TabsTrigger>
          </TabsList>
          <TabsContent value="general" className="space-y-6">
            <div className="h-[50%]">
              <CampaignsAgent campaignsData={campaignsArray} />
            </div>
            <div className="h-[50%]">
              <MessagesAgent campaignsData={messagesCampaignsArray} campaign={true} />
            </div>

            <div className="h-[50%]">
              <MessagesAgent campaignsData={messagesLiveChatArray} campaign={false} />
            </div>
          </TabsContent>
          <TabsContent value="costing" className="space-y-6">
            <div className="h-[50%]">
              <CampaignsGraph campaignsData={accountMessages} currency={currency} />
            </div>
            <div className="h-[50%]">
              <DataTable columns={columns} data={publishedCampaigns} account_id={params.account_id} currency={currency} />
            </div>
          </TabsContent>
          <TabsContent value="leads" className="space-y-6">
            <div className="h-[50%]">
              <LeadsGraph leadsData={leadData} adIdArray={adIdArray} />
            </div>
            <div className="h-[50%]">
              <ComparisonGraph leadsData={leadData} adIdArray={adIdArray} />
            </div>
            <div className="h-[50%]">
              <LeadTable metaKeys={metaKeys} columns={leadColumns(metaKeys)} data={leadData} account_id={params.account_id} adIdArray={adIdArray} />
            </div>
          </TabsContent>
        </Tabs>
      </div>
    );
  } else if (user.type == 'agent') {
    return (
      <div className="bg-gray-100">
        <Tabs defaultValue="general" className="w-full p-6">
          <TabsList className="grid w-full grid-cols-3 bg-gray-200">
            <TabsTrigger value="general">General</TabsTrigger>
            <TabsTrigger value="costing">Costing</TabsTrigger>
            <TabsTrigger value="leads">Leads</TabsTrigger>
          </TabsList>
          <TabsContent value="general" className="space-y-6">
            <div className="h-[50%]">
              <TotalCampaigns campaignsData={campaignsArray} />
            </div>
            <div className="h-[50%]">
              <TotalMessages campaignsData={messagesCampaignsArrayAgent} campaign={true} />
            </div>

            <div className="h-[50%]">
              <TotalMessages campaignsData={messagesLiveChatArrayAgent} campaign={false} />
            </div>
          </TabsContent>
          <TabsContent value="costing" className="space-y-6">
            <div className="h-[50%]">
              <CampaignsGraph campaignsData={accountMessages} currency={currency} />
            </div>
            <div className="h-[50%]">
              <DataTable columns={columns} data={publishedCampaignsAgent} account_id={params.account_id} currency={currency} />
            </div>
          </TabsContent>
          <TabsContent value="leads" className="space-y-6">
            <div className="h-[50%]">
              <LeadsGraph leadsData={leadData} adIdArray={adIdArray} />
            </div>
            <div className="h-[50%]">
              <ComparisonGraph leadsData={leadData} adIdArray={adIdArray} />
            </div>
            <div className="h-[50%]">
              <LeadTable metaKeys={metaKeys} columns={leadColumns(metaKeys)} data={leadData} account_id={params.account_id} adIdArray={adIdArray} />
            </div>
          </TabsContent>
        </Tabs>
      </div>
    );
  } else {
    return (
      <div className="bg-gray-100  w-full flex items-center justify-center h-full">
        <ConnectYourBusiness account={account} />
      </div>
    );
  }
}
