import { Loader2 } from 'lucide-react';

const Loading = () => {
  return (
    <div className="p-6 h-screen w-full bg-gray-100">
      <div className="flex w-full mb-4 rounded-lg space-x-4">
        <div className="bg-white w-full h-10 border border-gray-200 rounded-lg flex items-center justify-center">
          <div className="h-2 w-10 bg-gray-100"></div>
        </div>
        <div className="bg-white w-full h-10 border border-gray-200 rounded-lg flex items-center justify-center">
          <div className="h-2 w-10 bg-gray-100"></div>
        </div>
        <div className="bg-white w-full h-10 border border-gray-200 rounded-lg flex items-center justify-center">
          <div className="h-2 w-10 bg-gray-100"></div>
        </div>
      </div>
      <div className="bg-white h-full flex items-center p-4 flex-col rounded-lg">
        <div className="h-4 w-64 mb-4"></div>
        <div className="bg-gray-50 w-52 h-10 mb-4"></div>
        <div className="h-80 w-80 rounded-full bg-gray-50 flex items-center justify-center">
          <Loader2 className="animate-spin h-20 w-20 text-gray-300" />
        </div>
      </div>
    </div>
  );
};

export default Loading;
