import { getShopifyOrderDetails } from '@/lib/pocket';
import InvoicePage from './components/ViewPdf';
export const dynamic = 'force-dynamic';
export const fetchCache = 'default-no-store';
export default async ({ params }: { params: Record<string, string> }) => {
  const shopifyOrder = await getShopifyOrderDetails(params.invoice_id);
  const orderDetails = shopifyOrder.orderDetails;
  const logo_url = shopifyOrder.logo_url;
  const accountDetails = shopifyOrder.accountDetails;
  // console.log('invoice created : ' + orderDetails.invoice_pdf)
  // console.log('the object is ')
  // console.log(orderDetails)

  return (
    <InvoicePage
      order={orderDetails.response_object}
      account_id={orderDetails.account}
      accountDetails={accountDetails}
      invoice_id={params.invoice_id}
      invoice_pdf={orderDetails?.expand?.invoice_pdf?.url}
      logo_url={logo_url}
    />
  );
};
