'use client';
import React, { useEffect, useState } from 'react';
import { Button } from '@/components/ui/button';
import { Loader2, Download, FileCheck } from 'lucide-react';
import { generateShopifyInvoicePDF } from '@/lib/generateShopifyInvoicePdf';
import { updateShopifyInvoiceDocumentUrl, uploadPdfDocument } from '@/lib/pocket';
import { Input } from '@/components/ui/input';
import { Account } from '@/lib/types';

interface InvoicePageProps {
  invoice_id: string;
  account_id: string;
  accountDetails: Account;
  order: any;
  invoice_pdf: string | undefined;
  logo_url: string | undefined | null;
}

const InvoicePage: React.FC<InvoicePageProps> = ({ invoice_id, account_id, accountDetails, order, invoice_pdf, logo_url }) => {
  const [isLoading, setIsLoading] = useState(!invoice_pdf);
  const [pdfUrl, setPdfUrl] = useState<string | null>(invoice_pdf ?? null);

  useEffect(() => {
    const generateAndUploadInvoice = async () => {
      try {
        setIsLoading(true);
        const formData = await generateShopifyInvoicePDF(order, accountDetails, logo_url);
        const _file = await uploadPdfDocument(account_id, formData, 'shopify_invoice');
        console.log('file is ' + _file);
        await updateShopifyInvoiceDocumentUrl(invoice_id, _file.id);
        setPdfUrl(_file.url || null);
      } catch (error) {
        console.error('Error uploading invoice:', error);
      }
      setIsLoading(false);
    };

    if (invoice_pdf == undefined) {
      generateAndUploadInvoice();
    }
  }, []);

  return (
    <div className="flex items-center justify-center min-h-screen bg-gray-100">
      <div className="max-w-md w-full p-8 rounded-xl shadow-md bg-white border border-gray-200">
        {isLoading ? (
          <div className="flex flex-col items-center justify-center space-y-4">
            <Loader2 className="animate-spin text-blue-600" size={48} />
            <p className="text-lg font-medium text-gray-700">Preparing your invoice...</p>
          </div>
        ) : pdfUrl ? (
          <div className="flex flex-col items-center justify-center space-y-6">
            <h1 className="text-2xl font-semibold text-gray-800">Invoice is ready!</h1>
            <Button
              onClick={() => window.open(pdfUrl, '_blank')}
              className="bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-lg flex items-center space-x-2"
            >
              <FileCheck className="h-5 w-5" />
              <span>View Invoice</span>
            </Button>
          </div>
        ) : (
          <div className="flex flex-col items-center justify-center space-y-4">
            <p className="text-lg font-medium text-red-600">Failed to generate the invoice.</p>
          </div>
        )}
      </div>
    </div>
  );
};

export default InvoicePage;
