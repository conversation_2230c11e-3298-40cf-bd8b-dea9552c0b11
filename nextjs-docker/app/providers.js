// app/providers.js
'use client';
import posthog from 'posthog-js';
import { PostHogProvider } from 'posthog-js/react';

const NEXT_PUBLIC_POSTHOG_KEY = 'phc_9S2j6ZoqFBBr6GU1LuVLhBUVyLBhq7hymEs2dgXC3gE';
const NEXT_PUBLIC_POSTHOG_HOST = 'https://us.i.posthog.com';
const url = process.env.NEXT_PUBLIC_APP_URL;
const IS_BETA = url.includes('beta');
if (typeof window !== 'undefined' && process.env.NODE_ENV === 'production' && !IS_BETA) {
  posthog.init(NEXT_PUBLIC_POSTHOG_KEY, {
    api_host: NEXT_PUBLIC_POSTHOG_HOST,
    person_profiles: 'identified_only', // or 'always' to create profiles for anonymous users as well
  });
}
export function CSPostHogProvider({ children }) {
  return <PostHogProvider client={posthog}>{children}</PostHogProvider>;
}
