'use client';

import { useState } from 'react';
import Sidebar from '@/components/Sidebar';
import { Button } from '@/components/ui/button';

export default function LockedSidebarExample() {
  const [isLocked, setIsLocked] = useState(false);

  // Example account information
  const accountId = 'example-account';
  const userType = 'admin';
  const userName = 'Example User';
  const dashboard_options = ['live-chat', 'lead-management', 'templates', 'campaigns'];
  const logo = null; // Will use default logo

  return (
    <div className="flex h-screen">
      {/* Sidebar with locked parameter */}
      <Sidebar 
        accountId={accountId}
        userType={userType}
        userName={userName}
        dashboard_options={dashboard_options}
        logo={logo}
        locked={isLocked}
      />
      
      {/* Main content */}
      <div className="flex-1 p-8">
        <h1 className="text-2xl font-bold mb-6">Locked Sidebar Example</h1>
        
        <div className="mb-8">
          <p className="mb-4">
            This example demonstrates the locked sidebar functionality. When locked, all navigation items 
            except "Live Chat" will be disabled.
          </p>
          
          <Button 
            onClick={() => setIsLocked(!isLocked)}
            variant={isLocked ? "destructive" : "default"}
            className="mt-2"
          >
            {isLocked ? 'Unlock Sidebar' : 'Lock Sidebar'}
          </Button>
        </div>
        
        <div className="p-4 bg-gray-100 rounded-md">
          <h2 className="text-lg font-semibold mb-2">Current Status:</h2>
          <p>Sidebar is <strong>{isLocked ? 'Locked' : 'Unlocked'}</strong></p>
          {isLocked && (
            <p className="mt-2 text-gray-600">
              Only the "Live Chat" navigation item is accessible. All other items are disabled.
            </p>
          )}
        </div>
      </div>
    </div>
  );
}
