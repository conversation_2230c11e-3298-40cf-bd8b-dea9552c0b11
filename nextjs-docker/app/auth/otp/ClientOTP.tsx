'use client';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { verifyOTP } from '@/lib/actions';
import Link from 'next/link';
import { useFormState } from 'react-dom';

export const ClientOTP = ({ mfaId, otpId }: { mfaId: string; otpId: string }) => {
  const [state, formAction] = useFormState(verifyOTP, null);
  return (
    <div className="w-full h-full flex flex-col items-center justify-center">
      <Link className="mb-10" prefetch={false} href="/auth">
        <Button variant="link">Go back to login page</Button>
      </Link>
      <form className="flex flex-col gap-4 w-80" action={formAction}>
        <h1 className="text-2xl font-bold text-center">Enter OTP</h1>
        <input type="hidden" name="otpId" value={otpId} />
        <input type="hidden" name="mfaId" value={mfaId} />
        <Input type="text" name="otp" placeholder="Enter OTP" className="p-2 border rounded" pattern="[0-9]*" inputMode="numeric" maxLength={8} />
        <Button type="submit">Verify OTP</Button>
      </form>
      {state?.message?.status === 400 && <div className="text-red-600">Invalid OTP</div>}
    </div>
  );
};
