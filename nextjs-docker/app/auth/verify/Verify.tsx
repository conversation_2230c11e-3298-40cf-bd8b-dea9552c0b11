'use client';

import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/components/ui/alert-dialog';
import { Button } from '@/components/ui/button';
import { verifyUser } from '@/lib/pocket';
import { Loader2 } from 'lucide-react';
import Link from 'next/link';
import { useState } from 'react';

export function Verify({ token }: { token: string }) {
  const [status, setStatus] = useState('');
  const [loading, setLoading] = useState(false);
  const [alertOpen, setAlertOpen] = useState(false);

  const handleVerify = async () => {
    setStatus('');
    setLoading(true);
    const response = await verifyUser(token);
    if (response.success) {
      setAlertOpen(true);
    } else if (!response.success) {
      setStatus('error');
    }
    setLoading(false);
  };

  return (
    <div className="w-80">
      <AlertDialog open={alertOpen} onOpenChange={setAlertOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Verification Successful!</AlertDialogTitle>
            <AlertDialogDescription>You have been verified, and now you can login</AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <Link prefetch={false} href={`/auth`}>
              <AlertDialogAction onClick={() => setAlertOpen(false)}>OK</AlertDialogAction>
            </Link>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>

      <div className="font-bold text-2xl mb-3 text-center">Verify Account</div>

      <Button
        disabled={loading}
        onClick={() => {
          handleVerify();
        }}
        type="submit"
        className="w-full bg-[#075E54]"
      >
        Click to Verify {loading && <Loader2 className="ml-2 h-4 w-4 animate-spin" />}
      </Button>

      {status == 'error' && <div className="text-red-500">Some error occured, please try again</div>}
    </div>
  );
}
