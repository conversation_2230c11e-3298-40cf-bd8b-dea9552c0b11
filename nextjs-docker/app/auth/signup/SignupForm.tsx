'use client';

import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/components/ui/alert-dialog';
import { Button } from '@/components/ui/button';
import { Checkbox } from '@/components/ui/checkbox';
import { Input } from '@/components/ui/input';
import { InputWithPhoneCode } from '@/components/ui/inputWithPhoneCode';
import { createUserAction } from '@/lib/actions';
import { loginSignUpWithGoogle } from '@/state/user/user';
import { Loader2 } from 'lucide-react';
import Link from 'next/link';
import { useRef, useState } from 'react';
import { useFormState, useFormStatus } from 'react-dom';
import SignUpWithGoogleIcon from './SignUpWithGoogle';

const initialState = {
  message: '',
};

const SignUpButton = ({ agreeWithTerms }: { agreeWithTerms: boolean }) => {
  const { pending } = useFormStatus();
  return (
    <>
      {agreeWithTerms ? (
        pending ? (
          <Button className="w-full bg-[#306b64] hover:bg-[#15423d]" disabled>
            <Loader2 className="mr-2 h-4 w-4 animate-spin" /> Signing up
          </Button>
        ) : (
          <Button type="submit" className="w-full bg-[#075E54]">
            Sign up
          </Button>
        )
      ) : (
        <Button className="w-full bg-[#306b64]" disabled>
          Sign Up
        </Button>
      )}
    </>
  );
};

export function SignupForm() {
  const [agreeWithTerms, setAgreeWithTerms] = useState(false);
  const [numberCode, setNumberCode] = useState({
    code: '+92',
    emoji: '🇵🇰',
    phoneLength: 10,
  });

  const signInWithGoogle = async () => {
    try {
      await loginSignUpWithGoogle();
    } catch (error) {
      console.error(error);
    }
  };

  const signupUser = createUserAction.bind(null, numberCode);
  const [state, formAction] = useFormState(signupUser, initialState);

  const formRef = useRef<HTMLFormElement>(null);

  return (
    <div className="w-80">
      <AlertDialog open={state?.success ? state?.success : false}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Signup Successful!</AlertDialogTitle>
            <AlertDialogDescription>A verification email has been sent to the provided email address, verify yourself to login</AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <Link prefetch={false} href={`/app/auth`}>
              <AlertDialogAction>OK</AlertDialogAction>
            </Link>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
      <form action={formAction} ref={formRef}>
        <Button
          variant="default"
          onClick={() => signInWithGoogle()}
          type="button"
          className="bg-[#F2F2F2] w-full font-roboto text-black font-semibold hover:bg-[#F2F2F2]"
        >
          <SignUpWithGoogleIcon />
          <div className="flex-1">Signup with Google</div>
        </Button>
        <div className="flex space-x-4 items-center my-3">
          <div className="h-[1px] w-full bg-gray-200"></div>
          <div className="text-gray-400 text-xs">OR</div>
          <div className="h-[1px] w-full bg-gray-200"></div>
        </div>
        <div className="space-y-6 mb-6">
          <Input placeholder="Full Name" name="fullName" type="text" />
          <Input placeholder="User Name" name="userName" type="text" />
          <Input placeholder="Email" name="email" type="email" />
          <InputWithPhoneCode
            placeholder="Personal Whatsapp Number"
            type="tel"
            name="phoneNumber"
            numberCode={numberCode}
            setNumberCode={setNumberCode}
            formRef={formRef}
          />
          <Input placeholder="Password" name="password" type="password" />

          <Input placeholder="Confirm Password" name="confirmPassword" type="password" />
        </div>
        <div className="flex items-start space-x-2 text-sm text-gray-800 mb-2">
          <Checkbox id="terms" onClick={() => setAgreeWithTerms(!agreeWithTerms)} />
          <div className="grid gap-1.5 leading-none">
            <label htmlFor="terms" className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70">
              Accept terms and conditions
            </label>
            <p className="text-sm text-muted-foreground">
              I agree to the{' '}
              <Link href="https://wetarseel.ai/terms-and-conditions" className="underline hover:text-primary" target="_blank" rel="noopener noreferrer">
                Terms and Conditions
              </Link>{' '}
              and{' '}
              <Link href="https://wetarseel.ai/privacy-policy" className="underline hover:text-primary" target="_blank" rel="noopener noreferrer">
                Privacy Statement
              </Link>
              .
            </p>
          </div>
        </div>
        <SignUpButton agreeWithTerms={agreeWithTerms} />
        <div className="text-gray-800 mb-1 text-center">
          Already have an account?{' '}
          <Link prefetch={false} className="underline" href="/auth">
            Log in
          </Link>
        </div>
      </form>
      {state.error && (
        <ul>
          <li className="text-red-600 list-disc">{state.error}</li>
        </ul>
      )}
      {state?.issues?.length > 0 && (
        <>
          <div className="font-bold">Please resolve the following errors</div>
          <ul className="ml-4">
            {state.issues.map((item: { code: string; minimum: number; type: string; inclusive: boolean; exact: boolean; message: string }) => (
              <li className="text-red-600 list-disc" key={item.message}>
                {item.message}
              </li>
            ))}
          </ul>
        </>
      )}
      {state.message && <div className="text-red-600 font-bold text-center">{state.message}</div>}
    </div>
  );
}
