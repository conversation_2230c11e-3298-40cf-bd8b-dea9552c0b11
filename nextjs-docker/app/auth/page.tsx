import { ClientAuth } from '@/app/auth/components/ClientAuth';
import { Suspense } from 'react';

interface User {
  email: string;
  password: string;
}

export const dynamic = 'force-dynamic';
export default async function AuthPage() {
  return (
    <div className="w-full h-full flex flex-col items-center justify-center">
      <Suspense fallback={<div>Loading...</div>}>
        <ClientAuth />
      </Suspense>
      {/* <div>
        <div>Select Account</div>
        <div>
          <div>This is dev purpose only</div>
          {accounts.map((account) => {
            return (
              <div key={account.id}>
                <Link prefetch={false} href={`/${account.id}/dashboard`}>
                  <Button variant="link">{account.name}</Button>
                </Link>
              </div>
            );
          })}
        </div>
      </div> */}
    </div>
  );
}
