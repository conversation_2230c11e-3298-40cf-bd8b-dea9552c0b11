import { ChangePassword } from './ChangePassword';
import { ResetForm } from './ResetForm';

export default async ({ params, searchParams }: { params: Record<string, string>; searchParams: Record<string, string> }) => {
  const page = searchParams.page;
  const userId = searchParams.id;
  const token = searchParams.token;

  if (page == '1') {
    return (
      <>
        <div className="font-bold text-2xl mb-3">Reset Password</div>
        <ResetForm />
      </>
    );
  } else if (page == '2') {
    return (
      <>
        <ChangePassword userId={userId} token={token} />
      </>
    );
  }
};
