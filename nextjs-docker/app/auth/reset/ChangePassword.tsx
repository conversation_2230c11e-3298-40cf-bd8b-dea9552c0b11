'use client';

import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/components/ui/alert-dialog';
import { Button } from '@/components/ui/button';
import { InputPassword } from '@/components/ui/InputPassword';
import { Label } from '@/components/ui/label';
import { changePassword } from '@/lib/pocket';
import { Loader2 } from 'lucide-react';
import Link from 'next/link';
import { useState } from 'react';

export function ChangePassword({ userId, token }: { userId: string; token: string }) {
  const [email, setEmail] = useState('');
  const [isPasswordHidden, setPasswordHidden] = useState(true);
  const [isConfirmPasswordHidden, setConfirmPasswordHidden] = useState(true);
  const [password, setPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [loading, setLoading] = useState(false);
  const [status, setStatus] = useState('');
  const [alertOpen, setAlertOpen] = useState(false);

  const handleSave = async () => {
    setStatus('');
    setLoading(true);
    const response = await changePassword(token, password, confirmPassword, userId);
    if (response.success) {
      setAlertOpen(true);
    } else if (!response.success) {
      setStatus('error');
    }
    setLoading(false);
  };

  return (
    <div className="w-80">
      <AlertDialog open={alertOpen} onOpenChange={setAlertOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Password changed!</AlertDialogTitle>
            <AlertDialogDescription>The password has been changed successfully</AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <Link prefetch={false} href={`/auth`}>
              <AlertDialogAction onClick={() => setAlertOpen(false)}>OK</AlertDialogAction>
            </Link>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
      <div className="font-bold text-2xl mb-3 text-center">Change Password</div>
      <Label className="text-lg text-gray-600 font-semibold mt-4 mb-2">New Password</Label>
      <div className="space-y-6 mb-4">
        <InputPassword
          onChange={(e) => {
            setPassword(e.target.value);
          }}
          placeholder="Password"
          isPasswordHidden={isPasswordHidden}
          setPasswordHidden={setPasswordHidden}
          type={isPasswordHidden ? 'password' : 'text'}
          name="password"
          required
        />
      </div>

      <Label className="text-lg text-gray-600 font-semibold mt-4 mb-2">Confirm New Password</Label>
      <div className="space-y-2 mb-6">
        <InputPassword
          onChange={(e) => {
            setConfirmPassword(e.target.value);
          }}
          placeholder="Confirm Password"
          isPasswordHidden={isConfirmPasswordHidden}
          setPasswordHidden={setConfirmPasswordHidden}
          type={isConfirmPasswordHidden ? 'password' : 'text'}
          name="confirm-password"
          required
        />
        {password &&
          confirmPassword &&
          (password != confirmPassword ? <div className="text-red-500">Passwords do not match</div> : <div className="text-green-500">Passwords matched</div>)}
      </div>
      <Button
        disabled={password != confirmPassword || loading}
        onClick={() => {
          handleSave();
        }}
        type="submit"
        className="w-full bg-[#075E54]"
      >
        Save {loading && <Loader2 className="ml-2 h-4 w-4 animate-spin" />}
      </Button>
    </div>
  );
}
