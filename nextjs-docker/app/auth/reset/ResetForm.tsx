'use client';

import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { requestPasswordReset } from '@/lib/pocket';
import { Loader2 } from 'lucide-react';
import { useState } from 'react';

export function ResetForm() {
  const [email, setEmail] = useState('');
  const [loading, setLoading] = useState(false);
  const [status, setStatus] = useState('');

  const handleSubmit = async () => {
    setStatus('');
    setLoading(true);
    const response = await requestPasswordReset(email);
    if (response.success) {
      setStatus('sent');
    } else if (!response.success) {
      setStatus('error');
    }
    setLoading(false);
  };
  return (
    <div className="w-80">
      <div className="space-y-6 mb-4">
        <Input
          placeholder="Email"
          name="email"
          value={email}
          onChange={(e) => {
            setEmail(e.target.value);
          }}
        />
      </div>
      <Button
        disabled={loading}
        onClick={() => {
          handleSubmit();
        }}
        type="submit"
        className="w-full bg-[#075E54]"
      >
        Get Reset Password Email {loading && <Loader2 className="ml-2 h-4 w-4 animate-spin" />}
      </Button>

      {status == 'sent' && <div className="text-green-600 text-center font-semibold">Email sent successfully!</div>}
      {status == 'error' && <div className="text-red-500 text-center font-semibold">Some error occured, please try again</div>}
      <Label className="text-[14px] text-gray-600 font-semibold ">
        An email will be sent to the given email address containing the reset password link. The link will expire after 24 hours
      </Label>
    </div>
  );
}
