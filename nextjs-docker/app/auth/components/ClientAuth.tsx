'use client';
import Login from '@/components/ui/login';
import { getVerificationMail } from '@/lib/pocket';
import { loginUser } from '@/state/user/user';
import { useState } from 'react';
import { useFormState } from 'react-dom';
import { Loader2 } from 'lucide-react';
import { Button } from '@/components/ui/button';

interface ClientAuthProps {}

const initialState = {
  message: {
    status: 0,
    email: '',
  },
};

export function ClientAuth({}: ClientAuthProps) {
  const [state, formAction] = useFormState(loginUser, initialState);
  const [status, setStatus] = useState('');
  const [loading, setLoading] = useState(false);

  const handleVerificationMail = async () => {
    setStatus('');
    setLoading(true);
    const response = await getVerificationMail(state?.message.email);
    if (response.success) {
      setStatus('sent');
    } else if (!response.success) {
      setStatus('error');
    }
    setLoading(false);
  };

  return (
    <>
      <form
        action={formAction}
        className="w-80 flex flex-col 
         shadow-slate-300 items-center justify-center gap-2 p-4 rounded"
      >
        <Login />
      </form>
      <div className="text-red-600">{state?.message.status == 403 && 'User is locked'}</div>
      <div className="text-red-600">{state?.message.status == 400 && 'Incorrect email or password'}</div>
      {state?.message.status == 404 && (
        <>
          <div className="text-red-600">User not verified</div>
          {status == '' && loading ? (
            <Loader2 className="ml-2 h-4 w-4 animate-spin" />
          ) : (
            <div className="text-gray-800 mb-1">
              <div onClick={handleVerificationMail} className="underline cursor-pointer">
                Send account verification link again
              </div>
            </div>
          )}
          {status == 'error' && <div className="text-red-500">Some error occured, please try again</div>}
          {status == 'sent' && <div className="text-green-700">The verification mail has been sent</div>}
        </>
      )}
    </>
  );
}
