'use client';

import { useEffect } from 'react';

interface LogoutProps {}

export function Logout({}: LogoutProps) {
  const APP_ID = process.env.NEXT_PUBLIC_APP_ID;
  useEffect(() => {
    (function (d, s, id) {
      var js: HTMLScriptElement,
        fjs = d.getElementsByTagName(s)[0];
      if (d.getElementById(id)) {
        return;
      }
      js = d.createElement(s) as HTMLScriptElement;
      js.id = id;
      js.src = 'https://connect.facebook.net/en_US/sdk.js';
      if (fjs && fjs.parentNode) {
        fjs.parentNode.insertBefore(js, fjs);
      }
    })(document, 'script', 'facebook-jssdk');
    window.fbAsyncInit = function () {
      window.FB.init({
        appId: APP_ID,
        cookie: true,
        xfbml: true,
        version: 'v19.0',
      });

      window.FB.AppEvents.logPageView();

      window.FB.getLoginStatus(function (response: any) {});
    };

    (function (d, s, id) {
      var js: HTMLScriptElement,
        fjs = d.getElementsByTagName(s)[0];
      if (d.getElementById(id)) {
        return;
      }
      js = d.createElement(s) as HTMLScriptElement;
      js.id = id;
      js.src = 'https://connect.facebook.net/en_US/sdk.js';
      if (fjs && fjs.parentNode) {
        fjs.parentNode.insertBefore(js, fjs);
      }
    })(document, 'script', 'facebook-jssdk');
  }, []);
  return (
    <button
      className=" hover:bg-slate-700 "
      onClick={() => {
        window.location.href = '/auth';
        // logoutUser()
        // router.refresh()
        // router.replace('/next/auth')
      }}
    >
      Logout
    </button>
  );
}
