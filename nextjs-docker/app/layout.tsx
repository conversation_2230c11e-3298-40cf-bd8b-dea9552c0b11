import '../styles/globals.css';
// import { OnboardingProvider } from './context/OnboardingContext';
import { GeistSans } from 'geist/font/sans';
import { GeistMono } from 'geist/font/mono';
import { CSPostHogProvider } from './providers';

export const metadata = {
  title: 'WeTarseel',
  description: 'Send messages from whatsapp',
};

export default function RootLayout({ children }: { children: React.ReactNode }) {
  return (
    <html lang="en" className={`${GeistSans.variable} ${GeistMono.variable}`}>
      <head>{/* <script src="https://unpkg.com/react-scan/dist/auto.global.js" /> */}</head>
      <body data-theme="light" className="overflow-y-hidden">
        <div id="fb-root" />
        {/* <script src="https://connect.facebook.net/en_US/sdk.js"></script> */}
        {/* <OnboardingProvider> */}
        <CSPostHogProvider>{children}</CSPostHogProvider>
        {/* {children} */}
        {/* </OnboardingProvider> */}
      </body>
    </html>
  );
}
