import { withSentryConfig } from '@sentry/nextjs';
// const MillionLint = require('@million/lint');
// const million = require('million/compiler');
/** @type {import('next').NextConfig} */
import { withAxiom } from 'next-axiom';

const nextConfig = withAxiom({
  output: 'standalone',
  experimental: {
    serverActions: {
      allowedOrigins: ['app.wetarseel.ai', 'beta.app.wetarseel.ai', 'node.taskmate.ae', 'localhost', 'f4xs1rhv-3000.inc1.devtunnels.ms'],
      bodySizeLimit: '100mb',
    },
  },
  images: {
    remotePatterns: [
      {
        protocol: 'https',
        hostname: 'scontent.whatsapp.net',
      },
      {
        protocol: 'https',
        hostname: 'scontent.xx.fbcdn.net',
      },
      {
        protocol: 'https',
        hostname: 'app.wetarseel.ai',
      },
      {
        protocol: 'https',
        hostname: 'node.taskmate.ae',
      },
      {
        protocol: 'http',
        hostname: '127.0.0.1',
      },
    ],
  },
});

// export default MillionLint.next({ rsc: true })(nextConfig);
export default withSentryConfig(nextConfig, {
  // For all available options, see:
  // https://github.com/getsentry/sentry-webpack-plugin#options

  org: 'techinoviq',
  project: 'javascript-nextjs',

  // Only print logs for uploading source maps in CI
  silent: !process.env.CI,

  // For all available options, see:
  // https://docs.sentry.io/platforms/javascript/guides/nextjs/manual-setup/

  // Upload a larger set of source maps for prettier stack traces (increases build time)
  widenClientFileUpload: true,

  // Uncomment to route browser requests to Sentry through a Next.js rewrite to circumvent ad-blockers.
  // This can increase your server load as well as your hosting bill.
  // Note: Check that the configured route will not match with your Next.js middleware, otherwise reporting of client-
  // side errors will fail.
  // tunnelRoute: "/monitoring",

  // Hides source maps from generated client bundles
  hideSourceMaps: true,

  // Automatically tree-shake Sentry logger statements to reduce bundle size
  disableLogger: true,

  // Enables automatic instrumentation of Vercel Cron Monitors. (Does not yet work with App Router route handlers.)
  // See the following for more information:
  // https://docs.sentry.io/product/crons/
  // https://vercel.com/docs/cron-jobs
  automaticVercelMonitors: true,
});
