FROM node:20-alpine AS base

# Install dependencies only when needed
FROM base AS deps
# Check https://github.com/nodejs/docker-node/tree/b4117f9333da4138b03a546ec926ef50a31506c3#nodealpine to understand why libc6-compat might be needed.
RUN apk add --no-cache libc6-compat

WORKDIR /app

# Install dependencies based on the preferred package manager
COPY package.json package-lock.json* ./
# RUN npm config set registry https://registry.npmjs.org/
RUN npm install --force
RUN npm install --force sharp

# Rebuild the source code only when needed
FROM base AS builder
WORKDIR /app
COPY --from=deps /app/node_modules ./node_modules
COPY . .

# Add ARG for each NEXT_ environment variable
ARG NEXT_PUBLIC_AXIOM_TOKEN
ARG NEXT_PUBLIC_AXIOM_DATASET
ARG NEXT_PUBLIC_APP_ID
ARG NEXT_PUBLIC_PB_URL
ARG NEXT_PUBLIC_APP_URL
ARG FIREBASE_PRIVATE_KEY
ARG NEXT_PUBLIC_BUN_SERVER_URL
ARG NEXT_PUBLIC_QNAME
ARG STRIPE_SECRET_KEY
ARG NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY

ENV NODE_ENV production
RUN npm run build

# Production image, copy all the files and run next
FROM base AS runner
WORKDIR /app

ENV NODE_ENV production

RUN addgroup --system --gid 1001 nodejs
RUN adduser --system --uid 1001 nextjs

COPY --from=builder /app/public ./public

# Set the correct permission for prerender cache
RUN mkdir .next
RUN chown nextjs:nodejs .next
RUN npm install pm2 -g

# Automatically leverage output traces to reduce image size
# https://nextjs.org/docs/advanced-features/output-file-tracing
COPY --from=builder --chown=nextjs:nodejs /app/.next/standalone ./
COPY --from=builder --chown=nextjs:nodejs /app/.next/static ./.next/static

COPY --from=builder --chown=nextjs:nodejs /app/main.db ./
COPY --from=builder --chown=nextjs:nodejs /app/processes.json ./
RUN chown -R nextjs:nodejs ./
RUN chmod 777 ./main.db

RUN apk add --no-cache curl 
USER nextjs

EXPOSE 3001

ENV PORT 3001
# set hostname to localhost
ENV HOSTNAME "0.0.0.0"
ENV NODE_ENV production
# server.js is created by next build from the standalone output
# https://nextjs.org/docs/pages/api-reference/next-config-js/output
CMD ["pm2-runtime", "processes.json"]