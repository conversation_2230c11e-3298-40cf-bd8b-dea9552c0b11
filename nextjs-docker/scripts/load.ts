const fs = require('fs');
const path = require('path');
import { recieveMessage } from '@/lib/recieveMessage';
import { WARecieveMessage } from '@/lib/types';
import { Logger } from 'next-axiom';
const filePath = path.join(__dirname, 'matches.json');
const content = fs.readFileSync(filePath, 'utf8');

let contentObj = JSON.parse(content);
let matches = contentObj.matches.map((m: any) => m.data['fields.entry']);
const log = new Logger();
const stuff = matches.filter((m: any) => m[0].changes[0]?.value?.messages?.[0]?.from);

const dodo = async () => {
  for (const entry of stuff) {
    // console.log(a[0].changes[0].value.messages[0].type);
    const value = entry[0]?.changes[0]?.value;
    const message = entry[0].changes[0].value.messages;
    const business_phone_number_id = value?.metadata?.phone_number_id;
    const { contacts } = value;
    console.log(message[0], value);
    await recieveMessage(
      {
        ...message[0],
        business_phone_number_id,
      } as WARecieveMessage,
      contacts,
      log
    );
  }
};

dodo();
