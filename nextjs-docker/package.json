{"scripts": {"dev": "next dev", "build": "next build", "start": "next start", "test": "vitest", "dev-https": "next dev -p 3001 --experimental-https", "format": "prettier --write .", "prepare": "cd .. && husky install"}, "dependencies": {"@ai-sdk/openai": "^1.1.5", "@axiomhq/js": "^1.1.1", "@emotion/react": "^11.11.4", "@emotion/styled": "^11.11.5", "@ffmpeg/ffmpeg": "^0.12.10", "@ffmpeg/util": "^0.12.1", "@hookform/resolvers": "^3.3.4", "@lucia-auth/adapter-sqlite": "^3.0.1", "@million/lint": "^1.0.0-rc.18", "@mui/x-date-pickers": "^7.8.0", "@radix-ui/primitive": "^1.1.0", "@radix-ui/react-alert-dialog": "^1.1.2", "@radix-ui/react-avatar": "^1.0.4", "@radix-ui/react-checkbox": "^1.1.2", "@radix-ui/react-collapsible": "^1.1.1", "@radix-ui/react-context-menu": "^2.2.1", "@radix-ui/react-dialog": "^1.1.2", "@radix-ui/react-dropdown-menu": "^2.1.2", "@radix-ui/react-hover-card": "^1.1.1", "@radix-ui/react-label": "^2.1.0", "@radix-ui/react-popover": "^1.1.2", "@radix-ui/react-progress": "^1.1.1", "@radix-ui/react-radio-group": "^1.2.1", "@radix-ui/react-scroll-area": "^1.1.0", "@radix-ui/react-select": "^2.1.2", "@radix-ui/react-separator": "^1.1.0", "@radix-ui/react-slider": "^1.2.0", "@radix-ui/react-slot": "^1.1.0", "@radix-ui/react-switch": "^1.1.1", "@radix-ui/react-tabs": "^1.1.1", "@radix-ui/react-toast": "^1.1.5", "@radix-ui/react-toggle": "^1.1.0", "@radix-ui/react-tooltip": "^1.1.2", "@sentry/nextjs": "^8.11.0", "@stripe/react-stripe-js": "^2.9.0", "@stripe/stripe-js": "^4.10.0", "@tanstack/react-table": "^8.17.3", "@tavily/core": "^0.3.1", "@tiptap/extension-character-count": "^2.8.0", "@tiptap/extension-hard-break": "^2.7.2", "@tiptap/extension-highlight": "^2.8.0", "@tiptap/extension-placeholder": "^2.7.2", "@tiptap/extension-typography": "^2.8.0", "@tiptap/pm": "^2.5.8", "@tiptap/react": "^2.5.8", "@tiptap/starter-kit": "^2.5.8", "@types/canvas-confetti": "^1.6.4", "@types/papaparse": "^5.3.14", "@uppy/audio": "^2.0.2", "@uppy/core": "^4.3.0", "@uppy/dashboard": "^4.1.3", "@uppy/xhr-upload": "^4.2.3", "@xyflow/react": "^12.0.1", "ai": "^4.1.11", "axios": "^1.9.0", "bullmq": "^5.34.5", "canvas-confetti": "^1.9.3", "canvg": "^4.0.2", "chart.js": "^4.4.3", "class-variance-authority": "^0.7.0", "classnames": "^2.5.1", "clsx": "^2.1.0", "cmdk": "^1.1.1", "date-fns": "^3.6.0", "dayjs": "^1.11.11", "dexie": "^4.0.8", "dexie-react-hooks": "^1.1.7", "easy-currencies": "^1.8.1", "emoji-picker-react": "^4.12.0", "export-to-csv": "^1.4.0", "fetch-to-curl": "^0.6.0", "firebase": "^11.0.2", "firebase-admin": "^13.0.1", "geist": "^1.3.1", "html2pdf.js": "^0.10.2", "immer": "^10.1.1", "input-otp": "^1.2.4", "intro.js": "^7.2.0", "ioredis": "^5.4.2", "js-cookie": "^3.0.5", "jsep": "^1.4.0", "jwt-decode": "^4.0.0", "libphonenumber-js": "^1.11.17", "lodash": "^4.17.21", "lucia": "^3.1.1", "lucide-react": "^0.354.0", "million": "^3.1.7", "next": "^14.2.18", "next-axiom": "^1.1.1", "ogv": "^1.9.0", "openai": "^4.86.2", "oslo": "^1.2.0", "papaparse": "^5.4.1", "phone": "^3.1.56", "pocketbase": "^0.24.0", "posthog-js": "^1.204.0", "qrcode.react": "^4.1.0", "quill": "^2.0.3", "quill-paste-smart": "^2.0.0", "react": "latest", "react-aria": "^3.33.1", "react-chartjs-2": "^5.2.0", "react-countdown-circle-timer": "^3.2.1", "react-day-picker": "^8.10.1", "react-dom": "latest", "react-hook-form": "^7.51.4", "react-phone-number-input": "^3.4.11", "react-quill": "^2.0.0", "react-quilljs": "^2.0.5", "react-resizable-panels": "^2.0.19", "react-scroll-to-top": "^3.0.0", "react-select": "^5.8.0", "react-stately": "^3.31.1", "react-voice-recorder-player": "^1.2.0", "react-window": "^1.8.10", "recharts": "^2.13.0-alpha.5", "resend": "^3.4.0", "stripe": "^17.3.1", "swr": "^2.2.5", "tailwind-merge": "^2.2.2", "tailwindcss-animate": "^1.0.7", "tailwindcss-motion": "^0.4.3-beta", "use-debounce": "^10.0.4", "use-immer": "^0.10.0", "uuid": "^9.0.1", "wavesurfer.js": "^7.7.15", "zod": "^3.24.1"}, "devDependencies": {"@biomejs/biome": "^1.7.3", "@iconify/react": "^5.0.2", "@testing-library/dom": "^10.4.0", "@testing-library/react": "^16.2.0", "@types/js-cookie": "^3.0.6", "@types/lodash": "^4.17.7", "@types/node": "20.11.25", "@types/react": "18.2.64", "@types/react-dom": "^18.2.22", "@types/react-window": "^1.8.8", "@types/uuid": "^9.0.8", "@vitejs/plugin-react": "^4.3.4", "autoprefixer": "^10.4.18", "daisyui": "^4.9.0", "dotenv": "^16.4.5", "eslint-config-prettier": "^9.1.0", "eslint-plugin-prettier": "^5.2.1", "husky": "^8.0.3", "jsdom": "^26.0.0", "postcss": "^8.4.35", "prettier": "^3.3.3", "sass": "^1.79.4", "tailwindcss": "^3.4.1", "typescript": "^5.4.2", "vite-tsconfig-paths": "^5.1.4", "vitest": "^2.1.9"}}