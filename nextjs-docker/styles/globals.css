@tailwind base;
@tailwind components;
@tailwind utilities;

.full-height {
  height: calc(100dvh - env(safe-area-inset-bottom, 40px) - env(safe-area-inset-top));
}

:root {
  --warning: 38 92% 50%;
  --warning-foreground: 48 96% 89%;
  --destructive: 0 100% 50%;
  --destructive-foreground: 210 40% 98%;
  --secondary: 210 40% 96.1%;
  --secondary-foreground: 222.2 47.4% 11.2%;
  --muted: 210 40% 96.1%;
  --muted-foreground: 215.4 16.3% 46.9%;
}

@layer base {
  :root {
    --chart-1: 12 76% 61%;
    --chart-2: 173 58% 39%;
    --chart-3: 197 37% 24%;
    --chart-4: 43 74% 66%;
    --chart-5: 27 87% 67%;
  }

  .dark {
    --chart-1: 220 70% 50%;
    --chart-2: 160 60% 45%;
    --chart-3: 30 80% 55%;
    --chart-4: 280 65% 60%;
    --chart-5: 340 75% 55%;
  }
}

select {
  position: relative !important;
}

.CollapsibleContent {
  overflow: hidden;
}

.CollapsibleContent[data-state='open'] {
  animation: slideDown 300ms ease-out;
}

.CollapsibleContent[data-state='closed'] {
  animation: slideUp 300ms ease-out;
}

@keyframes slideDown {
  from {
    height: 0;
  }
  to {
    height: var(--radix-collapsible-content-height);
  }
}

@keyframes slideUp {
  from {
    height: var(--radix-collapsible-content-height);
  }
  to {
    height: 0;
    position: relative !important;
  }
}

@layer base {
  :root {
    --chart-1: 12 76% 61%;
    --chart-2: 0 100% 40%;
    --chart-3: 197 37% 24%;
    --chart-4: 196 95% 60%;
    --chart-5: 27 87% 67%;
    --chart-6: 9 95% 60%;
  }

  .dark {
    --chart-1: 220 70% 50%;
    --chart-2: 160 60% 45%;
    --chart-3: 30 80% 55%;
    --chart-4: 280 65% 60%;
    --chart-5: 340 75% 55%;
    --chart-6: 17 27% 97%;
  }
}

.tiptap p.is-editor-empty:first-child::before {
  color: #adb5bd;
  content: attr(data-placeholder);
  float: left;
  height: 0;
  pointer-events: none;
}

.ql-container {
  border: 1px solid #d1d5db !important; /* Red border for the editor */
}

/* Apply scaling for medium-sized screens */
@media screen and (min-width: 651px) and (max-width: 1280px) {
  /* Simple zoom approach - works in most modern browsers */
  html {
    /* For browsers that don't support zoom */
    font-size: 80%;
  }
}
