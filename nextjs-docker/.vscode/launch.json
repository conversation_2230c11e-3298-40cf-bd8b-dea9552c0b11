{"version": "0.2.0", "configurations": [{"name": "Next.js: debug server-side", "type": "node-terminal", "request": "launch", "command": "npm run dev"}, {"name": "Next.js: debug client-side", "type": "chrome", "request": "launch", "url": "http://localhost:3000"}, {"name": "Next.js: debug full stack", "type": "node-terminal", "request": "launch", "command": "npm run dev", "serverReadyAction": {"pattern": "- Local:.+(https?://.+)", "uriFormat": "%s", "action": "debugWithChrome"}}, {"type": "node", "request": "launch", "name": "Debug Vitest", "program": "${workspaceFolder}/node_modules/vitest/vitest.mjs", "args": ["run"], "autoAttachChildProcesses": true, "smartStep": true, "console": "integratedTerminal", "skipFiles": ["<node_internals>/**"]}]}