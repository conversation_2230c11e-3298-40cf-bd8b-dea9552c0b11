
var OGVDecoderVideoAV1SIMDW = (() => {
  var _scriptDir = typeof document != 'undefined' ? document.currentScript?.src : undefined;
  if (typeof __filename != 'undefined') _scriptDir ||= __filename;
  return (
function(moduleArg = {}) {

var b=moduleArg,aa,p,readyPromise=new Promise((a,c)=>{aa=a;p=c}),ba=Object.assign({},b),ca="object"==typeof window,u="function"==typeof importScripts,da="object"==typeof process&&"object"==typeof process.versions&&"string"==typeof process.versions.node,z="",A,E,F;
if(da){var fs=require("fs"),J=require("path");z=u?J.dirname(z)+"/":__dirname+"/";A=(a,c)=>{a=K(a)?new URL(a):J.normalize(a);return fs.readFileSync(a,c?void 0:"utf8")};F=a=>{a=A(a,!0);a.buffer||(a=new Uint8Array(a));return a};E=(a,c,e,d=!0)=>{a=K(a)?new URL(a):J.normalize(a);fs.readFile(a,d?void 0:"utf8",(g,l)=>{g?e(g):c(d?l.buffer:l)})};process.argv.slice(2)}else if(ca||u)u?z=self.location.href:"undefined"!=typeof document&&document.currentScript&&(z=document.currentScript.src),_scriptDir&&(z=_scriptDir),
z.startsWith("blob:")?z="":z=z.substr(0,z.replace(/[?#].*/,"").lastIndexOf("/")+1),A=a=>{var c=new XMLHttpRequest;c.open("GET",a,!1);c.send(null);return c.responseText},u&&(F=a=>{var c=new XMLHttpRequest;c.open("GET",a,!1);c.responseType="arraybuffer";c.send(null);return new Uint8Array(c.response)}),E=(a,c,e)=>{var d=new XMLHttpRequest;d.open("GET",a,!0);d.responseType="arraybuffer";d.onload=()=>{200==d.status||0==d.status&&d.response?c(d.response):e()};d.onerror=e;d.send(null)};
var ea=b.print||console.log.bind(console),L=b.printErr||console.error.bind(console);Object.assign(b,ba);ba=null;var N;b.wasmBinary&&(N=b.wasmBinary);var O,fa=!1,P,Q;function ha(){var a=O.buffer;b.HEAP8=new Int8Array(a);b.HEAP16=new Int16Array(a);b.HEAPU8=P=new Uint8Array(a);b.HEAPU16=new Uint16Array(a);b.HEAP32=new Int32Array(a);b.HEAPU32=Q=new Uint32Array(a);b.HEAPF32=new Float32Array(a);b.HEAPF64=new Float64Array(a)}var ia=[],ja=[],ka=[];function la(){var a=b.preRun.shift();ia.unshift(a)}
var R=0,S=null,T=null;function ma(a){b.onAbort?.(a);a="Aborted("+a+")";L(a);fa=!0;a=new WebAssembly.RuntimeError(a+". Build with -sASSERTIONS for more info.");p(a);throw a;}var na=a=>a.startsWith("data:application/octet-stream;base64,"),K=a=>a.startsWith("file://"),V;V="ogv-decoder-video-av1-simd-wasm.wasm";if(!na(V)){var oa=V;V=b.locateFile?b.locateFile(oa,z):z+oa}function pa(a){if(a==V&&N)return new Uint8Array(N);if(F)return F(a);throw"both async and sync fetching of the wasm failed";}
function qa(a){if(!N&&(ca||u)){if("function"==typeof fetch&&!K(a))return fetch(a,{credentials:"same-origin"}).then(c=>{if(!c.ok)throw`failed to load wasm binary file at '${a}'`;return c.arrayBuffer()}).catch(()=>pa(a));if(E)return new Promise((c,e)=>{E(a,d=>c(new Uint8Array(d)),e)})}return Promise.resolve().then(()=>pa(a))}function ra(a,c,e){return qa(a).then(d=>WebAssembly.instantiate(d,c)).then(e,d=>{L(`failed to asynchronously prepare wasm: ${d}`);ma(d)})}
function sa(a,c){var e=V;return N||"function"!=typeof WebAssembly.instantiateStreaming||na(e)||K(e)||da||"function"!=typeof fetch?ra(e,a,c):fetch(e,{credentials:"same-origin"}).then(d=>WebAssembly.instantiateStreaming(d,a).then(c,function(g){L(`wasm streaming compile failed: ${g}`);L("falling back to ArrayBuffer instantiation");return ra(e,a,c)}))}
var Ea=a=>{for(;0<a.length;)a.shift()(b)},Fa=[null,[],[]],Ga="undefined"!=typeof TextDecoder?new TextDecoder("utf8"):void 0,Ha={f:(a,c,e)=>P.copyWithin(a,c,c+e),b:()=>{ma("")},d:a=>{var c=P.length;a>>>=0;if(2147483648<a)return!1;for(var e=1;4>=e;e*=2){var d=c*(1+.2/e);d=Math.min(d,a+100663296);var g=Math;d=Math.max(a,d);a:{g=(g.min.call(g,2147483648,d+(65536-d%65536)%65536)-O.buffer.byteLength+65535)/65536;try{O.grow(g);ha();var l=1;break a}catch(v){}l=void 0}if(l)return!0}return!1},e:()=>52,c:function(){return 70},
a:(a,c,e,d)=>{for(var g=0,l=0;l<e;l++){var v=Q[c>>2],r=Q[c+4>>2];c+=8;for(var x=0;x<r;x++){var f=P[v+x],w=Fa[a];if(0===f||10===f){f=w;for(var m=0,q=m+NaN,t=m;f[t]&&!(t>=q);)++t;if(16<t-m&&f.buffer&&Ga)f=Ga.decode(f.subarray(m,t));else{for(q="";m<t;){var h=f[m++];if(h&128){var B=f[m++]&63;if(192==(h&224))q+=String.fromCharCode((h&31)<<6|B);else{var G=f[m++]&63;h=224==(h&240)?(h&15)<<12|B<<6|G:(h&7)<<18|B<<12|G<<6|f[m++]&63;65536>h?q+=String.fromCharCode(h):(h-=65536,q+=String.fromCharCode(55296|h>>
10,56320|h&1023))}}else q+=String.fromCharCode(h)}f=q}(1===a?ea:L)(f);w.length=0}else w.push(f)}g+=r}Q[d>>2]=g;return 0},g:function(a,c,e,d,g,l,v,r,x,f,w,m,q,t,h,B){function G(H,k,C,ta,ua,va,Ka,La,U){H.set(new Uint8Array(Ma,k,C*ta));var D,y;for(D=y=0;D<va;D++,y+=C)for(k=0;k<C;k++)H[y+k]=U;for(;D<va+La;D++,y+=C){for(k=0;k<ua;k++)H[y+k]=U;for(k=ua+Ka;k<C;k++)H[y+k]=U}for(;D<ta;D++,y+=C)for(k=0;k<C;k++)H[y+k]=U;return H}var Ma=O.buffer,n=b.videoFormat,wa=(q&-2)*x/v,xa=(t&-2)*f/r,ya=w*x/v,za=m*f/r;w===
n.cropWidth&&m===n.cropHeight&&(h=n.displayWidth,B=n.displayHeight);for(var Aa=b.recycledFrames,I,Ba=r*c,Ca=f*d,Da=f*l;0<Aa.length;){var M=Aa.shift();n=M.format;if(n.width===v&&n.height===r&&n.chromaWidth===x&&n.chromaHeight===f&&n.cropLeft===q&&n.cropTop===t&&n.cropWidth===w&&n.cropHeight===m&&n.displayWidth===h&&n.displayHeight===B&&M.y.bytes.length===Ba&&M.u.bytes.length===Ca&&M.v.bytes.length===Da){I=M;break}}I||={format:{width:v,height:r,chromaWidth:x,chromaHeight:f,cropLeft:q,cropTop:t,cropWidth:w,
cropHeight:m,displayWidth:h,displayHeight:B},y:{bytes:new Uint8Array(Ba),stride:c},u:{bytes:new Uint8Array(Ca),stride:d},v:{bytes:new Uint8Array(Da),stride:l}};G(I.y.bytes,a,c,r,q,t,w,m,0);G(I.u.bytes,e,d,f,wa,xa,ya,za,128);G(I.v.bytes,g,l,f,wa,xa,ya,za,128);b.frameBuffer=I}},W=function(){function a(e){W=e.exports;O=W.h;ha();ja.unshift(W.i);R--;b.monitorRunDependencies?.(R);0==R&&(null!==S&&(clearInterval(S),S=null),T&&(e=T,T=null,e()));return W}var c={a:Ha};R++;b.monitorRunDependencies?.(R);if(b.instantiateWasm)try{return b.instantiateWasm(c,
a)}catch(e){L(`Module.instantiateWasm callback failed with error: ${e}`),p(e)}sa(c,function(e){a(e.instance)}).catch(p);return{}}();b._ogv_video_decoder_init=()=>(b._ogv_video_decoder_init=W.j)();b._ogv_video_decoder_async=()=>(b._ogv_video_decoder_async=W.k)();b._ogv_video_decoder_destroy=()=>(b._ogv_video_decoder_destroy=W.l)();b._ogv_video_decoder_process_header=(a,c)=>(b._ogv_video_decoder_process_header=W.m)(a,c);
b._ogv_video_decoder_process_frame=(a,c)=>(b._ogv_video_decoder_process_frame=W.n)(a,c);b._free=a=>(b._free=W.o)(a);b._malloc=a=>(b._malloc=W.q)(a);var X;T=function Ia(){X||Ja();X||(T=Ia)};
function Ja(){function a(){if(!X&&(X=!0,b.calledRun=!0,!fa)){Ea(ja);aa(b);if(b.onRuntimeInitialized)b.onRuntimeInitialized();if(b.postRun)for("function"==typeof b.postRun&&(b.postRun=[b.postRun]);b.postRun.length;){var c=b.postRun.shift();ka.unshift(c)}Ea(ka)}}if(!(0<R)){if(b.preRun)for("function"==typeof b.preRun&&(b.preRun=[b.preRun]);b.preRun.length;)la();Ea(ia);0<R||(b.setStatus?(b.setStatus("Running..."),setTimeout(function(){setTimeout(function(){b.setStatus("")},1);a()},1)):a())}}
if(b.preInit)for("function"==typeof b.preInit&&(b.preInit=[b.preInit]);0<b.preInit.length;)b.preInit.pop()();Ja();var Y,Na,Oa;Oa="undefined"===typeof performance||"undefined"===typeof performance.now?Date.now:performance.now.bind(performance);function Z(a){var c=Oa();a=a();b.cpuTime+=Oa()-c;return a}b.loadedMetadata=!!b.videoFormat;b.videoFormat=b.videoFormat||null;b.frameBuffer=null;b.cpuTime=0;Object.defineProperty(b,"processing",{get:function(){return!1}});
b.init=function(a){Z(function(){b._ogv_video_decoder_init()});a()};b.processHeader=function(a,c){var e=Z(function(){var d=a.byteLength;Y&&Na>=d||(Y&&b._free(Y),Na=d,Y=b._malloc(Na));var g=Y;(new Uint8Array(O.buffer,g,d)).set(new Uint8Array(a));return b._ogv_video_decoder_process_header(g,d)});c(e)};b.s=[];
b.processFrame=function(a,c){function e(r){b._free(l);c(r)}var d=b._ogv_video_decoder_async(),g=a.byteLength,l=b._malloc(g);d&&b.s.push(e);var v=Z(function(){(new Uint8Array(O.buffer,l,g)).set(new Uint8Array(a));return b._ogv_video_decoder_process_frame(l,g)});d||e(v)};b.close=function(){};b.sync=function(){b._ogv_video_decoder_async()&&(b.s.push(function(){}),Z(function(){b._ogv_video_decoder_process_frame(0,0)}))};b.recycledFrames=[];
b.recycleFrame=function(a){var c=b.recycledFrames;c.push(a);16<c.length&&c.shift()};


  return readyPromise
}
);
})();
if (typeof exports === 'object' && typeof module === 'object')
  module.exports = OGVDecoderVideoAV1SIMDW;
else if (typeof define === 'function' && define['amd'])
  define([], () => OGVDecoderVideoAV1SIMDW);
