
var OGVDecoderVideoTheoraW = (() => {
  var _scriptDir = typeof document != 'undefined' ? document.currentScript?.src : undefined;
  if (typeof __filename != 'undefined') _scriptDir ||= __filename;
  return (
function(moduleArg = {}) {

var b=moduleArg,aa,l,readyPromise=new Promise((a,c)=>{aa=a;l=c}),ba=Object.assign({},b),ca="object"==typeof window,m="function"==typeof importScripts,da="object"==typeof process&&"object"==typeof process.versions&&"string"==typeof process.versions.node,t="",u,y,C;
if(da){var fs=require("fs"),D=require("path");t=m?D.dirname(t)+"/":__dirname+"/";u=(a,c)=>{a=E(a)?new URL(a):D.normalize(a);return fs.readFileSync(a,c?void 0:"utf8")};C=a=>{a=u(a,!0);a.buffer||(a=new Uint8Array(a));return a};y=(a,c,e,d=!0)=>{a=E(a)?new URL(a):D.normalize(a);fs.readFile(a,d?void 0:"utf8",(f,h)=>{f?e(f):c(d?h.buffer:h)})};process.argv.slice(2)}else if(ca||m)m?t=self.location.href:"undefined"!=typeof document&&document.currentScript&&(t=document.currentScript.src),_scriptDir&&(t=_scriptDir),
t.startsWith("blob:")?t="":t=t.substr(0,t.replace(/[?#].*/,"").lastIndexOf("/")+1),u=a=>{var c=new XMLHttpRequest;c.open("GET",a,!1);c.send(null);return c.responseText},m&&(C=a=>{var c=new XMLHttpRequest;c.open("GET",a,!1);c.responseType="arraybuffer";c.send(null);return new Uint8Array(c.response)}),y=(a,c,e)=>{var d=new XMLHttpRequest;d.open("GET",a,!0);d.responseType="arraybuffer";d.onload=()=>{200==d.status||0==d.status&&d.response?c(d.response):e()};d.onerror=e;d.send(null)};b.print||console.log.bind(console);
var F=b.printErr||console.error.bind(console);Object.assign(b,ba);ba=null;var I;b.wasmBinary&&(I=b.wasmBinary);var J,ea=!1,K;function fa(){var a=J.buffer;b.HEAP8=new Int8Array(a);b.HEAP16=new Int16Array(a);b.HEAPU8=K=new Uint8Array(a);b.HEAPU16=new Uint16Array(a);b.HEAP32=new Int32Array(a);b.HEAPU32=new Uint32Array(a);b.HEAPF32=new Float32Array(a);b.HEAPF64=new Float64Array(a)}var ha=[],ia=[],ja=[];function ka(){var a=b.preRun.shift();ha.unshift(a)}
var L=0,M=null,Q=null,la=a=>a.startsWith("data:application/octet-stream;base64,"),E=a=>a.startsWith("file://"),R;R="ogv-decoder-video-theora-wasm.wasm";if(!la(R)){var ma=R;R=b.locateFile?b.locateFile(ma,t):t+ma}function na(a){if(a==R&&I)return new Uint8Array(I);if(C)return C(a);throw"both async and sync fetching of the wasm failed";}
function oa(a){if(!I&&(ca||m)){if("function"==typeof fetch&&!E(a))return fetch(a,{credentials:"same-origin"}).then(c=>{if(!c.ok)throw`failed to load wasm binary file at '${a}'`;return c.arrayBuffer()}).catch(()=>na(a));if(y)return new Promise((c,e)=>{y(a,d=>c(new Uint8Array(d)),e)})}return Promise.resolve().then(()=>na(a))}
function pa(a,c,e){return oa(a).then(d=>WebAssembly.instantiate(d,c)).then(e,d=>{F(`failed to asynchronously prepare wasm: ${d}`);b.onAbort?.(d);d="Aborted("+d+")";F(d);ea=!0;d=new WebAssembly.RuntimeError(d+". Build with -sASSERTIONS for more info.");l(d);throw d;})}
function Ba(a,c){var e=R;return I||"function"!=typeof WebAssembly.instantiateStreaming||la(e)||E(e)||da||"function"!=typeof fetch?pa(e,a,c):fetch(e,{credentials:"same-origin"}).then(d=>WebAssembly.instantiateStreaming(d,a).then(c,function(f){F(`wasm streaming compile failed: ${f}`);F("falling back to ArrayBuffer instantiation");return pa(e,a,c)}))}
var S=a=>{for(;0<a.length;)a.shift()(b)},Ca={b:(a,c,e)=>K.copyWithin(a,c,c+e),a:a=>{var c=K.length;a>>>=0;if(2147483648<a)return!1;for(var e=1;4>=e;e*=2){var d=c*(1+.2/e);d=Math.min(d,a+100663296);var f=Math;d=Math.max(a,d);a:{f=(f.min.call(f,2147483648,d+(65536-d%65536)%65536)-J.buffer.byteLength+65535)/65536;try{J.grow(f);fa();var h=1;break a}catch(q){}h=void 0}if(h)return!0}return!1},c:function(a,c,e,d,f,h,q,n,z,p,v,G,N,O,X,Y){function Z(A,g,w,qa,ra,sa,Fa,Ga,P){A.set(new Uint8Array(Ha,g,w*qa));
var x,r;for(x=r=0;x<sa;x++,r+=w)for(g=0;g<w;g++)A[r+g]=P;for(;x<sa+Ga;x++,r+=w){for(g=0;g<ra;g++)A[r+g]=P;for(g=ra+Fa;g<w;g++)A[r+g]=P}for(;x<qa;x++,r+=w)for(g=0;g<w;g++)A[r+g]=P;return A}var Ha=J.buffer,k=b.videoFormat,ta=(N&-2)*z/q,ua=(O&-2)*p/n,va=v*z/q,wa=G*p/n;v===k.cropWidth&&G===k.cropHeight&&(X=k.displayWidth,Y=k.displayHeight);for(var xa=b.recycledFrames,B,ya=n*c,za=p*d,Aa=p*h;0<xa.length;){var H=xa.shift();k=H.format;if(k.width===q&&k.height===n&&k.chromaWidth===z&&k.chromaHeight===p&&k.cropLeft===
N&&k.cropTop===O&&k.cropWidth===v&&k.cropHeight===G&&k.displayWidth===X&&k.displayHeight===Y&&H.y.bytes.length===ya&&H.u.bytes.length===za&&H.v.bytes.length===Aa){B=H;break}}B||={format:{width:q,height:n,chromaWidth:z,chromaHeight:p,cropLeft:N,cropTop:O,cropWidth:v,cropHeight:G,displayWidth:X,displayHeight:Y},y:{bytes:new Uint8Array(ya),stride:c},u:{bytes:new Uint8Array(za),stride:d},v:{bytes:new Uint8Array(Aa),stride:h}};Z(B.y.bytes,a,c,n,N,O,v,G,0);Z(B.u.bytes,e,d,p,ta,ua,va,wa,128);Z(B.v.bytes,
f,h,p,ta,ua,va,wa,128);b.frameBuffer=B},d:function(a,c,e,d,f,h,q,n,z,p,v){b.videoFormat={width:a,height:c,chromaWidth:e,chromaHeight:d,cropLeft:n,cropTop:z,cropWidth:h,cropHeight:q,displayWidth:p,displayHeight:v,fps:f};b.loadedMetadata=!0}},T=function(){function a(e){T=e.exports;J=T.e;fa();ia.unshift(T.f);L--;b.monitorRunDependencies?.(L);0==L&&(null!==M&&(clearInterval(M),M=null),Q&&(e=Q,Q=null,e()));return T}var c={a:Ca};L++;b.monitorRunDependencies?.(L);if(b.instantiateWasm)try{return b.instantiateWasm(c,
a)}catch(e){F(`Module.instantiateWasm callback failed with error: ${e}`),l(e)}Ba(c,function(e){a(e.instance)}).catch(l);return{}}();b._ogv_video_decoder_init=()=>(b._ogv_video_decoder_init=T.g)();b._ogv_video_decoder_async=()=>(b._ogv_video_decoder_async=T.h)();b._ogv_video_decoder_process_header=(a,c)=>(b._ogv_video_decoder_process_header=T.i)(a,c);b._ogv_video_decoder_process_frame=(a,c)=>(b._ogv_video_decoder_process_frame=T.j)(a,c);
b._ogv_video_decoder_destroy=()=>(b._ogv_video_decoder_destroy=T.k)();b._free=a=>(b._free=T.l)(a);b._malloc=a=>(b._malloc=T.m)(a);var U;Q=function Da(){U||Ea();U||(Q=Da)};
function Ea(){function a(){if(!U&&(U=!0,b.calledRun=!0,!ea)){S(ia);aa(b);if(b.onRuntimeInitialized)b.onRuntimeInitialized();if(b.postRun)for("function"==typeof b.postRun&&(b.postRun=[b.postRun]);b.postRun.length;){var c=b.postRun.shift();ja.unshift(c)}S(ja)}}if(!(0<L)){if(b.preRun)for("function"==typeof b.preRun&&(b.preRun=[b.preRun]);b.preRun.length;)ka();S(ha);0<L||(b.setStatus?(b.setStatus("Running..."),setTimeout(function(){setTimeout(function(){b.setStatus("")},1);a()},1)):a())}}
if(b.preInit)for("function"==typeof b.preInit&&(b.preInit=[b.preInit]);0<b.preInit.length;)b.preInit.pop()();Ea();var V,Ia,Ja;Ja="undefined"===typeof performance||"undefined"===typeof performance.now?Date.now:performance.now.bind(performance);function W(a){var c=Ja();a=a();b.cpuTime+=Ja()-c;return a}b.loadedMetadata=!!b.videoFormat;b.videoFormat=b.videoFormat||null;b.frameBuffer=null;b.cpuTime=0;Object.defineProperty(b,"processing",{get:function(){return!1}});
b.init=function(a){W(function(){b._ogv_video_decoder_init()});a()};b.processHeader=function(a,c){var e=W(function(){var d=a.byteLength;V&&Ia>=d||(V&&b._free(V),Ia=d,V=b._malloc(Ia));var f=V;(new Uint8Array(J.buffer,f,d)).set(new Uint8Array(a));return b._ogv_video_decoder_process_header(f,d)});c(e)};b.o=[];
b.processFrame=function(a,c){function e(n){b._free(h);c(n)}var d=b._ogv_video_decoder_async(),f=a.byteLength,h=b._malloc(f);d&&b.o.push(e);var q=W(function(){(new Uint8Array(J.buffer,h,f)).set(new Uint8Array(a));return b._ogv_video_decoder_process_frame(h,f)});d||e(q)};b.close=function(){};b.sync=function(){b._ogv_video_decoder_async()&&(b.o.push(function(){}),W(function(){b._ogv_video_decoder_process_frame(0,0)}))};b.recycledFrames=[];
b.recycleFrame=function(a){var c=b.recycledFrames;c.push(a);16<c.length&&c.shift()};


  return readyPromise
}
);
})();
if (typeof exports === 'object' && typeof module === 'object')
  module.exports = OGVDecoderVideoTheoraW;
else if (typeof define === 'function' && define['amd'])
  define([], () => OGVDecoderVideoTheoraW);
