
var OGVDemuxerWebMW = (() => {
  var _scriptDir = typeof document != 'undefined' ? document.currentScript?.src : undefined;
  if (typeof __filename != 'undefined') _scriptDir ||= __filename;
  return (
function(moduleArg = {}) {

var b=moduleArg,h,k,readyPromise=new Promise((a,c)=>{h=a;k=c}),l=Object.assign({},b),m="object"==typeof window,n="function"==typeof importScripts,q="object"==typeof process&&"object"==typeof process.versions&&"string"==typeof process.versions.node,r="",t,u,v;
if(q){var fs=require("fs"),w=require("path");r=n?w.dirname(r)+"/":__dirname+"/";t=(a,c)=>{a=A(a)?new URL(a):w.normalize(a);return fs.readFileSync(a,c?void 0:"utf8")};v=a=>{a=t(a,!0);a.buffer||(a=new Uint8Array(a));return a};u=(a,c,d,e=!0)=>{a=A(a)?new URL(a):w.normalize(a);fs.readFile(a,e?void 0:"utf8",(f,g)=>{f?d(f):c(e?g.buffer:g)})};process.argv.slice(2)}else if(m||n)n?r=self.location.href:"undefined"!=typeof document&&document.currentScript&&(r=document.currentScript.src),_scriptDir&&(r=_scriptDir),
r.startsWith("blob:")?r="":r=r.substr(0,r.replace(/[?#].*/,"").lastIndexOf("/")+1),t=a=>{var c=new XMLHttpRequest;c.open("GET",a,!1);c.send(null);return c.responseText},n&&(v=a=>{var c=new XMLHttpRequest;c.open("GET",a,!1);c.responseType="arraybuffer";c.send(null);return new Uint8Array(c.response)}),u=(a,c,d)=>{var e=new XMLHttpRequest;e.open("GET",a,!0);e.responseType="arraybuffer";e.onload=()=>{200==e.status||0==e.status&&e.response?c(e.response):d()};e.onerror=d;e.send(null)};
var aa=b.print||console.log.bind(console),B=b.printErr||console.error.bind(console);Object.assign(b,l);l=null;var C;b.wasmBinary&&(C=b.wasmBinary);var D,E=!1,F,G;function I(){var a=D.buffer;b.HEAP8=new Int8Array(a);b.HEAP16=new Int16Array(a);b.HEAPU8=F=new Uint8Array(a);b.HEAPU16=new Uint16Array(a);b.HEAP32=new Int32Array(a);b.HEAPU32=G=new Uint32Array(a);b.HEAPF32=new Float32Array(a);b.HEAPF64=new Float64Array(a)}var J=[],K=[],L=[];function ba(){var a=b.preRun.shift();J.unshift(a)}
var M=0,N=null,O=null;function P(a){b.onAbort?.(a);a="Aborted("+a+")";B(a);E=!0;a=new WebAssembly.RuntimeError(a+". Build with -sASSERTIONS for more info.");k(a);throw a;}var Q=a=>a.startsWith("data:application/octet-stream;base64,"),A=a=>a.startsWith("file://"),R;R="ogv-demuxer-webm-wasm.wasm";if(!Q(R)){var ca=R;R=b.locateFile?b.locateFile(ca,r):r+ca}function da(a){if(a==R&&C)return new Uint8Array(C);if(v)return v(a);throw"both async and sync fetching of the wasm failed";}
function ea(a){if(!C&&(m||n)){if("function"==typeof fetch&&!A(a))return fetch(a,{credentials:"same-origin"}).then(c=>{if(!c.ok)throw`failed to load wasm binary file at '${a}'`;return c.arrayBuffer()}).catch(()=>da(a));if(u)return new Promise((c,d)=>{u(a,e=>c(new Uint8Array(e)),d)})}return Promise.resolve().then(()=>da(a))}function fa(a,c,d){return ea(a).then(e=>WebAssembly.instantiate(e,c)).then(d,e=>{B(`failed to asynchronously prepare wasm: ${e}`);P(e)})}
function ha(a,c){var d=R;return C||"function"!=typeof WebAssembly.instantiateStreaming||Q(d)||A(d)||q||"function"!=typeof fetch?fa(d,a,c):fetch(d,{credentials:"same-origin"}).then(e=>WebAssembly.instantiateStreaming(e,a).then(c,function(f){B(`wasm streaming compile failed: ${f}`);B("falling back to ArrayBuffer instantiation");return fa(d,a,c)}))}
var S=a=>{for(;0<a.length;)a.shift()(b)},ia="undefined"!=typeof TextDecoder?new TextDecoder("utf8"):void 0,T=(a,c)=>{for(var d=c+NaN,e=c;a[e]&&!(e>=d);)++e;if(16<e-c&&a.buffer&&ia)return ia.decode(a.subarray(c,e));for(d="";c<e;){var f=a[c++];if(f&128){var g=a[c++]&63;if(192==(f&224))d+=String.fromCharCode((f&31)<<6|g);else{var p=a[c++]&63;f=224==(f&240)?(f&15)<<12|g<<6|p:(f&7)<<18|g<<12|p<<6|a[c++]&63;65536>f?d+=String.fromCharCode(f):(f-=65536,d+=String.fromCharCode(55296|f>>10,56320|f&1023))}}else d+=
String.fromCharCode(f)}return d},ja=[null,[],[]],ka={a:(a,c,d,e)=>{P(`Assertion failed: ${a?T(F,a):""}, at: `+[c?c?T(F,c):"":"unknown filename",d,e?e?T(F,e):"":"unknown function"])},f:(a,c,d)=>F.copyWithin(a,c,c+d),g:()=>{P("")},e:a=>{var c=F.length;a>>>=0;if(2147483648<a)return!1;for(var d=1;4>=d;d*=2){var e=c*(1+.2/d);e=Math.min(e,a+100663296);var f=Math;e=Math.max(a,e);a:{f=(f.min.call(f,2147483648,e+(65536-e%65536)%65536)-D.buffer.byteLength+65535)/65536;try{D.grow(f);I();var g=1;break a}catch(p){}g=
void 0}if(g)return!0}return!1},b:(a,c,d,e)=>{for(var f=0,g=0;g<d;g++){var p=G[c>>2],H=G[c+4>>2];c+=8;for(var x=0;x<H;x++){var y=F[p+x],z=ja[a];0===y||10===y?((1===a?aa:B)(T(z,0)),z.length=0):z.push(y)}f+=H}G[e>>2]=f;return 0},c:function(a,c,d,e){var f=D.buffer;b.audioPackets.push({data:f.slice?f.slice(a,a+c):(new Uint8Array(new Uint8Array(f,a,c))).buffer,timestamp:d,discardPadding:e})},j:function(a,c,d,e,f,g,p,H,x,y,z){b.videoFormat={width:a,height:c,chromaWidth:d,chromaHeight:e,cropLeft:H,cropTop:x,
cropWidth:g,cropHeight:p,displayWidth:y,displayHeight:z,fps:f}},i:function(a,c){function d(e){for(var f="",g=new Uint8Array(D.buffer);0!=g[e];e++)f+=String.fromCharCode(g[e]);return f}a&&(b.videoCodec=d(a));c&&(b.audioCodec=d(c));a=b._ogv_demuxer_media_duration();b.duration=0<=a?a:NaN;b.loadedMetadata=!0},d:function(a,c){if(b.onseek)b.onseek(a+4294967296*c)},h:function(a,c,d,e,f){var g=D.buffer;b.videoPackets.push({data:g.slice?g.slice(a,a+c):(new Uint8Array(new Uint8Array(g,a,c))).buffer,timestamp:d,
keyframeTimestamp:e,isKeyframe:!!f})}},U=function(){function a(d){U=d.exports;D=U.k;I();K.unshift(U.l);M--;b.monitorRunDependencies?.(M);0==M&&(null!==N&&(clearInterval(N),N=null),O&&(d=O,O=null,d()));return U}var c={a:ka};M++;b.monitorRunDependencies?.(M);if(b.instantiateWasm)try{return b.instantiateWasm(c,a)}catch(d){B(`Module.instantiateWasm callback failed with error: ${d}`),k(d)}ha(c,function(d){a(d.instance)}).catch(k);return{}}();b._ogv_demuxer_init=()=>(b._ogv_demuxer_init=U.m)();
b._ogv_demuxer_receive_input=(a,c)=>(b._ogv_demuxer_receive_input=U.n)(a,c);b._ogv_demuxer_process=()=>(b._ogv_demuxer_process=U.o)();b._ogv_demuxer_destroy=()=>(b._ogv_demuxer_destroy=U.p)();b._ogv_demuxer_flush=()=>(b._ogv_demuxer_flush=U.q)();b._ogv_demuxer_media_length=()=>(b._ogv_demuxer_media_length=U.r)();b._ogv_demuxer_media_duration=()=>(b._ogv_demuxer_media_duration=U.s)();b._ogv_demuxer_seekable=()=>(b._ogv_demuxer_seekable=U.t)();
b._ogv_demuxer_keypoint_offset=a=>(b._ogv_demuxer_keypoint_offset=U.u)(a);b._ogv_demuxer_seek_to_keypoint=a=>(b._ogv_demuxer_seek_to_keypoint=U.v)(a);b._malloc=a=>(b._malloc=U.x)(a);b._free=a=>(b._free=U.y)(a);var V;O=function la(){V||ma();V||(O=la)};
function ma(){function a(){if(!V&&(V=!0,b.calledRun=!0,!E)){S(K);h(b);if(b.onRuntimeInitialized)b.onRuntimeInitialized();if(b.postRun)for("function"==typeof b.postRun&&(b.postRun=[b.postRun]);b.postRun.length;){var c=b.postRun.shift();L.unshift(c)}S(L)}}if(!(0<M)){if(b.preRun)for("function"==typeof b.preRun&&(b.preRun=[b.preRun]);b.preRun.length;)ba();S(J);0<M||(b.setStatus?(b.setStatus("Running..."),setTimeout(function(){setTimeout(function(){b.setStatus("")},1);a()},1)):a())}}
if(b.preInit)for("function"==typeof b.preInit&&(b.preInit=[b.preInit]);0<b.preInit.length;)b.preInit.pop()();ma();var W,X,Y;Y="undefined"===typeof performance||"undefined"===typeof performance.now?Date.now:performance.now.bind(performance);function Z(a){var c=Y();a=a();c=Y()-c;b.cpuTime+=c;return a}b.loadedMetadata=!1;b.videoCodec=null;b.audioCodec=null;b.duration=NaN;b.onseek=null;b.cpuTime=0;b.audioPackets=[];Object.defineProperty(b,"hasAudio",{get:function(){return b.loadedMetadata&&b.audioCodec}});
Object.defineProperty(b,"audioReady",{get:function(){return 0<b.audioPackets.length}});Object.defineProperty(b,"audioTimestamp",{get:function(){return 0<b.audioPackets.length?b.audioPackets[0].timestamp:-1}});b.videoPackets=[];Object.defineProperty(b,"hasVideo",{get:function(){return b.loadedMetadata&&b.videoCodec}});Object.defineProperty(b,"frameReady",{get:function(){return 0<b.videoPackets.length}});
Object.defineProperty(b,"frameTimestamp",{get:function(){return 0<b.videoPackets.length?b.videoPackets[0].timestamp:-1}});Object.defineProperty(b,"keyframeTimestamp",{get:function(){return 0<b.videoPackets.length?b.videoPackets[0].keyframeTimestamp:-1}});Object.defineProperty(b,"nextKeyframeTimestamp",{get:function(){for(var a=0;a<b.videoPackets.length;a++){var c=b.videoPackets[a];if(c.isKeyframe)return c.timestamp}return-1}});Object.defineProperty(b,"processing",{get:function(){return!1}});
Object.defineProperty(b,"seekable",{get:function(){return!!b._ogv_demuxer_seekable()}});b.init=function(a){Z(function(){b._ogv_demuxer_init()});a()};b.receiveInput=function(a,c){Z(function(){var d=a.byteLength;W&&X>=d||(W&&b._free(W),X=d,W=b._malloc(X));var e=W;(new Uint8Array(D.buffer,e,d)).set(new Uint8Array(a));b._ogv_demuxer_receive_input(e,d)});c()};b.process=function(a){var c=Z(function(){return b._ogv_demuxer_process()});a(!!c)};
b.dequeueVideoPacket=function(a){if(b.videoPackets.length){var c=b.videoPackets.shift().data;a(c)}else a(null)};b.dequeueAudioPacket=function(a){if(b.audioPackets.length){var c=b.audioPackets.shift();a(c.data,c.discardPadding)}else a(null)};b.getKeypointOffset=function(a,c){var d=Z(function(){return b._ogv_demuxer_keypoint_offset(1E3*a)});c(d)};
b.seekToKeypoint=function(a,c){var d=Z(function(){return b._ogv_demuxer_seek_to_keypoint(1E3*a)});d&&(b.audioPackets.splice(0,b.audioPackets.length),b.videoPackets.splice(0,b.videoPackets.length));c(!!d)};b.flush=function(a){Z(function(){b.audioPackets.splice(0,b.audioPackets.length);b.videoPackets.splice(0,b.videoPackets.length);b._ogv_demuxer_flush()});a()};b.close=function(){};


  return readyPromise
}
);
})();
if (typeof exports === 'object' && typeof module === 'object')
  module.exports = OGVDemuxerWebMW;
else if (typeof define === 'function' && define['amd'])
  define([], () => OGVDemuxerWebMW);
