
var OGVDecoderAudioOpusW = (() => {
  var _scriptDir = typeof document != 'undefined' ? document.currentScript?.src : undefined;
  if (typeof __filename != 'undefined') _scriptDir ||= __filename;
  return (
function(moduleArg = {}) {

var b=moduleArg,l,n,readyPromise=new Promise((a,c)=>{l=a;n=c}),q=Object.assign({},b),r="object"==typeof window,u="function"==typeof importScripts,v="object"==typeof process&&"object"==typeof process.versions&&"string"==typeof process.versions.node,x="",y,z,A;
if(v){var fs=require("fs"),B=require("path");x=u?B.dirname(x)+"/":__dirname+"/";y=(a,c)=>{a=C(a)?new URL(a):B.normalize(a);return fs.readFileSync(a,c?void 0:"utf8")};A=a=>{a=y(a,!0);a.buffer||(a=new Uint8Array(a));return a};z=(a,c,e,d=!0)=>{a=C(a)?new URL(a):B.normalize(a);fs.readFile(a,d?void 0:"utf8",(f,m)=>{f?e(f):c(d?m.buffer:m)})};process.argv.slice(2)}else if(r||u)u?x=self.location.href:"undefined"!=typeof document&&document.currentScript&&(x=document.currentScript.src),_scriptDir&&(x=_scriptDir),
x.startsWith("blob:")?x="":x=x.substr(0,x.replace(/[?#].*/,"").lastIndexOf("/")+1),y=a=>{var c=new XMLHttpRequest;c.open("GET",a,!1);c.send(null);return c.responseText},u&&(A=a=>{var c=new XMLHttpRequest;c.open("GET",a,!1);c.responseType="arraybuffer";c.send(null);return new Uint8Array(c.response)}),z=(a,c,e)=>{var d=new XMLHttpRequest;d.open("GET",a,!0);d.responseType="arraybuffer";d.onload=()=>{200==d.status||0==d.status&&d.response?c(d.response):e()};d.onerror=e;d.send(null)};
var aa=b.print||console.log.bind(console),D=b.printErr||console.error.bind(console);Object.assign(b,q);q=null;var E;b.wasmBinary&&(E=b.wasmBinary);var F,G=!1,H,I;function J(){var a=F.buffer;b.HEAP8=new Int8Array(a);b.HEAP16=new Int16Array(a);b.HEAPU8=H=new Uint8Array(a);b.HEAPU16=new Uint16Array(a);b.HEAP32=new Int32Array(a);b.HEAPU32=I=new Uint32Array(a);b.HEAPF32=new Float32Array(a);b.HEAPF64=new Float64Array(a)}var K=[],L=[],ba=[];function ca(){var a=b.preRun.shift();K.unshift(a)}
var P=0,Q=null,R=null;function da(a){b.onAbort?.(a);a="Aborted("+a+")";D(a);G=!0;a=new WebAssembly.RuntimeError(a+". Build with -sASSERTIONS for more info.");n(a);throw a;}var ea=a=>a.startsWith("data:application/octet-stream;base64,"),C=a=>a.startsWith("file://"),S;S="ogv-decoder-audio-opus-wasm.wasm";if(!ea(S)){var fa=S;S=b.locateFile?b.locateFile(fa,x):x+fa}function ha(a){if(a==S&&E)return new Uint8Array(E);if(A)return A(a);throw"both async and sync fetching of the wasm failed";}
function ka(a){if(!E&&(r||u)){if("function"==typeof fetch&&!C(a))return fetch(a,{credentials:"same-origin"}).then(c=>{if(!c.ok)throw`failed to load wasm binary file at '${a}'`;return c.arrayBuffer()}).catch(()=>ha(a));if(z)return new Promise((c,e)=>{z(a,d=>c(new Uint8Array(d)),e)})}return Promise.resolve().then(()=>ha(a))}function la(a,c,e){return ka(a).then(d=>WebAssembly.instantiate(d,c)).then(e,d=>{D(`failed to asynchronously prepare wasm: ${d}`);da(d)})}
function ma(a,c){var e=S;return E||"function"!=typeof WebAssembly.instantiateStreaming||ea(e)||C(e)||v||"function"!=typeof fetch?la(e,a,c):fetch(e,{credentials:"same-origin"}).then(d=>WebAssembly.instantiateStreaming(d,a).then(c,function(f){D(`wasm streaming compile failed: ${f}`);D("falling back to ArrayBuffer instantiation");return la(e,a,c)}))}
var T=a=>{for(;0<a.length;)a.shift()(b)},na=[null,[],[]],oa="undefined"!=typeof TextDecoder?new TextDecoder("utf8"):void 0,pa={e:(a,c,e)=>H.copyWithin(a,c,c+e),f:()=>{da("")},c:a=>{var c=H.length;a>>>=0;if(2147483648<a)return!1;for(var e=1;4>=e;e*=2){var d=c*(1+.2/e);d=Math.min(d,a+100663296);var f=Math;d=Math.max(a,d);a:{f=(f.min.call(f,2147483648,d+(65536-d%65536)%65536)-F.buffer.byteLength+65535)/65536;try{F.grow(f);J();var m=1;break a}catch(h){}m=void 0}if(m)return!0}return!1},d:()=>52,b:function(){return 70},
a:(a,c,e,d)=>{for(var f=0,m=0;m<e;m++){var h=I[c>>2],ia=I[c+4>>2];c+=8;for(var M=0;M<ia;M++){var g=H[h+M],N=na[a];if(0===g||10===g){g=N;for(var p=0,t=p+NaN,w=p;g[w]&&!(w>=t);)++w;if(16<w-p&&g.buffer&&oa)g=oa.decode(g.subarray(p,w));else{for(t="";p<w;){var k=g[p++];if(k&128){var O=g[p++]&63;if(192==(k&224))t+=String.fromCharCode((k&31)<<6|O);else{var ja=g[p++]&63;k=224==(k&240)?(k&15)<<12|O<<6|ja:(k&7)<<18|O<<12|ja<<6|g[p++]&63;65536>k?t+=String.fromCharCode(k):(k-=65536,t+=String.fromCharCode(55296|
k>>10,56320|k&1023))}}else t+=String.fromCharCode(k)}g=t}(1===a?aa:D)(g);N.length=0}else N.push(g)}f+=ia}I[d>>2]=f;return 0},g:function(a,c,e){var d=F.buffer,f=new Uint32Array(d,a,c),m=[];if(0!==a)for(a=0;a<c;a++){var h=f[a];d.slice?(h=d.slice(h,h+4*e),h=new Float32Array(h)):(h=new Float32Array(d,h,e),h=new Float32Array(h));m.push(h)}b.audioBuffer=m},h:function(a,c){b.audioFormat={channels:a,rate:c};b.loadedMetadata=!0}},U=function(){function a(e){U=e.exports;F=U.i;J();L.unshift(U.j);P--;b.monitorRunDependencies?.(P);
0==P&&(null!==Q&&(clearInterval(Q),Q=null),R&&(e=R,R=null,e()));return U}var c={a:pa};P++;b.monitorRunDependencies?.(P);if(b.instantiateWasm)try{return b.instantiateWasm(c,a)}catch(e){D(`Module.instantiateWasm callback failed with error: ${e}`),n(e)}ma(c,function(e){a(e.instance)}).catch(n);return{}}();b._ogv_audio_decoder_init=()=>(b._ogv_audio_decoder_init=U.k)();b._ogv_audio_decoder_process_header=(a,c)=>(b._ogv_audio_decoder_process_header=U.l)(a,c);
b._ogv_audio_decoder_process_audio=(a,c)=>(b._ogv_audio_decoder_process_audio=U.m)(a,c);b._malloc=a=>(b._malloc=U.n)(a);b._free=a=>(b._free=U.o)(a);b._ogv_audio_decoder_destroy=()=>(b._ogv_audio_decoder_destroy=U.p)();var V;R=function qa(){V||ra();V||(R=qa)};
function ra(){function a(){if(!V&&(V=!0,b.calledRun=!0,!G)){T(L);l(b);if(b.onRuntimeInitialized)b.onRuntimeInitialized();if(b.postRun)for("function"==typeof b.postRun&&(b.postRun=[b.postRun]);b.postRun.length;){var c=b.postRun.shift();ba.unshift(c)}T(ba)}}if(!(0<P)){if(b.preRun)for("function"==typeof b.preRun&&(b.preRun=[b.preRun]);b.preRun.length;)ca();T(K);0<P||(b.setStatus?(b.setStatus("Running..."),setTimeout(function(){setTimeout(function(){b.setStatus("")},1);a()},1)):a())}}
if(b.preInit)for("function"==typeof b.preInit&&(b.preInit=[b.preInit]);0<b.preInit.length;)b.preInit.pop()();ra();var W,X;function sa(a){if(W&&X>=a)return W;W&&b._free(W);X=a;return W=b._malloc(X)}var Y;Y="undefined"===typeof performance||"undefined"===typeof performance.now?Date.now:performance.now.bind(performance);function Z(a){var c=Y();a=a();b.cpuTime+=Y()-c;return a}b.loadedMetadata=!!b.audioFormat;b.audioFormat=b.audioFormat||null;b.audioBuffer=null;b.cpuTime=0;
Object.defineProperty(b,"processing",{get:function(){return!1}});b.init=function(a){Z(function(){b._ogv_audio_decoder_init()});a()};b.processHeader=function(a,c){var e=Z(function(){var d=a.byteLength,f=sa(d);(new Uint8Array(F.buffer,f,d)).set(new Uint8Array(a));return b._ogv_audio_decoder_process_header(f,d)});c(e)};b.processAudio=function(a,c){var e=Z(function(){var d=a.byteLength,f=sa(d);(new Uint8Array(F.buffer,f,d)).set(new Uint8Array(a));return b._ogv_audio_decoder_process_audio(f,d)});c(e)};
b.close=function(){};


  return readyPromise
}
);
})();
if (typeof exports === 'object' && typeof module === 'object')
  module.exports = OGVDecoderAudioOpusW;
else if (typeof define === 'function' && define['amd'])
  define([], () => OGVDecoderAudioOpusW);
