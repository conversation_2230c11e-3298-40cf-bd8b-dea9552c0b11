importScripts('https://www.gstatic.com/firebasejs/10.13.2/firebase-app-compat.js');
importScripts('https://www.gstatic.com/firebasejs/10.13.2/firebase-messaging-compat.js');


// Initialize the Firebase app in the service worker by passing in
// your app's Firebase config object.
// https://firebase.google.com/docs/web/setup#config-object
const firebaseApp = firebase.initializeApp({
  apiKey: "AIzaSyBeTzxf-heSfoLnPTFMHvwE1OsznpvzaPA",
  authDomain: "wetarseelfirebase.firebaseapp.com",
  projectId: "wetarseelfirebase",
  storageBucket: "wetarseelfirebase.firebasestorage.app",
  messagingSenderId: "856399030221",
  appId: "1:856399030221:web:57887ea5fd4dbec9fe4684",
  measurementId: "G-P9ZMF0Y3P6"
});

// Retrieve an instance of Firebase Messaging so that it can handle background
// messages.
const messaging = firebase.messaging();

messaging.onMessage((payload) => {
  console.log('Message received. ', payload);
  // ...
});
