import { cookies } from 'next/headers';
import { encodeNextPBCookie } from './encodeCookies';
import { RequestCookie } from 'next/dist/compiled/@edge-runtime/cookies';
import { unstable_noStore } from 'next/cache';

export async function getNextjsCookie(request_cookie?: RequestCookie) {
  unstable_noStore();
  try {
    if (request_cookie) {
      const cookie = encodeNextPBCookie(request_cookie);
      return cookie;
    }
    const next_cookie = cookies().get('pb_auth');
    const cookie = encodeNextPBCookie(next_cookie);
    return cookie;
  } catch (error: any) {
    console.log('issue getting next-cookie  === ', error);
    return '';
  }
}
