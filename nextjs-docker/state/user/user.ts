import { getVerificationMail, requestPasswordReset } from '@/lib/pocket';
import { ClientResponseError } from 'pocketbase';
import { pb_user_collection } from '../consts';
import { PB, pb } from '../pb/client_config';
import { P<PERSON><PERSON><PERSON>R<PERSON>ord, TUserSignUpFormFields } from './types';
import { clearImpersonate } from '@/lib/actions';
import { User } from '@/lib/types';

interface ILoginUser {
  pb: PB;
  user: string;
  password: string;
}

async function createFileFromUrl(url: string, filename: string) {
  const response = await fetch(url);
  const blob = await response.blob();
  const file = new File([blob], filename, { type: blob.type });
  return file;
}

export async function loginSignUpWithGoogle() {
  try {
    const userData = await pb.collection('users').authWithOAuth2({
      provider: 'google',
      expand: 'roles',
    });
    if (userData?.meta?.isNew) {
      const userId = userData.record.id;
      const imageFile = await createFileFromUrl(userData?.meta?.avatarUrl, 'avatar.jpg');
      await pb.collection('users').update(userId, {
        name: userData?.meta?.name,
        avatar: imageFile,
        type: 'admin',
      });
    }
    document.cookie = pb.authStore.exportToCookie({ httpOnly: false });
    window.location.href = `/auth`;
  } catch (error) {
    console.log(error);
  }
}

export async function loginUser(prevState: any, formData: FormData) {
  const user = (formData.get('email') ?? '').toString().toLowerCase();
  const password = formData.get('password') as string;
  const otpLogin = (formData.get('otpLogin') ?? '') as string;
  let authData;
  try {
    authData = await pb.collection('_superusers').authWithPassword(user, password, { expand: 'roles' });
  } catch (e) {
    console.log(e);
    try {
      if (otpLogin !== '') {
        const result = await pb.collection('users').requestOTP(user);
        window.location.href = `/auth/otp?otpId=${result.otpId}`;
        return;
      }
      authData = await pb.collection('users').authWithPassword<User>(user, password, {
        expand: 'roles',
      });
    } catch (err: any) {
      const mfaId = err.response?.mfaId;
      if (!mfaId) {
        return { message: { status: 400 } };
      }
      const result = await pb.collection('users').requestOTP(user);
      window.location.href = `/auth/otp?mfaId=${mfaId}&otpId=${result.otpId}`;

      return { message: { status: 200 } };
    }
  }

  if (!authData) {
    return { message: { status: 403 } };
  }

  if (authData.record.lock) {
    return {
      message: { status: 403 },
    };
  }

  //@ts-ignore
  pb.authStore.save(pb.authStore.token, {
    id: authData.record.id,
    email: authData.record.email,
    collectionId: authData.record.collectionId,
    collectionName: authData.record.collectionName,
    verified: authData.record.verified,
    favorite: authData.record.favorite,
  });

  document.cookie = pb.authStore.exportToCookie({ httpOnly: false });
  // pb.authStore.save(pb.authStore.token, userRecord);
  window.location.href = `/auth`;
  return { message: { status: 200 } };
  // if ((authData.record.type == 'admin' || authData.record.password_changed || password != 'test1234') && authData.record.verified) {
  //   // update the cookie with the pb_auth data
  //   document.cookie = pb.authStore.exportToCookie({ httpOnly: false });
  //   window.location.href = `/auth`;
  //   return {
  //     message: { status: 200 },
  //   };
  // }
  // if ((authData.record.type == 'admin' || !authData.record.password_changed || password == 'test1234') && !authData.record.verified) {
  //   return {
  //     message: { status: 404, email: authData.record.email },
  //   };
  // }

  // const respone = await requestPasswordReset(authData.record.email);
  // if (respone.success) {
  //   window.location.href = `/auth/redirect`;
  // }
  return {
    message: { status: 504, email: authData.record.email },
  };
}

export async function loginUserFromSignUp({ pb, user, password }: ILoginUser) {
  try {
    const authData = await pb.collection(pb_user_collection).authWithPassword<PBUserRecord>(user, password);
    // update the cookie with the pb_auth data
    // document.cookie = pb.authStore.exportToCookie({ httpOnly: false });
    return authData;
  } catch (error) {
    throw error;
  }
}

export interface ISignupuser {
  pb: PB;
  user: TUserSignUpFormFields;
}

export async function createUser({ pb, user }: ISignupuser) {
  try {
    await pb.collection(pb_user_collection).create(user);
    await pb.collection('users').requestVerification(user.email);
    // const logged_in_user = await loginUserFromSignUp({
    //     pb, user: user.email, password: user.password,
    // });
    // return logged_in_user;
    return { success: true };
  } catch (error) {
    console.log(error);
    if (error instanceof ClientResponseError) {
      console.log(error.response.data.username.message);
      return { success: false, error: error.response.data.username.message };
    }
    return { success: false };
  }
}

export async function createAgent({ pb, user, account_id }: { pb: any; user: any; account_id: string }) {
  try {
    const add_to_pb = await pb.collection('users').create(user);
    console.log(add_to_pb);
    await pb.collection('accounts').update(account_id, { 'pb_user_id+': add_to_pb.id });
    await getVerificationMail(user.email);
    return add_to_pb;
  } catch (error) {
    console.log(error);
    throw error;
  }
}

export function logoutUser() {
  try {
    clearImpersonate();
    setTimeout(() => {
      pb.authStore.clear();
      document.cookie = pb.authStore.exportToCookie({ httpOnly: false });
      window.location.href = '/auth';
    }, 100);

    // window.FB.logout(function(response: any) {
    //     // user is now logged out

    //     window.location.href = '/next/auth';
    // });
  } catch (error) {
    console.log(error);

    // throw error;
  }
}
