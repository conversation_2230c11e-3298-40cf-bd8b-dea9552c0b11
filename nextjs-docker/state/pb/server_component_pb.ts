import { jwtDecode } from 'jwt-decode';
import { cookies, headers } from 'next/headers';
import PocketBase from 'pocketbase';
import { pb_url } from '../consts';
import { getNextjsCookie } from '../utils/server-cookie';
import { User } from '@/lib/types';
import { redirect } from 'next/navigation';

const normalizeUser = (_user: User) => {
  const user: User = {
    id: _user.id || '',
    name: _user.name || '',
    type: _user.type || '',
    username: _user.username || '',
    dashboard_options: _user.dashboard_options || [],
    avatar: _user.avatar || '',
    email: _user.email || '',
    emailVisibility: _user.emailVisibility || false,
    phoneNumber: _user.phoneNumber || '',
    phone_number: _user.phone_number || '',
    collectionName: _user.collectionName || '',
    verified: _user.verified || false,
    collectionId: _user.collectionId || '',
    created: _user.created || '',
    favorite: _user.favorite || '',
    updated: _user.updated || '',
    lock: _user.lock || false,
    notification: _user.notification || '',
  };
  return user;
};
export async function server_component_pb() {
  // const cookie = req.cookies.get('pb_auth')?.value;
  const cookie = await getNextjsCookie();
  const localCookie = cookies();
  // const response = NextResponse.next();
  let pb = new PocketBase(pb_url);
  pb.authStore.loadFromCookie(cookie || '');

  let impersonate = false;
  // impersonate any user here !-----
  const impersonateCookie = localCookie.get('impersonate');
  if (impersonateCookie?.value) {
    pb = await pb.collection<User>('users').impersonate(impersonateCookie!.value, 20000, { expand: 'roles' });
  }

  // -----------------

  if (!pb.authStore.isValid) {
    return { pb, cookies, headers };
  }

  // get more stuff in user if not available
  if (!pb.authStore.record?.username) {
    let record;
    if (pb.authStore.isSuperuser) {
      record = await pb.collection<User>('_superusers').getOne(pb.authStore.record?.id || '', { expand: 'roles' });
    } else {
      record = await pb.collection<User>('users').getOne(pb.authStore.record?.id || '', { expand: 'roles' });
    }
    pb.authStore.save(pb.authStore.token, record);
  }
  try {
    const exp = jwtDecode(pb.authStore.token).exp;
    if (exp) {
      const timeLeft = exp - Math.floor(Date.now() / 1000);
      if (timeLeft < 60 && !pb.authStore.isSuperuser && pb.authStore.isValid) {
        if (!impersonate) {
          // get an up-to-date auth store state by verifying and refreshing the loaded auth model (if any)
          if (pb.authStore.isValid) {
            if (pb.authStore.isSuperuser) {
              await pb.collection<User>('_superusers').authRefresh({ expand: 'roles' });
            } else {
              await pb.collection<User>('users').authRefresh({ expand: 'roles' });
            }
          }
        }
      }
    }
  } catch (err) {
    console.log('jwtDecode', pb.authStore.token, err);
    // clear the auth store on failed refresh
    pb.authStore.clear();
    // redirect('/auth');
  }

  if (pb.authStore.isSuperuser) {
    const changePb = {
      pb: {
        ...pb,
        authStore: {
          ...pb.authStore,
          isSuperuser: pb.authStore.isSuperuser,
          record: {
            ...pb.authStore.record,
            ...normalizeUser(pb.authStore.record as User),
          },
        },
      },
      cookies,
      headers,
    };
    return changePb;
  }
  return { pb, cookies, headers };
}
