import { db } from '@/app/db';
import { archiveLead, blockUnBlockLeadWeTarseel, revalidateConversation } from '@/lib/pocket';
import { IExpandedConversationsWithMessages } from '@/lib/types';
import { cleanParseMessageContent } from '@/lib/utils';
import clsx from 'clsx';
import { Archive, ArchiveX, BanIcon, Camera, CircleOffIcon, Ellipsis, File, Loader2, MapPin, Mic, SquarePlay } from 'lucide-react';
import React, { memo } from 'react';
import { FixedSizeList as List } from 'react-window';
import { Badge } from './ui/badge';
import { Button } from './ui/button';
import { DropdownMenu, DropdownMenuContent, DropdownMenuGroup, DropdownMenuItem, DropdownMenuTrigger } from './ui/dropdown-menu';
import { blockLeadFromFacebook } from '@/lib/actions';
import { useToast } from '@/components/ui/use-toast';

const messageTypeIcon = (type: string | null) => {
  switch (type) {
    case 'image':
      return <Camera className="h-4 w-4 mr-1" />;
    case 'audio':
      return <Mic className="h-4 w-4 mr-1" />;
    case 'locaiton':
      return <MapPin className="h-4 w-4 mr-1" />;
    case 'document':
      return <File className="h-4 w-4 mr-1" />;
    case 'video':
      return <SquarePlay className="h-4 w-4 mr-1" />;
    default:
      return '';
  }
};

interface ISlowList {
  liveParentConversations: IExpandedConversationsWithMessages[];
  convo_id: string;
  setConvoId: React.Dispatch<React.SetStateAction<string>>;
  accountId: string;
  toggleConvo: () => void;
  formatDate: (date: Date) => string;
  archiveLeads: boolean;
  phone_id: string;
}

const SlowList = memo(function SlowList({
  liveParentConversations,
  convo_id,
  setConvoId,
  accountId,
  formatDate,
  toggleConvo,
  archiveLeads,
  phone_id,
}: ISlowList) {
  const { toast } = useToast();
  const Row = ({ index, style }: { index: number; style: React.CSSProperties }) => {
    const [loading, setLoading] = React.useState(false);
    const convo = liveParentConversations[index];
    if (!convo) return null;

    const lead = convo as any;
    // const message = convo.last_message ?? convo.last_message_by_user;
    const message = convo.last_message;
    const assigned_agent = (convo as any)?.expand?.assigned_agent?.name;
    // const createdAt = new Date(convo.last_message_by_user_created_at);
    // const minutes = getConversationExpiry(createdAt).timeInMinsToExpire;
    // const remainingTime = minutes * 60;
    // const isWithin24Hour = getConversationExpiry(createdAt).isWithin24Hour;
    // const hours = Math.floor(remainingTime / 3600);
    const unread = convo.unread_count ?? 0;

    let cleanedMessage = cleanParseMessageContent(message);

    const onClick = () => {
      setConvoId(convo.convo_id);
      setTimeout(() => {
        toggleConvo();
      }, 500);

      db.conversations.update(convo.convo_id as any, { unread_count: 0 });
      db.conversations.get(convo.convo_id as any).then((_convo) => {
        if (!_convo) return;
        db.users.toArray().then((users) => {
          const _loggedInUser = users?.[0];
          if (_loggedInUser) {
            revalidateConversation({
              accountId,
              convoId: _convo.convo_id as string,
              userId: _convo.from,
              skipRevalidation: false,
              loggedInUser: _loggedInUser,
            });
          }
        });
      });
    };
    return (
      // <Popover>
      <Button
        variant="ghost"
        style={style} // Style passed from React Window for positioning
        className={`rounded-none items-start flex flex-col space-y-1 border-b border-gray-200 w-full sm:w-[285px] p-2 text-gray-900  ${
          convo_id === convo.convo_id ? 'bg-gray-200' : ''
        }`}
        key={convo.convo_id}
        onClick={onClick}
      >
        <div className="flex items-center justify-between text-left w-full" id={convo.convo_id}>
          <div className="flex flex-col w-full overflow-y-hidden sm:w-40 space-y-1">
            <div className={clsx('truncate', unread > 0 ? 'font-bold' : 'font-normal')}>{lead?.name || lead?.phone_number}</div>
            <div
              className={clsx(
                'text-xs truncate flex',
                convo?.last_message_delivery_status === 'failed' ? 'text-red-500 font-bold' : `text-gray-500 ${unread > 0 ? 'font-bold' : 'font-normal'}`
              )}
            >
              {messageTypeIcon(convo.last_message_type)}
              {convo?.last_message_from === 'agent' ? `You: ${cleanedMessage}` : cleanedMessage}
            </div>
          </div>

          <div className="flex flex-col items-end space-y-1 w-[74px]">
            {/* <PopoverTrigger> */}{' '}
            <div className={clsx(unread > 0 ? 'font-semibold text-green-800' : 'font-normal text-gray-600', 'text-xs')}>
              {convo?.last_message_created ? formatDate(new Date(convo?.last_message_created)) : null}
            </div>
            {/* </PopoverTrigger> */}
            {/* <PopoverContent>{dayjs(convo?.last_message_created).format('MMMM D, YYYY h:mm A')}</PopoverContent> */}
            <div className="flex items-center space-x-1">
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <div className="hover:bg-gray-700 hover:text-white rounded-lg p-1">
                    <Ellipsis className="h-4 w-4" />
                  </div>
                </DropdownMenuTrigger>
                <DropdownMenuContent className="w-56">
                  <DropdownMenuGroup>
                    <DropdownMenuItem
                      className="whitespace-nowrap"
                      onClick={async () => {
                        db.conversations.update(convo.convo_id as any, {
                          chat_archive: !archiveLeads,
                        });
                        await archiveLead(!archiveLeads, convo.convo_id, accountId);
                      }}
                    >
                      {archiveLeads ? <ArchiveX className="mr-2 h-4 w-4" /> : <Archive className="mr-2 h-4 w-4" />}
                      <span>{archiveLeads ? 'Unarchive this conversation' : 'Archive this conversation'}</span>
                    </DropdownMenuItem>

                    <DropdownMenuItem
                      className="whitespace-nowrap"
                      onClick={async () => {
                        setLoading(true);
                        console.log(convo.convo_id);
                        const currentBlockStatus = convo.chat_block;
                        const res = await blockLeadFromFacebook(lead?.phone_number, phone_id, currentBlockStatus);
                        if (res?.message.status == 200) {
                          db.conversations.update(convo.convo_id as any, {
                            chat_block: !convo.chat_block,
                          });
                          db.conversations.update(convo.convo_id as any, {
                            chat_archive: !archiveLeads,
                          });
                          await blockUnBlockLeadWeTarseel(lead.id, !archiveLeads, !convo.chat_block, convo.convo_id, accountId);
                          toast({
                            variant: 'success',
                            description: res.message.description,
                          });
                        } else {
                          toast({
                            variant: 'destructive',
                            description: res?.message.description,
                          });
                        }
                        setLoading(false);
                      }}
                    >
                      {convo.chat_block ? <CircleOffIcon className="mr-2 h-4 w-4" /> : <BanIcon className="mr-2 h-4 w-4" />}
                      <span>{convo.chat_block ? 'Unblock Lead' : 'Block Lead'}</span>
                    </DropdownMenuItem>
                  </DropdownMenuGroup>
                </DropdownMenuContent>
              </DropdownMenu>
              {unread > 0 && (
                <div className="text-normal text-xs rounded-full h-5 w-5 bg-green-800 text-white flex items-center justify-center">{convo.unread_count}</div>
              )}
            </div>
          </div>
        </div>
        <div className="flex space-x-1">
          {assigned_agent && <Badge className="text-black bg-cyan-100 font-normal">{assigned_agent}</Badge>}
          {!assigned_agent && <Badge className="text-black bg-yellow-100 font-normal">Unassigned</Badge>}
          {loading ? <Loader2 className="animate-spin" /> : convo.chat_block && <Badge variant={'destructive'}> Blocked</Badge>}

          <div className="ml-auto">
            {/* {isWithin24Hour && (
              <div
                className={clsx(
                  'px-1 py-1 text-xs rounded-full flex items-center space-x-1 max-w-max ml-auto',
                  hours > 20 ? 'bg-green-200' : hours > 10 ? 'bg-orange-200' : 'bg-red-200'
                )}
              >
                <Clock className="h-4 w-4 font-normal" />
                <span className="text-xs font-normal">{hours < 1 ? `${minutes}m` : `${hours}h`}</span>
              </div>
            )} */}
          </div>
        </div>
      </Button>
      // </Popover> }
    );
  };

  return (
    <div id="contact-list" className="flex w-full flex-col items-start text-sm font-medium sm:w-[200px] md:w-[300px]">
      {liveParentConversations?.length === 0 ? (
        <div className="justify-between rounded-lg mb-2 px-3 py-2 text-gray-900 mx-auto">
          <div className="text-center">No results</div>
        </div>
      ) : (
        <List
          height={690} // Set the height of the scrollable container, subtracting the header height
          itemCount={liveParentConversations.length} // Total number of items
          itemSize={100} // Height of each row
          width="100%" // Full width of the container
        >
          {Row}
        </List>
      )}
    </div>
  );
});

export default SlowList;
