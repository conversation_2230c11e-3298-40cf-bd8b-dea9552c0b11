'use client';

import ImageGalleryDialogServiceMessage from '@/app/[account_id]/flows/create-automation/ImageGalleryDialogServiceMessage';
import { Button } from '@/components/ui/button';
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from '@/components/ui/dropdown-menu';
import { IImageWithUrl } from '@/lib/types';
import { Image, PlusCircle, Video } from 'lucide-react';
import { useState } from 'react';

export function AddMediaButtonComponent({ images }: { images: IImageWithUrl[] }) {
  const [imageDialogOpen, setImageDialogOpen] = useState(false);
  const openImageDialog = () => {
    // Add your image upload logic here
    console.log('Add image clicked');
    setImageDialogOpen(true);
  };

  const setImage = (image: IImageWithUrl) => {
    console.log('Image selected', image);
  };

  const handleAddVideo = () => {
    // Add your video upload logic here
    console.log('Add video clicked');
  };

  return (
    <div className="mb-4">
      {imageDialogOpen ? <ImageGalleryDialogServiceMessage handleSetImage={setImage} images={images} /> : null}
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button variant="outline" size="sm">
            <PlusCircle className="mr-2 h-4 w-4" />
            Add Media
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent>
          <DropdownMenuItem onClick={openImageDialog}>
            <Image className="mr-2 h-4 w-4" />
            Add Image
          </DropdownMenuItem>
          <DropdownMenuItem onClick={handleAddVideo}>
            <Video className="mr-2 h-4 w-4" />
            Add Video
          </DropdownMenuItem>
        </DropdownMenuContent>
      </DropdownMenu>
    </div>
  );
}
