import { ExternalLink } from 'lucide-react';
import Link from 'next/link';
import { memo } from 'react';

export const Document = memo(function Document({ fileName, url, className }: { fileName: string; url: string; className?: string }) {
  return (
    <div className={className}>
      <div className="mt-5">
        <span>{fileName}</span>
      </div>
      <Link href={url} target="_blank">
        <div className="flex items-center text-blue-800 text-sm">
          Preview File <ExternalLink className="ml-2 h-4 w-4" />
        </div>
      </Link>
    </div>
  );
});
