'use client';

import { getMenuList } from '@/lib/utils';
import { Icon } from '@iconify/react';
import clsx from 'clsx';
import Image from 'next/image';
import Link from 'next/link';
import { usePathname } from 'next/navigation';

interface SidebarProps {
  accountId: string;
  userType: string;
  userName: string;
  dashboard_options?: string[] | null;
  logo: string | null;
  locked?: boolean;
}

const Sidebar: React.FC<SidebarProps> = ({ accountId, logo, userType, userName, dashboard_options, locked = false }) => {
  const pathname = usePathname();

  const navItems = getMenuList(accountId, userType, userName, dashboard_options ?? []);

  return (
    <>
      <div className="h-full bg-zinc-950 hidden md:block overflow-y-auto hide-scrollbar w-24">
        <div className="flex flex-col space-y-5 mt-7 m-2">
          <Image src={logo ?? '/assets/WT_Logo.png'} width={50} height={50} alt="logo" className={clsx('mx-auto w-auto', logo ? 'rounded-full' : '')} />
          {navItems.map((item, index) => {
            // Determine if the item should be disabled based on locked state
            const isDisabled = locked && item.name !== 'live-chat';

            return (
              // biome-ignore lint/suspicious/noArrayIndexKey: <explanation>
              <div key={index}>
                {isDisabled ? (
                  // Render a disabled version of the navigation item
                  <div
                    id={item.name}
                    className={`cursor-not-allowed text-gray-600 opacity-50`}
                  >
                    <div
                      className={`flex flex-col items-center text-center space-y-1 rounded-lg p-2`}
                    >
                      <Icon icon={item.icon} />
                      <div className="text-xs">{item.text}</div>
                    </div>
                  </div>
                ) : (
                  // Render the normal clickable navigation item
                  <Link href={item.href}>
                    <div
                      id={item.name}
                      className={`cursor-pointer ${pathname.includes(item.name) ? 'text-white font-bold' : 'text-gray-400'}`}
                      onClick={item.onClick}
                    >
                      <div
                        className={`flex flex-col items-center text-center space-y-1 rounded-lg p-2 hover:bg-zinc-900 ${
                          pathname.includes(item.name) && 'bg-zinc-900'
                        }`}
                      >
                        <Icon icon={`${pathname.includes(item.name) ? item.iconActive : item.icon}`} />
                        <div className="text-xs">{item.text}</div>
                      </div>
                    </div>
                  </Link>
                )}
              </div>
            );
          })}
        </div>
      </div>
    </>
  );
};

export default Sidebar;
