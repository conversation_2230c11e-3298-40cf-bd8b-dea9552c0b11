'use client';
import PocketBase from 'pocketbase';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/components/ui/alert-dialog';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { handleTransform, redirectFunction } from '@/lib/actions';
import { ILead, IPendingContacts } from '@/lib/types';
import { useParams, usePathname } from 'next/navigation';
import Papa from 'papaparse';
import React, { useEffect, useState } from 'react';

import Link from 'next/link';
import { Popover, PopoverContent, PopoverTrigger } from './ui/popover';
import { Check, ChevronsDown } from 'lucide-react';
import { Command, CommandEmpty, CommandGroup, CommandInput, CommandItem, CommandList } from './ui/command';
import { cn } from '@/lib/utils';
import { pb_url } from '@/state/consts';
import { Progress } from './ui/progress';

export function CSVForm({ lists }: { lists: any }) {
  const params = useParams<{ account_id: string }>();
  const [value, setValue] = useState<string>();
  const [backgroundStarted, setBackgroundStarted] = useState(false);
  const [listId, setListId] = useState<string>();
  const [alertOpen, setAlertOpen] = useState(false);
  const [isPending, setIsPending] = useState(false);
  const [fileName, setFilename] = useState({ fileUrl: '', id: '' });
  const [csvHeaders, setCsvHeaders] = useState<string[]>([]);
  const [open, setOpen] = React.useState(false);
  const [selectedMappings, setSelectedMappings] = useState<{
    [key: string]: string;
  }>({});
  const [uploadResult, setUploadResult] = useState<null | {
    pendingItems: number;
    totalLeads: number;
    numberSuccessfull: number;
    numberFailed: number;
    failedLeads: { phone_number: string; reason: string }[];
  }>(null);

  const [currentStep, setCurrentStep] = useState<'upload' | 'mapping' | 'results'>('upload');
  const [errorAlert, setErrorAlert] = useState(false);
  const ref = React.useRef<HTMLFormElement>(null);
  const path = usePathname();

  useEffect(() => {
    let i: any;
    const effect = async () => {
      const pb = new PocketBase(pb_url);
      pb.authStore.loadFromCookie(document.cookie);
      pb.autoCancellation(false);
      if (fileName.id === '') return;
      i = setInterval(async () => {
        let pending;
        try {
          pending = await pb.collection<IPendingContacts>('pending_contacts').getOne(fileName.id);
        } catch (e) {
          console.log(e);
          return;
        }
        console.log(pending);
        let pendingLogs: any = [];
        try {
          pendingLogs = await pb.collection('pending_logs').getFullList({ filter: `pending_id = "${fileName.id}"` });
        } catch (e) {
          console.log(e);
          return;
        }

        pendingLogs = pendingLogs.map((log: any) => log.log);
        if (!pending?.listId) {
          return;
        }
        setListId(pending.listId);
        if (currentStep !== 'results') {
          setCurrentStep('results');
        }
        if (pending.total_items === pendingLogs.length) {
          clearInterval(i);
        }
        setUploadResult({
          totalLeads: pending.total_items,
          pendingItems: pending.total_items - pendingLogs.length,
          failedLeads: (pendingLogs as unknown as any[]).filter((i) => i.status === 'failure').map((l) => ({ ...l.lead, reason: l.error })),
          numberFailed: (pendingLogs as unknown as any[]).filter((i) => i.status === 'failure').length,
          numberSuccessfull: (pendingLogs as unknown as any[]).filter((i) => i.status === 'success').length,
        });
      }, 1000);
    };
    effect();
    return () => {
      clearInterval(i);
    };
  }, [backgroundStarted]);

  useEffect(() => {
    if (currentStep === 'mapping') {
      if (csvHeaders.length > 0 && Object.keys(selectedMappings).length === 0) {
        const initialMappings: { [key: string]: string } = {};
        csvHeaders.forEach((header) => {
          initialMappings[header] = '';
        });
        setSelectedMappings(initialMappings);
      }
    } else if (currentStep === 'upload') {
    }
  }, [currentStep, csvHeaders, fileName, selectedMappings]);

  const handleSubmit = async (event: React.FormEvent<HTMLFormElement>) => {
    event.preventDefault();
    setIsPending(true);

    const formData = new FormData(ref.current as HTMLFormElement);
    const csvFile = formData.get('csv-file') as File;
    const pb = new PocketBase(pb_url);
    pb.authStore.loadFromCookie(document.cookie);
    const newRecord = await pb.collection<IPendingContacts>('pending_contacts').create({ csv_file: csvFile, account_id: params.account_id });
    setFilename({ fileUrl: csvFile.name, id: newRecord.id });
    const text = await csvFile.text();
    const { data: leads, meta } = Papa.parse<ILead>(text, {
      header: true,
      skipEmptyLines: true,
      preview: 1,
    });
    setCsvHeaders(meta.fields as string[]);

    setIsPending(false);
    await redirectFunction(path, true);

    // perform intelligent mapping
    mappingOptions.forEach((option) => {
      const header = meta.fields?.find((field) => field.toLowerCase().includes(option));
      if (header) {
        setSelectedMappings((prevMappings) => ({
          ...prevMappings,
          [header]: option,
        }));
      }
    });

    // if we have more fields than the possible mappings, keep the same name
    if (meta.fields?.length ?? 0 > Object.keys(selectedMappings).length) {
      meta.fields?.forEach((field) => {
        if (!selectedMappings[field]) {
          setSelectedMappings((prevMappings) => ({
            ...prevMappings,
            [field]: field,
          }));
        }
      });
    }
    setCurrentStep('mapping');
  };

  const handleMappingChange = (header: string, mappedValue: string) => {
    setSelectedMappings((prevMappings) => ({
      ...prevMappings,
      [header]: mappedValue,
    }));
  };

  const mappingOptions = ['name', 'phone_number', 'tags', 'status', 'country'];
  return (
    <div>
      <div className="flex justify-center mb-4">
        <h1 className={`mx-4 ${currentStep === 'upload' ? 'font-bold text-blue-600' : ''}`}>Upload</h1>
        <h1 className={`mx-4 ${currentStep === 'mapping' ? 'font-bold text-blue-600' : ''}`}>Mapping</h1>
        <h1 className={`mx-4 ${currentStep === 'results' ? 'font-bold text-blue-600' : ''}`}>Results</h1>
      </div>

      {currentStep === 'upload' && (
        <Card className="w-full max-w-sm">
          <AlertDialog open={errorAlert}>
            <AlertDialogContent>
              <AlertDialogHeader>
                <AlertDialogTitle>This file cannot be uploaded</AlertDialogTitle>
                <AlertDialogDescription>
                  This file exceeds the maximum limit of five headers. Please ensure the file contains no more than five headers before uploading.
                </AlertDialogDescription>
              </AlertDialogHeader>
              <AlertDialogFooter>
                {/* <AlertDialogCancel>Cancel</AlertDialogCancel> */}
                <AlertDialogAction
                  onClick={() => {
                    setIsPending(false);
                    setErrorAlert(false);
                  }}
                >
                  {/* <AlertDialogAction onClick={() => activeToTrue()}> */}
                  OK
                </AlertDialogAction>
              </AlertDialogFooter>
            </AlertDialogContent>
          </AlertDialog>
          <CardHeader>
            <CardTitle className="text-2xl">Upload Contacts</CardTitle>
            <CardDescription>Upload a CSV file with your contacts to add them to your address book.</CardDescription>
            <div className="flex-col space-y-2">
              <div>
                <a href="/sample.csv" download className="text-blue-600 underline">
                  Download CSV Template
                </a>
              </div>
              <div>
                <a href="/country_codes.csv" download className="text-blue-600 underline">
                  Download Country Codes
                </a>
              </div>
            </div>
          </CardHeader>
          <form ref={ref} onSubmit={handleSubmit}>
            <CardContent className="grid gap-4">
              <div id="choose-file" className="grid gap-2">
                <Label htmlFor="csv-file">CSV File</Label>
                <Input accept=".csv" name="csv-file" required type="file" />
              </div>
            </CardContent>
            <CardFooter>
              {/* <Link prefetch={false} href={`?upload=yes`}> */}
              <Button className="w-full" type="submit" disabled={isPending}>
                {isPending ? 'Wait...' : 'Next'}
              </Button>
              {/* </Link> */}
            </CardFooter>
          </form>
        </Card>
      )}

      {currentStep === 'mapping' && (
        <div className="mt-2 p-4">
          {!errorAlert && (
            <>
              <h2 className="text-xl mb-4">Map CSV Headers</h2>
              <Table className="w-full border rounded-lg">
                <TableHeader>
                  <TableRow className="bg-gray-200">
                    <TableHead className="p-2">CSV Header</TableHead>
                    <TableHead className="p-2">Map to</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody id="map-headers">
                  {csvHeaders
                    .filter((csv) => csv !== '')
                    .map((header) => (
                      <TableRow key={header}>
                        <TableCell className="p-2 border-b">{header}</TableCell>
                        <TableCell className="p-2 border-b">
                          <Select value={selectedMappings[header]} onValueChange={(value) => handleMappingChange(header, value)}>
                            <SelectTrigger className="border rounded p-2">
                              <SelectValue placeholder="Select a field" />
                            </SelectTrigger>
                            <SelectContent>
                              {mappingOptions.map((option) => (
                                <SelectItem key={option} value={option}>
                                  {option}
                                </SelectItem>
                              ))}
                              {/* find the selectedMappings[header] in mappingOptions, if it does not exist, create a selectitem */}
                              {!mappingOptions.includes(selectedMappings[header]) && (
                                <SelectItem key={selectedMappings[header]} value={selectedMappings[header]}>
                                  {selectedMappings[header]}
                                </SelectItem>
                              )}
                            </SelectContent>
                          </Select>
                        </TableCell>
                      </TableRow>
                    ))}
                </TableBody>
              </Table>
              <div className="text-md font-semibold mt-2">Select a list(optional)</div>
              <div className="text-sm">Selecting a list will update that list by adding all the leads in it. New list will not be created</div>
              <Popover open={open} onOpenChange={setOpen}>
                <PopoverTrigger asChild>
                  <Button variant="outline" role="combobox" className="w-full justify-between">
                    <span className="break-words overflow-hidden w-full">
                      {value ? lists.find((item: any) => item.name === value)?.name : 'Select a list...'}
                    </span>
                    <ChevronsDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
                  </Button>
                </PopoverTrigger>
                <PopoverContent className="w-[200px] p-0">
                  <Command>
                    <CommandInput maxLength={20} placeholder="Search framework..." />
                    <CommandList>
                      <CommandEmpty>No list found.</CommandEmpty>
                      <CommandGroup>
                        {lists.map((item: any) => (
                          <CommandItem
                            key={item.id}
                            value={item.name}
                            onSelect={(currentValue) => {
                              setListId(item.id);
                              setValue(currentValue === value ? '' : currentValue);
                              setOpen(false);
                            }}
                          >
                            <Check className={cn('mr-2 h-4 w-4', value === item.name ? 'opacity-100' : 'opacity-0')} />
                            {item.name}
                          </CommandItem>
                        ))}
                      </CommandGroup>
                    </CommandList>
                  </Command>
                </PopoverContent>
              </Popover>
              <div className="mt-4 justify-center flex items-center gap-4">
                <Button
                  id="back"
                  onClick={async () => {
                    await redirectFunction(path, false);
                    setCurrentStep('upload');
                  }}
                  className="w-32 bg-black hover:bg-slate-700 text-white font-semibold py-2 rounded-md"
                >
                  Back
                </Button>
                <Button
                  id="upload-file"
                  className="w-32 bg-black text-white hover:bg-gray-700"
                  onClick={async () => {
                    setBackgroundStarted(true);
                    await handleTransform(fileName.id, fileName.fileUrl, selectedMappings, params.account_id, listId);
                  }}
                  disabled={backgroundStarted}
                  // disabled={isPending}
                >
                  {isPending ? 'Uploading...' : 'Upload'}
                </Button>
              </div>
            </>
          )}
        </div>
      )}

      {currentStep === 'results' && uploadResult && (
        <Card className="mt-8 p-4">
          <CardHeader>
            <CardTitle className="text-2xl">Upload Results</CardTitle>
          </CardHeader>
          <CardContent>
            <p>Number of successful uploads: {uploadResult.numberSuccessfull}</p>
            <p>Number of failed uploads: {uploadResult.numberFailed}</p>
            {uploadResult.failedLeads.length > 0 && (
              <div>
                <h3 className="text-xl mt-4 text-red-600 font-medium">Failed Uploads:</h3>
                <div className="flex-col max-h-28 max-w-96 px-1 overflow-y-scroll">
                  {uploadResult.failedLeads.map((failedLead, index) => (
                    <li key={index}>
                      {failedLead.phone_number}: {failedLead.reason}
                    </li>
                  ))}
                </div>
              </div>
            )}
            {uploadResult.pendingItems > 0 ? (
              <div>
                <Progress value={((uploadResult.totalLeads - uploadResult.pendingItems) / uploadResult.totalLeads) * 100} />
              </div>
            ) : (
              <div>List successfully uploaded</div>
            )}
          </CardContent>
          {backgroundStarted && uploadResult.pendingItems > 0 && (
            <div>
              <div>Contacts are being uploaded, it is safe to close this window</div>
            </div>
          )}
          <div className="mt-1 flex justify-center items-center gap-4">
            <Link href={`/${params.account_id}/upload-contact`}>
              <Button>Upload Another File</Button>
            </Link>
            <Link href={listId ? `/${params.account_id}/list/view-list?listId=${listId}` : `/${params.account_id}/lead-management`}>
              <Button className="w-32 bg-black text-white hover:bg-gray-700">Done</Button>
            </Link>
          </div>
        </Card>
      )}

      <AlertDialog open={alertOpen} onOpenChange={setAlertOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>List created!!</AlertDialogTitle>
            <AlertDialogDescription>The contacts have been uploaded successfully</AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogAction onClick={() => setAlertOpen(false)}>OK</AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  );
}
