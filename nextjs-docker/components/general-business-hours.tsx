'use client';

import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';
import { saveHours } from '@/lib/pocket';
import { useParams } from 'next/navigation';
import { useToast } from './ui/use-toast';

type BusinessHours = {
  isOpen: boolean;
  openTime: string;
  closeTime: string;
};

export function GeneralBusinessHoursComponent() {
  const { account_id } = useParams();
  const { toast } = useToast();
  const [hours, setHours] = useState<BusinessHours>({
    isOpen: true,
    openTime: '09:00',
    closeTime: '17:00',
  });

  const handleSwitchChange = (checked: boolean) => {
    setHours((prev) => ({ ...prev, isOpen: checked }));
  };

  const handleTimeChange = (field: 'openTime' | 'closeTime', value: string) => {
    setHours((prev) => ({ ...prev, [field]: value }));
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    console.log('Submitted hours:', hours);
    saveHours(account_id as string, hours.openTime, hours.closeTime);
    toast({
      title: 'Business hours updated',
      description: 'Your new general business hours have been saved successfully.',
    });
  };

  return (
    <Card className="w-full max-w-md mx-auto">
      <CardHeader>
        <CardTitle>Set General Business Hours</CardTitle>
        <CardDescription>Define your working hours for all days of the week.</CardDescription>
      </CardHeader>
      <CardContent>
        <form onSubmit={handleSubmit} className="space-y-4">
          <div className="flex items-center justify-between">
            <Label htmlFor="is-open" className="text-lg font-semibold">
              Business is open
            </Label>
            <Switch id="is-open" disabled checked={hours.isOpen} onCheckedChange={handleSwitchChange} />
          </div>
          {hours.isOpen && (
            <div className="space-y-4">
              <div>
                <Label htmlFor="open-time">Open Time</Label>
                <Input type="time" id="open-time" value={hours.openTime} onChange={(e) => handleTimeChange('openTime', e.target.value)} />
              </div>
              <div>
                <Label htmlFor="close-time">Close Time</Label>
                <Input type="time" id="close-time" value={hours.closeTime} onChange={(e) => handleTimeChange('closeTime', e.target.value)} />
              </div>
            </div>
          )}
          <Button type="submit" className="w-full">
            Save Business Hours
          </Button>
        </form>
      </CardContent>
      <CardFooter className="text-sm text-slate-500 dark:text-slate-400">Note: These hours will apply to all days of the week.</CardFooter>
    </Card>
  );
}
