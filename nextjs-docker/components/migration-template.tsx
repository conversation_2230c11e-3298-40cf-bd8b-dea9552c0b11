'use client';

import { useState } from 'react';
import { Button } from '@/components/ui/button';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/components/ui/alert-dialog';
import { Account, ITemplateDatabase } from '@/lib/types';
import { Card, CardContent, CardHeader, CardTitle } from './ui/card';
import { ScrollArea } from './ui/scroll-area';
import { Checkbox } from './ui/checkbox';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from './ui/select';
import { useToast } from '@/components/ui/use-toast';
import { addTemplates } from '@/lib/pocket';
import { useParams, useRouter, useSearchParams } from 'next/navigation';
import { Badge } from './ui/badge';
import { Toaster } from './ui/toaster';

export function MigrationTemplate({
  templates,
  accountList,
  templatesOfDestination,
}: {
  templates: ITemplateDatabase[];
  accountList: Account[];
  templatesOfDestination: ITemplateDatabase[];
}) {
  const { toast } = useToast();
  const router = useRouter();
  const { account_id } = useParams();
  const searchParams = useSearchParams();
  const selected_account = searchParams.get('selected_account');
  const [selectedTemplates, setSelectedTemplates] = useState<ITemplateDatabase[]>(
    templates.filter((template) => !templatesOfDestination.map((t) => t.template_name).includes(template.template_name))
  );

  const handleCheckboxChange = (template: ITemplateDatabase) => {
    setSelectedTemplates((prev) => (prev.includes(template) ? prev.filter((t) => t !== template) : [...prev, template]));
  };
  const [selectedAccount, setSelectedAccount] = useState<Account | undefined>(accountList.find((account) => account.id === selected_account));
  const [openTemplate, setOpenTemplate] = useState(false);
  const [openAccount, setOpenAccount] = useState(false);
  const [showConfirmDialog, setShowConfirmDialog] = useState(false);

  const handleMigrate = () => {
    if (selectedTemplates && selectedAccount) {
      setShowConfirmDialog(true);
    }
  };

  const startMigration = async () => {
    // Implement the actual migration process here
    if (selectedTemplates.length === 0 || !selectedAccount) return;
    toast({
      variant: 'default',
      description: 'Migrating templates please wait',
    });
    await addTemplates(
      // @ts-ignore
      selectedTemplates.map((template) => ({
        ...template,
        account: selectedAccount.id,
      })),
      selectedAccount.id
    );
    toast({
      variant: 'success',
      description: 'Templates have been migrated successfully',
    });
    setSelectedTemplates([]);
    // console.log(`Starting migration of ${selectedTemplate.name} to ${selectedAccount.name}`);
    setShowConfirmDialog(false);
  };

  return (
    <div className="w-full max-w-md mx-auto space-y-6">
      <h1 className="text-2xl font-bold text-center mb-6">Migrate Template</h1>

      <TemplateList
        selectedTemplates={selectedTemplates}
        handleCheckboxChange={handleCheckboxChange}
        templatesOfDestination={templatesOfDestination}
        templates={templates}
      />
      <p>Selected Templates: {selectedTemplates.length}</p>
      <p>
        Migrating from: <Badge variant="secondary">{accountList.find((account) => account.id === account_id)?.name}</Badge>
      </p>
      <p>Migrating to:</p>
      <Select
        defaultValue={selected_account ?? undefined}
        name="account"
        onValueChange={(value: string) => {
          setSelectedAccount(accountList.find((account) => account.id === value));
          router.push(`/${account_id}/templates/migrate-templates?selected_account=${value}`);
        }}
      >
        <SelectTrigger name="account">
          <SelectValue placeholder="Select Account" />
        </SelectTrigger>
        <SelectContent position="popper">
          {accountList
            .filter((account) => account.id !== account_id)
            .map((account) => (
              <SelectItem value={account.id}>{account.name}</SelectItem>
            ))}
        </SelectContent>
      </Select>
      <Button className="w-full" onClick={handleMigrate} disabled={selectedTemplates.length === 0 || !selectedAccount}>
        Start Migration
      </Button>

      <AlertDialog open={showConfirmDialog} onOpenChange={setShowConfirmDialog}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Confirm Migration</AlertDialogTitle>
            <AlertDialogDescription>
              Are you sure you want to migrate {selectedTemplates?.length} template/s to the "{selectedAccount?.name}" account?
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction onClick={startMigration}>Confirm</AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
      <Toaster />
    </div>
  );
}

function TemplateList({
  templates,
  selectedTemplates,
  templatesOfDestination,
  handleCheckboxChange,
}: {
  templates: ITemplateDatabase[];
  templatesOfDestination: ITemplateDatabase[];
  selectedTemplates: ITemplateDatabase[];
  handleCheckboxChange: (templateId: ITemplateDatabase) => void;
}) {
  return (
    <Card className="w-full max-w-3xl mx-auto">
      <CardHeader>
        <CardTitle>Approved Templates</CardTitle>
      </CardHeader>
      <CardContent>
        <ScrollArea className="h-[400px] pr-4">
          {templates.map((template) => (
            <div key={template.id} className="flex items-center space-x-4 py-2 border-b last:border-b-0">
              <Checkbox
                disabled={templatesOfDestination.map((t) => t.template_name).includes(template.template_name)}
                id={`template-${template.id}`}
                checked={selectedTemplates.includes(template)}
                onCheckedChange={() => handleCheckboxChange(template)}
              />
              <div className="flex-1">
                <label
                  htmlFor={`template-${template.id}`}
                  className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                >
                  {template.template_name}
                </label>
                <p className="text-sm text-muted-foreground">
                  {template.type} | {template.category} | {template.language}
                </p>
                {templatesOfDestination.map((template) => template.template_name).includes(template.template_name) && (
                  <Badge variant="pending">Already migrated</Badge>
                )}
              </div>
              <div className="text-sm text-muted-foreground">ID: {template.template_id}</div>
            </div>
          ))}
        </ScrollArea>
      </CardContent>
    </Card>
  );
}
