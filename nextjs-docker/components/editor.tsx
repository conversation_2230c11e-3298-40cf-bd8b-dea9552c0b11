import EmojiPicker, { Categories } from 'emoji-picker-react';
import Placeholder from '@tiptap/extension-placeholder';
import CharacterCount from '@tiptap/extension-character-count';
import Italic from '@tiptap/extension-italic';
import Bold from '@tiptap/extension-bold';
import { Transaction } from '@tiptap/pm/state';
import { markInputRule, markPasteRule, mergeAttributes, Node, RawCommands } from '@tiptap/core';
import { type Editor, EditorContent, Extension, JSONContent } from '@tiptap/react';
import StarterKit from '@tiptap/starter-kit';
import { RefObject, useEffect, useState } from 'react';
import { BoldIcon, ItalicIcon, List, ListOrdered, Smile, Strikethrough, TextQuote } from 'lucide-react';
import { Toggle } from '@/components/ui/toggle';
import { getLimit } from '@/lib/utils';
import { Button } from './ui/button';
import { IBody } from '@/app/[account_id]/templates/manage/[template_operation]/types';

export const Emoji = Node.create({
  name: 'emoji',

  inline: true,
  group: 'inline',
  selectable: false,
  atom: true,

  addAttributes() {
    return {
      emoji: {
        default: '😊',
      },
    };
  },

  parseHTML() {
    return [
      {
        tag: 'span[data-emoji]',
      },
    ];
  },

  renderHTML({ HTMLAttributes }) {
    return ['span', mergeAttributes(HTMLAttributes, { 'data-emoji': '' }), HTMLAttributes.emoji];
  },

  addCommands() {
    return {
      insertEmoji:
        (emoji: string) =>
        ({ commands }: { commands: any }) => {
          return commands.insertContent({
            type: this.name,
            attrs: { emoji },
          });
        },
    } as Partial<RawCommands>;
  },
});

export const KeyboardShortcutHandler = Extension.create({
  name: 'keyboardHandler',
});

export const CustomerCharacterCount = CharacterCount.extend({
  onBeforeCreate() {
    this.storage.characters = (options) => {
      const node = options?.node || this.editor.state.doc;
      const mode = options?.mode || this.options.mode;

      if (mode === 'textSize') {
        let content = parseContent(node.content.toJSON());
        const text = content;

        return this.options.textCounter(text);
      }

      return node.nodeSize;
    };

    this.storage.words = (options) => {
      const node = options?.node || this.editor.state.doc;
      const text = node.textBetween(0, node.content.size, ' ', ' ');

      return this.options.wordCounter(text);
    };
  },
});

/**
 * Matches an italic to a _italic_ on input.
 */
export const underscoreInputRegex = /_(\S[\s\S]*?\S|[^\s])_/;

/**
 * Matches an italic to a _italic_ on paste.
 */
export const underscorePasteRegex = /(?:^|\s)(_(?!\s+_)((?:[^_]+))_(?!\s+_))/g;

/**
 * Matches bold text via `**` as input.
 */
export const boldStarInputRegex = /\*(\S[^\*]*\S)\*/g;

/**
 * Matches bold text via `**` while pasting.
 */
export const boldStarPasteRegex = /\*(\S[^\*]*\S)\*/g;

export const CustomItalic = Italic.extend({
  addInputRules() {
    return [
      markInputRule({
        find: underscoreInputRegex,
        type: this.type,
      }),
    ];
  },

  addPasteRules() {
    return [
      markPasteRule({
        find: underscorePasteRegex,
        type: this.type,
      }),
    ];
  },
});

export const CustomBold = Bold.extend({
  addInputRules() {
    return [
      markInputRule({
        find: boldStarInputRegex,
        type: this.type,
      }),
    ];
  },

  addPasteRules() {
    return [
      markPasteRule({
        find: boldStarPasteRegex,
        type: this.type,
      }),
    ];
  },
});

export const parseContent = (content: JSONContent[] | undefined, type: string | undefined = undefined): string => {
  return (
    content
      ?.map((node: JSONContent, index: number, arr: JSONContent[]) => {
        if (node.type === 'text') {
          return applyMarks(node); // assuming applyMarks returns a string
        } else if (node.type === 'emoji') {
          return node?.attrs?.emoji;
        } else if (node.type === 'paragraph') {
          if (type === 'listItem' || type == 'bulletList' || type === 'orderedList') {
            return parseContent(node.content);
          } else {
            // Do not add '\n' if this is the last paragraph
            return parseContent(node.content) + (index === arr.length - 1 ? '' : '\n');
          }
        } else if (node.type === 'blockquote') {
          return node.content
            ?.map((item: JSONContent) => {
              return `> ${parseContent(item.content)} \n`; // no need for join
            })
            .join('');
        } else if (node.type === 'orderedList') {
          return node.content
            ?.map((item: JSONContent, index: number) => {
              return `${index + 1}- ${parseContent(item.content, 'listItem')}`; // no need for join
            })
            .join('\n');
        } else if (node.type === 'bulletList') {
          return (
            node.content
              ?.map((item: JSONContent) => {
                return `- ${parseContent(item.content)}`; // no need for join
              })
              .join('\n') + '\n'
          );
        }
        return ''; // default case if no type matches
      })
      .join('') ?? ''
  ); // ensure it returns an empty string if content is undefined
};
export const applyMarks = (node: JSONContent) => {
  let text = node.text;
  const marks = node.marks || [];
  marks.forEach((mark: any) => {
    if (mark.type === 'bold') {
      text = `*${text}*` as string;
    }
    if (mark.type === 'italic') {
      text = `_${text}_` as string;
    }
    if (mark.type === 'strike') {
      text = `~${text}~` as string;
    }
  });
  return text;
};

export const hookParams = ({
  placeholder,
  setInputValue,
  enterHandler,
  submitOnEnter = true,
  type = 'basic-template',
  setSubmitLoading = undefined,
}: {
  setInputValue: (value: string) => void;
  placeholder: string;
  enterHandler?: RefObject<HTMLFormElement>;
  submitOnEnter?: boolean;
  type: 'basic-template' | 'carousel-template' | 'input-box' | 'messageBody';
  setSubmitLoading?: React.Dispatch<React.SetStateAction<boolean>>;
}) => {
  return {
    shouldRerenderOnTransaction: true,
    immediatelyRender: false,
    editorProps: {
      attributes: {
        class: 'w-full text-sm focus-visible:outline-none focus:ring-0 focus:outline-none focus:border-[#075E54] max-h-[200px] overflow-y-auto',
      },
    },
    extensions: [
      CustomerCharacterCount.configure({
        limit: getLimit(type),
      }),
      CustomItalic,
      CustomBold,
      StarterKit.configure({
        orderedList: {
          HTMLAttributes: {
            class: 'list-decimal pl-4',
          },
        },
        bulletList: {
          HTMLAttributes: {
            class: 'list-disc pl-4',
          },
        },
        blockquote: {
          HTMLAttributes: {
            class: 'p-4 my-4 border-l-4 border-gray-300 bg-gray-50 dark:border-gray-500 dark:bg-gray-800',
          },
        },
        italic: false,
        bold: false,
      }),
      Emoji,
      // HardBreak.extend({
      //   addKeyboardShortcuts() {
      //     return {
      //       Enter: () => this.editor.commands.setHardBreak(),
      //     };
      //   },
      // }),
      KeyboardShortcutHandler.extend({
        addKeyboardShortcuts() {
          return {
            Enter: () => {
              if (!submitOnEnter) {
                /**
                 * currently we do not have an option to show a soft line break in the posts, so we overwrite
                 * the behavior from tiptap with the default behavior on pressing enter
                 */
                return this.editor.commands.first(({ commands }) => [
                  () => commands.newlineInCode(),
                  () => commands.splitListItem('listItem'), // This line added
                  () => commands.createParagraphNear(),
                  () => commands.liftEmptyBlock(),
                  () => commands.splitBlock(),
                ]);
              }
              const isCodeBlockActive = this.editor.isActive('codeBlock');

              if (isCodeBlockActive) {
                return false;
              }

              setSubmitLoading && setSubmitLoading(true);
              enterHandler?.current && enterHandler?.current.requestSubmit();
              setInputValue('');
              return this.editor.commands.clearContent();
            },

            'Shift-Enter': () => {
              /**
               * currently we do not have an option to show a soft line break in the posts, so we overwrite
               * the behavior from tiptap with the default behavior on pressing enter
               */
              return this.editor.commands.first(({ commands }) => [
                () => commands.newlineInCode(),
                () => commands.splitListItem('listItem'), // This line added
                () => commands.createParagraphNear(),
                () => commands.liftEmptyBlock(),
                () => commands.splitBlock(),
              ]);
            },
          };
        },
      }),
      Placeholder.configure({
        placeholder,
        showOnlyWhenEditable: false,
      }),
    ],
    // content: value, // Set the initial content with the provided value
    onUpdate: ({ editor, transaction }: { editor: Editor; transaction: Transaction }) => {
      let content = parseContent(editor.getJSON().content);
      setInputValue(content);
    },
  };
};
export const MyEditor = ({
  editor,
  contentEditable,
  placeholder,
}: {
  editor: Editor | null;
  setInputValue: (value: string) => void;
  contentEditable: boolean;
  placeholder: string;
  enterHandler?: RefObject<HTMLFormElement>;
}) => {
  // Insert emoji function

  useEffect(() => {
    if (editor !== null && placeholder !== '') {
      editor.setEditable(contentEditable);
      editor.extensionManager.extensions.filter((extension) => extension.name === 'placeholder')[0].options['placeholder'] = placeholder;
      editor.view.dispatch(editor.state.tr);
    }
  }, [editor, placeholder, contentEditable]);

  return (
    <>
      <EditorContent editor={editor} className="w-full flex flex-1" />
    </>
  );
};

export const RichTextEditorToolbar = ({
  editorRef,
  type = 'basic-template',
  body,
  setBody,
}: {
  editorRef: HTMLDivElement;
  type?: 'basic-template' | 'carousel-template' | 'input-box' | 'messageBody';
  body: IBody;
  setBody: (body: Partial<IBody>) => void;
}) => {
  const [emojiPickerOpen, setEmojiPickerOpen] = useState(false);
  const addEmoji = (emoji: string) => {
    if (editorRef === null) return;
    // insertCharacter('*');
    setEmojiPickerOpen(false);
  };

  const wrapSelection = (wrapper: string) => {
    const selection = window.getSelection();
    if (!selection || selection.rangeCount === 0) return;

    const range = selection.getRangeAt(0);
    const selectedText = range.toString();
    if (!selectedText) return;

    // Create a document fragment to store modified content
    const fragment = document.createDocumentFragment();
    let isTextWrapped = false;

    // Iterate over the range's cloned content to preserve <br> and wrap only text
    range.cloneContents().childNodes.forEach((node) => {
      if (node.nodeType === 3) {
        // 3 is TEXT_NODE
        // Wrap only text nodes with the wrapper (e.g., <b> or **)
        const wrappedText = document.createTextNode(`${wrapper}${node.textContent}${wrapper}`);
        fragment.appendChild(wrappedText);
        isTextWrapped = true;
      } else {
        // Append non-text elements (like <br>) unchanged
        fragment.appendChild(node.cloneNode(true));
      }
    });

    if (!isTextWrapped) return; // If no text is wrapped, exit

    range.deleteContents(); // Remove selected content
    range.insertNode(fragment); // Insert the modified content with <b>

    // Ensure the cursor moves correctly after the last inserted node
    const lastNode = fragment.lastChild;
    if (lastNode && lastNode.nodeType === 1) {
      // If lastChild is a valid node (element node)
      const newRange = document.createRange();
      newRange.setStartAfter(lastNode);
      newRange.collapse(true);
      selection.removeAllRanges();
      selection.addRange(newRange);
    } else {
      // If the last node is invalid, set the cursor to the end of the editor
      const editor = editorRef;
      if (editor) {
        const range = document.createRange();
        range.selectNodeContents(editor);
        range.collapse(false);
        selection.removeAllRanges();
        selection.addRange(range);
      }
    }

    // Update state with the new content (preserving <br>)
    setBody({ ...body, text: editorRef.innerHTML });
  };

  const applyBold = () => {
    wrapSelection('*'); // Wrap selection with asterisks for bold
  };
  const applyItalic = () => {
    wrapSelection('_'); // Wrap selection with underscores for italic
  };

  const applyStrike = () => {
    wrapSelection('~'); // Wrap selection with tildes for strikethrough
  };

  return (
    <div className="border border-input rounded-md flex flex-row items-center gap-1">
      {emojiPickerOpen && (
        <EmojiPicker
          className="z-30"
          onEmojiClick={(EmojiClickData, event) => addEmoji(EmojiClickData.emoji)}
          style={type == 'input-box' ? { position: 'absolute', bottom: '64px' } : { position: 'absolute', top: '36px' }}
          allowExpandReactions={false}
          autoFocusSearch={true}
          categories={[
            {
              category: Categories.SMILEYS_PEOPLE,
              name: 'Smilies',
            },
          ]}
          previewConfig={{
            showPreview: false,
          }}
        />
      )}
      <Toggle size="sm" onPressedChange={() => setEmojiPickerOpen(!emojiPickerOpen)}>
        <Smile className="h-4 w-4" />
      </Toggle>
      <Button variant={'ghost'} size="sm" onClick={applyBold}>
        <BoldIcon className="h-4 w-4" />
      </Button>
      <Button variant={'ghost'} size="sm" onClick={applyItalic}>
        <ItalicIcon className="h-4 w-4" />
      </Button>
      <Button variant={'ghost'} size="sm" onClick={applyStrike}>
        <Strikethrough className="h-4 w-4" />
      </Button>
    </div>
  );
};
