'use client';

import { Switch } from '@/components/ui/switch';
import { Label } from '@/components/ui/label';
import { CardTitle } from '@/components/ui/card';
import { Badge } from './ui/badge';
import { IAutomation } from '@/lib/types';
import Link from 'next/link';
import { useParams } from 'next/navigation';
import { toggleAutomation } from '@/lib/pocket';

type Automation = {
  id: string;
  name: string;
  description: string;
  isActive: boolean;
  template?: string;
};

export function ActiveAutomationsComponent({ automations }: { automations: IAutomation[] }) {
  const { account_id } = useParams();
  const toggleAutomationLocal = async (id: string, status: boolean) => {
    await toggleAutomation(id, account_id as string, status);
  };

  return (
    <div className="mx-auto py-8">
      {/* <Card> */}
      {/* <CardHeader> */}
      <div className="mb-6">
        <CardTitle>Currently Active Automations</CardTitle>
        <p>These automations are now live and are responding to users, manage them or disable them</p>
      </div>
      {/* </CardHeader> */}
      {/* <CardContent> */}
      <ul className="space-y-4">
        {automations.map((automation) => {
          const params = new URLSearchParams();
          params.set('automation_id', automation.id);
          return (
            <li key={automation.id} className="flex items-center justify-between p-4 bg-slate-100 rounded-lg dark:bg-slate-800">
              <div className="flex items-center space-x-4">
                <div className={`w-3 h-3 rounded-full ${automation.isActive ? 'bg-green-500' : 'bg-gray-300'} ${automation.isActive ? 'animate-pulse' : ''}`} />
                <div>
                  <Link href={`/${account_id}/flows/create-automation/${automation.id}`}>
                    <h3 className="text-lg font-semibold">{automation.name}</h3>
                  </Link>
                  <p className="text-sm text-slate-500 dark:text-slate-400">{automation.description}</p>
                  <Badge variant="secondary" className="mt-1">
                    {automation.template || 'General'}
                  </Badge>
                  {!automation.template ? <small>This automation will run for any incoming messages</small> : null}
                </div>
              </div>
              <div className="flex items-center space-x-2">
                <Switch
                  id={`automation-switch-${automation.id}`}
                  checked={automation.isActive}
                  onCheckedChange={() => toggleAutomationLocal(automation.id, !automation.isActive)}
                />
                <Label htmlFor={`automation-switch-${automation.id}`}>{automation.isActive ? 'Active' : 'Inactive'}</Label>
              </div>
            </li>
          );
        })}
      </ul>
    </div>
  );
}
