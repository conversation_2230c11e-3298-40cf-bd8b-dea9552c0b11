/**
 * This code was generated by v0 by Vercel.
 * @see https://v0.dev/t/YM8CwHHa3IE
 */
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import type { AgentWithConvos, IExpandedMessage, Team, User } from '@/lib/types';
import { pb_url } from '@/state/consts';
import cn from 'classnames';
import clsx from 'clsx';
import { useLiveQuery } from 'dexie-react-hooks';
import debounce from 'lodash/debounce';
import PocketBase from 'pocketbase';
import { Dispatch, type ReactElement, SetStateAction, useCallback, useDeferredValue, useEffect, useState } from 'react';
// import { revalidateFullPath } from "@/lib/actions";
import ConversationTopBar from '@/app/[account_id]/live-chat/components/ConversationTopBar';
import RightBar from '@/app/[account_id]/live-chat/components/RightBar';
import { db } from '@/app/db';
import { revalidateConversation } from '@/lib/pocket';
import { cleanParseMessageContent } from '@/lib/utils';
import { AvatarImage } from '@radix-ui/react-avatar';
import classNames from 'classnames';
import dayjs from 'dayjs';
import isToday from 'dayjs/plugin/isToday';
import isYesterday from 'dayjs/plugin/isYesterday';
import relativeTime from 'dayjs/plugin/relativeTime';
import weekday from 'dayjs/plugin/weekday';
import { Archive, ChevronLeft, CircleX, Loader2, PhoneCall, TagIcon, UserIcon, UsersIcon } from 'lucide-react';
import { useSearchParams } from 'next/navigation';
import SlowList from './SlowList';
import { Avatar, AvatarFallback } from './ui/avatar';
import { Badge } from './ui/badge';
import { Option } from './ui/multiple-selector';

const bouncedRevalidate = debounce(revalidateConversation, 3000);

export function ChatLayout({
  children,
  account_id,
  convo_id,
  setConvoId,
  allTags,
  agentsOfAccount,
  refetchConversations,
  currentUser,
  teams,
  loading,
  phone_id,
}: {
  children: ReactElement;
  account_id: string;
  convo_id: string;
  setConvoId: React.Dispatch<React.SetStateAction<string>>;
  allTags: Option[];
  refetchConversations: () => Promise<void>;
  agentsOfAccount: AgentWithConvos[];
  currentUser: User;
  teams: Team[];
  loading: boolean;
  phone_id: string;
}) {
  dayjs.extend(relativeTime);
  dayjs.extend(weekday);
  dayjs.extend(isToday);
  dayjs.extend(isYesterday);

  const accountId = account_id;
  const formatDate = useCallback(
    (date: Date) => {
      if (!date) return '';
      const now = dayjs();
      const inputDate = dayjs(date);

      if (inputDate.isToday()) {
        return inputDate.format('hh:mm a');
      } else if (inputDate.isYesterday()) {
        return 'Yesterday';
      } else if (now.diff(inputDate, 'day') <= 5) {
        return inputDate.format('dddd');
      } else {
        return inputDate.format('DD/MM/YYYY');
      }
    },
    [dayjs]
  );
  const [drawerState, toggleDrawer] = useState(true);
  const [searchQuery, setSearchQuery] = useState('');
  const deferredQuery = useDeferredValue(searchQuery);
  const [archiveLeads, setArchiveLeads] = useState(false);
  const [archiveLeadsExists, setArchiveLeadsExists] = useState(false);
  const [filterUnread, setFilterUnread] = useState(true);

  const [showConvoState, setShowConvoState] = useState(false);
  const [agentId, setAgentId] = useState<string | null>(null);
  const [teamId, setTeamId] = useState<string | null>(null);
  // agent or team filters
  const searchParams = useSearchParams();

  useEffect(() => {
    if (agentId || teamId) {
      setFilterUnread(false);
    }
  }, [searchParams, agentId, teamId]);
  const toggleUnread = () => {
    setFilterUnread(!filterUnread);
  };
  // const pathname = usePathname();

  const conversations = useLiveQuery(
    () =>
      convo_id
        ? db.conversations
            .where({
              convo_id: convo_id,
            })
            .toArray()
        : [],
    [convo_id]
  );
  const conversation = conversations?.[0] as any;
  const tags = conversation?.tags ? JSON.parse(conversation.tags) : [];
  const convoFrom = conversation as any;
  const convoFromId = convoFrom?.id;
  const allConversations = useLiveQuery(() => {
    return db.conversations.toArray();
  }, []);
  const liveParentConversations = useLiveQuery(
    () =>
      db.conversations
        .filter((convo) => {
          if (agentId) {
            return convo.assigned_agent === agentId;
          }
          return true;
        })
        .filter((convo) => {
          if (teamId) {
            if (teamId.toLocaleLowerCase() === 'you') {
              return convo.assigned_agent === currentUser.id;
            }
            if (teamId.toLocaleLowerCase() === 'unassigned') {
              return !convo.assigned_agent;
            }
            return convo?.team_id === teamId;
          }
          return true;
        })
        .filter((convo) => {
          if (convo.chat_archive) {
            setArchiveLeadsExists(true);
          }
          if (archiveLeads) {
            return convo.chat_archive;
          }
          return true;
        })
        .filter((convo) => {
          if (filterUnread) {
            return convo.unread_count > 0 || convo.convo_id === convo_id;
          }
          return true;
        })
        .filter((convo) => {
          const lead = convo as any;
          // const leadNameOrPhone = lead?.name || lead?.phone_number;
          const nameCondition = lead?.name?.toLowerCase().includes(deferredQuery.toLowerCase());
          const phoneCondition = lead?.phone_number?.toLowerCase().includes(deferredQuery.toLowerCase());
          return (nameCondition || phoneCondition) && convo.chat_archive == archiveLeads;
        })
        .reverse()
        .sortBy('last_message_created'),
    [deferredQuery, filterUnread, convo_id, agentId, teamId, archiveLeads],
    []
  );

  const allCount = allConversations?.filter((convo) => !convo.chat_archive).length;
  const unreadCount = liveParentConversations.filter((convo) => convo.unread_count > 0).length;
  const archivedCount = liveParentConversations.filter((convo) => convo.chat_archive).length;
  useEffect(() => {
    const pb = new PocketBase(pb_url);

    if (convoFromId) {
      const effect = async () => {
        // subscribe to messages in real time
        pb.collection<IExpandedMessage>('messages').subscribe(
          '*',
          async (e) => {
            if (e.action === 'create' || e.action === 'update') {
              const message = e.record;
              // On recieving a message, add to message table.
              await boundAddMessageInDb(message, pb);

              // messageDaddy is the ID of the user who sent the message, while convoFromId is the ID of the user whose conversation is being viewed
              const messageDaddy = message.expand?.user?.id;
              // If a user exists, update the conversation
              if (messageDaddy) {
                const otherConvo = await db.conversations.where({ from: messageDaddy }).toArray();
                if (otherConvo.length === 0) {
                  // fetch the conversation and append it to the database
                  refetchConversations();
                }
                // If the other conversation exists, update it.
                if (otherConvo.length > 0) {
                  bouncedRevalidate({
                    accountId,
                    convoId: otherConvo[0].convo_id as string,
                    userId: messageDaddy,
                    skipRevalidation: false,
                    loggedInUser: currentUser,
                    unread_count:
                      messageDaddy !== convoFromId && e.action === 'create' && e.record.from === 'user'
                        ? otherConvo[0].unread_count + 1
                        : otherConvo[0].unread_count,
                  });

                  // Make sure the message.created field is converted to proper JS date
                  const updatedConvo = {
                    ...otherConvo[0],
                    expand: {
                      ...otherConvo[0].expand,
                    },
                  };

                  // If the message is not from the user whose conversation is being viewed, increment the unread count
                  if (messageDaddy !== convoFromId && e.action === 'create') {
                    if (e.record.from === 'user') {
                      updatedConvo.unread_count = otherConvo[0].unread_count + 1;
                    }
                  }
                  if (e.action === 'create') {
                    updatedConvo.last_message = cleanParseMessageContent(e.record.message);
                  }

                  await db.conversations.put(updatedConvo);
                }
              }
            }
            // this filter says subscribe to chat of my current account
          },
          { filter: `account = "${accountId}"`, expand: 'user, interactive_message, replied_to, created_by' }
        );
      };
      effect();
    }
    return () => {
      pb.collection('messages').unsubscribe();
    };
  }, [convoFromId]);
  // biome-ignore lint/correctness/useExhaustiveDependencies: <explanation>

  const toggleConvo = () => {
    setShowConvoState(!showConvoState);
  };

  // if (liveParentConversations.length > 0 && !liveParentConversations[0]?.expand?.from) {
  //   throw new Error('Conversations did not load');
  // }

  console.log(convoFrom);
  return (
    <div className="flex w-full flex-col">
      <ConversationTopBar convo_id={convo_id} />
      <div className="flex flex-grow overflow-hidden">
        {/* agent and team sidebar start */}
        <div className="hidden sm:block border-r bg-white ">
          <div className={clsx('h-full flex-col gap-2', drawerState ? 'flex' : 'hidden')}>
            {/* sidebar having names of the users */}
            <div className="flex-1">
              <TeamInbox
                agentId={agentId}
                teamId={teamId}
                setAgentId={setAgentId}
                setTeam={setTeamId}
                currentUser={currentUser}
                teams={teams}
                agents={agentsOfAccount}
                loading={loading}
              />
              <AgentList agentId={agentId} setTeam={setTeamId} setAgent={setAgentId} agents={agentsOfAccount} />
            </div>
          </div>
        </div>
        {/* agent and team sidebar ends */}
        {/* --------------- */}

        {/* conversation side bar starts */}
        <div
          className={classNames(`sm:flex w-full sm:w-[200px] md:w-[300px] flex-shrink-0 flex-col`, {
            hidden: !showConvoState,
          })}
        >
          <div className="px-4 md:border-b md:py-3 space-y-2">
            <div className="flex items-center">
              {archiveLeadsExists && (
                <Button
                  variant={archiveLeads ? 'secondary' : 'outline'}
                  className="rounded-full"
                  onClick={() => {
                    setArchiveLeads(!archiveLeads);
                    setFilterUnread(false);
                  }}
                >
                  <Archive className="mr-2 h-5 w-5" />
                  Archived Conversations {archivedCount}
                </Button>
              )}
            </div>
            {!archiveLeads && (
              <div className="flex space-x-2">
                <Button variant={filterUnread ? 'outline' : 'secondary'} className="rounded-full" onClick={() => setFilterUnread(false)}>
                  All {allCount}
                </Button>
                <Button variant={filterUnread ? 'secondary' : 'outline'} className="rounded-full" onClick={toggleUnread}>
                  Unread {unreadCount}
                </Button>
              </div>
            )}
            {teamId || agentId ? (
              <div>
                <div className="text-sm">
                  Browsing conversations of:{' '}
                  <div className="font-semibold capitalize">{getTeamName(teams, teamId) ?? agentsOfAccount.find((a) => a.id === agentId)?.name}</div>
                </div>
              </div>
            ) : null}
            {/* <ListFilter style={{ color: filterUnread ? 'red' : 'black' }} onClick={toggleUnread} /> */}
          </div>
          <div className="flex items-center md:border-b">
            <form
              onSubmit={(e) => {
                e.preventDefault();
              }}
              className="w-full md:p-4 pt-1 px-2"
            >
              <Input
                className="w-full bg-white placeholder-gray-300 rounded-lg border"
                placeholder={archiveLeads ? 'Search archive contacts' : filterUnread ? 'Search unread contacts' : 'Search contacts'}
                type="search"
                value={searchQuery}
                onChange={(e) => {
                  setSearchQuery(e.target.value);
                }}
              />
            </form>
          </div>
          <SlowList
            liveParentConversations={liveParentConversations}
            convo_id={convo_id}
            toggleConvo={toggleConvo}
            setConvoId={setConvoId}
            accountId={accountId}
            formatDate={formatDate}
            archiveLeads={archiveLeads}
            phone_id={phone_id}
          />
        </div>
        {/* conversation side bar ends */}
        {/* --------------- */}
        {/* conversation main content starts */}
        <div
          className={classNames('sm:flex grow flex-col', {
            flex: !showConvoState,
            hidden: showConvoState,
          })}
        >
          <div className="md:hidden items-center py-2 sm:px-0 px-4 gap-2 sm:text-xs sm:py-1 flex border-b border-gray-200">
            <div onClick={toggleConvo} className="block sm:hidden">
              <ChevronLeft />
            </div>
            <div className="flex flex-col sm:space-y-2">
              <div className="flex sm:space-x-2 flex-col sm:flex-row">
                <div className="text-sm">Conversation of: </div>

                <div className="font-semibold text-sm ">{convoFrom?.name ?? convoFrom?.phone_number}</div>
              </div>
              <div className="text-sm font-medium flex space-x-2 sm:hidden">
                <div>{convoFrom?.phone_number}</div>
                <a href={`tel:+${convoFrom?.phone_number}`}>
                  <PhoneCall className="h-4 w-4 flex-shrink-0 block sm:hidden" />
                </a>
              </div>

              <div className="flex sm:hidden items-center gap-2">
                {tags.slice(0, 3).map((_tags: any, index: number) => (
                  <Badge variant={'secondary'} key={index}>
                    <TagIcon className="h-3 w-3 mr-1" />
                    {_tags}
                  </Badge>
                ))}
                {tags && tags?.length > 3 && <div>+{tags?.length - 3}</div>}
              </div>
            </div>
          </div>
          {children}
        </div>
        {/* conversation main content ends */}
        {/* --------------- */}

        {/* right bar starts */}
        {convoFrom && (
          <RightBar
            lead={convoFrom}
            otags={tags}
            currentUser={currentUser}
            allTags={allTags}
            convo_id={convo_id}
            agentsOfAccount={agentsOfAccount}
            teams={teams}
            conversation={conversation}
          />
        )}
        {/* right bar ends */}
        {/* --------------- */}
      </div>
    </div>
  );
}

const TeamInbox = ({
  currentUser,
  teams,
  setTeam,
  setAgentId,
  agents,
  agentId,
  teamId,
  loading,
}: {
  setAgentId: Dispatch<SetStateAction<string | null>>;
  agentId: string | null;
  setTeam: Dispatch<SetStateAction<string | null>>;
  currentUser: User;
  teams: Team[];
  agents: User[];
  teamId: string | null;
  loading: boolean;
}) => {
  const conversations = useLiveQuery(() => db.conversations.toArray(), []) ?? [];
  const pTeam = teamId;
  return (
    <div className="flex-grow w-64">
      <div className="text-md p-3 border-b">Team Inbox</div>
      <div className="flex flex-col gap-2">
        <Button
          variant={'ghost'}
          onClick={() => setTeam(null)}
          className={clsx(
            'text-sm flex items-center justify-between text-gray-700 gap-x-2 p-2 cursor-pointer hover:bg-gray-100/40',
            !pTeam && !agentId && 'bg-gray-100 font-semibold'
          )}
        >
          <div className="flex space-x-2 items-center pl-2">
            <UsersIcon className="h-4 w-4 mr-2" />
            <div>All</div>
          </div>

          <div className="flex space-x-2 items-center">
            {loading && <Loader2 className="animate-spin text-gray-400" />}
            <div className="no-underlinetext-xs">{conversations.length}</div>
          </div>
        </Button>
        <Button
          variant={'ghost'}
          onClick={() => {
            setTeam('you');
            setAgentId(null);
          }}
          className={clsx(
            'text-sm flex items-center justify-between text-gray-700 gap-x-2 p-2 cursor-pointer hover:bg-gray-100/40',
            pTeam === 'you' && !agentId && 'bg-gray-100 font-semibold'
          )}
        >
          <div className="flex space-x-2 items-center pl-2">
            <UserIcon className="h-4 w-4 mr-2" />
            <div>You</div>
          </div>

          <div className="no-underline text-xs">{conversations.filter((convo) => convo.assigned_agent === currentUser.id).length}</div>
        </Button>

        {teams.map((team) => {
          const teamConversations = conversations.filter((convo) => {
            return convo.team_id === team.id;
          }).length;
          return (
            <Button
              variant={'ghost'}
              onClick={() => {
                setTeam(team.id);
                setAgentId(null);
              }}
              className={clsx(
                'text-sm flex items-center justify-between text-gray-700 gap-x-2 p-2 cursor-pointer hover:bg-gray-100/40',
                pTeam === team.id && 'bg-gray-100 font-font-semibold'
              )}
              key={team.id}
            >
              <div className="flex space-x-2 items-center pl-2">
                <UsersIcon className="h-4 w-4 mr-2" />
                <div className="capitalize">{team.name}</div>
              </div>

              <div className="no-underlinetext-xs">{teamConversations}</div>
            </Button>
          );
        })}
        <Button
          variant={'ghost'}
          onClick={() => {
            setTeam('unassigned');
            setAgentId(null);
          }}
          className={cn('text-sm flex items-center justify-between text-gray-700 gap-x-2 p-2 cursor-pointer hover:bg-gray-100/40', {
            'bg-gray-100': pTeam === 'unassigned',
          })}
        >
          <div className="flex space-x-2 items-center pl-2">
            <CircleX className="h-4 w-4 mr-2" />
            <div>Unassigned</div>
          </div>
          <div className="no-underline text-xs">{conversations.filter((convo) => !convo?.assigned_agent).length}</div>
        </Button>
      </div>
    </div>
  );
};

async function addMessageInDb(message: IExpandedMessage, pb: PocketBase) {
  const messageId = localStorage.getItem('latestMessage');
  if (messageId) {
    db.messages.filter((msg) => msg.id === messageId).delete();
  }
  await db.messages.put({
    ...message,
    ...(message.file && { url: pb.files.getURL(message, message.file) }),
    created: new Date(message.created),
  });
}

const boundAddMessageInDb = debounce(addMessageInDb, 1500);

const AgentList = ({
  agents,
  agentId,
  setTeam,
  setAgent,
}: {
  setTeam: Dispatch<SetStateAction<string | null>>;
  agents: AgentWithConvos[];
  agentId: string | null;
  setAgent: (agentId: string) => void;
}) => {
  const conversations = useLiveQuery(() => db.conversations.toArray(), []) ?? [];
  const getRandomBgAndText = useCallback((index: number) => {
    const colors = [
      { bg: 'bg-blue-100', text: 'text-blue-600' },
      { bg: 'bg-red-100', text: 'text-red-600' },
      { bg: 'bg-green-100', text: 'text-green-600' },
      { bg: 'bg-violet-100', text: 'text-violet-600' },
      { bg: 'bg-cyan-100', text: 'text-cyan-600' },
    ];
    const randomColor = colors[index % colors.length];
    return `${randomColor.bg} ${randomColor.text}`;
  }, []);
  return (
    <div>
      <div className="text-md p-3 border-b border-t">Agents</div>
      <div className="flex flex-col overflow-y-scroll">
        {agents.map((agent, index) => {
          const agentConversations = conversations.filter((convo) => {
            return convo.assigned_agent === agent?.id;
          }).length;
          return (
            <Button
              variant={'ghost'}
              onClick={() => {
                setAgent(agent.id);
                setTeam(null);
              }}
              key={agent.id}
              className={clsx(
                'text-sm py-2 flex items-center justify-between text-gray-700 gap-x-2 p-2 cursor-pointer hover:bg-gray-100/40',
                agentId === agent.id && 'bg-gray-100 font-semibold'
              )}
            >
              <div className="flex py-2 space-x-2 items-center">
                <Avatar className="h-8 w-8">
                  <AvatarImage src={agent.avatar} alt="@shadcn" />
                  <AvatarFallback className={clsx('text-xs font-semibold', getRandomBgAndText(index))}>{agent.name.charAt(0).toUpperCase()}</AvatarFallback>
                </Avatar>
                <div>{agent.name}</div>
              </div>
              <div className="no-underline text-gray-800 text-xs">{agentConversations}</div>
            </Button>
          );
        })}
      </div>
    </div>
  );
};

const getTeamName = (teams: Team[], teamId: string | null) => {
  if (!teamId) return null;
  if (teamId === 'you') return 'You';
  if (teamId === 'unassigned') return 'Unassigned';
  return teams.find((t) => t.id === teamId)?.name;
};
