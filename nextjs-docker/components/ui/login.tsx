'use client';
import { useState } from 'react';
import SignUpWithGoogleIcon from '@/app/auth/signup/SignUpWithGoogle';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Loader2 } from 'lucide-react';
import Link from 'next/link';
import { useFormStatus } from 'react-dom';
import { InputPassword } from './InputPassword';
import { loginSignUpWithGoogle } from '@/state/user/user';

declare global {
  interface Window {
    fbAsyncInit: () => void;
    FB: any;
    fbq: any;
  }
}

// import { useEffect } from "react"

export default function Login() {
  const [isPasswordHidden, setPasswordHidden] = useState(true);
  const { pending } = useFormStatus();
  const signInWithGoogle = async () => {
    try {
      await loginSignUpWithGoogle();
    } catch (error) {
      console.log(error);
    }
  };
  return (
    <>
      <div className="font-bold text-2xl mb-3 w-full">Login with your account</div>
      <Button
        variant="default"
        onClick={() => signInWithGoogle()}
        type="button"
        className="bg-[#F2F2F2] w-full font-roboto text-black font-semibold hover:bg-[#F2F2F2]"
      >
        <SignUpWithGoogleIcon />
        <div className="flex-1">Sign in with Google</div>
      </Button>
      <div className="flex space-x-4 items-center my-3 w-full justify-center">
        <div className="h-[1px] w-full bg-gray-200"></div>
        <div className="text-gray-400 text-xs">OR</div>
        <div className="h-[1px] w-full bg-gray-200"></div>
      </div>
      <div className="space-y-6 mb-6 w-full">
        <Input placeholder="Email" type="email" name="email" required />
        <InputPassword
          placeholder="Password"
          isPasswordHidden={isPasswordHidden}
          setPasswordHidden={setPasswordHidden}
          type={isPasswordHidden ? 'password' : 'text'}
          name="password"
          required
        />
      </div>
      {pending ? (
        <Button className="w-full bg-[#306b64] hover:bg-[#15423d]" disabled>
          <Loader2 className="mr-2 h-4 w-4 animate-spin" /> Login
        </Button>
      ) : (
        <Button type="submit" className="w-full bg-[#075E54] hover:bg-[#15423d]">
          Login
        </Button>
      )}
      <p>If you don't remember your password, then login with OTP</p>
      <Button variant="link" type="submit" name="otpLogin" value="otpLogin">
        Login with OTP instead
      </Button>
      <div className="text-gray-800 mb-1">
        Don't have an account yet?{' '}
        <Link className="underline" href="/auth/signup">
          Sign Up
        </Link>
      </div>
      <div className="text-gray-800 mb-1">
        <Link className="underline" href="/auth/reset?page=1">
          Reset Password
        </Link>
      </div>
    </>
  );
}
