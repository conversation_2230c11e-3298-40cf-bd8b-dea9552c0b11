import * as React from 'react';
import { Slot } from '@radix-ui/react-slot';
import { cva, type VariantProps } from 'class-variance-authority';

import { cn } from '@/lib/utils';

const buttonVariants = cva(
  'inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-white transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-gray-950 focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 white',
  {
    variants: {
      variant: {
        default: 'bg-zinc-950 hover:bg-zinc-950/90 text-white',
        destructive: 'bg-destructive text-destructive-foreground hover:bg-destructive/90',
        warning: 'bg-gradient-to-r from-orange-500 to-yellow-500 hover:bg-gradient-to-bl text-white',
        outline: 'border border-input bg-background hover:bg-gray-50 hover:text-accent-foreground',
        secondary: 'bg-gradient-to-r from-green-500 to-emerald-500 hover:bg-gradient-to-bl text-white',
        ghost: 'hover:bg-accent hover:text-accent-foreground',
        link: 'bg-blue-100 text-blue-500 underline-offset-4 hover:underline',
        trigger: 'hover:bg-gray-100 text-gray-900',
      },
      size: {
        default: 'h-10 px-4 py-2',
        xl: 'h-7 rounded-md px-3',
        sm: 'h-9 rounded-md px-3',
        lg: 'h-11 rounded-md px-8',
        icon: 'h-10 w-10',
      },
    },
    defaultVariants: {
      variant: 'default',
      size: 'default',
    },
  }
);

export interface ButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement>, VariantProps<typeof buttonVariants> {
  asChild?: boolean;
}

const Button = React.forwardRef<HTMLButtonElement, ButtonProps>(({ className, variant, size, asChild = false, ...props }, ref) => {
  const Comp = asChild ? Slot : 'button';
  return <Comp className={cn(buttonVariants({ variant, size, className }))} ref={ref} {...props} />;
});
Button.displayName = 'Button';

export { Button, buttonVariants };
