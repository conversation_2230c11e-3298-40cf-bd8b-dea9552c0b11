import * as React from 'react';

import { cn } from '@/lib/utils';
import countrycodes from '@/lib/countrylistfiltered.json';

interface NumberCode {
  country: string;
  code: string;
  emoji: string;
  phoneLength: number;
  iso: string;
}

interface InputProps extends React.InputHTMLAttributes<HTMLInputElement> {
  numberCode: NumberCode;
  setNumberCode: React.Dispatch<React.SetStateAction<NumberCode>>;
  setNumberInput: React.Dispatch<React.SetStateAction<any>>;
  numberInput: string | null | undefined;
  formRef: any;
}

const InputWithPhoneCode2 = React.forwardRef<HTMLInputElement, InputProps>(
  ({ className, type, numberCode, setNumberCode, setNumberInput, numberInput, formRef, ...props }, ref) => {
    const [toggleButton, setToggleButton] = React.useState(true);
    const [data, setData] = React.useState(countrycodes);
    const [searchTerm, setSearchTerm] = React.useState('');

    const filterResult = (countryName: string) => {
      let fitlerData = countrycodes.filter((item) => item.name.toUpperCase().includes(countryName.toUpperCase()));
      setData(fitlerData);
    };
    const dropdownRef = React.useRef<HTMLInputElement>(null);

    React.useEffect(() => {
      if (!toggleButton && dropdownRef.current) {
        dropdownRef.current.focus();
      }
    }, [toggleButton]);

    const handleKeyPress = (e: React.KeyboardEvent<HTMLInputElement>) => {
      if (e.key === 'Enter') {
        // Check if the event is coming from the dropdown search input
        if (e.currentTarget.name === 'phoneNumberSearch') {
          e.preventDefault();
          if (data.length > 0) {
            setNumberCode({
              country: data[0].name,
              code: data[0].phone[0],
              emoji: data[0].emoji,
              phoneLength: data[0].phoneLength,
              iso: data[0].iso['alpha-2'],
            });
            setData(countrycodes);
            setSearchTerm('');
            setToggleButton(true);
          }
        } else {
          // Else submit the form
          if (formRef.current) {
            formRef.current.submit();
          }
        }
      }
    };

    return (
      <div>
        <div
          className={cn(
            'flex h-10 w-full rounded-md border-2 border-gray-200 text-sm file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-gray-400 disabled:cursor-not-allowed disabled:opacity-50 focus:ring-0 focus:outline-none focus:border-[#075E54] ',
            className
          )}
        >
          <button
            type="button"
            className="bg-white w-20 ml-1 pl-1"
            onClick={() => {
              setToggleButton(!toggleButton);
            }}
          >
            {numberCode.emoji} {numberCode.code}
          </button>
          <input
            value={numberInput ?? ''}
            onChange={(event) => {
              setNumberInput(event.target.value);
            }}
            type={type}
            className="appearance-none w-full rounded-md focus:ring-0 focus:outline-none py-2 pr-3"
            ref={ref}
            {...props}
          />
        </div>

        <div
          id="dropdown"
          className={cn('z-10 bg-white divide-y divide-gray-100 rounded-lg shadow dark:bg-gray-700 absolute w-80 mt-1', { hidden: toggleButton })}
        >
          <input
            type="text"
            placeholder="Search..."
            name="phoneNumberSearch"
            className="w-full px-4 py-2 text-sm border-b border-gray-300 focus:outline-none focus:border-[#075E54]"
            value={searchTerm}
            onChange={(e) => {
              filterResult(e.target.value);
              setSearchTerm(e.target.value);
            }}
            onKeyDown={handleKeyPress}
            ref={dropdownRef}
          />
          <ul className="py-2 text-sm text-gray-700 dark:text-gray-200 h-48 overflow-y-auto overflow-x-hidden" aria-labelledby="dropdownDefaultButton">
            {data.map((item) => {
              let classname = 'px-4 py-2 hover:bg-gray-100 dark:hover:bg-gray-600 dark:hover:text-white flex space-x-2';
              if (item.emoji == numberCode.emoji) {
                classname = 'px-4 py-2 bg-[#25D366] hover:bg-[#2e9e57] dark:hover:bg-gray-600 dark:hover:text-white flex space-x-2';
              }
              if (searchTerm && data.length == 1) {
                classname = 'px-4 py-2 bg-yellow-200 flex space-x-2';
              }
              return (
                <button
                  key={item.name}
                  type="button"
                  className="w-80"
                  onClick={() => {
                    setNumberCode({
                      country: item.name,
                      code: item.phone[0],
                      emoji: item.emoji,
                      phoneLength: item.phoneLength,
                      iso: item.iso['alpha-2'],
                    });
                    setToggleButton(!toggleButton);
                  }}
                >
                  <div className={classname}>
                    <img src={item.image} width={20} height={20} />
                    <div className="w-10">{item?.phone?.[0] ?? ''}</div>
                    <div>{item?.name ?? ''}</div>
                  </div>
                </button>
              );
            })}
          </ul>
        </div>
      </div>
    );
  }
);
InputWithPhoneCode2.displayName = 'Input';

export { InputWithPhoneCode2 };
