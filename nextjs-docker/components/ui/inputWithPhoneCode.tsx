import * as React from 'react';

import { cn } from '@/lib/utils';
import countrycodes from '@/lib/countrylistfiltered.json';

interface NumberCode {
  code: string;
  emoji: string;
  phoneLength: number;
}

interface InputProps extends React.InputHTMLAttributes<HTMLInputElement> {
  numberCode: NumberCode;
  setNumberCode: React.Dispatch<React.SetStateAction<NumberCode>>;
  formRef: any;
}

const InputWithPhoneCode = React.forwardRef<HTMLInputElement, InputProps>(({ className, type, numberCode, setNumberCode, formRef, ...props }, ref) => {
  const [toggleButton, setToggleButton] = React.useState(true);
  const [data, setData] = React.useState(countrycodes);
  const [searchTerm, setSearchTerm] = React.useState('');

  const filterResult = (countryName: string) => {
    let fitlerData = countrycodes.filter((item) => item.name.toUpperCase().includes(countryName.toUpperCase()));
    setData(fitlerData);
  };
  const dropdownRef = React.useRef<HTMLInputElement>(null);

  React.useEffect(() => {
    if (!toggleButton && dropdownRef.current) {
      dropdownRef.current.focus();
    }
  }, [toggleButton]);

  const handleKeyPress = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === 'Enter') {
      // Check if the event is coming from the dropdown search input
      if (e.currentTarget.name === 'phoneNumberSearch') {
        e.preventDefault();
        if (data.length > 0) {
          setNumberCode({
            code: data[0].phone[0],
            emoji: data[0].emoji,
            phoneLength: data[0].phoneLength,
          });
          setData(countrycodes);
          setSearchTerm('');
          setToggleButton(true);
        }
      } else {
        // Else submit the form
        if (formRef.current) {
          formRef.current.submit();
        }
      }
    }
  };

  return (
    <div>
      <div
        className={cn(
          'flex h-10 w-full rounded-md border-2 border-gray-400 text-sm file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-gray-400 disabled:cursor-not-allowed disabled:opacity-50 focus:ring-0 focus:outline-none focus:border-[#075E54] space-x-2',
          className
        )}
      >
        <button
          type="button"
          className="bg-white w-1/4 ml-1 pl-2"
          onClick={() => {
            setToggleButton(!toggleButton);
          }}
        >
          {numberCode.emoji} {numberCode.code}
        </button>
        <input type={type} className="appearance-none w-full focus:ring-0 focus:outline-none rounded-md py-2 pr-3" ref={ref} {...props} />
      </div>

      <div
        id="dropdown"
        className={cn('z-10 bg-white divide-y divide-gray-100 rounded-lg shadow dark:bg-gray-700 absolute w-80 mt-1', { hidden: toggleButton })}
      >
        <input
          type="text"
          placeholder="Search..."
          name="phoneNumberSearch"
          className="w-full px-4 py-2 text-sm border-b border-gray-300 focus:outline-none focus:border-[#075E54]"
          value={searchTerm}
          onChange={(e) => {
            filterResult(e.target.value);
            setSearchTerm(e.target.value);
          }}
          onKeyDown={handleKeyPress}
          ref={dropdownRef}
        />
        <ul className="py-2 text-sm text-gray-700 dark:text-gray-200 h-48 overflow-y-auto overflow-x-hidden" aria-labelledby="dropdownDefaultButton">
          {data.map((item) => {
            let classname = 'px-4 py-2 hover:bg-gray-100 dark:hover:bg-gray-600 dark:hover:text-white flex space-x-2';
            if (item.emoji == numberCode.emoji) {
              classname = 'px-4 py-2 bg-[#25D366] hover:bg-[#2e9e57] dark:hover:bg-gray-600 dark:hover:text-white flex space-x-2';
            }
            if (searchTerm && data.length == 1) {
              classname = 'px-4 py-2 bg-yellow-200 flex space-x-2';
            }
            return (
              <button
                type="button"
                className="w-80"
                onClick={() => {
                  setNumberCode({
                    code: item.phone[0],
                    emoji: item.emoji,
                    phoneLength: item.phoneLength,
                  });
                  setToggleButton(!toggleButton);
                }}
              >
                <div className={classname}>
                  <img src={item.image} width={20} height={20} />
                  <div className="w-10">{item?.phone?.[0] ?? ''}</div>
                  <div>{item?.name ?? ''}</div>
                </div>
              </button>
            );
          })}
        </ul>
      </div>
    </div>
  );
});
InputWithPhoneCode.displayName = 'Input';

export { InputWithPhoneCode };
