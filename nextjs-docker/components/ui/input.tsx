import * as React from 'react';

import { cn } from '@/lib/utils';

export interface InputProps extends React.InputHTMLAttributes<HTMLInputElement> {}

const Input = React.forwardRef<HTMLInputElement, InputProps>(({ className, type, ...props }, ref) => {
  return (
    <input
      type={type}
      className={cn(
        'flex h-10 w-full rounded-md border-2 border-gray-400 bg-white px-3 py-2 text-sm file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-gray-400 disabled:cursor-not-allowed disabled:opacity-50 focus:ring-0 focus:outline-none focus:border-[#075E54]',
        className
      )}
      ref={ref}
      {...props}
    />
  );
});
Input.displayName = 'Input';

export { Input };

// import * as React from "react";
// import { cn } from "@/lib/utils";

// export interface InputProps extends React.InputHTMLAttributes<HTMLInputElement> { }

// const Input = React.forwardRef<HTMLInputElement, InputProps>(
//   ({ className, type, value, onChange, ...props }, ref) => {
//     const internalRef = React.useRef<HTMLInputElement>(null);
//     const combinedRef = ref || internalRef;

//     return (
//       <div className="relative flex items-center">
//         <input
//           type={type}
//           className={cn(
//             "h-10 w-full rounded-md border-2 border-gray-400 bg-white px-3 py-2 pr-10 text-sm file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-gray-400 disabled:cursor-not-allowed disabled:opacity-50 focus:ring-0 focus:outline-none focus:border-[#075E54]",
//             className
//           )}
//           ref={combinedRef}
//           value={value}
//           onChange={onChange}
//           {...props}
//         />
//         {value && (
//           <button
//             type="button"
//             className="text-lg absolute right-2 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600 focus:outline-none"
//             onClick={() => {
//               if (combinedRef && typeof combinedRef !== 'function' && combinedRef.current) {
//                 combinedRef.current.value = '';
//                 const event = new Event('input', { bubbles: true });
//                 combinedRef.current.dispatchEvent(event);
//                 if (onChange) {
//                   onChange(event as any);
//                 }
//               }
//             }}
//             style={{ fontSize: '1.5rem' }}
//           >
//             &times;
//           </button>
//         )}
//       </div>
//     );
//   }
// );

// Input.displayName = "Input";

// export { Input };

// import * as React from "react";
// import { cn } from "@/lib/utils";

// export interface InputProps extends React.InputHTMLAttributes<HTMLInputElement> { }

// const Input = React.forwardRef<HTMLInputElement, InputProps>(
//   ({ className, type, value, onChange, ...props }, ref) => {
//     const internalRef = React.useRef<HTMLInputElement>(null);
//     const combinedRef = ref || internalRef;

//     return (
//       <div className={cn("w-full h-10 relative flex items-center rounded-md border-2 box-border border-gray-400 bg-white text-sm file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-gray-400 disabled:cursor-not-allowed disabled:opacity-50 focus:ring-0 focus:outline-none focus:border-[#075E54]", className)}>
//         <input
//           type={type}
//           className={cn(
//             "w-[90%] border-0 outline-none px-1",
//             // className
//           )}
//           ref={combinedRef}
//           value={value}
//           onChange={onChange}
//           {...props}
//         />
//         {value && (
//           <button
//             type="button"
//             className="text-lg absolute right-2 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600 focus:outline-none"
//             onClick={() => {
//               if (combinedRef && typeof combinedRef !== 'function' && combinedRef.current) {
//                 combinedRef.current.value = '';
//                 const event = new Event('input', { bubbles: true });
//                 combinedRef.current.dispatchEvent(event);
//                 if (onChange) {
//                   onChange(event as any);
//                 }
//               }
//             }}
//             style={{ fontSize: '1.5rem' }}
//           >
//             &times;
//           </button>
//         )}
//       </div>
//     );
//   }
// );

// Input.displayName = "Input";

// export { Input };
