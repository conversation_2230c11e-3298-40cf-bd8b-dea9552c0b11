'use client';

import React, { useRef, useState } from 'react';
import { QRCodeSVG } from 'qrcode.react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Check, Copy, Download } from 'lucide-react';

export function WhatsappQr({ number }: { number: string }) {
  const [copied, setCopied] = useState(false);
  const qrRef = useRef<SVGSVGElement>(null);

  // Hardcoded phone number (replace with your actual number)
  const whatsappLink = `https://wa.me/${number}`;

  const downloadQRCode = () => {
    if (qrRef.current) {
      const canvas = document.createElement('canvas');
      const svg = qrRef.current;
      const svgData = new XMLSerializer().serializeToString(svg);
      const img = new Image();
      img.onload = () => {
        canvas.width = img.width;
        canvas.height = img.height;
        const ctx = canvas.getContext('2d');
        ctx?.drawImage(img, 0, 0);
        const pngFile = canvas.toDataURL('image/png');
        const downloadLink = document.createElement('a');
        downloadLink.download = 'whatsapp-qr.png';
        downloadLink.href = pngFile;
        downloadLink.click();
      };
      img.src = 'data:image/svg+xml;base64,' + btoa(svgData);
    }
  };

  const copyToClipboard = async () => {
    try {
      await navigator.clipboard.writeText(whatsappLink);
      setCopied(true);
      setTimeout(() => setCopied(false), 2000);
    } catch (err) {
      console.error('Failed to copy text: ', err);
    }
  };

  return (
    <Card className="w-full max-w-md">
      <CardHeader>
        <CardTitle>Download your QR code</CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="flex justify-center">
          <QRCodeSVG value={whatsappLink} size={100} ref={qrRef} fgColor="#0d6100" bgColor="transparent" level="L" />
        </div>
        <Button onClick={downloadQRCode} className="w-full">
          <Download className="mr-2 h-4 w-4" /> Download QR Code
        </Button>
        <div className="flex space-x-2">
          <Button variant="outline" className="flex-1" onClick={() => window.open(whatsappLink, '_blank')}>
            Open in WhatsApp
          </Button>
          <Button variant="outline" className="flex-1" onClick={copyToClipboard}>
            {copied ? (
              <>
                <Check className="mr-2 h-4 w-4" /> Copied
              </>
            ) : (
              <>
                <Copy className="mr-2 h-4 w-4" /> Copy Link
              </>
            )}
          </Button>
        </div>
      </CardContent>
    </Card>
  );
}
