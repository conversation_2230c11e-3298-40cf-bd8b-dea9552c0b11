'use client';

import * as React from 'react';
import { ChevronsUpDown, Loader2Icon } from 'lucide-react';

import { Button } from '@/components/ui/button';
import { Checkbox } from '@/components/ui/checkbox';
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from '@/components/ui/collapsible';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { cn } from '@/lib/utils';
import { IExpandedLead, Meta } from '@/lib/types';
import { Table } from '@tanstack/react-table';
import { usePathname, useRouter, useSearchParams } from 'next/navigation';

interface TextFilter {
  id: string;
  label: string;
  type: 'text';
  value: string;
  isChecked: boolean;
}

interface DropdownFilter {
  id: string;
  label: string;
  type: 'dropdown';
  value: string;
  options: string[];
  isChecked: boolean;
}

type Filter = TextFilter | DropdownFilter;

export function DropdownFilter({ table, metaKeys }: { table: Table<IExpandedLead>; metaKeys: Meta[] }) {
  const { replace } = useRouter();
  const searchParams = useSearchParams();
  const pathname = usePathname();
  const [pending, transition] = React.useTransition();
  const params = new URLSearchParams(searchParams);
  const [isOpen, setIsOpen] = React.useState(false);
  const initFilters: DropdownFilter[] = metaKeys.map((metaKey) => ({
    id: metaKey.key,
    label: metaKey.key,
    type: 'dropdown',
    value: '',
    options: metaKey.values,
    isChecked: false,
  }));

  const [filters, setFilters] = React.useState<Filter[]>(initFilters);
  React.useEffect(() => {
    const metaFilters = searchParams.get('meta_filters');
    if (!metaFilters) {
      setFilters(initFilters);
    }
  }, [searchParams]);
  const handleCheckboxChange = (id: string) => {
    setFilters(filters.map((filter) => (filter.id === id ? { ...filter, isChecked: !filter.isChecked } : filter)));
  };

  const handleInputChange = (id: string, value: string) => {
    setFilters(filters.map((filter) => (filter.id === id ? { ...filter, value } : filter)));
  };

  const handleSubmit = () => {
    console.log('Applied filters:', activeFilters);
    transition(() => {
      activeFilters.forEach((filter) => {
        table.getColumn(filter.id)?.setFilterValue(filter.value);
      });
      params.set('meta_filters', activeFilters.map((filter) => `${filter.id}=${filter.value}`).join('&'));
      replace(`${pathname}?${params.toString()}`);
      setIsOpen(false);
    });
    // Here you would typically send these filters to your data fetching logic
  };

  const activeFilters = filters.filter((filter) => filter.isChecked);

  return (
    <div className="w-full absolute bg-white z-10">
      <Collapsible open={isOpen} onOpenChange={setIsOpen} className="w-full">
        <CollapsibleTrigger asChild>
          <Button variant="outline" className="w-full" aria-expanded={isOpen}>
            {activeFilters.length > 0 ? <div>{activeFilters.map((f) => f.value).join(', ')}</div> : <div>Meta Filters</div>}
            <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
          </Button>
        </CollapsibleTrigger>
        <CollapsibleContent className="w-[420px] bg-white shadow-md p-2 mt-2 space-y-2">
          {filters.map((filter) => (
            <div key={filter.id} className="flex items-center space-x-2">
              <div className="flex items-center space-x-2 w-1/3">
                <Checkbox id={filter.id} checked={filter.isChecked} onCheckedChange={() => handleCheckboxChange(filter.id)} />
                <Label htmlFor={filter.id} className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70">
                  {filter.label}
                </Label>
              </div>
              <div className="w-2/3">
                {filter.type === 'text' ? (
                  <Input
                    type="text"
                    value={filter.value}
                    onChange={(e) => handleInputChange(filter.id, e.target.value)}
                    className={cn('h-8 w-full', !filter.isChecked && 'opacity-50')}
                    disabled={!filter.isChecked}
                    placeholder={`Enter ${filter.label.toLowerCase()}`}
                  />
                ) : (
                  <Select value={filter.value} onValueChange={(value) => handleInputChange(filter.id, value)} disabled={!filter.isChecked}>
                    <SelectTrigger className={cn('w-full', !filter.isChecked && 'opacity-50')}>
                      <SelectValue placeholder={`Select ${filter.label.toLowerCase()}`} />
                    </SelectTrigger>
                    <SelectContent>
                      {filter.options.map((option) => (
                        <SelectItem key={option} value={option}>
                          {option}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                )}
              </div>
            </div>
          ))}
          <Button onClick={handleSubmit} className="w-full mt-2">
            {pending ? (
              <div className="animate-spin">
                <Loader2Icon />
              </div>
            ) : (
              <>Apply Filters</>
            )}
          </Button>
        </CollapsibleContent>
      </Collapsible>
    </div>
  );
}
