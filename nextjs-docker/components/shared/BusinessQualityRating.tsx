import clsx from 'clsx';
import { <PERSON><PERSON>elp } from 'lucide-react';
import React from 'react';
import { HoverCard, HoverCardContent, HoverCardTrigger } from '../ui/hover-card';

const BusinessQualityRating = ({ quality_rating }: { quality_rating: string | null }) => {
  const ratingColors: { [key: string]: string } = {
    GREEN: 'text-green-500',
    YELLOW: 'text-yellow-500',
    RED: 'text-red-500',
  };

  const ratingMessage: { [key: string]: string } = {
    GREEN:
      "Messages are highly relevant and valuable to recipients.\nMessages are compliant with WhatsApp's Business Messaging Policy.\nRecipients engage with the messages actively (e.g., open rates, replies).\nMessages are not perceived as spam or unsolicited.",
    YELLOW:
      'Messages may have minor relevance or value issues.\nMessages may have some compliance concerns.\nRecipient engagement is moderate.\nMessages might be borderline spam or unsolicited.',
    RED: "Messages are irrelevant or have little value to recipients.\nMessages are significantly non-compliant with WhatsApp's Business Messaging Policy.\nRecipient engagement is low or non-existent.\nMessages are clearly spam or unsolicited",
  };

  return quality_rating && ratingColors[quality_rating] ? (
    <div className="hidden sm:flex font-bold space-x-2 items-center">
      <div className="lg:block hidden">Business Quality:</div>
      <div className="lg:hidden block">Quality:</div>

      <HoverCard openDelay={0}>
        <HoverCardTrigger asChild>
          <div className="flex space-x-2 items-center hover:cursor-help">
            <div className={ratingColors[quality_rating]}>• {quality_rating}</div>
            <CircleHelp className={clsx('h-4 w-4', ratingColors[quality_rating])} />
          </div>
        </HoverCardTrigger>
        <HoverCardContent className="text-xs">
          <div className="whitespace-pre-line">{ratingMessage[quality_rating]}</div>
        </HoverCardContent>
      </HoverCard>
    </div>
  ) : (
    <div>Business Quality: {quality_rating ?? 'UNKNOWN'}</div>
  );
};

export default BusinessQualityRating;
