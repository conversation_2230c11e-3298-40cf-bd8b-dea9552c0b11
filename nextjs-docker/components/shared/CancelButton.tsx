'use client';
import Link from 'next/link';
import { useParams, useSearchParams } from 'next/navigation';
import { Button } from '../ui/button';

import { Dialog, DialogClose, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { createDraftCampaign } from '@/lib/pocket';
import { useState } from 'react';
import { useToast } from '../ui/use-toast';
import { Loader2 } from 'lucide-react';
import { Input } from '../ui/input';

const CancelButton = () => {
  const [loading, setLoading] = useState(false);
  const { toast } = useToast();
  const params = useParams();
  const account_id = params.account_id as string;
  const searchParams = useSearchParams();
  const campaignName = searchParams.get('campaign_name');
  const templateId = params?.template_id as string; // "template_id"
  const list = searchParams.getAll('list'); //list = ['1', '2']
  const [name, setName] = useState(campaignName ?? '');
  const handleAddCampaignToDraft = async () => {
    setLoading(true);
    try {
      if (templateId && list.length > 0 && campaignName) {
        // meaning last page
        let listIdsJoined = list.join('&list=');
        const draft_url = `/${account_id}/campaign/create/${templateId}/summary?campaign_name=${name}&list=${listIdsJoined}`;
        await createDraftCampaign(name, list, account_id, templateId, draft_url);
        toast({
          variant: 'success',
          description: 'Campaign saved to draft',
        });
        setLoading(false);
        return;
      }
      if (templateId && campaignName) {
        //only template id is there
        const draft_url = `/${account_id}/campaign/create/template/${templateId}?campaign_name=${name}`;
        await createDraftCampaign(name, list, account_id, templateId, draft_url);
        toast({
          variant: 'success',
          description: 'Campaign saved to draft',
        });
        setLoading(false);
        return;
      }
      if (campaignName) {
        const draft_url = `/${account_id}/campaign/create/template?campaign_name=${name}`;
        await createDraftCampaign(name, list, account_id, templateId, draft_url);
        toast({
          variant: 'success',
          description: 'Campaign saved to draft',
        });
        setLoading(false);
        return;
      }
      setLoading(false);
      return;
    } catch (error: unknown) {
      if (error instanceof Error) {
        toast({
          variant: 'destructive',
          description: error.message,
        });
      } else {
        toast({
          variant: 'destructive',
          description: 'Something went wrong. Please try again',
        });
      }

      setLoading(false);
    }
  };
  return (
    <Dialog>
      <DialogTrigger asChild>
        <Button className="mb-0" type="button" variant="destructive">
          Cancel
        </Button>
      </DialogTrigger>
      {campaignName ? (
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Save Campaign to draft?</DialogTitle>
            <DialogDescription>Do you want to save the campaign to draft or discard?</DialogDescription>
          </DialogHeader>
          <Input type="text" value={name} onChange={(e) => setName(e.target.value)} />
          <DialogFooter>
            <Link prefetch={false} href={`/${account_id}/campaign`}>
              <Button type="button" variant={'outline'}>
                Discard
              </Button>
            </Link>

            {loading ? (
              <Button disabled type="button" variant={'secondary'}>
                <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                Saving to Draft
              </Button>
            ) : (
              <Button onClick={handleAddCampaignToDraft} type="button" variant={'secondary'}>
                Save to Draft
              </Button>
            )}
          </DialogFooter>
        </DialogContent>
      ) : (
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Discard Changes?</DialogTitle>
            <DialogDescription>Discard changes or continue editing campaign</DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <Link prefetch={false} href={`/${account_id}/campaign`}>
              <Button type="button" variant={'outline'}>
                Close
              </Button>
            </Link>
            <DialogClose asChild>
              <Button type="button" variant={'secondary'}>
                Continue
              </Button>
            </DialogClose>
          </DialogFooter>
        </DialogContent>
      )}
    </Dialog>
  );
};

export default CancelButton;
