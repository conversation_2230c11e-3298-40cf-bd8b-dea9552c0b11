import { createComponentsCarousel } from '@/app/[account_id]/send-message/[id]/components/helper';
import MessagePreviewCarousel from '@/app/[account_id]/templates/manage/[template_operation]/components/MessagePreviewCarousel';
import { whatsApptoHtml } from '@/app/[account_id]/templates/manage/[template_operation]/components/utils';
import { ITemplateDatabase } from '@/lib/types';
import clsx from 'clsx';
import React from 'react';

const TemplatePreviewCarousel = ({ template, fromLiveChat }: { template: ITemplateDatabase; fromLiveChat?: boolean }) => {
  if (template.type == 'carousel-template') {
    const componentsArray = template.template_body.components[1]?.cards || [];
    const body = template.template_body.components[0];
    const cards = componentsArray.map((eachCard) => createComponentsCarousel(eachCard));
    return (
      <div className={clsx(!fromLiveChat && 'lg:w-80 w-52')}>
        <div className="sticky top-12">
          <div className="bg-white p-4">
            <div id="message-preview">
              <MessagePreviewCarousel body={body} cards={cards} fromLiveChat={fromLiveChat} />
            </div>
          </div>
        </div>
      </div>
    );
  }
};

export default TemplatePreviewCarousel;
