import React, { useState } from 'react';

interface MultiPurposeDropdownProps {
  options: string[];
}

const MultiPurposeDropdown: React.FC<MultiPurposeDropdownProps> = ({ options }) => {
  const [selectedOptions, setSelectedOptions] = useState<string[]>([]);
  const [filteredOptions, setFilteredOptions] = useState<string[]>([]);
  const [inputValue, setInputValue] = useState<string>('');

  const handleInputChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const value = event.target.value;
    setInputValue(value);

    // Filter options based on input value
    const filtered = options.filter((option) => option.toLowerCase().includes(value.toLowerCase()) && !selectedOptions.includes(option));
    setFilteredOptions(filtered);
  };

  const handleOptionClick = (option: string) => {
    // Add selected option to the list and remove it from filtered options
    setSelectedOptions([...selectedOptions, option]);
    setFilteredOptions(filteredOptions.filter((item) => item !== option));
    setInputValue('');
  };

  const handleCreateOption = () => {
    if (inputValue.trim() !== '' && !selectedOptions.includes(inputValue)) {
      setSelectedOptions([...selectedOptions, inputValue]);
      setInputValue('');
    }
  };

  return (
    <div className="relative">
      <div className="flex flex-wrap gap-2">
        {selectedOptions.map((option) => (
          <div key={option} className="bg-gray-200 px-2 py-1 rounded-full">
            {option}
            <button
              className="ml-1 rounded-full focus:outline-none hover:text-gray-600"
              onClick={() => setSelectedOptions(selectedOptions.filter((item) => item !== option))}
            >
              <svg className="h-4 w-4 fill-current" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24">
                <path d="M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z" />
              </svg>
            </button>
          </div>
        ))}
      </div>
      {inputValue.trim() !== '' && !filteredOptions.includes(inputValue) && !selectedOptions.includes(inputValue) && (
        <button onClick={handleCreateOption} className="mt-2 mr-2 px-2 py-1 bg-blue-500 text-white rounded hover:bg-blue-600">
          Create "{inputValue}"
        </button>
      )}
      <div className="mt-2 flex flex-wrap gap-2 items-center border border-gray-300 rounded px-3 py-2 cursor-text focus-within:ring-2 focus-within:ring-blue-500">
        <input
          type="text"
          value={inputValue}
          onChange={handleInputChange}
          placeholder="Type to search or create..."
          className="flex-1 outline-none bg-white placeholder-gray-500"
        />
      </div>

      {filteredOptions.length > 0 && (
        <div className="absolute mt-1 w-full bg-white border border-gray-300 rounded shadow-md">
          {filteredOptions.map((option) => (
            <div key={option} className="cursor-pointer px-3 py-2 hover:bg-gray-100" onClick={() => handleOptionClick(option)}>
              {option}
            </div>
          ))}
        </div>
      )}
    </div>
  );
};

export default MultiPurposeDropdown;
