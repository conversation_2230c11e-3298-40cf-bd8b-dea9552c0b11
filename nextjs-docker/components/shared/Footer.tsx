import { Heart } from 'lucide-react';
import Image from 'next/image';
import Link from 'next/link';

export default function Footer() {
  return (
    <footer className="bg-white border-t">
      <div className="w-full mx-auto px-4 sm:px-6 lg:px-8 py-6">
        <div className="md:flex md:items-center md:justify-between">
          <Link href="/" className="flex items-center">
            <Image src={'/assets/WT_Logo.png'} width={30} height={30} alt="logo" className="mt-2 w-auto" />
            <span className="ml-2 text-xl font-bold text-gray-400">WeTarseel</span>
          </Link>
          <p className="text-base text-gray-400 md:mt-0 md:order-1">&copy; {new Date().getFullYear()} WeTarseel. All rights reserved.</p>
          <div className="flex items-center space-x-1 text-gray-400">
            <span>Made with</span>
            <Heart className="h-5 w-5 mx-1 text-red-400 fill-current" />
            <span>by WeTarseel</span>
          </div>
        </div>
      </div>
    </footer>
  );
}
