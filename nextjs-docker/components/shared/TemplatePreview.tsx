import { ITemplateDatabase } from '@/lib/types';
import MessagePreview from './MessagePreview';
import { createComponents } from '@/app/[account_id]/send-message/[id]/components/helper';
import clsx from 'clsx';

const TemplatePreview = ({ template, fromLiveChat }: { template: ITemplateDatabase; fromLiveChat?: boolean }) => {
  const { header, body, footer, callToActionButtons, quickReplyButtons } = createComponents({
    components: template.template_body.components,
    template,
  });
  return (
    <div className={clsx(!fromLiveChat && 'flex-col space-y-2 max-w-[360px] bg-white p-4 rounded-md')}>
      <MessagePreview
        body={body}
        footer={footer}
        callToActionButtons={callToActionButtons}
        header={header}
        quickReplyButtons={quickReplyButtons}
        fromLiveChat={fromLiveChat}
      />
    </div>
  );
};

export default TemplatePreview;
