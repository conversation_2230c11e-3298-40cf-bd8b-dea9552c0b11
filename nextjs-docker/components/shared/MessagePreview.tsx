'use client';
import { Icon } from '@iconify/react/dist/iconify.js';
import { MenuIcon, SquareArrowOutUpRightIcon } from 'lucide-react';
import React from 'react';
import { IBody, ICallToActionBtn, <PERSON>ooter, IHeader, IQuickReplyBtn } from '../../app/[account_id]/templates/manage/[template_operation]/types';
import { whatsApptoHtml } from '@/app/[account_id]/templates/manage/[template_operation]/components/utils';
import clsx from 'clsx';
import HeaderPreview from './HeaderPreview';

interface MessagePreviewProps {
  quickReplyButtons: IQuickReplyBtn[];
  callToActionButtons: ICallToActionBtn[];
  header: IHeader;
  body: IBody;
  footer: IFooter;
  fromLiveChat?: boolean;
  fromManageTemplate?: boolean;
}

const MessagePreview: React.FC<MessagePreviewProps> = ({ quickReplyButtons, callToActionButtons, header, body, footer, fromLiveChat, fromManageTemplate }) => {
  const replaceVariablesToText = (TYPE: string) => {
    let str = '';
    let regexPattern = /\{\{\d+\}\}/g;
    let matches = [];
    let values = undefined;
    try {
      if (TYPE == 'HEADER') {
        matches = [...header.text.matchAll(regexPattern)];
        values = header.example?.header_text;
        str = header.text;
      } else {
        matches = [...body.text.matchAll(regexPattern)];
        values = body.example?.body_text;
        str = body.text;
      }
      // Iterate through all matches
      for (let i = 0; i < matches.length; i++) {
        // Replace the match with the corresponding value
        if (values?.[i]) {
          str = str.replace(matches[i][0], values[i]);
        }
      }
      const myString = whatsApptoHtml(str, fromManageTemplate);
      return myString;
    } catch (error) {
      return str;
    }
  };

  return (
    <div id="enter-message" className={clsx(!fromLiveChat && "bg-[url('/assets/whatsapp_convo_bg.png')] p-4 rounded-lg bg-[#e5ddd5]")}>
      <div className="bg-white rounded-lg shadow">
        <div className="p-4">
          <HeaderPreview header={header} />
          <div
            dangerouslySetInnerHTML={{ __html: replaceVariablesToText('BODY') }}
            key={`body-item`}
            className="text-gray-800 flex-wrap text-wrap break-words text-sm whitespace-pre-wrap"
          />
          <div className="text-gray-600 flex-wrap text-wrap break-words text-xs">{footer?.text ?? ''}</div>
          <div className="text-right text-xs text-gray-600 my-2">2:34 PM</div>
        </div>
        {(quickReplyButtons.length > 0 || callToActionButtons.length > 0) && <div className="h-[1px] bg-gray-200" />}
        {callToActionButtons
          .slice(
            0,
            quickReplyButtons.length + callToActionButtons.length > 3
              ? callToActionButtons.length > quickReplyButtons.length
                ? Math.abs(quickReplyButtons.length - callToActionButtons.length) - 1
                : Math.max(0, callToActionButtons.length - quickReplyButtons.length)
              : 3
          )
          .map((btn, index) => (
            <div key={`${btn.type}-index`}>
              <div className="flex items-center justify-center gap-3 text-[#00a5f4] text-sm my-2">
                {btn.type == 'PHONE_NUMBER' ? (
                  <Icon icon="ic:round-phone" className="text-[#00a5f4] " />
                ) : btn.type == 'Visit Website' ? (
                  <SquareArrowOutUpRightIcon className="text-[#00a5f4] h-4 w-4" />
                ) : (
                  <Icon icon="mdi:share" className="text-[#00a5f4]" />
                )}{' '}
                {btn.text}
              </div>
              <div className="h-[1px] bg-gray-200 mx-4" />
            </div>
          ))}
        {quickReplyButtons.slice(0, quickReplyButtons.length + callToActionButtons.length > 3 ? 2 : 3).map((btn, index) => (
          <div key={`${btn.type}-${index}`}>
            <div className="flex items-center justify-center gap-3 text-[#00a5f4] text-sm my-2">
              <Icon icon="mdi:share" className="text-[#00a5f4]" /> {btn.text}
            </div>
            <div className="h-[1px] bg-gray-200 mx-4" />
          </div>
        ))}
        {quickReplyButtons.length + callToActionButtons.length > 3 && (
          <div className="flex items-center justify-center gap-3 text-[#00a5f4] text-sm my-2 pb-2">
            <MenuIcon className="text-[#00a5f4] h-4 w-4" /> See all options
          </div>
        )}
      </div>
    </div>
  );
};

export default React.memo(MessagePreview);
