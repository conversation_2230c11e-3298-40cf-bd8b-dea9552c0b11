'use client';

import { Account } from '@/lib/types';
import { Button } from '../ui/button';
import Link from 'next/link';
import { Link2 } from 'lucide-react';

const ConnectYourBusiness = ({ account }: { account: Account }) => {
  return (
    <div className="p-4 bg-white shadow-md rounded-md space-y-4">
      <div className="text-lg text-center text-gray-700">
        Connect your Business <span className="text-black font-semibold">{account.name}</span> with <span className="text-blue-500 font-semibold">Meta</span> to
        acccess this feature
      </div>
      <div className="text-center">
        <Button variant={'secondary'} asChild>
          <Link href="dashboard">
            <Link2 className="mr-2 h-5 w-5" />
            Connect Now
          </Link>
        </Button>
      </div>
    </div>
  );
};

export default ConnectYourBusiness;
