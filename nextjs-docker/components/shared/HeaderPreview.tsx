import React from 'react';
import Image from 'next/image';
import { IHeader } from '../../app/[account_id]/templates/manage/[template_operation]/types';

interface HeaderPreviewProps {
  header: IHeader;
}

const HeaderPreview: React.FC<HeaderPreviewProps> = ({ header }) => {
  const headerFile = header.file as File;
  return (
    <div className="text-gray-800 font-bold flex-wrap text-wrap break-words">
      {header.format === 'TEXT' && header.text}
      {/* if there is header.file and type image*/}
      {header.format === 'IMAGE' && header?.file && (
        <Image
          height={600}
          width={1125}
          src={URL.createObjectURL(headerFile)}
          alt="header"
          className="rounded-lg object-cover"
          style={{ aspectRatio: '1125 / 600' }}
        />
      )}
      {/* if there is no header file but fileurl and type image */}
      {!header.file && header.fileUrl && header.format === 'IMAGE' && (
        <>
          {header.fileUrl ? (
            <Image height={600} width={1125} src={header.fileUrl} alt="header" className="rounded-lg object-cover" style={{ aspectRatio: '1125 / 600' }} />
          ) : (
            <div className="bg-slate-300 rounded-lg w-full h-40 my-4" />
          )}
        </>
      )}
      {header.format === 'VIDEO' && header?.file && <video height={250} width={400} src={URL.createObjectURL(headerFile)} controls />}
      {!header.file && header.fileUrl && header.format === 'VIDEO' && (
        <>{header.fileUrl ? <video height={250} width={400} src={header.fileUrl} controls /> : <div className="bg-slate-300 rounded-lg w-full h-40 my-4" />}</>
      )}
      {header.format === 'DOCUMENT' && header?.file && (
        <a
          href={URL.createObjectURL(headerFile)}
          target="_blank"
          rel="noopener noreferrer"
          className="my-4 rounded-lg bg-gray-100 p-4 flex items-center hover:bg-gray-200"
        >
          <span className="ml-2 font-semibold text-sm">{headerFile.name}</span>
        </a>
      )}
      {header.fileUrl && header.format === 'DOCUMENT' && (
        <a href={header.fileUrl} target="_blank" rel="noopener noreferrer" className="my-4 rounded-lg bg-gray-100 p-4 flex items-center hover:bg-gray-200">
          <span className="font-semibold text-sm ml-2 break-all">
            {header.fileUrl ? header.fileUrl.split('/').pop() || 'Download Document' : 'Download Document'}
          </span>
        </a>
      )}
    </div>
  );
};

export default React.memo(HeaderPreview);
