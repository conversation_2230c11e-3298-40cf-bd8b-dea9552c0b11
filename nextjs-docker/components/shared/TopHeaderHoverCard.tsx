'use client';
import { HoverCard, HoverCardContent, HoverCardTrigger } from '@/components/ui/hover-card';
import { Icon } from '@iconify/react';
import clsx from 'clsx';
import dayjs from 'dayjs';

type CardProps = {
  assigned_limit: number;
  remaining_limit: number;
  limit_end_date: Date;
};
const TopHeaderHoverCard = ({ assigned_limit, remaining_limit, limit_end_date }: CardProps) => {
  return (
    <HoverCard>
      <HoverCardTrigger asChild>
        <div className="sm:flex hidden items-center space-x-2 hover:cursor-help">
          <div className="font-bold">
            <span className={clsx('', assigned_limit == 0 && 'text-red-500')}>{assigned_limit}</span>
            <span className={clsx('', remaining_limit == 0 && 'text-red-500')}>/{remaining_limit}</span>
          </div>
          <Icon icon="material-symbols:info" className="text-gray-400" />
        </div>
      </HoverCardTrigger>
      <HoverCardContent className="w-80  text-xs">
        <div>
          Message limit: <span className="font-bold">{assigned_limit}</span>
        </div>
        <div>
          Remaining Messages to send: <span className="font-bold">{remaining_limit}</span>
        </div>
        <div>
          Limit Resets: <span className="font-bold">{dayjs(limit_end_date).format('MMMM D, YYYY')}</span>
        </div>
        {dayjs(limit_end_date).diff(dayjs(), 'days') == 0 && <div className="font-bold text-red-500">Limit ends today</div>}
      </HoverCardContent>
    </HoverCard>
  );
};

export default TopHeaderHoverCard;
