'use client';
import { ArrowLeftRight, CreditCard, LifeBuoy, LogOut, Settings, User as UserIcon, Users } from 'lucide-react';

import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuGroup,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuShortcut,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';

import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';

import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/components/ui/alert-dialog';

import { clearImpersonate, impersonate } from '@/lib/actions';
import { Account, User } from '@/lib/types';
import { useDialog } from '@/state/hooks/use-dialog';
import { logoutUser } from '@/state/user/user';
import Link from 'next/link';
import { usePathname } from 'next/navigation';
import { Button } from '../ui/button';

const ProfileDropDown = ({
  impersonated,
  account,
  isSuperuser,
  user,
  user_avatar,
  users,
}: {
  impersonated: boolean;
  account: Account;
  isSuperuser: boolean;
  user: User;
  user_avatar: string | undefined;
  users: User[] | undefined;
}) => {
  const pathname = usePathname();
  const deactivateLeadDialog = useDialog();
  return (
    <>
      {isSuperuser && (
        <DropdownMenu>
          <DropdownMenuTrigger>Open Impersonations</DropdownMenuTrigger>
          <DropdownMenuContent>
            <DropdownMenuLabel>Agents</DropdownMenuLabel>
            <DropdownMenuSeparator />
            {users
              ?.filter((user) => user.type == 'agent')
              .map((user) => (
                <DropdownMenuItem key={user.id} onClick={async () => await impersonate(user.id)}>
                  {user.name}
                </DropdownMenuItem>
              ))}
            <DropdownMenuLabel>Admins</DropdownMenuLabel>
            <DropdownMenuSeparator />
            {users
              ?.filter((user) => user.type == 'admin')
              .map((user) => (
                <DropdownMenuItem key={user.id} onClick={async () => await impersonate(user.id)}>
                  {user.name}
                </DropdownMenuItem>
              ))}
          </DropdownMenuContent>
        </DropdownMenu>
      )}
      {impersonated && (
        <Button variant={'outline'} onClick={async () => await clearImpersonate()}>
          Clear Impersonation
        </Button>
      )}
      <DropdownMenu modal={false}>
        <DropdownMenuTrigger asChild>
          <Avatar className="hover:cursor-pointer border border-gray-200">
            <AvatarImage src={user_avatar} alt="@shadcn" />
            <AvatarFallback className="font-bold hover:bg-black hover:text-white">
              {user &&
                user.name
                  .toUpperCase()
                  .split(' ')
                  .map((chars) => chars.charAt(0))
                  .join('')}
            </AvatarFallback>
          </Avatar>
        </DropdownMenuTrigger>
        <DropdownMenuContent className="w-56">
          <DropdownMenuLabel>{user?.name}</DropdownMenuLabel>
          <DropdownMenuSeparator />
          <DropdownMenuGroup>
            <Link href={`/${account?.id}/profile`}>
              <DropdownMenuItem className={`${pathname.includes('profile') && 'bg-slate-100'}`}>
                <UserIcon className="mr-2 h-4 w-4" />
                <span>Profile</span>
              </DropdownMenuItem>
            </Link>
            <Link href={`/${account?.id}/billing`}>
              <DropdownMenuItem>
                <CreditCard className="mr-2 h-4 w-4" />
                <span>Billing</span>
              </DropdownMenuItem>
            </Link>
            <DropdownMenuItem>
              <Settings className="mr-2 h-4 w-4" />
              <span>Settings</span>
              <DropdownMenuShortcut>Coming Soon</DropdownMenuShortcut>
            </DropdownMenuItem>
          </DropdownMenuGroup>
          <DropdownMenuSeparator />
          <DropdownMenuGroup>
            <Link prefetch={false} href={`/${account?.id}/agent`}>
              <DropdownMenuItem className={`${pathname == `/${account?.id}/agent` && 'bg-slate-100'}`}>
                <Users className="mr-2 h-4 w-4" />
                <span>Agents</span>
              </DropdownMenuItem>
            </Link>
            <Link prefetch={false} href={`/business/select`}>
              <DropdownMenuItem className={`${pathname.includes('business') && 'bg-slate-100'}`}>
                <ArrowLeftRight className="mr-2 h-4 w-4" />
                <span>Switch Business</span>
              </DropdownMenuItem>
            </Link>
          </DropdownMenuGroup>
          <DropdownMenuSeparator />
          <DropdownMenuItem>
            <LifeBuoy className="mr-2 h-4 w-4" />
            <span>Support</span>
            <DropdownMenuShortcut>Coming Soon</DropdownMenuShortcut>
          </DropdownMenuItem>
          <DropdownMenuSeparator />

          <DropdownMenuItem {...deactivateLeadDialog.triggerProps}>
            <LogOut className="mr-2 h-4 w-4" />
            <span>Log out</span>
          </DropdownMenuItem>
        </DropdownMenuContent>
      </DropdownMenu>

      <AlertDialog {...deactivateLeadDialog.dialogProps}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Are you sure you want to logout?</AlertDialogTitle>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction onClick={() => logoutUser()}>Continue</AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </>
  );
};

export default ProfileDropDown;
