'use client';

/**
 * Facebook Login Button Component for WhatsApp Business API Integration
 *
 * This component handles the WhatsApp Business onboarding flow through Facebook's
 * embedded signup process. It manages the authentication flow, processes session events,
 * and handles phone number selection for WhatsApp Business accounts.
 */

import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/components/ui/alert-dialog';
import { But<PERSON> } from '@/components/ui/button';
import { revalidateFullPath } from '@/lib/actions';
import { getAccount, getAccountByWabaId, updateAccountDetails, updateAccount } from '@/lib/pocket';
import { Account, IPhoneVerificationResponse } from '@/lib/types';
import { PATHS } from '@/lib/utils';
import { Loader2, Phone } from 'lucide-react';
import { useParams, usePathname } from 'next/navigation';
import { useEffect, useState } from 'react';
import { Badge } from './ui/badge';
import { useToast } from './ui/use-toast';

// Facebook Login with JavaScript SDK
export const dynamic = 'force-dynamic';
export default function FBLoginButton({ appId, className, btnTitle }: { appId?: string; className: string; btnTitle: string }) {
  const pathname = usePathname();
  const [firstTimeLoading, setFirstTimeLoading] = useState(true);
  const [accountsByWabaId, setAccountsByWabaId] = useState<Account[] | undefined>(undefined);
  const [fbWindowOpened, setFbWindowOpened] = useState(false);
  const [loading, setLoading] = useState(false);
  const [showSelectPhoneIdDialog, setShowSelectPhoneIdDialog] = useState(false);
  const [phoneData, setPhoneData] = useState<IPhoneVerificationResponse | null>(null);
  const [businessArray, setBusinessArray] = useState<IPhoneVerificationResponse[]>([]);
  useEffect(() => {
    console.log('FBLoginButton mounted with appId:', appId, window.FB);
    // Initialize Facebook SDK if appId is provided
    if (appId) {
      if (window.FB) {
        window.FB.getLoginStatus(function (response: any) {
          setFirstTimeLoading(false);
        });
      }
      window.fbAsyncInit = function () {
        window.FB.init({
          appId: appId,
          cookie: true,
          xfbml: true,
          version: 'v19.0',
        });

        window.FB.AppEvents.logPageView();

        window.FB.getLoginStatus(function (response: any) {
          setFirstTimeLoading(false);
          console.log('log status change');
        });

        // Add session event listener for WhatsApp embedded signup events
        window.FB.Event.subscribe('session.event', handleSessionEvent);
      };

      (function (d, s, id) {
        var js: HTMLScriptElement,
          fjs = d.getElementsByTagName(s)[0];
        if (d.getElementById(id)) {
          return;
        }
        js = d.createElement(s) as HTMLScriptElement;
        js.id = id;
        js.src = 'https://connect.facebook.net/en_US/sdk.js';
        if (fjs && fjs.parentNode) {
          fjs.parentNode.insertBefore(js, fjs);
        }
      })(document, 'script', 'facebook-jssdk');
    }
  }, [appId]);
  const params = useParams<{ account_id: string }>;
  const accountId = params().account_id;
  const { toast } = useToast();

  // Handler for Facebook session events including WhatsApp embedded signup events
  const handleSessionEvent = async (response: any) => {
    console.log('Session event received:', response);

    // Check if this is a WhatsApp embedded signup event
    if (response.type === 'WA_EMBEDDED_SIGNUP' && response.event === 'FINISH_WHATSAPP_BUSINESS_APP_ONBOARDING' && response.data?.waba_id) {
      try {
        // Get WhatsApp Business account details
        const wabaId = response.data.waba_id;
        const accessToken = window.localStorage.getItem('access_token');

        // Get business app status
        const statusResponse = await checkBusinessAppStatus(wabaId);
        console.log('Business app status:', statusResponse);
        await updateAccount(accountId, {
          is_biz_app: statusResponse.is_on_biz_app,
        });

        // Get associated phone numbers
        const phoneNumbers = await fetchPhoneNumbers(wabaId);

        if (phoneNumbers && phoneNumbers.length > 0) {
          // Auto-select the first phone number and skip manual selection
          const phoneData = phoneNumbers[0];

          // Update account details with phone data
          await updateAccountDetails(accountId, phoneData);

          // Initiate WhatsApp data sync for this phone
          await syncWhatsAppData(phoneData.id, accessToken);

          // Initiate messages sync history
          await syncMessageHistory(phoneData.id, accessToken);

          toast({
            description: 'Your app is connected to our system, messages will sync now',
            variant: 'success',
          });

          // Refresh the page to show updated data
          revalidateFullPath(pathname);
        } else {
          toast({
            description: 'Connected successfully, but no phone numbers found',
            variant: 'destructive',
          });
        }
      } catch (error) {
        console.error('Error processing WhatsApp onboarding:', error);
        toast({
          description: 'Connected, but failed to complete setup automatically',
          variant: 'destructive',
        });
      }
    }
  };

  /**
   * Initiates the WhatsApp data synchronization process
   * @param {string} phoneId - The WhatsApp Business phone ID
   * @param {string} accessToken - The access token for authentication
   * @returns {Promise<boolean>} Success status of the sync operation
   */
  const syncWhatsAppData = async (phoneId: string, accessToken: string | null): Promise<boolean> => {
    try {
      const response = await fetch('/api/whatsapp-sync', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          phoneId,
          accessToken,
          syncType: 'smb_app_state_sync',
        }),
      });

      const data: {
        messaging_product: 'whatsapp';
        request_id: string;
      } = await response.json();

      updateAccount(accountId, {
        sync_request_id: data.request_id,
      });

      if (!response.ok) {
        console.error('Error syncing WhatsApp data:', data);
        return false;
      }

      console.log('WhatsApp data sync initiated successfully:', data);
      return true;
    } catch (error) {
      console.error('Failed to trigger WhatsApp data sync:', error);
      return false;
    }
  };

  /**
   * Initiates the WhatsApp message history synchronization process
   * @param {string} phoneId - The WhatsApp Business phone ID
   * @param {string} accessToken - The access token for authentication
   * @returns {Promise<boolean>} Success status of the sync operation
   */
  const syncMessageHistory = async (phoneId: string, accessToken: string | null): Promise<boolean> => {
    try {
      const response = await fetch('/api/whatsapp-sync', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          phoneId,
          accessToken,
          syncType: 'history',
        }),
      });

      const data: {
        messaging_product: 'whatsapp';
        request_id: string;
      } = await response.json();

      updateAccount(accountId, {
        history_sync_request_id: data.request_id,
      });

      if (!response.ok) {
        console.error('Error syncing WhatsApp message history:', data);
        return false;
      }

      console.log('WhatsApp message history sync initiated successfully:', data);
      return true;
    } catch (error) {
      console.error('Failed to trigger WhatsApp message history sync:', error);
      return false;
    }
  };

  /**
   * Fetches the access token using the authentication code from Facebook
   * @param {string} code - Authentication code from Facebook
   * @returns {Promise<string>} Access token
   */
  const fetchAccessToken = async (code: string): Promise<string> => {
    try {
      const response = await fetch(`/api/facebook?code=${code}`);
      const data = await response.json();
      const accessToken = data.access_token;

      if (accessToken) {
        window.localStorage.setItem('access_token', accessToken);
      }

      return accessToken;
    } catch (error) {
      console.error('Error fetching access token:', error);
      throw error;
    }
  };

  /**
   * Retrieves WhatsApp Business ID using the access token
   * @param {string} accessToken - Facebook access token
   * @returns {Promise<string>} WhatsApp Business ID (waba_id)
   */
  const getWabaId = async (accessToken: string): Promise<string> => {
    try {
      const response = await fetch(`/api/facebook-data?input_token=${accessToken}&accountId=${accountId}`);
      const data = await response.json();

      if (data.message) {
        toast({
          description: data.message,
          variant: 'destructive',
        });
        throw new Error(data.message);
      }

      return data.waba_id;
    } catch (error) {
      console.error('Error getting WABA ID:', error);
      throw error;
    }
  };

  /**
   * Fetches phone numbers associated with a WhatsApp Business account
   * @param {string} wabaId - WhatsApp Business ID
   * @returns {Promise<Array>} Array of phone numbers and their details
   */
  const fetchPhoneNumbers = async (wabaId: string): Promise<any[]> => {
    try {
      // Get account details
      const account = await getAccount(accountId);
      setAccountsByWabaId(await getAccountByWabaId(account.waba_id));

      // Fetch phone verification data
      const response = await fetch(`${PATHS[process.env.NODE_ENV]}/api/get-phone-verification?waba_id=${account.waba_id}`, { cache: 'no-store' });
      const data = await response.json();

      // Format the business array data
      return data.data.map((d: IPhoneVerificationResponse) => ({
        verified_name: d.verified_name,
        display_phone_number: d.display_phone_number,
        code_verification_status: d.code_verification_status,
        id: d.id,
      }));
    } catch (error) {
      console.error('Error fetching phone numbers:', error);
      throw error;
    }
  };

  /**
   * Checks if the WhatsApp Business account is using the Facebook Business App
   * @param {string} wabaId - WhatsApp Business ID
   * @returns {Promise<{is_on_biz_app: boolean, platform_type: string}>} Business app status
   */
  const checkBusinessAppStatus = async (wabaId: string): Promise<{ is_on_biz_app?: boolean; platform_type?: string }> => {
    try {
      // Fetch business app status from Facebook Graph API
      const response = await fetch(`/api/facebook-biz-app-status?waba_id=${wabaId}`, { cache: 'no-store' });

      if (!response.ok) {
        throw new Error(`Failed to fetch business app status: ${response.statusText}`);
      }

      const data = await response.json();

      // Log for debugging purposes
      console.log('Business app status:', data);

      return {
        is_on_biz_app: data.is_on_biz_app,
        platform_type: data.platform_type,
      };
    } catch (error) {
      console.error('Error checking business app status:', error);
      // Return default values instead of throwing to prevent breaking the flow
      return { is_on_biz_app: false, platform_type: 'unknown' };
    }
  };

  async function asyncFacebookLogin(response: any) {
    // When user completes or cancels the login process
    if (!response.authResponse) {
      toast({
        description: 'Uhoh! Not connected!',
        variant: 'destructive',
      });
      setFbWindowOpened(false);
      return;
    }

    // Successfully authenticated
    const code = response.authResponse.code;
    setFbWindowOpened(false);
    setLoading(true);

    try {
      // Step 1: Get access token from authentication code
      const accessToken = await fetchAccessToken(code);
      if (!accessToken) throw new Error('Failed to retrieve access token');

      // Step 2: Get WhatsApp Business ID using the access token
      const wabaId = await getWabaId(accessToken);
      if (!wabaId) throw new Error('Failed to retrieve WABA ID');

      // Step 3: Register system user with the WABA
      await fetch(`/api/retrieve-system-user-id-and-save?waba_id=${wabaId}&accountId=${accountId}`);

      // Step 4: Fetch phone numbers for the WABA
      const phoneNumbers = await fetchPhoneNumbers(wabaId);

      // Step 5: Update UI to show phone selection dialog
      if (phoneNumbers && phoneNumbers.length > 0) {
        setBusinessArray(phoneNumbers);
        setShowSelectPhoneIdDialog(true);
      } else {
        toast({
          description: 'No phone numbers found for this WhatsApp Business account',
          variant: 'destructive',
        });
      }
    } catch (err) {
      console.error('Error in WhatsApp signup flow:', err);
      toast({
        description: 'An error occurred during WhatsApp signup',
        variant: 'destructive',
      });
      setFbWindowOpened(false);
    } finally {
      setLoading(false);
    }
  }
  function facebookLogin(response: any) {
    asyncFacebookLogin(response);
  }

  /**
   * Main function that initiates the WhatsApp Business signup process
   * This function:
   * 1. Tracks the conversion with Facebook Pixel
   * 2. Launches the Facebook login popup
   * 3. Processes the authentication response
   * 4. Retrieves WhatsApp Business account info
   * 5. Shows the phone number selection dialog
   */
  function launchWhatsAppSignup() {
    try {
      // Track conversion with Facebook Pixel
      window.fbq &&
        window.fbq('trackCustom', 'WhatsAppOnboardingStart', {
          appId: appId,
          feature: 'whatsapp_embedded_signup',
        });

      // Configure login options for WhatsApp Business onboarding
      const loginOptions = {
        config_id: '****************', // Config for generating super token
        response_type: 'code', // Required for System User access token
        override_default_response_type: true,
        extras: {
          setup: {},
          featureType: 'whatsapp_business_app_onboarding',
          sessionInfoVersion: '3',
        },
      };

      // Launch Facebook login popup
      window.FB.login(facebookLogin, loginOptions);
    } catch (error) {
      console.error('Error launching WhatsApp signup:', error);
      toast({
        description: 'Failed to launch WhatsApp signup',
        variant: 'destructive',
      });
      setFbWindowOpened(false);
      setLoading(false);
    }
  }

  return (
    <>
      <AlertDialog open={showSelectPhoneIdDialog} onOpenChange={setShowSelectPhoneIdDialog}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Select Business</AlertDialogTitle>
            <AlertDialogDescription>Select your appropriate business from the list below.</AlertDialogDescription>
          </AlertDialogHeader>
          <div className="flex flex-col space-y-2">
            {businessArray.map((business: IPhoneVerificationResponse) => {
              let businessAlreadyAdded = accountsByWabaId && accountsByWabaId.filter((_account) => _account.phone_id == business.id)[0] ? true : false;
              return (
                <div
                  key={business.id}
                  className="flex flex-col sm:flex-row items-start sm:items-center justify-between gap-4 p-4 rounded-lg border bg-card hover:bg-accent/50 transition-colors"
                >
                  <div className="space-y-1">
                    <div className="flex items-center gap-2">
                      <h3 className="font-medium">{business.verified_name}</h3>
                    </div>
                    <div className="flex items-center gap-2 text-sm text-muted-foreground">
                      <Phone className="w-3 h-3" />
                      {business.display_phone_number}
                    </div>
                    {businessAlreadyAdded && <div className=" text-sm text-muted-foreground">Business already added</div>}
                  </div>
                  {!businessAlreadyAdded && (
                    <Button
                      onClick={() => {
                        setPhoneData(business);
                      }}
                      type="button"
                      variant={phoneData && phoneData.id === business.id ? 'default' : 'outline'}
                    >
                      {phoneData && phoneData.id === business.id ? 'Selected' : 'Select'}
                    </Button>
                  )}
                </div>
              );
            })}
          </div>
          <AlertDialogFooter>
            <AlertDialogAction asChild>
              {phoneData ? (
                <Button
                  type="button"
                  variant={'secondary'}
                  onClick={() => {
                    updateAccountDetails(accountId, phoneData);
                    setLoading(false);
                    revalidateFullPath(pathname);
                  }}
                >
                  Done
                </Button>
              ) : (
                <Button type="button" disabled variant={'secondary'}>
                  Done
                </Button>
              )}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
      {loading ? (
        <Button disabled variant={'link'}>
          <Loader2 className="animate-spin mr-2" />
          Loading Please wait
        </Button>
      ) : fbWindowOpened ? (
        <Button disabled variant={'link'}>
          <Loader2 className="animate-spin mr-2" />
          Facebook window is opened.
        </Button>
      ) : firstTimeLoading ? (
        <Button disabled variant={'link'}>
          <Loader2 className="animate-spin mr-2" />
          Initializing
        </Button>
      ) : (
        <Button
          onClick={() => {
            setFbWindowOpened(true);
            launchWhatsAppSignup();
          }}
          type="button"
          // className={className}
          variant="link"
        >
          {btnTitle}
        </Button>
      )}
    </>
  );
}
