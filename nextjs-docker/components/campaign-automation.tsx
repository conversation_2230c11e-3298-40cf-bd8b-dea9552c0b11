'use client';

import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { Textarea } from '@/components/ui/textarea';
import { PlusCircle } from 'lucide-react';
import { ITemplateDatabase } from '@/lib/types';
import { createAutomation } from '@/lib/pocket';
import { useParams, useRouter } from 'next/navigation';

export function CampaignAutomationComponent({ templates }: { templates: ITemplateDatabase[] }) {
  const [selectedTemplate, setSelectedTemplate] = useState<string | undefined>();
  const [automationName, setAutomationName] = useState('');
  const [automationDescription, setAutomationDescription] = useState('');

  const router = useRouter();
  const { account_id } = useParams();
  const handleCreateAutomation = async () => {
    // Here you would typically send the data to your backend
    console.log('Creating automation:', {
      template: selectedTemplate,
      name: automationName,
      description: automationDescription,
    });
    const res = await createAutomation({
      account: account_id as string,
      template: selectedTemplate,
      name: automationName,
      description: automationDescription,
    });
    const searchParams = new URLSearchParams({ automation_id: res.id });
    // Reset form
    setSelectedTemplate(undefined);
    setAutomationName('');
    setAutomationDescription('');
    router.push(`/${account_id}/flows/create-automation?${searchParams.toString()}`);
  };

  return (
    <div className="mx-auto py-8">
      <div className="mb-6">
        <CardTitle>Create an automation specific to your campaign</CardTitle>
        <p>
          This kind of automation will <strong>only</strong> run if a message is recieved against this template
        </p>
      </div>

      <div className="flex space-x-8 w-full">
        <Card className="flex-grow">
          <CardHeader>
            <CardTitle>Select a Template</CardTitle>
            <CardDescription>Choose a template for your automation campaign</CardDescription>
          </CardHeader>
          <CardContent>
            <RadioGroup value={selectedTemplate || ''} onValueChange={setSelectedTemplate}>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {templates
                  .filter((t) => t.template_name !== 'blank')
                  .map((template) => (
                    <Label
                      key={template.id}
                      htmlFor={template.id}
                      className="flex items-center space-x-3 border border-slate-200 rounded-lg p-4 cursor-pointer hover:bg-slate-100 dark:border-slate-800 dark:hover:bg-slate-800"
                    >
                      <RadioGroupItem value={template.id} id={template.id} />
                      <div>
                        <p className="font-medium">{template.template_name}</p>
                        <p className="text-sm text-slate-500 dark:text-slate-400">{template.category}</p>
                      </div>
                    </Label>
                  ))}
              </div>
            </RadioGroup>
          </CardContent>
        </Card>

        <Card className="flex-grow">
          <CardHeader>
            <CardTitle>Customize Your Automation</CardTitle>
            <CardDescription>Provide details for your new automation</CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="automation-name">Automation Name</Label>
              <Input id="automation-name" placeholder="Enter automation name" value={automationName} onChange={(e) => setAutomationName(e.target.value)} />
            </div>
            <div className="space-y-2">
              <Label htmlFor="automation-description">Description</Label>
              <Textarea
                id="automation-description"
                placeholder="Describe your automation"
                value={automationDescription}
                onChange={(e) => setAutomationDescription(e.target.value)}
              />
            </div>
          </CardContent>
          <CardFooter>
            {selectedTemplate ? (
              <Button variant="secondary" onClick={handleCreateAutomation} disabled={!automationName}>
                <PlusCircle className="mr-2 h-4 w-4" />
                Create Automation
              </Button>
            ) : (
              <Button variant="secondary" disabled>
                Select a template
              </Button>
            )}
          </CardFooter>
        </Card>
      </div>
    </div>
  );
}
