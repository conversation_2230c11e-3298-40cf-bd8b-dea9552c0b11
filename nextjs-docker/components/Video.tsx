import { memo } from 'react';

export const Video = memo(function MyVideo({ file, src, className }: { file: File | null; src?: string; className?: string }) {
  if (!file && !src) return null;
  if (src) return <video height={250} width={400} src={src} controls className={className} />;
  if (file) return <video height={250} width={400} src={URL.createObjectURL(file)} controls className={className} />;
});
