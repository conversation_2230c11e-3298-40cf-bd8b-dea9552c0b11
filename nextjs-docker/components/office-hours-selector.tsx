import { useState } from 'react';
import { Clock } from 'lucide-react';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Label } from '@/components/ui/label';
import { Button } from '@/components/ui/button';

const generateTimeOptions = () => {
  const options = [];
  for (let hour = 0; hour < 24; hour++) {
    for (let minute = 0; minute < 60; minute += 30) {
      const time = `${hour.toString().padStart(2, '0')}:${minute.toString().padStart(2, '0')}`;
      options.push(time);
    }
  }
  return options;
};

export function OfficeHoursSelector({ setTime }: { setTime: (startTime: string, endTime: string) => void }) {
  const [startTime, setStartTime] = useState('09:00');
  const [endTime, setEndTime] = useState('17:00');
  const [error, setError] = useState('');

  const timeOptions = generateTimeOptions();

  const handleStartTimeChange = (time: string) => {
    setStartTime(time);
    if (time >= endTime) {
      setError('Start time must be before end');
    } else {
      setError('');
    }
  };

  const handleEndTimeChange = (time: string) => {
    setEndTime(time);
    if (time <= startTime) {
      setError('End time must be after start');
    } else {
      setError('');
    }
  };

  const handleSubmit = () => {
    if (startTime < endTime) {
      console.log(`Office hours set from ${startTime} to ${endTime}`);
      setTime(startTime, endTime);
      // Here you would typically send this data to your backend
    } else {
      setError('Invalid time range');
    }
  };

  return (
    <div className="w-full max-w-md mx-auto p-2 space-y-4 bg-white rounded-lg shadow dark:bg-slate-950">
      <h2 className="text-2xl font-bold text-center text-slate-950 dark:text-slate-50">Set Office Hours</h2>
      <div className="space-y-2">
        <Label htmlFor="start-time">Start Time</Label>
        <Select value={startTime} onValueChange={handleStartTimeChange}>
          <SelectTrigger id="start-time" className="w-full">
            <Clock className="mr-2 h-4 w-4" />
            <SelectValue placeholder="Select start time" />
          </SelectTrigger>
          <SelectContent>
            {timeOptions.map((time) => (
              <SelectItem key={time} value={time}>
                {time}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>
      <div className="space-y-2">
        <Label htmlFor="end-time">End Time</Label>
        <Select value={endTime} onValueChange={handleEndTimeChange}>
          <SelectTrigger id="end-time" className="w-full">
            <Clock className="mr-2 h-4 w-4" />
            <SelectValue placeholder="Select end time" />
          </SelectTrigger>
          <SelectContent>
            {timeOptions.map((time) => (
              <SelectItem key={time} value={time}>
                {time}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>
      {error && <p className="text-red-500 text-sm dark:text-red-900">{error}</p>}
      <Button onClick={handleSubmit} className="w-full">
        Set Office Hours
      </Button>
    </div>
  );
}
