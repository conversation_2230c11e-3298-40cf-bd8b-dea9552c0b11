// flowHelpers.test.ts
import { FlowBot } from './flowHelpers';
import { beforeEach, describe, expect, MockInstance, test, vi } from 'vitest';
import { data } from './complexflowdata';
import { type Edge, type Node } from '@xyflow/react';
import { Account } from './types';

describe('Complex FlowBot', () => {
  let flowBot: FlowBot;
  let sendInteractiveOrTextMessageMock: MockInstance;
  let updateStateMock: MockInstance;
  let assignAgentMock: MockInstance;

  beforeEach(() => {
    flowBot = new FlowBot({
      conversationId: 'convo1',
      account: {
        id: 'account1',
        name: 'Account 1',
      } as any as Account,
      flow: {
        name: 'Flow 1',
        flow: {
          nodes: data.nodes as Node[],
          edges: data.edges,
        },
      },
      phoneNo: '**********',
      leadId: 'lead1',
      state: '',
      userMessage: '',
    });

    sendInteractiveOrTextMessageMock = vi.spyOn(flowBot, 'sendInteractiveOrTextMessage').mockImplementation((data) => {
      return Promise.resolve();
    });

    updateStateMock = vi.spyOn(flowBot, 'updateState').mockImplementation(({ node }) => {
      flowBot.state = `Flow 1:${node.id}`;
      return Promise.resolve();
    });

    assignAgentMock = vi.spyOn(flowBot, 'assignAgent').mockImplementation(({ node }) => {
      return Promise.resolve();
    });
  });

  test('should create an instance of FlowBot', () => {
    expect(flowBot).toBeInstanceOf(FlowBot);
  });

  test('test if keyword matching for the message works', async () => {
    flowBot.userMessage = 'demo';
    flowBot.state = '';
    await flowBot.runFlow();

    const SendMessage2 = data.nodes.find((node) => node.id === 'Send Message2');
    expect(sendInteractiveOrTextMessageMock).toHaveBeenCalledOnce();
    expect(updateStateMock).toHaveBeenCalledWith({ node: SendMessage2 });
    // expect(sendInteractiveOrTextMessageMock).toHaveBeenCalledWith();
  });

  test('messages working', async () => {
    const SendMessage5 = data.nodes.find((node) => node.id === 'Send Message5');
    flowBot.userMessage = 'Yes';
    flowBot.state = 'Flow 1:Send Message2';
    await flowBot.runFlow();

    expect(sendInteractiveOrTextMessageMock).toHaveBeenCalledOnce();
    expect(assignAgentMock).toHaveBeenCalledTimes(0);
    expect(updateStateMock).toHaveBeenCalledWith({ node: SendMessage5 });
  });

  // test other flows
  test('test if multi message nodes work', async () => {
    const SendMessage9 = data.nodes.find((node) => node.id === 'Send Message9');
    flowBot.userMessage = 'Price';
    flowBot.state = 'Flow 1:Send Message5';
    await flowBot.runFlow();
    expect(sendInteractiveOrTextMessageMock).toHaveBeenCalledTimes(2);
    expect(updateStateMock).toHaveBeenCalledWith({ node: SendMessage9 });
  });

  test('Test if assign agent works', async () => {
    flowBot.state = 'Flow 1:Send Message5';
    flowBot.userMessage = 'Talk to Agent';
    await flowBot.runFlow();
    expect(assignAgentMock).toHaveBeenCalledTimes(1);
  });

  test('Test some more flows', async () => {
    flowBot.state = 'Flow 1:Send Message14';
    flowBot.userMessage = 'I am not interested';

    await flowBot.runFlow();
    expect(sendInteractiveOrTextMessageMock).toHaveBeenCalledTimes(1);
    expect(updateStateMock).toHaveBeenCalledTimes(1);
    expect(updateStateMock).toHaveBeenCalledWith({ node: data.nodes.find((node) => node.id === 'Send Message17') });
  });
});
