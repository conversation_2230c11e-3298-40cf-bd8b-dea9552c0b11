export const data = {
  edges: [
    {
      id: 'xy-edge__Send Message2_1Send Message2_1-Send Message5Send Message5',
      source: 'Send Message2_1',
      sourceHandle: 'Send Message2_1',
      target: 'Send Message5',
      targetHandle: 'Send Message5',
    },
    {
      id: 'xy-edge__Send Message9_1Send Message9_1-Assign agent12Assign agent12',
      source: 'Send Message9_1',
      sourceHandle: 'Send Message9_1',
      target: 'Assign agent12',
      targetHandle: 'Assign agent12',
    },
    {
      id: 'xy-edge__Send Message9_2Send Message9_2-Assign agent12Assign agent12',
      source: 'Send Message9_2',
      sourceHandle: 'Send Message9_2',
      target: 'Assign agent12',
      targetHandle: 'Assign agent12',
    },
    {
      id: 'xy-edge__Send Message8Send Message8-Send Message9Send Message9',
      source: 'Send Message8',
      sourceHandle: 'Send Message8',
      target: 'Send Message9',
      targetHandle: 'Send Message9',
    },
    {
      id: 'xy-edge__Send Message5_1Send Message5_1-Send Message8Send Message8',
      source: 'Send Message5_1',
      sourceHandle: 'Send Message5_1',
      target: 'Send Message8',
      target<PERSON>andle: 'Send Message8',
    },
    {
      id: 'xy-edge__Send Message5_2Send Message5_2-Assign agent12Assign agent12',
      source: 'Send Message5_2',
      sourceHandle: 'Send Message5_2',
      target: 'Assign agent12',
      targetHandle: 'Assign agent12',
    },
    {
      id: 'xy-edge__Send Message2_2Send Message2_2-Send Message5Send Message5',
      source: 'Send Message2_2',
      sourceHandle: 'Send Message2_2',
      target: 'Send Message5',
      targetHandle: 'Send Message5',
    },
    {
      id: 'xy-edge__On Message1On Message1-Send Message2Send Message2',
      source: 'On Message1',
      sourceHandle: 'On Message1',
      target: 'Send Message2',
      targetHandle: 'Send Message2',
    },
    {
      id: 'xy-edge__Send Message5_3Send Message5_3-Send Message14Send Message14',
      source: 'Send Message5_3',
      sourceHandle: 'Send Message5_3',
      target: 'Send Message14',
      targetHandle: 'Send Message14',
    },
    {
      id: 'xy-edge__Send Message14_1Send Message14_1-Assign agent12Assign agent12',
      source: 'Send Message14_1',
      sourceHandle: 'Send Message14_1',
      target: 'Assign agent12',
      targetHandle: 'Assign agent12',
    },
    {
      id: 'xy-edge__Send Message14_2Send Message14_2-Send Message17Send Message17',
      source: 'Send Message14_2',
      sourceHandle: 'Send Message14_2',
      target: 'Send Message17',
      targetHandle: 'Send Message17',
    },
    {
      id: 'xy-edge__Send Message17_1Send Message17_1-Send Message22Send Message22',
      source: 'Send Message17_1',
      sourceHandle: 'Send Message17_1',
      target: 'Send Message22',
      targetHandle: 'Send Message22',
    },
    {
      id: 'xy-edge__Send Message17_2Send Message17_2-Send Message22Send Message22',
      source: 'Send Message17_2',
      sourceHandle: 'Send Message17_2',
      target: 'Send Message22',
      targetHandle: 'Send Message22',
    },
    {
      id: 'xy-edge__Send Message17_3Send Message17_3-Send Message22Send Message22',
      source: 'Send Message17_3',
      sourceHandle: 'Send Message17_3',
      target: 'Send Message22',
      targetHandle: 'Send Message22',
    },
    {
      id: 'xy-edge__Send Message17_4Send Message17_4-Send Message22Send Message22',
      source: 'Send Message17_4',
      sourceHandle: 'Send Message17_4',
      target: 'Send Message22',
      targetHandle: 'Send Message22',
    },
  ],
  nodes: [
    {
      data: {
        keywords: [
          {
            label: 'whatsapp Marketing',
            value: 'whatsapp Marketing',
          },
          {
            label: 'demo',
            value: 'demo',
          },
          {
            label: 'hi',
            value: 'hi',
          },
          {
            label: 'hello',
            value: 'hello',
          },
          {
            label: 'hey',
            value: 'hey',
          },
          {
            label: 'hi',
            value: 'hi',
          },
          {
            label: 'hello',
            value: 'hello',
          },
          {
            label: 'pricing',
            value: 'pricing',
          },
        ],
        label: 'On Message',
      },
      id: 'On Message1',
      measured: {
        height: 134,
        width: 321,
      },
      position: {
        x: 200,
        y: 200,
      },
      selected: false,
      type: 'On Message',
    },
    {
      data: {
        label: 'Send Message',
        options: [
          {
            id: 'Send Message2_1',
            label: 'Option 1',
          },
          {
            id: 'Send Message2_2',
            label: 'Option 2',
          },
        ],
        text: 'Hi,\n\nI’d like to introduce you to We Tarseel—our WhatsApp marketing platform perfect for small businesses. It supports multi-agents, automatic flows and replies, broadcast messages, and customer listing for easy management.\n\nInterested in a quick demo? Let me know!\n\n',
      },
      dragging: false,
      height: 440,
      id: 'Send Message2',
      measured: {
        height: 440,
        width: 433,
      },
      position: {
        x: 690,
        y: 176,
      },
      selected: false,
      type: 'Send Message',
    },
    {
      data: {
        label: 'Button',
        parentId: 'Send Message2',
        text: 'Yes',
      },
      extent: 'parent',
      height: 80,
      id: 'Send Message2_1',
      measured: {
        height: 80,
        width: 174,
      },
      parentId: 'Send Message2',
      position: {
        x: 10,
        y: 260,
      },
      selected: false,
      type: 'button',
      width: 174,
    },
    {
      data: {
        label: 'Button',
        parentId: 'Send Message2',
        text: 'No',
      },
      extent: 'parent',
      height: 80,
      id: 'Send Message2_2',
      measured: {
        height: 80,
        width: 174,
      },
      parentId: 'Send Message2',
      position: {
        x: 10,
        y: 350,
      },
      selected: false,
      type: 'button',
      width: 174,
    },
    {
      data: {
        label: 'Send Message',
        options: [
          {
            id: 'Send Message5_1',
            label: 'Option 1',
          },
          {
            id: 'Send Message5_2',
            label: 'Option 2',
          },
          {
            id: 'Send Message5_3',
            label: 'Option 3',
          },
        ],
        text: 'Here you can a quick overview 1min video . \n\nhttps://www.youtube.com/watch?v=MAZbAhytSSs\n\nDo you want to more info?',
      },
      dragging: false,
      height: 546,
      id: 'Send Message5',
      measured: {
        height: 546,
        width: 433,
      },
      position: {
        x: 1311,
        y: 156,
      },
      selected: false,
      type: 'Send Message',
    },
    {
      data: {
        label: 'Button',
        parentId: 'Send Message5',
        text: 'Price',
      },
      extent: 'parent',
      height: 80,
      id: 'Send Message5_1',
      measured: {
        height: 80,
        width: 174,
      },
      parentId: 'Send Message5',
      position: {
        x: 10,
        y: 260,
      },
      selected: false,
      type: 'button',
      width: 174,
    },
    {
      data: {
        label: 'Button',
        parentId: 'Send Message5',
        text: 'Talk to Agent',
      },
      extent: 'parent',
      height: 80,
      id: 'Send Message5_2',
      measured: {
        height: 80,
        width: 174,
      },
      parentId: 'Send Message5',
      position: {
        x: 10,
        y: 350,
      },
      selected: false,
      type: 'button',
      width: 174,
    },
    {
      data: {
        label: 'Send Message',
        options: [],
        selectedImage: {
          id: 'Send Message8_11',
          url: 'https://node.taskmate.ae/api/files/9wv2tekahneayd0/5byfne2gwtnc32p/whatsapp_marketing_pk_zn_if_etv2_yz_YuzJIK6tIs.png',
        },
        text: 'Price plan starting from 5000 Rs Per month with 5000 messages and 5 Agents. Which includes \n\n•\tWhatsApp API\n•\tCRM\n•\tSegmentation\n•\tSchedule Campaigns\n•\tBroadcast Messaging\n•\tAgents\n',
      },
      dragging: false,
      height: 260,
      id: 'Send Message8',
      measured: {
        height: 260,
        width: 433,
      },
      position: {
        x: 1641.1287237033507,
        y: 802.281957413287,
      },
      selected: false,
      type: 'Send Message',
    },
    {
      data: {
        label: 'Send Message',
        options: [
          {
            id: 'Send Message9_1',
            label: 'Option 1',
          },
          {
            id: 'Send Message9_2',
            label: 'Option 2',
          },
        ],
        text: 'Do you want to have a discussion with agent?',
      },
      dragging: false,
      height: 440,
      id: 'Send Message9',
      measured: {
        height: 440,
        width: 433,
      },
      position: {
        x: 1229.37677645052,
        y: 1172.098237198747,
      },
      selected: false,
      type: 'Send Message',
    },
    {
      data: {
        label: 'Button',
        parentId: 'Send Message9',
        text: 'Yes',
      },
      extent: 'parent',
      height: 80,
      id: 'Send Message9_1',
      measured: {
        height: 80,
        width: 174,
      },
      parentId: 'Send Message9',
      position: {
        x: 10,
        y: 260,
      },
      selected: false,
      type: 'button',
      width: 174,
    },
    {
      data: {
        label: 'Button',
        parentId: 'Send Message9',
        text: 'No',
      },
      extent: 'parent',
      height: 80,
      id: 'Send Message9_2',
      measured: {
        height: 80,
        width: 174,
      },
      parentId: 'Send Message9',
      position: {
        x: 10,
        y: 350,
      },
      selected: false,
      type: 'button',
      width: 174,
    },
    {
      data: {
        label: 'Assign agent',
        selectedAgent: 'xxuyf3nf0htcdh2',
      },
      dragging: false,
      id: 'Assign agent12',
      measured: {
        height: 86,
        width: 321,
      },
      position: {
        x: 2028.787376161515,
        y: 1497.1821322077976,
      },
      selected: false,
      type: 'Assign agent',
    },
    {
      data: {
        label: 'Button',
        parentId: 'Send Message5',
        text: 'Demo in Urdu',
      },
      extent: 'parent',
      height: 80,
      id: 'Send Message5_3',
      measured: {
        height: 80,
        width: 174,
      },
      parentId: 'Send Message5',
      position: {
        x: 10,
        y: 440,
      },
      selected: false,
      type: 'button',
      width: 174,
    },
    {
      data: {
        label: 'Send Message',
        options: [
          {
            id: 'Send Message14_1',
            label: 'Option 1',
          },
          {
            id: 'Send Message14_2',
            label: 'Option 2',
          },
        ],
        text: 'Follow below Link \n\nhttps://youtu.be/dBSHPLSLA94',
      },
      dragging: false,
      height: 456,
      id: 'Send Message14',
      measured: {
        height: 456,
        width: 433,
      },
      position: {
        x: 2080.8635578772446,
        y: 404.05635602126563,
      },
      selected: false,
      type: 'Send Message',
    },
    {
      data: {
        label: 'Button',
        parentId: 'Send Message14',
        text: 'Talk to Agent',
      },
      extent: 'parent',
      height: 80,
      id: 'Send Message14_1',
      measured: {
        height: 80,
        width: 174,
      },
      parentId: 'Send Message14',
      position: {
        x: 10,
        y: 276,
      },
      selected: false,
      type: 'button',
      width: 174,
    },
    {
      data: {
        label: 'Button',
        parentId: 'Send Message14',
        text: 'I am not interested',
      },
      extent: 'parent',
      height: 80,
      id: 'Send Message14_2',
      measured: {
        height: 80,
        width: 174,
      },
      parentId: 'Send Message14',
      position: {
        x: 10,
        y: 366,
      },
      selected: false,
      type: 'button',
      width: 174,
    },
    {
      data: {
        label: 'Send Message',
        options: [
          {
            id: 'Send Message17_1',
            label: 'Option 1',
          },
          {
            id: 'Send Message17_2',
            label: 'Option 2',
          },
          {
            id: 'Send Message17_3',
            label: 'Option 3',
          },
          {
            id: 'Send Message17_4',
            label: 'Option 4',
          },
        ],
        text: 'Opps please let me know why are you not interested.',
      },
      dragging: false,
      height: 636,
      id: 'Send Message17',
      measured: {
        height: 636,
        width: 433,
      },
      position: {
        x: 2611.8921508789062,
        y: 560,
      },
      selected: true,
      type: 'Send Message',
    },
    {
      data: {
        label: 'Button',
        parentId: 'Send Message17',
        text: 'I am going for another product',
      },
      extent: 'parent',
      height: 80,
      id: 'Send Message17_1',
      measured: {
        height: 80,
        width: 174,
      },
      parentId: 'Send Message17',
      position: {
        x: 10,
        y: 276,
      },
      selected: false,
      type: 'button',
      width: 174,
    },
    {
      data: {
        label: 'Button',
        parentId: 'Send Message17',
        text: 'I did not understand',
      },
      extent: 'parent',
      height: 80,
      id: 'Send Message17_2',
      measured: {
        height: 80,
        width: 174,
      },
      parentId: 'Send Message17',
      position: {
        x: 10,
        y: 366,
      },
      selected: false,
      type: 'button',
      width: 174,
    },
    {
      data: {
        label: 'Button',
        parentId: 'Send Message17',
        text: 'I need morre info',
      },
      extent: 'parent',
      height: 80,
      id: 'Send Message17_3',
      measured: {
        height: 80,
        width: 174,
      },
      parentId: 'Send Message17',
      position: {
        x: 10,
        y: 456,
      },
      selected: false,
      type: 'button',
      width: 174,
    },
    {
      data: {
        label: 'Button',
        parentId: 'Send Message17',
        text: 'I was looking for something else',
      },
      extent: 'parent',
      height: 80,
      id: 'Send Message17_4',
      measured: {
        height: 80,
        width: 174,
      },
      parentId: 'Send Message17',
      position: {
        x: 10,
        y: 546,
      },
      selected: false,
      type: 'button',
      width: 174,
    },
    {
      data: {
        label: 'Send Message',
        text: 'Thank you for your response',
      },
      dragging: false,
      height: 260,
      id: 'Send Message22',
      measured: {
        height: 260,
        width: 433,
      },
      position: {
        x: 3438.1070251464844,
        y: 740,
      },
      selected: false,
      type: 'Send Message',
    },
  ],
  viewport: {
    x: -651.8651662856973,
    y: -343.7373996636569,
    zoom: 0.5,
  },
};
