export const data = {
  nodes: [
    {
      id: 'save_data1',
      position: {
        x: 408.74816085427335,
        y: 221.4989800215365,
      },
      type: 'Save Data',
      data: {
        label: 'Save Data',
        text: 'test',
      },
      measured: {
        width: 202,
        height: 86,
      },
      selected: true,
      dragging: false,
    },
    {
      id: 'on_message2',
      position: {
        x: 59.909872117730174,
        y: 71.01019978463532,
      },
      type: 'On Message',
      data: {
        label: 'On Message',
      },
      measured: {
        width: 114,
        height: 42,
      },
      selected: false,
      dragging: false,
    },
    {
      id: 'service3',
      position: {
        x: 93.892130861449,
        y: 176.12010888011326,
      },
      type: 'Send Message',
      data: {
        label: 'Send Message',
        text: 'hi',
        options: [
          {
            id: 1,
            label: 'Option 1',
          },
          {
            id: 2,
            label: 'Option 2',
          },
        ],
      },
      measured: {
        width: 248,
        height: 342,
      },
      selected: false,
      dragging: false,
    },
    {
      id: 'service3_1',
      position: {
        x: 10,
        y: 162,
      },
      type: 'button',
      extent: 'parent',
      parentId: 'service3',
      width: 174,
      height: 80,
      data: {
        label: 'Button',
        text: '1',
      },
      measured: {
        width: 174,
        height: 80,
      },
      selected: false,
    },
    {
      id: 'service3_2',
      position: {
        x: 10,
        y: 242,
      },
      type: 'button',
      extent: 'parent',
      parentId: 'service3',
      width: 174,
      height: 80,
      data: {
        label: 'Button',
        text: '2',
      },
      measured: {
        width: 174,
        height: 80,
      },
      selected: false,
    },
  ],
  edges: [
    {
      source: 'on_message2',
      sourceHandle: 'on_message2',
      target: 'service3',
      targetHandle: 'service3',
      id: 'xy-edge__on_message2on_message2-service3service3',
    },
    {
      source: 'service3_1',
      sourceHandle: 'service3_1',
      target: 'save_data1',
      targetHandle: 'a',
      id: 'xy-edge__service3_1service3_1-save_data1a',
    },
    {
      source: 'service3_2',
      sourceHandle: 'service3_2',
      target: 'save_data1',
      targetHandle: 'a',
      id: 'xy-edge__service3_2service3_2-save_data1a',
    },
  ],
  viewport: {
    x: 13.47189008637747,
    y: -152.3606580864514,
    zoom: 1.4419288714602252,
  },
};
