import { Queue } from 'bullmq';
import { QNAME } from './utils';
const _connection = {
  password: 'a5gcersboVQDY9cgz8CUDwUxf5hwyAktol0Hm2nbqnLJhDvofM9Gg0Wmoyh6raMD',
  host: '************',
  port: 5433,
};

const myQueue = new Queue(QNAME, {
  connection: _connection,
  defaultJobOptions: {
    attempts: 1,
    removeOnComplete: {
      count: 20, //keep up to 20 jobs
    },
  },
});

export { myQueue };
