import { type ClassValue, clsx } from 'clsx';
import dayjs from 'dayjs';
import { v4 as uuidv4 } from 'uuid';
import jsep from 'jsep';
import { twMerge } from 'tailwind-merge';
import { Account, ICarouselParams, ICountryCurrencyMap, IExpandedLead, ILead, ILeadReferral, ITeamplateParams, ITemplateDatabase } from './types';
import { ICallToActionBtn, IFooter, IQuickReplyBtn, ITemplateObject } from '@/app/[account_id]/templates/manage/[template_operation]/types';

export const QNAME = process.env.NEXT_PUBLIC_QNAME ?? 'production';

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs));
}

export const getUrl = (url: string): string => {
  // const path = process.env.NODE_ENV === 'production' ? '/next' : '';
  // const path = '/next'
  const path = '/';
  return `${path}${url}`;
};

export const convertFirstToUpperCaseCell = (row: any, cell: string) => {
  let value = row.getValue(cell) as string;
  if (!value) return value;
  if (value.length === 0) return value;
  value = value.charAt(0).toUpperCase() + value.slice(1).toLowerCase();
  return value;
};

export const convertFirstToUpperCase = (data: string) => {
  return data.charAt(0).toUpperCase() + data.slice(1).toLowerCase();
};

export const getMenuList = (accountId: string, userType: string, userName: string, dashboard_options: string[]) => {
  const navItems = [
    {
      name: 'dashboard',
      text: 'Dashboard',
      icon: 'material-symbols:dashboard-outline',
      iconActive: 'material-symbols:dashboard',
      href: `/${accountId}/dashboard`,
    },
    (userType === 'admin' || dashboard_options?.includes('live-chat')) && {
      name: 'live-chat',
      text: 'Live Chat',
      icon: 'mdi:conversation-outline',
      iconActive: 'mdi:conversation',
      href: `/${accountId}/live-chat`,
    },
    (userType === 'admin' || dashboard_options?.includes('lead-management')) && {
      name: 'lead-management',
      text: 'Contacts',
      icon: 'mdi:account',
      iconActive: 'mdi:account',
      href: `/${accountId}/lead-management`,
    },
    (userType === 'admin' || dashboard_options?.includes('templates')) && {
      name: 'templates',
      text: 'Templates',
      icon: 'material-symbols:account-tree-outline',
      iconActive: 'material-symbols:account-tree',
      href: `/${accountId}/templates`,
      // onClick: handleTemplatesClick,
    },
    (userType === 'admin' || dashboard_options?.includes('campaigns')) && {
      name: 'campaign',
      text: 'Campaign',
      icon: 'material-symbols:campaign-outline',
      iconActive: 'material-symbols:campaign-sharp',
      href: `/${accountId}/campaign`,
    },
    // (userType === "admin" || dashboard_options?.includes("rate_reviews")) && {
    //   name: "rateReview",
    //   text: "Rate Review",
    //   icon: "material-symbols:rate-review-outline",
    //   iconActive: "material-symbols:rate-review",
    //   href: `/${accountId}/admin`,
    // },
    // (userType === "admin" || dashboard_options?.includes("report")) && {
    {
      name: 'report',
      text: 'Reports',
      icon: 'material-symbols:book-5-outline',
      iconActive: 'material-symbols:book-5',
      href: `/${accountId}/report`,
    },
    (userType === 'agents' || dashboard_options?.includes('agents') || userType === 'admin') && {
      name: 'agent',
      text: 'Agents',
      icon: 'material-symbols:support-agent',
      iconActive: 'material-symbols:support-agent',
      href: `/${accountId}/agent`,
    },
    (userType === 'admin' || dashboard_options?.includes('flows')) && {
      name: 'flow',
      text: 'Flows',
      icon: 'material-symbols:support-agent',
      iconActive: 'material-symbols:support-agent',
      href: `/${accountId}/flows`,
    },
    // (userType === "admin" || dashboard_options?.includes("interactive_message")) && {
    //   name: "interactive-messages",
    //   text: "Interactive Message",
    //   icon: "material-symbols:interactive-space-outline-rounded",
    //   iconActive: "material-symbols:interactive-space-rounded",
    //   href: `/${accountId}/interactive-messages`,
    // },
    (userType === 'admin' || dashboard_options?.includes('messaging-limits')) && {
      name: 'messaging-limits',
      text: 'Messaging Limits',
      icon: 'mdi:message-outline',
      iconActive: 'mdi:message',
      href: `/${accountId}/messaging-limits`,
    },
    (userType === 'admin' || dashboard_options?.includes('api-settings')) && {
      name: 'api-settings',
      text: 'API Settings',
      icon: 'ant-design:api-twotone',
      iconActive: 'ant-design:api-twotone',
      href: `/${accountId}/api-settings`,
    },
    (userType === 'admin' || dashboard_options?.includes('office-settings')) && {
      name: 'office-settings',
      text: 'Office Settings',
      icon: 'ant-design:api-twotone',
      iconActive: 'ant-design:api-twotone',
      href: `/${accountId}/office-settings`,
    },
    userName === USERNAME && {
      name: 'business-settings',
      text: 'Business Settings',
      icon: 'ph:gear',
      iconActive: 'ph:gear-fill',
      href: `/${accountId}/business-settings`,
    },
    // {
    //   name: 'business-settings',
    //   text: 'Business Settings',
    //   icon: 'ph:gear',
    //   iconActive: 'ph:gear-fill',
    //   href: `/${accountId}/business-settings`,
    // },
  ].filter(Boolean) as Array<{
    name: string;
    text: string;
    icon: string;
    iconActive: string;
    href: string;
    onClick?: () => void;
  }>;
  return navItems;
};

export const countryCurrencyMap: ICountryCurrencyMap = {
  Afghanistan: ['AFN', 'USD'],
  Albania: ['ALL', 'USD'],
  Algeria: ['DZD', 'USD'],
  Andorra: ['EUR', 'USD'],
  Angola: ['AOA', 'USD'],
  'Antigua and Barbuda': ['XCD', 'USD'],
  Argentina: ['ARS', 'USD'],
  Armenia: ['AMD', 'USD'],
  Australia: ['AUD', 'USD'],
  Austria: ['EUR', 'USD'],
  Azerbaijan: ['AZN', 'USD'],
  Bahamas: ['BSD', 'USD'],
  Bahrain: ['BHD', 'USD'],
  Bangladesh: ['BDT', 'USD'],
  Barbados: ['BBD', 'USD'],
  Belarus: ['BYN', 'USD'],
  Belgium: ['EUR', 'USD'],
  Belize: ['BZD', 'USD'],
  Benin: ['XOF', 'USD'],
  Bhutan: ['BTN', 'USD'],
  Bolivia: ['BOB', 'USD'],
  'Bosnia and Herzegovina': ['BAM', 'USD'],
  Botswana: ['BWP', 'USD'],
  Brazil: ['BRL', 'USD'],
  Brunei: ['BND', 'USD'],
  Bulgaria: ['BGN', 'USD'],
  'Burkina Faso': ['XOF', 'USD'],
  Burundi: ['BIF', 'USD'],
  'Cabo Verde': ['CVE', 'USD'],
  Cambodia: ['KHR', 'USD'],
  Cameroon: ['XAF', 'USD'],
  Canada: ['CAD', 'USD'],
  'Central African Republic': ['XAF', 'USD'],
  Chad: ['XAF', 'USD'],
  Chile: ['CLP', 'USD'],
  China: ['CNY', 'USD'],
  Colombia: ['COP', 'USD'],
  Comoros: ['KMF', 'USD'],
  'Congo, Democratic Republic of the': ['CDF', 'USD'],
  'Congo, Republic of the': ['XAF', 'USD'],
  'Costa Rica': ['CRC', 'USD'],
  Croatia: ['HRK', 'USD'],
  Cuba: ['CUP', 'USD'],
  Cyprus: ['EUR', 'USD'],
  'Czech Republic': ['CZK', 'USD'],
  Denmark: ['DKK', 'USD'],
  Djibouti: ['DJF', 'USD'],
  Dominica: ['XCD', 'USD'],
  'Dominican Republic': ['DOP', 'USD'],
  'East Timor': ['USD'],
  Ecuador: ['USD'],
  Egypt: ['EGP', 'USD'],
  'El Salvador': ['USD'],
  'Equatorial Guinea': ['XAF', 'USD'],
  Eritrea: ['ERN', 'USD'],
  Estonia: ['EUR', 'USD'],
  Eswatini: ['SZL', 'USD'],
  Ethiopia: ['ETB', 'USD'],
  Fiji: ['FJD', 'USD'],
  Finland: ['EUR', 'USD'],
  France: ['EUR', 'USD'],
  Gabon: ['XAF', 'USD'],
  Gambia: ['GMD', 'USD'],
  Georgia: ['GEL', 'USD'],
  Germany: ['EUR', 'USD'],
  Ghana: ['GHS', 'USD'],
  Greece: ['EUR', 'USD'],
  Grenada: ['XCD', 'USD'],
  Guatemala: ['GTQ', 'USD'],
  Guinea: ['GNF', 'USD'],
  'Guinea-Bissau': ['XOF', 'USD'],
  Guyana: ['GYD', 'USD'],
  Haiti: ['HTG', 'USD'],
  Honduras: ['HNL', 'USD'],
  Hungary: ['HUF', 'USD'],
  Iceland: ['ISK', 'USD'],
  India: ['INR', 'USD'],
  Indonesia: ['IDR', 'USD'],
  Iran: ['IRR', 'USD'],
  Iraq: ['IQD', 'USD'],
  Ireland: ['EUR', 'USD'],
  Israel: ['ILS', 'USD'],
  Italy: ['EUR', 'USD'],
  Jamaica: ['JMD', 'USD'],
  Japan: ['JPY', 'USD'],
  Jordan: ['JOD', 'USD'],
  Kazakhstan: ['KZT', 'USD'],
  Kenya: ['KES', 'USD'],
  Kiribati: ['AUD', 'USD'],
  'Korea, North': ['KPW', 'USD'],
  'Korea, South': ['KRW', 'USD'],
  Kosovo: ['EUR', 'USD'],
  Kuwait: ['KWD', 'USD'],
  Kyrgyzstan: ['KGS', 'USD'],
  Laos: ['LAK', 'USD'],
  Latvia: ['EUR', 'USD'],
  Lebanon: ['LBP', 'USD'],
  Lesotho: ['LSL', 'USD'],
  Liberia: ['LRD', 'USD'],
  Libya: ['LYD', 'USD'],
  Liechtenstein: ['CHF', 'USD'],
  Lithuania: ['EUR', 'USD'],
  Luxembourg: ['EUR', 'USD'],
  Madagascar: ['MGA', 'USD'],
  Malawi: ['MWK', 'USD'],
  Malaysia: ['MYR', 'USD'],
  Maldives: ['MVR', 'USD'],
  Mali: ['XOF', 'USD'],
  Malta: ['EUR', 'USD'],
  'Marshall Islands': ['USD'],
  Mauritania: ['MRU', 'USD'],
  Mauritius: ['MUR', 'USD'],
  Mexico: ['MXN', 'USD'],
  Micronesia: ['USD'],
  Moldova: ['MDL', 'USD'],
  Monaco: ['EUR', 'USD'],
  Mongolia: ['MNT', 'USD'],
  Montenegro: ['EUR', 'USD'],
  Morocco: ['MAD', 'USD'],
  Mozambique: ['MZN', 'USD'],
  Myanmar: ['MMK', 'USD'],
  Namibia: ['NAD', 'USD'],
  Nauru: ['AUD', 'USD'],
  Nepal: ['NPR', 'USD'],
  Netherlands: ['EUR', 'USD'],
  'New Zealand': ['NZD', 'USD'],
  Nicaragua: ['NIO', 'USD'],
  Niger: ['XOF', 'USD'],
  Nigeria: ['NGN', 'USD'],
  'North Macedonia': ['MKD', 'USD'],
  Norway: ['NOK', 'USD'],
  Oman: ['OMR', 'USD'],
  Pakistan: ['PKR', 'USD'],
  Palau: ['USD'],
  Panama: ['PAB', 'USD'],
  'Papua New Guinea': ['PGK', 'USD'],
  Paraguay: ['PYG', 'USD'],
  Peru: ['PEN', 'USD'],
  Philippines: ['PHP', 'USD'],
  Poland: ['PLN', 'USD'],
  Portugal: ['EUR', 'USD'],
  Qatar: ['QAR', 'USD'],
  Romania: ['RON', 'USD'],
  Russia: ['RUB', 'USD'],
  Rwanda: ['RWF', 'USD'],
  'Saint Kitts and Nevis': ['XCD', 'USD'],
  'Saint Lucia': ['XCD', 'USD'],
  'Saint Vincent and the Grenadines': ['XCD', 'USD'],
  Samoa: ['WST', 'USD'],
  'San Marino': ['EUR', 'USD'],
  'Sao Tome and Principe': ['STN', 'USD'],
  'Saudi Arabia': ['SAR', 'USD'],
  Senegal: ['XOF', 'USD'],
  Serbia: ['RSD', 'USD'],
  Seychelles: ['SCR', 'USD'],
  'Sierra Leone': ['SLL', 'USD'],
  Singapore: ['SGD', 'USD'],
  Slovakia: ['EUR', 'USD'],
  Slovenia: ['EUR', 'USD'],
  'Solomon Islands': ['SBD', 'USD'],
  Somalia: ['SOS', 'USD'],
  'South Africa': ['ZAR', 'USD'],
  'South Sudan': ['SSP', 'USD'],
  Spain: ['EUR', 'USD'],
  'Sri Lanka': ['LKR', 'USD'],
  Sudan: ['SDG', 'USD'],
  Suriname: ['SRD', 'USD'],
  Sweden: ['SEK', 'USD'],
  Switzerland: ['CHF', 'USD'],
  Syria: ['SYP', 'USD'],
  Taiwan: ['TWD', 'USD'],
  Tajikistan: ['TJS', 'USD'],
  Tanzania: ['TZS', 'USD'],
  Thailand: ['THB', 'USD'],
  Togo: ['XOF', 'USD'],
  Tonga: ['TOP', 'USD'],
  'Trinidad and Tobago': ['TTD', 'USD'],
  Tunisia: ['TND', 'USD'],
  Turkey: ['TRY', 'USD'],
  Turkmenistan: ['TMT', 'USD'],
  Tuvalu: ['AUD', 'USD'],
  Uganda: ['UGX', 'USD'],
  Ukraine: ['UAH', 'USD'],
  'United Arab Emirates': ['AED', 'USD'],
  'United Kingdom': ['GBP', 'USD'],
  'United States': ['USD'],
  Uruguay: ['UYU', 'USD'],
  Uzbekistan: ['UZS', 'USD'],
  Vanuatu: ['VUV', 'USD'],
  'Vatican City': ['EUR', 'USD'],
  Venezuela: ['VES', 'USD'],
  Vietnam: ['VND', 'USD'],
  Yemen: ['YER', 'USD'],
  Zambia: ['ZMW', 'USD'],
  Zimbabwe: ['ZWL', 'USD'],
};

// Expiry time in minutes
export const expireMaxTimeInMins = 1430;

export const getConversationExpiry = (createdAt: Date) => {
  const currentDate = dayjs(new Date());
  const conversationUpdatedTime = dayjs(createdAt);
  const diffInMinutes = currentDate.diff(conversationUpdatedTime, 'minutes');
  const timeInMinsToExpire = 1440 - diffInMinutes;
  const isWithin24Hour = diffInMinutes >= 0 && diffInMinutes <= expireMaxTimeInMins;
  return { timeInMinsToExpire, isWithin24Hour };
};

export const createTemplateComponents = (template: ITemplateDatabase, lead: ILead) => {
  //check if template is there
  const headerComponent = template?.template_body.components.find((item) => item.type == 'HEADER');
  const bodyComponent = template?.template_body.components.find((item) => item.type == 'BODY');
  const imageHeader = template?.updated_template?.HEADER ?? headerComponent?.fileUrl;
  const templateParams = template?.params;
  const bodyParams = templateParams?.body as string[];
  let headerParams = templateParams?.header?.[0];
  //if headercomponent has an example header text
  if (headerComponent?.example?.header_text) {
    if (headerParams == 'custom') {
      headerParams = templateParams.defaultHeader[0];
    } else if (headerParams == 'name') {
      //if header value is a number then use default header
      if (!isNaN(Number(lead?.[headerParams]))) {
        headerParams = templateParams.defaultHeader[0];
      } else {
        headerParams = lead?.[headerParams] ?? templateParams.defaultHeader[0];
      }
    } else {
      //@ts-ignore
      headerParams = lead?.[headerParams] ?? templateParams.defaultHeader[0];
    }
  }

  const tempComponents: any = [
    {
      type: 'body',
      //check if body params event exists
      //map the vody params and get their values correspending to lead. Will always recive keys.
      //if {name, phone_number} is undefined for user then use default value
      ...(bodyComponent?.example && {
        parameters: bodyParams.map((item, index) => {
          return {
            type: 'text',
            //@ts-ignore
            text: item == 'custom' ? templateParams.defaultBody[index] : (lead?.[item] ?? templateParams.defaultBody[index]),
          };
        }),
      }),
    },
  ];

  if (headerComponent && headerComponent.format == 'IMAGE') {
    tempComponents.push({
      type: 'header',
      parameters: [{ type: 'image', image: { link: imageHeader } }],
    });
  } else if (headerComponent && headerComponent.format == 'VIDEO') {
    tempComponents.push({
      type: 'header',
      parameters: [{ type: 'video', video: { link: imageHeader } }],
    });
  } else if (headerComponent) {
    tempComponents.push({
      type: 'header',
      parameters: [
        headerComponent.example && {
          type: 'text',
          text: headerParams,
        },
      ],
    });
  }
  return tempComponents;
};

export const createIndividualTemplateComponents = (template: ITemplateDatabase, lead: ILead, fileUrl: string, params: ITeamplateParams) => {
  //check if template is there
  const headerComponent = template?.template_body.components.find((item) => item.type == 'HEADER');
  const bodyComponent = template?.template_body.components.find((item) => item.type == 'BODY');
  const imageHeader = fileUrl;
  const templateParams = params;
  const bodyParams = templateParams?.body as string[];
  let headerParams = templateParams?.header?.[0];
  //if headercomponent has an example header text
  if (headerComponent?.example?.header_text) {
    if (headerParams == 'custom') {
      headerParams = templateParams.defaultHeader[0];
    } else if (headerParams == 'name') {
      //if header value is a number then use default header
      if (!isNaN(Number(lead?.[headerParams]))) {
        headerParams = templateParams.defaultHeader[0];
      } else {
        headerParams = lead?.[headerParams] ?? templateParams.defaultHeader[0];
      }
    } else {
      //@ts-ignore
      headerParams = lead?.[headerParams] ?? templateParams.defaultHeader[0];
    }
  }

  const tempComponents: any = [
    {
      type: 'body',
      //check if body params event exists
      //map the vody params and get their values correspending to lead. Will always recive keys.
      //if {name, phone_number} is undefined for user then use default value
      ...(bodyComponent?.example && {
        parameters: bodyParams.map((item, index) => {
          return {
            type: 'text',
            //@ts-ignore
            text: item == 'custom' ? templateParams.defaultBody[index] : (lead?.[item] ?? templateParams.defaultBody[index]),
          };
        }),
      }),
    },
  ];

  if (headerComponent && headerComponent.format == 'IMAGE') {
    tempComponents.push({
      type: 'header',
      parameters: [{ type: 'image', image: { link: imageHeader } }],
    });
  } else if (headerComponent && headerComponent.format == 'VIDEO') {
    tempComponents.push({
      type: 'header',
      parameters: [{ type: 'video', video: { link: imageHeader } }],
    });
  } else if (headerComponent) {
    tempComponents.push({
      type: 'header',
      parameters: [
        headerComponent.example && {
          type: 'text',
          text: headerParams,
        },
      ],
    });
  }
  return tempComponents;
};

export const createIndividualCarouselTemplateComponent = (template: any, lead: ILead, carousel_params: ICarouselParams[], params: ICarouselParams) => {
  const carouselParams: ICarouselParams[] = carousel_params;
  const messageBodyParams: ICarouselParams = params;
  let mainComponent = [];
  let carouselComponent = {
    type: 'carousel',
    cards: [],
  } as any;
  let _messageBodyParameter = {
    type: 'body',
    parameters: [] as { type: string; text: string }[],
  };
  if ('example' in template.template_body.components[0]) {
    let messageParams: string;
    messageBodyParams?.body.forEach((item, bodyIndex) => {
      if (item == 'custom') {
        messageParams = messageBodyParams.defaultBody[bodyIndex];
      } else if (item == 'name') {
        //if header value is a number then use default header
        if (!isNaN(Number(lead?.[item]))) {
          messageParams = messageBodyParams.defaultBody[bodyIndex];
        } else {
          messageParams = lead?.[item] ?? messageBodyParams.defaultBody[bodyIndex];
        }
      } else {
        //@ts-ignore
        messageParams = lead?.[item] ?? messageBodyParams.defaultBody[bodyIndex];
      }
      _messageBodyParameter.parameters.push({ type: 'text', text: messageParams });
    });

    mainComponent.push(_messageBodyParameter);
  }
  const cards = template.template_body.components[1].cards;
  for (let index = 0; index < cards.length; index++) {
    let cardComponent = [];

    if ('example' in cards[index].components[1]) {
      let cardComponentParameter = {
        type: 'body',
        parameters: [] as { type: string; text: string }[],
      };
      let cardParams: string;
      const carouselParamsEntry = carouselParams?.find((item: any) => item.cardIndex === index);
      carouselParamsEntry?.body.forEach((item, bodyIndex) => {
        if (item == 'custom') {
          cardParams = carouselParamsEntry.defaultBody[bodyIndex];
        } else if (item == 'name') {
          //if header value is a number then use default header
          if (!isNaN(Number(lead?.[item]))) {
            cardParams = carouselParamsEntry.defaultBody[bodyIndex];
          } else {
            cardParams = lead?.[item] ?? carouselParamsEntry.defaultBody[bodyIndex];
          }
        } else {
          //@ts-ignore
          cardParams = lead?.[item] ?? carouselParamsEntry.defaultBody[bodyIndex];
        }
        cardComponentParameter.parameters.push({ type: 'text', text: cardParams });
      });

      cardComponent.push(cardComponentParameter);
    }

    const headerComponent = {
      type: 'header',
      parameters: [
        {
          type: cards[index].components[0].format.toLowerCase(),
          [cards[index].components[0].format.toLowerCase()]: {
            id: template.header_asset_id[index],
          },
        },
      ],
    };
    cardComponent.push(headerComponent);
    for (let btnIndex = 0; btnIndex < cards[index].components[2].buttons.length; btnIndex++) {
      if (cards[index].components[2].buttons[btnIndex].type == 'QUICK_REPLY') {
        cardComponent.push({
          type: 'button',
          sub_type: 'quick_reply',
          index: btnIndex,
          parameters: [
            {
              type: 'payload',
              payload: cards[index].components[2].buttons[btnIndex].text,
            },
          ],
        });
      }

      if (cards[index].components[2].buttons[btnIndex].type == 'URL') {
        cardComponent.push({
          type: 'button',
          sub_type: 'quick_reply',
          index: btnIndex,
          parameters: [
            {
              type: 'text',
              text: cards[index].components[2].buttons[btnIndex].text,
            },
          ],
        });
      }
    }
    carouselComponent.cards.push({
      card_index: index,
      components: cardComponent,
    });
  }
  mainComponent.push(carouselComponent);
  return mainComponent;
};
export const parseMessageContent = (message: string) => {
  const rules: [RegExp, string][] = [
    [/\*\s?([^\n]+)\*/g, '<b>$1</b>'], // Bold using *text*
    [/_([^_`]+)_/g, '<i>$1</i>'], // Italic using _text_
    [/^>\s*(.+)$/gm, '<p class="p-4 my-4 border-l-4 border-gray-300 text-gray-600">$1</p>'], // Blockquote
    [/(https?:\/\/[^\s)]+)/g, '<a href="$1" target="_blank" class="font-bold text-blue-600 break-all">$1</a>'], // Link
    [/([^\n]+\n?)/g, '<p>$1</p>'], // Paragraph for any text
  ];
  let currentMessage = message;
  rules.forEach((rule) => {
    currentMessage = currentMessage.replace(rule[0], rule[1]);
  });
  return currentMessage;
};

export const cleanParseMessageContent = (message: string) => {
  const cleanRules: [RegExp, string][] = [
    [/\*\s?([^\n]+)\*/g, '$1'], // Remove *text* (bold/italic formatting)
    [/_([^_`]+)_/g, '$1'], // Remove _text_ (italic formatting)
    [/^>\s*(.+)$/gm, '$1'], // Remove blockquote (>)
    [/([^\n]+\n?)/g, '$1'], // Handle remaining newlines
  ];

  let cleanedMessage = message;
  cleanRules.forEach((rule) => {
    if (cleanedMessage) {
      cleanedMessage = cleanedMessage.toString().replace(rule[0], rule[1]);
    }
  });
  return cleanedMessage;
};

export const USERNAME = 'Super-User';
export const SUPERUSERID = '4s8mk6d0b9vp1nm';
export const PATHS: Record<any, string> = {
  // development: "https://localhost:3001",
  development: process.env.NEXT_PUBLIC_APP_URL ?? 'https://localhost:3001',
  production: process.env.NEXT_PUBLIC_APP_URL ?? 'https://beta.app.wetarseel.ai',
};

function isValidBody(body: any): boolean {
  return body?.length && body.every((item: any) => item !== undefined && item !== '');
}

function validateCarousel(carouselParams: any[], exampleNum: number): boolean {
  const combinedBodyLength = carouselParams?.reduce((acc, item) => acc + item.body.length + item.defaultBody.length, 0);
  if (!carouselParams || combinedBodyLength / 2 !== exampleNum) return false;
  return carouselParams.every((param) => isValidBody(param.body) && isValidBody(param.defaultBody));
}

export const isTemplateSubmitDisabled = (template: any, cardsState: any, messageBody: any, carousel_params: any) => {
  let submitDisabled: boolean = true;
  let exampleNum: number = 0;
  let isMessageBodyExample: boolean = 'example' in template.template_body.components[0];

  template.template_body.components[1].cards.forEach((card: any) => {
    if ('example' in card.components[1]) {
      exampleNum = exampleNum + card.components[1].example.body_text[0]?.length;
      // exampleNum++;
    }
  });

  if (exampleNum === 0 && !isMessageBodyExample) {
    submitDisabled = false;
  } else {
    const combinedMessageBodyLength = messageBody?.body.length + messageBody?.defaultBody.length;
    if (exampleNum === 0 && isMessageBodyExample) {
      submitDisabled = !(
        template.template_body.components[0].example.body_text[0].length == combinedMessageBodyLength / 2 &&
        isValidBody(messageBody?.defaultBody) &&
        isValidBody(messageBody?.body)
      );
    } else if (exampleNum > 0) {
      if (validateCarousel(carousel_params, exampleNum)) {
        if (isMessageBodyExample) {
          submitDisabled = !(
            template.template_body.components[0].example.body_text[0].length == combinedMessageBodyLength / 2 &&
            isValidBody(messageBody?.defaultBody) &&
            isValidBody(messageBody?.body)
          );
        } else {
          submitDisabled = false;
        }
      } else {
        submitDisabled = true;
      }
    }
  }

  return submitDisabled;
};

export const getLimit = (type: 'basic-template' | 'carousel-template' | 'input-box' | 'messageBody') => {
  switch (type) {
    case 'basic-template':
      return 1024;
      break;
    case 'messageBody':
      return 1024;
      break;
    case 'carousel-template':
      return 160;
      break;
    case 'input-box':
      return 1024;
      break;
    default:
      return 1024;
      break;
  }
};

// convert template from our DB to whatsapp API compatible template
const dbToWhatsappTemplate = (template: ITemplateDatabase) => {
  let header = {
    type: 'HEADER',
    format: 'TEXT',
    file: null,
    fileUrl: '',
    text: '',
  };

  let body = {
    type: 'BODY',
    format: 'TEXT',
    text: '',
  };

  let footer = {
    type: 'FOOTER',
    text: '',
    disabled: false,
  };

  let quickReplyButtons: IQuickReplyBtn[] = [];

  let callToActionButtons: ICallToActionBtn[] = [];

  template.template_body.components.forEach((component) => {
    if (component.type === 'HEADER') {
      if (component.format == 'IMAGE') {
        header = {
          ...header,
          format: 'IMAGE',
          fileUrl: component.fileUrl ?? '',
        };
      }
      if (component.format == 'TEXT') {
        header = { ...header, format: 'TEXT', text: component.text };
      }
    }
    if (component.type === 'BODY') {
      body = { ...body, text: component.text };
    }
    if (component.type === 'FOOTER') {
      footer = { ...footer, text: component.text };
    }
    if (component.type === 'BUTTONS') {
      component.buttons.forEach((btn) => {
        if (btn.type === 'URL') {
          const id = uuidv4();
          const itemToAdd = {
            id,
            btnType: 'Visit Website',
            type: 'URL',
            text: btn.text,
            url: btn.url,
          };

          callToActionButtons = [...callToActionButtons, itemToAdd];
        }
        if (btn.type === 'QUICK_REPLY') {
          const id = uuidv4();
          const itemToAdd = {
            id,
            btnType: 'Marketing opt-out',
            type: 'QUICK_REPLY',
            text: 'Stop promotions',
          };
          quickReplyButtons = [...quickReplyButtons, itemToAdd];
        }
        if (btn.type === 'PHONE_NUMBER') {
          const id = uuidv4();
          const itemToAdd = {
            id,
            btnType: 'Call phone number',
            type: 'PHONE_NUMBER',
            text: btn.text,
            phone_number: btn.phone_number,
          };
          callToActionButtons = [...callToActionButtons, itemToAdd];
        }
      });
    }
  });

  const templateObject = {
    category: 'MARKETING',
    allow_category_change: true,
    id: template.template_id,
    language: template.language,
    name: template.template_name,
  } as ITemplateObject;

  const newQuickReplyButtons = [...quickReplyButtons] as IQuickReplyBtn[];
  const newFooter = { ...footer } as IFooter;

  newQuickReplyButtons.forEach((btn: IQuickReplyBtn) => {
    delete btn.id;
    delete btn.btnType;
  });

  delete newFooter.disabled;

  const newCallToActionButtons = [...callToActionButtons] as ICallToActionBtn[];
  newCallToActionButtons.forEach((btn: ICallToActionBtn) => {
    delete btn.btnType;
    delete btn.id;
  });

  const newBody = { ...body };
  const newHeader = { ...header };

  if (newHeader.format === 'TEXT') {
    newHeader.text = newHeader.text.trim();
  }

  // @ts-ignore
  delete newBody.format;

  const btns = [] as any;
  if (newQuickReplyButtons.length > 0) {
    newQuickReplyButtons.forEach((btn: ICallToActionBtn) => {
      btns.push(btn);
    });
  }
  if (newCallToActionButtons.length > 0) {
    newCallToActionButtons.forEach((btn: ICallToActionBtn) => {
      btns.push(btn);
    });
  }

  newBody.text = newBody.text.replace(/\s\s+/g, ' ').trim();
  templateObject.components = [newHeader, newBody, newFooter];

  if (btns.length > 0) {
    templateObject.components.push({
      type: 'BUTTONS',
      buttons: btns,
    });
  }
};

//upload images to facebook resumable api
export const uploadMediaToMeta = async (file: File, account: Account) => {
  const _uploadSession = await fetch(
    `https://graph.facebook.com/v19.0/${process.env.NEXT_PUBLIC_APP_ID}/uploads?file_length=${file.size}&file_type=${file.type}`,
    {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        Authorization: `Bearer ${account.access_token}`,
      },
    }
  );
  const uploadSession = await _uploadSession.json();

  //upload session id to meta
  const _fileUrl = await fetch(`https://graph.facebook.com/v19.0/${uploadSession.id}`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      Authorization: `OAuth ${account.access_token}`,
      file_offset: '0',
      data_binary: `@${file?.name}`,
    },
    body: file,
  });

  const fileUrl = await _fileUrl.json();
  return fileUrl;
};

export const extractUniqueTags = (data: IExpandedLead[] | ILead[]) => {
  return Array.from(new Set(data.flatMap((lead) => lead.tags))).map((tag) => ({ label: tag, value: tag }));
};

export const extractUniqueAdIds = (data: IExpandedLead[] | ILead[]) => {
  const filteredLeads = data.filter((lead) => lead.referral?.source_id) as ILeadReferral[];
  return Array.from(new Set(filteredLeads.flatMap((lead) => lead.referral.source_id))).map((ref) => ({ label: ref, value: ref }));
};

type Context = {
  [key: string]: string | string[]; // Context contains variables with single or multiple values
};

// Recursive function to evaluate the AST
function evaluateAST(ast: any, context: any): boolean {
  switch (ast.type) {
    case 'Literal':
      return ast.value;
    case 'Identifier':
      return context[ast.name] || false;
    case 'BinaryExpression':
      const left = evaluateAST(ast.left, context);
      const right = evaluateAST(ast.right, context);
      switch (ast.operator) {
        case '&&':
          return left && right;
        case '||':
          return left || right;
        case '==':
          return left == right;
        case '===':
          return left === right;
        default:
          throw new Error(`Unsupported operator: ${ast.operator}`);
      }
    default:
      throw new Error(`Unsupported AST node type: ${ast.type}`);
  }
}

// Function to parse and evaluate logical expressions with variables from context
export const parseAndEvaluate = (expression: string, context: Context): boolean => {
  // Replace variables in the expression
  const parsedExpression = expression.replace(/\b[a-zA-Z_][a-zA-Z0-9_ ]*\b/g, (match) => {
    if (context[match] !== undefined) {
      const value = context[match];
      if (Array.isArray(value)) {
        // If the variable is an array, generate a valid comparison expression
        if (value.length > 0) {
          return `${value.map((v) => `"${v.toLowerCase()}"`).join(' || ')}`;
        } else {
          return "''";
        }
      }
      return `"${value.toLowerCase()}"`; // Single value replacement
    }
    return `${match.toLowerCase()}`; // Default for undefined variables
  });

  // Step 2: Evaluate the final logical expression
  try {
    const ast = jsep(parsedExpression); // Parse the expression into an AST
    return evaluateAST(ast, context);
  } catch (error) {
    return false;
  }
};

export const canadianAreaCodes = new Set([
  '204',
  '226',
  '236',
  '249',
  '250',
  '289',
  '306',
  '343',
  '365',
  '387',
  '403',
  '416',
  '418',
  '431',
  '437',
  '438',
  '450',
  '506',
  '514',
  '519',
  '579',
  '581',
  '587',
  '604',
  '613',
  '639',
  '647',
  '672',
  '705',
  '709',
  '742',
  '778',
  '780',
  '782',
  '807',
  '819',
  '825',
  '867',
  '873',
  '902',
  '905',
]);

export const languagesArray = [
  {
    value: 'English-en',
    label: 'English',
  },
  {
    value: 'English (US)-en_US',
    label: 'English (US)',
  },
  {
    value: 'English (UK)-en_GB',
    label: 'English (UK)',
  },
  {
    value: 'English (UAE)-en_AE',
    label: 'English (UAE)',
  },
  {
    value: 'English (CAN)-en_CA',
    label: 'English (CAN)',
  },
  {
    value: 'Arabic-ar',
    label: 'Arabic',
  },
  {
    value: 'Arabic (EGY)-ar_EG',
    label: 'Arabic (EGY)',
  },
  {
    value: 'Arabic (UAE)-ar_AE',
    label: 'Arabic (UAE)',
  },
  {
    value: 'Arabic (LBN)-ar_LB',
    label: 'Arabic (LBN)',
  },
  {
    value: 'Arabic (MAR)-ar_MA',
    label: 'Arabic (MAR)',
  },
  {
    value: 'Arabic (QAT)-ar_QA',
    label: 'Arabic (QAT)',
  },
];
