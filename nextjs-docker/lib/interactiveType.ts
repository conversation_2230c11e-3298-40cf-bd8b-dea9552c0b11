interface IProvider {
  name: string;
}

interface IMediaWithId {
  id: string;
}

interface IMediaWithLink {
  link: string;
  provider: <PERSON>rovider;
}

interface IDocument extends IMediaWithId {
  filename: string;
}

interface IDocumentWithLink extends IMedia<PERSON><PERSON>Link {
  filename: string;
}

interface IVideo extends IMedia<PERSON>ithId {}

interface IVideoWithLink extends IMedia<PERSON>ithLink {}

interface IImage extends IMediaWithId {}

interface IImageWithLink extends IMediaWithLink {}

interface IHeaderBase {
  type: 'text' | 'image' | 'video' | 'document';
}

interface ITextHeader extends IHeaderBase {
  type: 'text';
  text: string;
}

interface IDocumentHeader extends IHeaderBase {
  type: 'document';
  document: IDocument | IDocumentWithLink;
}

interface IVideoHeader extends IHeaderBase {
  type: 'video';
  video: IVideo | IVideoWithLink;
}

interface IImageHeader extends IHeaderBase {
  type: 'image';
  image: IImage | IImageWithLink;
}

type IHeader = ITextHeader | IDocumentHeader | IVideoHeader | IImageHeader;

interface IBody {
  text: string;
}

interface IFooter {
  text: string;
}

interface IReplyButton {
  type: 'reply';
  reply: {
    /**
     * Unique Id
     */
    id: string;
    /**
     * 20 characters max
     */
    title: string;
  };
}

interface IRow {
  id: string;
  title: string;
  description: string;
}

interface ISection {
  title: string;
  rows: IRow[];
}

interface IAction {
  /**
   *  Required for List Messages.

   *  Button content. It cannot be an empty string and must be unique within the message. Emojis are supported, markdown is not. Maximum length: 20 characters.
   */
  button: string;
  /**
   * Required for Reply Button Messages.

   * A button object. The object can contain the following parameters:

   * type – The only supported option is reply for Reply Button Messages.

   * title – Button title. It cannot be an empty string and must be unique within the message. Emojis are supported, markdown is not. Maximum length: 20 characters.

   * id – Unique identifier for your button. This ID is returned in the webhook when the button is clicked by the user. Maximum length: 256 characters.

   * You cannot have leading or trailing spaces when setting the ID.
   */
  buttons: IReplyButton[];
  /**
   * Required for List Messages and Multi-Product Messages.

   Array of section objects. There is a minimum of 1 and maximum of 10. See section object.
   */
  sections: ISection[];
}

interface IInteractive {
  /**
   * https://developers.facebook.com/docs/whatsapp/on-premises/reference/messages
   */
  /**
   *  list: Use it for List Messages.

   *   button: Use it for Reply Buttons.

   *   product: Use it for Single-Product Messages.

   *   product_list: Use it for Multi-Product Messages.

   *   catalog_message: Use it for Catalog Messages.

   *   flow: Use it for Flows Messages.
   */
  type: 'list' | 'button' | 'product' | 'product_list' | 'catalog_message' | 'flow';
  /**
   * Required for type product_list. Optional for other types.

   * Header content displayed on top of a message. You cannot set a header if your interactive object is of product type.

   * The header object contains the following fields:

   * document object – Required if type is set to document. Contains the media object with the document.

   * image object – Required if type is set to image. Contains the media object with the image.

   * video object – Required if type is set to video. Contains the media object with the video.

   * text string – Required if type is set to text. Text for the header. Formatting allows emojis, but not markdown. Maximum length: 60 characters.

   * type string – Required. The header type you would like to use. Supported values are:

   * text – for List Messages, Reply Buttons, and Multi-Product Messages.

   * video – for Reply Buttons.

   * image – for Reply Buttons.

   * document – for Reply Buttons.
   */
  header?: IHeader;
  /**
   * Optional for type product. Required for other message types.

   *   An object with the body of the message.

   *   The body object contains the following field:

   *   textstring – Required if body is present. The content of the message. Emojis and markdown are supported. Maximum length: 1024 characters
   */
  body?: IBody;
  /**
   * Optional.

   * An object with the footer of the message.

   * The footer object contains the following field:

   * textstring – Required if footer is present. The footer content. Emojis, markdown, and links are supported. Maximum length: 60 characters.
   */
  footer?: IFooter;
  /**
   * Required.
   * An action object with what you want the user to perform after reading the message. See {@linkhttps://developers.facebook.com/docs/whatsapp/on-premises/reference/messages#action-object|action object} for full information.
   */
  action: IAction;
}

export default IInteractive;
