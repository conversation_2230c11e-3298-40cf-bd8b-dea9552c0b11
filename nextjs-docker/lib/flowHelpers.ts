import <PERSON> from 'papa<PERSON><PERSON>';
import { actually_send_interactive_message, handleSubmit } from './actions';
import { Edge, Node } from '@xyflow/react';
import { Account, IAction, IAutomation, ICondition, ILead, InteractiveMessage, ITrigger } from './types';
import { getPb, saveMetaToUser, storeUserBankSession } from './pocket';
import type PocketBase from 'pocketbase';
import path from 'path';
import fs from 'fs';
import { myQueue } from './worker';
import { OpenAI } from 'openai';
import handleBankMessages from './handleBank';

export interface WNode extends Node {
  type: ITrigger | IAction | ICondition;
}

const create_interactive_message_from_node = (node: Node, allNodes: Node[]): InteractiveMessage => {
  const button_nodes = allNodes.filter((_node) => _node.parentId === node.id);
  const optionTexts: any[] = button_nodes.map((node) => node.data.text);

  let interactiveMessage: InteractiveMessage;

  if (optionTexts.length > 3) {
    interactiveMessage = {
      type: 'list',
      body: {
        text: node?.data.text as string,
      },
      action: {
        button: 'See Options',
        sections: [
          {
            title: 'Options',
            rows: optionTexts.map((title: string) => {
              return {
                id: title.toLowerCase().split(' ').join('-'),
                title: title.slice(0, 24),
              };
            }),
          },
        ],
      },
    };
  } else {
    interactiveMessage = {
      type: 'button',
      body: {
        text: node?.data.text as string,
      },
      action: {
        buttons: optionTexts.map((title: string) => {
          return {
            type: 'reply',
            reply: {
              id: title.toLowerCase().split(' ').join('-'),
              title,
            },
          };
        }),
      },
    };
  }

  return interactiveMessage;
};

interface ServiceNode extends Node {
  data: {
    options: { value: string }[];
    selectedImage?: { url: string; filename?: string };
    text: string;
    ai?: boolean;
  };
}

// Track recursion frequency for each conversation
const flowRecursionMap = new Map<string, { count: number; timestamp: number }>();

// Maximum number of recursions allowed in a short time period
const MAX_RECURSION_COUNT = 5;
// Time window in milliseconds (e.g., 10 seconds)
const RECURSION_TIME_WINDOW = 10000;

export class FlowBot {
  public conversationId: string;
  public flow: Partial<IAutomation>;
  allNodes: Node[];
  allEdges: Edge[];
  flowName: string;
  public account: Account;
  public phoneNo: string;
  #state: string;
  public userMessage: string;
  public leadId: string;
  localState: string;
  templateState: string;
  pb: PocketBase | undefined;
  messageSent: boolean = false;
  testMeta: { [key: string]: any } = {};

  set state(x: string) {
    this.#state = x;
    this.localState = x !== '' ? x.split(':')?.[1] : '';
    this.templateState = x !== '' ? x.split(':')?.[0] : '';
  }

  get state() {
    return this.#state;
  }

  constructor({
    conversationId,
    leadId,
    flow,
    account,
    phoneNo,
    state,
    userMessage,
  }: {
    conversationId: string;
    leadId: string;
    flow: Partial<IAutomation>;
    account: Account;
    phoneNo: string;
    state: string;
    userMessage: string;
  }) {
    this.conversationId = conversationId;
    this.leadId = leadId;
    this.allNodes = flow?.flow?.nodes ?? [];
    this.allEdges = flow?.flow?.edges ?? [];
    this.flowName = flow?.name ?? '';
    this.account = account;
    this.phoneNo = phoneNo;
    this.flow = flow;
    this.#state = state;
    this.localState = state !== '' ? state.split(':')?.[1] : '';
    this.templateState = state !== '' ? state.split(':')?.[0] : '';
    this.userMessage = userMessage.toLocaleLowerCase();
  }

  /**
   * Initialize the FlowBot, by getting the PocketBase instance
   */
  init = async () => {
    this.pb = await getPb();
  };

  /**
   * If the flow is valid, return true
   * @returns boolean
   */
  isValidFlow = () => {
    return this?.allEdges?.length > 0;
  };

  /**
   * If the flow is empty, return true
   * @returns boolean
   */
  isEmptyFlow = () => {
    return !this.isValidFlow();
  };

  sendInteractiveMessageFromNodeOptions = async (node: Node) => {
    const pb = this.pb;
    if (!pb) return;
    const interactiveMessage = create_interactive_message_from_node(node, this.allNodes);
    await actually_send_interactive_message(interactiveMessage, this.account, this.phoneNo);
  };

  sendTextMessage = async ({ message }: { message: string }) => {
    const map = new Map();
    map.set('message', message.slice(0, 4999));
    await handleSubmit(
      {
        leadId: this.leadId,
        convoId: this.conversationId,
        phoneNo: this.phoneNo,
        phoneId: this.account.phone_id,
        accountId: this.account.id,
      },
      map
    );
  };

  sendImageMessage = async ({ message, url, filename }: { message: string; url: string; filename?: string }) => {
    const map = new Map();
    map.set('message', message);

    const response = await fetch(url);
    const file = await response.blob();
    const fileType = file.type;

    const fileMappings: Record<string, { key: string; filename: string }> = {
      'image/jpeg': { key: 'picture', filename: filename ?? 'image.jpg' },
      'image/png': { key: 'picture', filename: filename ?? 'image.png' },
      'video/mp4': { key: 'picture', filename: filename ?? 'video.mp4' },
      'application/pdf': { key: 'picture', filename: filename ?? 'document.pdf' },
      'audio/mpeg': { key: 'audio', filename: 'audio.mp3' },
      'audio/aac': { key: 'audio', filename: 'audio.aac' },
      'audio/wav': { key: 'audio', filename: 'audio.wav' },
      'audio/ogg': { key: 'audio', filename: 'audio.ogg' },
      'audio/webm': { key: 'audio', filename: 'audio.webm' },
      'audio/x-m4a': { key: 'audio', filename: 'audio.m4a' },
    };

    const mapping = fileMappings[fileType];
    if (mapping) {
      if (mapping.key == 'audio') {
        map.set(mapping.key, file);
      } else {
        map.set(mapping.key, url);
        map.set('filename', mapping.filename);
      }
    } else {
      console.warn(`Unsupported file type: ${fileType}`);
    }
    await handleSubmit(
      {
        leadId: this.leadId,
        convoId: this.conversationId,
        phoneNo: this.phoneNo,
        phoneId: this.account.phone_id,
        accountId: this.account.id,
      },
      map
    );
  };

  getImportantNodes = ({ edge }: { edge: Edge }) => {
    const source_node = this.allNodes.find((node) => node.id === edge.source) as WNode;
    const target_node = this.allNodes.find((node) => node.id === edge.target) as WNode;

    const source_type = source_node?.type;
    const target_type = target_node?.type;

    return { source_node, target_node, source_type, target_type };
  };

  assignAgent = async ({ node }: { node: Node }) => {
    if (!this.pb) return;
    await this.pb.collection('conversations').update(this.conversationId, {
      assigned_agent: node?.data?.selectedAgent,
    });
  };

  sendInteractiveOrTextMessage = async ({ node, meta = {} }: { node: Node; meta?: Record<string, string | number> }) => {
    if ((process?.env?.NODE_ENV ?? '') !== 'test') {
      await new Promise((resolve) => setTimeout(resolve, 2000));
    }
    let _node = node as ServiceNode;
    if (_node?.data?.ai) {
      // generate the AI response
      // await this.sendTextMessage({ message: `💭 I am thinking ...` as string });
      const response = await this.generateAIResponse(this.userMessage);
      await this.sendTextMessage({ message: response as string });
      return;
    }
    if (_node?.data?.selectedImage?.url) {
      // send a media image with caption.
      const caption = _node?.data.text as string;
      await this.sendImageMessage({ message: caption, url: _node?.data.selectedImage?.url, filename: _node?.data.selectedImage?.filename });
      return;
    }
    if (_node?.data?.options?.length > 0) {
      await this.sendInteractiveMessageFromNodeOptions(node);
      return;
    }
    // replace all curly braces with the meta data
    let message = _node?.data.text as string;
    Object.entries(meta ?? {}).forEach(([key, value]) => {
      message = message.replace(new RegExp(`{${key}}`, 'g'), value.toString());
    });
    await this.sendTextMessage({ message });
  };

  updateState = async ({ node }: { node: Node }) => {
    if (!this.pb) return;
    this.state = `${this.flowName}:${node.id}`;
    await this.pb.collection('conversations').update(this.conversationId, { state: `${this.flowName}:${node.id}` });
    await myQueue.add(
      'clearState',
      { convoId: this.conversationId },
      {
        delay: 1000 * 60 * 5,
      }
    );
  };

  removeState = async () => {
    if (!this.pb) return;
    this.state = '';
    await this.pb.collection('conversations').update(this.conversationId, { state: '' });
  };

  /**
   * An edge has a source and target node, the source node either can be a stand-alone node, or an option node with a larger parent node,
   * the user state holds the parent node id, so we check if the user is sitting on an option node, and has the state of the parent means its n valid node.
   * in all other cases we just check if user is on the source node id.
   * @returns boolean
   */
  checkValidEdge = ({ sourceNode, targetNode, actionPerformed }: { sourceNode?: WNode; targetNode?: WNode; actionPerformed: boolean }) => {
    if (this.templateState !== '' && this.templateState !== this.flowName) {
      return false;
    }
    // if for some reason the source and target node do not exist, leave the edge
    if (!sourceNode || !targetNode) {
      return false;
    }

    // if the user does not have any state, means this is the first time the user is interacting with the flow,
    // and the source node is of the type selectKeywords or onMessage, then we can proceed
    if (!this.localState && (sourceNode?.type ?? '') === 'On Message') {
      return true;
    }

    if ((sourceNode.id === this.localState || sourceNode?.parentId === this.localState) && !actionPerformed) {
      return true;
    }

    return false;
  };

  saveData = async (key: string, value: string) => {
    if (process.env.NODE_ENV === 'test') {
      this.testMeta[key] = value;
      return;
    }
    if (!this.pb) return;
    await saveMetaToUser(this.leadId, { [key]: value });
  };

  getLeadData = async () => {
    if (process.env.NODE_ENV === 'test') {
      return { meta: this.testMeta };
    }
    if (!this.pb) return;
    return await this.pb.collection<ILead>('leads').getOne(this.leadId);
  };

  /**
   * Executes the flow logic by processing edges and nodes in the graph.
   * This method handles various node types and their corresponding actions:
   * - 'On Message': Checks for keyword matches in user messages
   * - 'button': Validates button text against user input
   * - 'Save Data': Stores user message data
   * - 'Assign agent': Handles agent assignment
   * - 'Send Message': Sends interactive or text messages
   * - 'Data Node': Processes CSV data for tire information
   *
   * The flow continues until either:
   * - The last node is reached (resets state)
   * - An intermediate node is reached (updates state)
   *
   * If conditions are met, the flow can recursively continue after a delay
   * (except in test environment).
   *
   * @returns {Promise<void>} A promise that resolves when the flow execution is complete
   *
   * @remarks
   * The method maintains a state through `localState` and tracks whether actions
   * were performed during execution. It includes special handling for tire data
   * lookups and interactive messaging.
   */
  runFlow = async () => {
    // Check for excessive recursion
    const now = Date.now();
    const recursionInfo = flowRecursionMap.get(this.conversationId) || { count: 0, timestamp: now };

    // If we're within the time window, increment the counter
    if (now - recursionInfo.timestamp < RECURSION_TIME_WINDOW) {
      recursionInfo.count++;

      // If we've exceeded the maximum recursion count, bail out
      if (recursionInfo.count > MAX_RECURSION_COUNT) {
        console.warn(`Excessive recursion detected for conversation ${this.conversationId}. Bailing out.`);
        // Reset the counter but with current timestamp
        flowRecursionMap.set(this.conversationId, { count: 0, timestamp: now });
        return;
      }
    } else {
      // If we're outside the time window, reset the counter
      recursionInfo.count = 1;
      recursionInfo.timestamp = now;
    }

    // Update the recursion info
    flowRecursionMap.set(this.conversationId, recursionInfo);

    let actionPerformed = false;
    let currentState = this.localState;
    for (const edge of this.allEdges) {
      const { source_node, target_node, source_type, target_type } = this.getImportantNodes({ edge });
      if (!this.checkValidEdge({ sourceNode: source_node, targetNode: target_node, actionPerformed })) {
        continue;
      }

      // process all source node conditions here

      // if source type is select keywords, then check if the user message matches the keywords, if not, we continue to the next edge
      // if there are no keywords then we always proceed
      if (source_type === 'On Message') {
        const keywords = (source_node?.data?.keywords ?? []) as { value: string }[];
        const sourceKeywords = keywords.map((k) => k.value.toLowerCase()).join('|');
        if (!this.userMessage.toLowerCase().match(new RegExp(`\\b(${sourceKeywords})\\b`, 'g'))) {
          continue;
        }
      }

      if (source_type === 'button') {
        const optionText = source_node?.data.text as string;
        if (optionText.toLocaleLowerCase().trim() !== this.userMessage.toLocaleLowerCase()) {
          continue;
        }
      }

      const hasNoOtherTargets = !this.allEdges.some((_edge) => _edge.source === source_node.id && _edge.target !== target_node.id);
      const hasOtherTargets = this.allEdges.some((_edge) => _edge.source === source_node.id && _edge.target !== target_node.id);
      // process all target node conditions here
      if (target_type === 'Save Data') {
        await this.saveData(target_node.data.text as string, this.userMessage);
        if (hasNoOtherTargets) {
          actionPerformed = true;
        }
      }

      if (target_type === 'Assign agent') {
        this.assignAgent({ node: target_node });
      }

      /* means this service node came from another service node with options */
      if (target_type === 'Send Message') {
        const leadData = await this.getLeadData();
        const meta = leadData?.meta;
        await this.sendInteractiveOrTextMessage({ node: target_node, meta });
        actionPerformed = true;
      }

      // MITF bank messages
      actionPerformed = await handleBankMessages({ ctx: this, target_type, target_node, actionPerformed });

      if (target_type === 'Data Node') {
        const filePath = path.join(process.cwd(), 'lib/data-files', 'tyre_data.csv'); // Path to your CSV file
        const fileContent = fs.readFileSync(filePath, 'utf8');
        const parsedData = Papa.parse(fileContent, {
          header: true, // Treat the first row as headers
        });

        const leadData = await this.getLeadData();
        const meta = leadData?.meta;
        const input = meta?.tyre_size.trim().toLowerCase().replace(/\s/g, '');
        const results = parsedData.data.filter((data: any) => {
          /*
          data['2024 Material Code']
          data['Material Description']
          data['Inch']
          data['Pattern	Origin']
          data['Runflat	Ply Rating']
          data['Front & Rear']
          data['OEM & REPLACEMENT']
          data['Unit Price (AED)']
          data['Stocks'] */
          // const oem = data['OEM & REPLACEMENT'];
          // const inch = data['Inch'];
          // const input = 225/50R17

          if (data['name'].trim().toLowerCase().replace(/\s/g, '').includes(input)) {
            return data;
          }
        });
        // create a formatted message
        let message = `Following are the prices for your Tyre Size: ${input}:\n\n`;
        results.forEach((result: any) => {
          // Michelin 245/45 R18 100Y RunFlat Primacy 3 ZP * MOE 2024	(AED 954+VAT)
          message += `${result['name']} *(AED ${result['TV 1 tyre SP without VAT']} + VAT)*\n`;
        });
        await this.sendTextMessage({ message });
        actionPerformed = true;
      }

      // check if this is the last node in the graph
      // go throw all the edges and find if the target node is never the source node
      // and check if node has no children
      // also check there are no other target nodes from source node
      const isLastNode = this.allEdges.every((_edge) => _edge.source !== target_node.id);
      const hasNoChildren = !this.allNodes.some((_node) => _node.parentId === target_node.id);
      if (isLastNode && hasNoChildren && hasNoOtherTargets) {
        // If the user reached the last node, reset his state so the flow can begin again
        await this.removeState();
      } else {
        if (hasOtherTargets && target_node.type === 'Save Data') {
          // do nothing
        } else {
          // move user to the next state
          await this.updateState({ node: target_node });
        }
      }
    }

    // if some message was sent, and we didn't reach end of flow, and our state changed, means we can run the flow again
    // if are waiting for user input, we should not run flow again

    // find the target node from the node this.state
    const nextTargetNodeId = this.allEdges.find((edge) => edge.source === this.localState)?.target;
    const nextTargetNode = this.allNodes.find((node) => node.id === nextTargetNodeId);
    if (actionPerformed && this.localState !== '' && currentState !== this.localState && nextTargetNode?.type !== 'Save Data') {
      if ((process?.env?.NODE_ENV ?? '') !== 'test') {
        await new Promise((resolve) => setTimeout(resolve, 2000));
      }

      // Before making the recursive call, get the LATEST recursion info
      const latestRecursionInfo = flowRecursionMap.get(this.conversationId);
      if (!latestRecursionInfo || latestRecursionInfo.count < MAX_RECURSION_COUNT) {
        this.userMessage = '';
        await this.runFlow();
      } else {
        console.warn(`Skipping recursive flow execution for conversation ${this.conversationId} due to recursion limit.`);
      }
    }
  };

  /**
   * Generates an AI response to the user message using OpenAI
   * @param userMessage The message from the user to respond to
   * @returns A promise resolving to the AI-generated response
   */
  generateAIResponse = async (userMessage: string): Promise<string> => {
    try {
      // Get lead data to provide context
      const leadData = await this.getLeadData();
      const meta = leadData?.meta || {};

      // Create OpenAI instance
      const openai = new OpenAI({
        apiKey: process.env.GEMINI_API_KEY,
        // baseURL: "https://api.deepseek.com"
        baseURL: 'https://generativelanguage.googleapis.com/v1beta/openai/',
      });

      // Build prompt with context
      let prompt = `
        You are a helpful WhatsApp assistant for a business.
        Context about the conversation:
        - Flow name: ${this.flowName}
        ${Object.entries(meta)
          .map(([key, value]) => `- ${key}: ${value}`)
          .join('\n')}

        Respond to the user's message in a helpful, professional manner.
      `;
      const completion = await openai.chat.completions.create({
        messages: [
          {
            role: 'system',
            content: prompt,
          },
          {
            role: 'user',
            content: userMessage,
          },
        ],
        model: 'gemini-2.0-flash-lite',
        max_tokens: 250,
      });

      return completion.choices[0]?.message?.content || "I'm sorry, I couldn't generate a response at this time.";
    } catch (error) {
      console.error('Error generating AI response:', error);
      return "I apologize, but I'm having trouble responding right now.";
    }
  };
}
