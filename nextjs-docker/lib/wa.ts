import type { ISendMessageErrorResponse, ISendMessageResponse, WAMediaReturn, WAMessageTypeMedia } from './types';
import { getPb } from './pocket';

// Your test sender phone number
// const wa = new WhatsApp(Number(process.env.WA_PHONE_NUMBER_ID));

export async function actually_send_message(data: Record<string, any>, phone_id: string): Promise<ISendMessageResponse | ISendMessageErrorResponse> {
  const myHeaders = new Headers();
  myHeaders.append('Content-Type', 'application/json');
  myHeaders.append('Authorization', `Bearer ${process.env.CLOUD_API_ACCESS_TOKEN}`);
  myHeaders.append('Cache-Control', 'no-cache');
  const raw = JSON.stringify(data);
  const requestOptions = {
    method: 'POST',
    headers: myHeaders,
    body: raw,
  };

  const response = await fetch(`https://graph.facebook.com/v19.0/${phone_id}/messages`, requestOptions);
  const result: ISendMessageResponse = await response.json();
  return result;
}

export async function send_media_message(
  phone_id: string,
  recipient_number: number,
  type: WAMessageTypeMedia,
  file_url: string,
  filename: string,
  message: string | null
) {
  try {
    const data = {
      messaging_product: 'whatsapp',
      to: `${recipient_number}`,
      type,
      [type]: {
        link: file_url,
        filename: type === 'document' ? filename : undefined,
        ...(type != 'audio' && message && { caption: message }),
      },
    };
    return await actually_send_message(data, phone_id);
  } catch (e) {
    console.log('error');
  }
}

export async function send_message(phone_id: string, recipient_number: string, message: string): Promise<ISendMessageResponse | ISendMessageErrorResponse> {
  try {
    const data = {
      messaging_product: 'whatsapp',
      to: `${recipient_number}`,
      type: 'text',
      text: {
        // the text object
        body: message,
      },
    };
    return await actually_send_message(data, phone_id);
  } catch (e) {
    console.log('error');
    throw e;
  }
}

export async function get_media_url(media_id: string): Promise<WAMediaReturn> {
  try {
    const myHeaders = new Headers();
    myHeaders.append('Content-Type', 'application/json');
    myHeaders.append('Authorization', `Bearer ${process.env.CLOUD_API_ACCESS_TOKEN}`);
    myHeaders.append('Cache-Control', 'no-cache');

    const requestOptions = {
      method: 'GET',
      headers: myHeaders,
    };

    const response = await fetch(`https://graph.facebook.com/v19.0/${media_id}`, requestOptions);
    const result = await response.json();
    return result;
  } catch (e) {
    console.log('error');
    throw e;
  }
}

export async function send_template({
  recipient_number,
  template,
  phoneId,
  components,
  templateLang,
}: Record<string, any>): Promise<ISendMessageResponse | ISendMessageErrorResponse> {
  try {
    const data = {
      messaging_product: 'whatsapp',
      to: `${recipient_number}`,
      type: 'template',
      template: {
        name: template,
        language: {
          code: templateLang,
        },
        components: components,
      },
    };
    //No need to update template id in conversation as template id is already present.
    // update the template_id in the conversation
    // const conversation = await pb.collection('conversations').getFirstListItem(`from.phone_number = "${recipient_number}" && account.id = "${accountId}"`);
    // if (conversation) {
    //   await pb.collection('conversations').update(conversation.id, {
    //     template: template_id,
    //     message: message_id,
    //   });
    // }
    return await actually_send_message(data, phoneId);
  } catch (e) {
    console.log('error');

    console.log(e);
    throw e;
  }
}

export async function send_interactive_message({
  recipient_number,
  interactive,
  phoneId,
}: Record<string, any>): Promise<ISendMessageResponse | ISendMessageErrorResponse> {
  try {
    const data = {
      recipient_type: 'individual',
      messaging_product: 'whatsapp',
      to: `${recipient_number}`,
      type: 'interactive',
      interactive,
    };
    return await actually_send_message(data, phoneId);
  } catch (e) {
    console.log('error');

    throw e;
  }
}

// export async function send_list(recipient_number: string, list?: string[]) {
//   try {
//     const list_message =
//     {
//       "type": 'list',
//       "header": {
//         "type": "text",
//         "text": "Our Services"
//       },
//       "body": {
//         "text": "Please select a service that you are interested in."
//       },
//       "footer": {
//         "text": "Press button below"
//       },
//       "action": {
//         "button": "Service List",
//         "sections": [
//           {
//             "title": "Hair Cut & Styling",
//             "rows": [
//               {
//                 "id": "Haircut1",
//                 "title": "Gent's Haircut",
//                 "description": "600 PKR - Discount price"
//               },
//               {
//                 "id": "Haircut2",
//                 "title": "Gent's Haircut and Shave",
//                 "description": "1200 PKR - Discount price"
//               }
//             ]
//           },
//           {
//             "title": "Gent's Grooming",
//             "rows": [
//               {
//                 "id": "FaceWash1",
//                 "title": "Face Polish",
//                 "description": "1200 PKR - Discount price"
//               },
//               {
//                 "id": "Facewash2",
//                 "title": "Complete Facial",
//                 "description": "2500 PKR - Discount price"
//               }
//             ]
//           }
//         ]
//       }
//     }
//     const sent_list_message = wa.messages.interactive(list_message as unknown as any, Number(recipient_number));

//     await sent_list_message.then((res) => {

//     });
//   }
//   catch (e) {

//   }
// }

export async function readMessage(message_id: string, business_phone_number_id: string) {
  try {
    const myHeaders = new Headers();
    myHeaders.append('Content-Type', 'application/json');
    myHeaders.append('Authorization', `Bearer ${process.env.CLOUD_API_ACCESS_TOKEN}`);

    const raw = JSON.stringify({
      messaging_product: 'whatsapp',
      status: 'read',
      message_id: message_id,
    });

    const requestOptions = {
      method: 'POST',
      headers: myHeaders,
      body: raw,
    };

    const response = await fetch(`https://graph.facebook.com/v19.0/${business_phone_number_id}/messages`, requestOptions);
    const result = await response.json();
  } catch (e) {
    console.log(JSON.stringify(e));
  }
}
