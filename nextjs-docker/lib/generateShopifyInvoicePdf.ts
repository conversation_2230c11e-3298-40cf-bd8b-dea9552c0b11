// @ts-ignore
import html2pdf from 'html2pdf.js';
import dayjs from 'dayjs';
import { Account } from './types';

export async function generateShopifyInvoicePDF(order: any, accountDetails: Account, logo_url: string | undefined | null) {
  const invoiceContent = `
  <div style="padding: 20px; font-family: Arial, sans-serif; max-width: 800px; margin: 0 auto;">
  <!-- Invoice Title and Order Number -->
<div style="display: flex; justify-content: space-between; margin-bottom: 30px;">
  ${
    logo_url
      ? `<img src="${logo_url}" alt="Company Logo" 
            style="max-width: 90px; max-height: 50px; margin-bottom: 20px; object-fit: contain;" 
            crossorigin="anonymous"/>`
      : ''
  }
  <h1 style="font-size: 26px; margin: 0;">Tax Invoice</h1>
  <div style="text-align: right;">
    <p style="margin: 0; font-size: 16px;">Order ${order.name}</p>
    <p style="margin: 0; font-size: 16px;">${dayjs(order.created_at).format('MMMM DD, YYYY')}</p>
  </div>
</div>


  <!-- Address Sections -->
  <div style="display: grid; grid-template-columns: repeat(3, 1fr); gap: 20px; margin-bottom: 40px;">
    <div>
      <h3 style="font-size: 16px; color: #666; margin-bottom: 8px;">From</h3>
      <p style="margin: 0; font-size: 14px;">${accountDetails.name}</p>
      <p style="margin: 0; font-size: 14px;">${accountDetails.address}, ${accountDetails.city}</p>
      <p style="margin: 0; font-size: 14px;">${accountDetails.business_location}</p>
      <p style="margin: 0; font-size: 14px;">${accountDetails.display_phone_number}</p>
      <p style="margin: 0; font-size: 14px;">TRN: ${accountDetails.TRN}</p>
    </div>
    <div>
      <h3 style="font-size: 16px; color: #666; margin-bottom: 8px;">Bill to</h3>
      <p style="margin: 0; font-size: 14px;">${order.billing_address.name}</p>
      <p style="margin: 0; font-size: 14px;">${order.billing_address.company || ''}</p>
      <p style="margin: 0; font-size: 14px;">${order.billing_address.address1}</p>
      <p style="margin: 0; font-size: 14px;">${order.billing_address.city} ${order.billing_address.province} ${order.billing_address.zip}</p>
      <p style="margin: 0; font-size: 14px;">${order.billing_address.country}</p>
    </div>
    <div>
      <h3 style="font-size: 16px; color: #666; margin-bottom: 8px;">Ship to</h3>
      <p style="margin: 0; font-size: 14px;">${order.shipping_address.name}</p>
      <p style="margin: 0; font-size: 14px;">${order.shipping_address.company || ''}</p>
      <p style="margin: 0; font-size: 14px;">${order.shipping_address.address1}</p>
      <p style="margin: 0; font-size: 14px;">${order.shipping_address.city} ${order.shipping_address.province} ${order.shipping_address.zip}</p>
      <p style="margin: 0; font-size: 14px;">${order.shipping_address.country}</p>
      <p style="margin: 0; font-size: 14px;">${order.shipping_address.phone}</p>
    </div>
  </div>

  <!-- Order Details -->
  <div style="margin-bottom: 40px;">
    <h3 style="font-size: 18px; margin-bottom: 16px;">Order Details</h3>
    <table style="width: 100%; border-collapse: collapse;">
      <thead>
        <tr style="background-color: #f8f8f8;">
          <th style="text-align: left; padding: 12px; font-size: 14px;">Qty</th>
          <th style="text-align: left; padding: 12px; font-size: 14px;">Item</th>
          <th style="text-align: right; padding: 12px; font-size: 14px;">Price</th>
        </tr>
      </thead>
      <tbody>
        ${order.line_items
          .map(
            (item: any) => `
          <tr style="border-bottom: 1px solid #eee;">
            <td style="padding: 12px; font-size: 14px;">${item.quantity}</td>
            <td style="padding: 12px; font-size: 14px;">${item.name}</td>
            <td style="text-align: right; padding: 12px; font-size: 14px;">${parseFloat(item.price).toFixed(2)}</td>
          </tr>
        `
          )
          .join('')}
      </tbody>
    </table>
  </div>

  <!-- Totals -->
  <div style="margin-left: auto; width: 300px;">
    <div style="display: flex; justify-content: space-between; margin-bottom: 8px;">
      <span style="font-size: 14px;">Subtotal</span>
      <span style="font-size: 14px;">${parseFloat(order.total_line_items_price).toFixed(2)}</span>
    </div>
    <div style="display: flex; justify-content: space-between; margin-bottom: 8px;">
      <span style="font-size: 14px;">Discount</span>
      <span style="font-size: 14px;">-${parseFloat(order.total_discounts).toFixed(2)}</span>
    </div>
    <div style="display: flex; justify-content: space-between; margin-bottom: 8px;">
      <span style="font-size: 14px;">Tax</span>
      <span style="font-size: 14px;">${parseFloat(order.total_tax).toFixed(2)}</span>
    </div>
    <div style="display: flex; justify-content: space-between; margin-bottom: 8px;">
      <span style="font-size: 14px;">Shipping</span>
      <span style="font-size: 14px;">${parseFloat(order.total_shipping_price_set.presentment_money.amount).toFixed(2)}</span>
    </div>
    <div style="display: flex; justify-content: space-between; padding-top: 8px; border-top: 1px solid #000;">
      <span style="font-weight: bold; font-size: 14px;">Total</span>
      <span style="font-weight: bold; font-size: 14px;">${parseFloat(order.total_price).toFixed(2)}</span>
    </div>
    <div style="display: flex; justify-content: space-between; margin-top: 8px;">
      <span style="font-size: 14px;">Total Paid</span>
      <span style="font-size: 14px;">0.00</span>
    </div>
    <div style="display: flex; justify-content: space-between; margin-top: 8px; font-weight: bold; font-size: 14px;">
      <span>Outstanding Amount</span>
      <span>${parseFloat(order.total_price).toFixed(2)}</span>
    </div>
  </div>

  <!-- Footer -->
  <div style="text-align: center; margin-top: 40px; font-size: 14px; color: #666;">
    <p>Computer generated invoice</p>
  </div>
</div>


  `;

  //   <div>
  //   <h3 style="font-size: 14px; color: #666; margin-bottom: 8px;">From</h3>
  //   <p style="margin: 0;">Vonto.ae</p>
  //   <p style="margin: 0;">Iris Bay Tower</p>
  //   <p style="margin: 0;">Business Bay, Dubai</p>
  //   <p style="margin: 0;">United Arab Emirates</p>
  //   <p style="margin: 0;">+971 43307619</p>
  //   <p style="margin: 0;">TRN: 100505887800003</p>
  // </div>

  //   <div style="margin-bottom: 40px;">
  //   <img src=${logo_url} alt="Company Logo" class="w-44 h-16 mr-4" />
  // </div>

  //   <!-- Footer -->
  // <div style="margin-top: 40px; font-size: 14px; color: #666;">
  //   <p>If you have any questions, please send an <NAME_EMAIL></p>
  // </div>
  const options = {
    margin: 10,
    filename: `shopify-invoice-${order.name}.pdf`,
    html2canvas: { useCORS: true, scale: 2 },
    jsPDF: { unit: 'mm', format: 'a4', orientation: 'portrait' },
  };

  const blob = await html2pdf().from(invoiceContent).set(options).outputPdf('blob');

  const file = new File([blob], `invoice-${Date.now()}.pdf`, { type: 'application/pdf' });

  const formData = new FormData();
  formData.append('file', file);

  return formData;
}
