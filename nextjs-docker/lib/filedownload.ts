// biome-ignore lint/style/useNodejsImportProtocol: <explanation>
import { exec } from 'child_process';
// biome-ignore lint/style/useNodejsImportProtocol: <explanation>
import { promisify } from 'util';

export const downloadAndReturnFile = async (url: string, mime_type: string, id: string): Promise<File> => {
  const execPromise = promisify(exec);
  const headers = `-H 'Authorization: Bearer ${process.env.CLOUD_API_ACCESS_TOKEN}'`;
  const curlCommand = `curl -s '${url}' ${headers}`;
  try {
    const { stdout, stderr } = await execPromise(curlCommand, {
      encoding: 'binary',
      maxBuffer: 1024 * 1024 * 100, // 10MB
    });
    if (stderr) {
      console.error(`stderr: ${stderr}`);
      throw new Error(stderr);
    }
    // Convert stdout to a Buffer
    const buffer = Buffer.from(stdout, 'binary');
    const blob = new Blob([buffer], { type: mime_type });
    const ext = mime_type.split('/')[1];
    const name = `${id}.${ext}`;
    // Create a File object
    const file = new File([blob], name, { type: mime_type });
    return file;
  } catch (error) {
    console.error(`Error executing curl: ${(error as Error).message}`);
    throw error;
  }
};
