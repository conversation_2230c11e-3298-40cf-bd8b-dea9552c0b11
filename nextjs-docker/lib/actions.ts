'use server';

import { pb_url } from '@/state/consts';
import { revalidatePath } from 'next/cache';
import PocketBase from 'pocketbase';
import {
  addCountryCode,
  addLeadToList,
  addMediaMessageToPb,
  addMessage,
  addTeam,
  createList,
  getAccount,
  getPb,
  getUser,
  increaseMessageLimitQuota,
  insertInteractiveMessage,
  markMessageAsFailed,
  markMessageAsSentAndAddWAMID,
  markMessageSentByWhatsapp,
  removeLeadsFromList,
  resAddWAMI,
  saveMessagePocket,
  updateAccountDetails,
  updateAccountRemainingLimit,
  updateAccountWithPackageId,
  updateLeads,
  updateListName,
  updateUserRemainingLimit,
  upsertLead,
  upsertNumberUpload,
} from './pocket';
import { send_interactive_message, send_media_message, send_message, send_template } from './wa';

import { IBody, IHeader } from '@/app/[account_id]/templates/manage/[template_operation]/types';
import { server_component_pb } from '@/state/pb/server_component_pb';
import { createAgent, createUser } from '@/state/user/user';
import dayjs from 'dayjs';
import parsePhoneNumber from 'libphonenumber-js';
import { log } from 'next-axiom';
import { cookies } from 'next/headers';
import { redirect, RedirectType } from 'next/navigation';
import { z } from 'zod';
import {
  type Account,
  BlockUsersResponse,
  ICampaign,
  IConversation,
  ICreateShopifyCustomer,
  IExpandedLead,
  IExpandedList,
  type ILead,
  ILog,
  type IMessage,
  IMessagingLimit,
  IPhoneVerificationResponse,
  ISyncShopifyCustomer,
  ISyncShopifyCustomerResponse,
  type ITemplateDatabase,
  User,
  type WAMessageTypeMedia,
} from './types';
import { createIndividualCarouselTemplateComponent, createIndividualTemplateComponents, createTemplateComponents, PATHS, SUPERUSERID } from './utils';
import { myQueue } from './worker';

export const saveMessage = async (accountId: string, convoId: string, message_id: string) => {
  // save message in saved_messages table
  const res = await saveMessagePocket(accountId, convoId, message_id);
  return res;
};

// send message to whatsapp and also add to db
export const handleSubmit = async (
  { leadId, deliveryStatus, phoneNo, accountId, userId }: Record<string, string | null | undefined>,
  formData: Map<string, string | File> | any
) => {
  try {
    const formDataObj = Object.fromEntries(formData.entries()) as unknown as Record<string, string>;
    let { message } = formDataObj;
    let finalMessage;
    const picture = formData.get('picture') as string;
    const filename = formData.get('filename') as string;
    const audio: File = formData.get('audio') as File;
    const account = await getAccount(accountId!);
    const phoneId = account.phone_id;
    if (picture) {
      // picture is a URL;
      let file = await (await fetch(picture as unknown as string)).blob();
      // let type = 'image';
      let type = file.type.split('/')[0] as string;
      if (type === 'application') {
        type = 'document';
      }
      const data = {
        user: `${leadId}`,
        message,
        account: accountId,
        from: 'agent',
        file: new File([file], filename),
        type,
        delivery_status: 'sent from wetarseel',
        ...(userId && { created_by: userId }),
      };
      if (leadId && phoneId && accountId) {
        const media = await addMediaMessageToPb(leadId, accountId, 'agent', data);
        finalMessage = media;
        const result = await send_media_message(phoneId, Number(phoneNo), type as WAMessageTypeMedia, media?.url ?? '', filename, message);
        if (result && 'error' in result) {
          finalMessage = await markMessageAsFailed(media, result.error.message);
          log.info('error in sending message', result);
          // await addToFailedList(leadId)
          // revalidatePath(`/${accountId}/list`)
        }

        if (result && 'messaging_product' in result) {
          const res = await resAddWAMI(media, result);
          finalMessage = await markMessageSentByWhatsapp(result.messages[0].id, 'sent');
          log.info('add whatsapp message id', res);
          // await addToDeliveredList(leadId)
          // revalidatePath(`/${accountId}/list`)
        }
        const pb = await getPb();
        finalMessage.url = pb.files.getURL(finalMessage, finalMessage.file);
        return {
          finalMessage,
        };
      }
    } else if (audio) {
      let type = 'audio';
      const data = {
        user: `${leadId}`,
        message,
        account: accountId,
        from: 'agent',
        file: audio,
        type: 'audio',
        ...(userId && { created_by: userId }),
        delivery_status: 'sent from wetarseel',
      };
      if (leadId && phoneId && accountId) {
        const media: IMessage & { url?: string } = await addMediaMessageToPb(leadId, accountId, 'agent', data);
        const result = await send_media_message(phoneId, Number(phoneNo), type as WAMessageTypeMedia, media?.url ?? '', 'test.aac', message);
        finalMessage = media;
        if (result && 'error' in result) {
          finalMessage = await markMessageAsFailed(media, result.error.message);
          log.info('error in sending message', result);
          const pb = await getPb();
          finalMessage.url = pb.files.getURL(finalMessage, finalMessage.file);
          return { finalMessage };
          // await addToFailedList(leadId)
          // revalidatePath(`/${accountId}/list`)
        }

        if (result && 'messaging_product' in result) {
          const res = await resAddWAMI(media, result);
          finalMessage = await markMessageSentByWhatsapp(result.messages[0].id, 'sent');
          log.info('add whatsapp message id', res);
          const pb = await getPb();
          finalMessage.url = pb.files.getURL(finalMessage, finalMessage.file);
          return { finalMessage };
        }
      }
    } else {
      if (leadId && accountId && phoneId && phoneNo) {
        const createdMessage = await addMessage(leadId, message, 'agent', accountId, null, deliveryStatus as any, null, null, null, userId);
        finalMessage = createdMessage;
        const sendMessage = await send_message(phoneId, phoneNo, message);
        if ('error' in sendMessage) {
          finalMessage = await markMessageAsFailed(createdMessage, sendMessage.error.message);
          log.info('error in sending message', sendMessage);
          return {
            status: 400,
            finalMessage,
            message: 'Error in sending message',
            createdMessage,
            sendMessage,
          };
          // await addToFailedList(leadId)
          // revalidatePath(`/${accountId}/list`)
        }

        if ('messaging_product' in sendMessage) {
          const res = await resAddWAMI(createdMessage, sendMessage);
          finalMessage = await markMessageSentByWhatsapp(sendMessage.messages[0].id, 'sent');
          log.info('add whatsapp message id', res);
          return {
            status: 200,
            finalMessage,
            message: 'Message sent successfully',
            createdMessage,
            sendMessage,
          };
          // await addToDeliveredList(leadId)
          // revalidatePath(`/${accountId}/list`)
        }
      }
    }
  } catch (e: any) {
    if (e?.response?.data) {
      throw new Error('Error in sending message', {
        cause: e?.response?.data,
      });
    }
    throw new Error('Error in sending message');
  }
};

export const revalidateFullPath = async (path: string) => {
  revalidatePath(path);
};

export const revalidateCampaignAndRedirect = async (account_id: string) => {
  revalidatePath(`/${account_id}/campaign`);
  redirect(`/${account_id}/campaign`, RedirectType.replace);
};

export const send = async (
  allLeads: ILead[],
  template: ITemplateDatabase,
  accountId: string,
  url: string,
  header: IHeader,
  body: IBody,
  prevData: any,
  formData: FormData
) => {
  const user = await getUser();
  const account = await getAccount(accountId);
  const pb = await getPb();
  let matches: any = [];
  if (body.example?.body_text) {
    if (body.example.body_text.filter((item) => item == null).length > 0) {
      return { message: { status: 400, description: 'Please fill all the variables' } };
    }
    matches = body.example.body_text.filter((item) => item.match(/\{\{\d+\}\}/g) && item != undefined);
  }

  if (matches.length > 0) {
    return { message: { status: 400, description: 'Please fill all the variables' } };
  }

  let params = {
    header: header.example?.header_text,
    body: body.example?.body_text,
    defaultBody: [],
    defaultHeader: [],
  };
  for (const pair of formData.entries()) {
    if (pair[0].includes('body')) {
      //@ts-ignore
      params.defaultBody = [...params.defaultBody, pair[1]];
    } else if (pair[0].includes('header')) {
      //@ts-ignore
      params.defaultHeader = [...params.defaultHeader, pair[1]];
    }
  }

  // await pb.collection('templates').update(template.id, {
  //   ...((header.format == 'IMAGE' || header.format == 'VIDEO' || header.format == 'DOCUMENT') && { updated_template: { HEADER: url } }),
  //   params,
  // });
  const sendPromises = allLeads.map(async (leadItem, idx: number) => {
    const phone_number = leadItem.phone_number;
    // add message to db
    const lead = await upsertLead(phone_number, accountId);
    const message = await addMessage(lead.id, template.template_name, 'agent', accountId, null, 'pending', template.id);
    const tempComponents = createIndividualTemplateComponents(template, lead, url, params);
    const res = await send_template({
      recipient_number: lead?.phone_number,
      template: template?.template_name,
      template_id: template?.id,
      phoneId: account.phone_id,
      token: account.access_token,
      components: tempComponents,
      templateLang: template?.template_body.language,
      accountId: account.id,
      message_id: message.id,
    });
    if ('error' in res) {
      await markMessageAsFailed(message, res.error.message);
    } else {
      await Promise.all([
        updateAccountRemainingLimit(accountId, 1),
        updateUserRemainingLimit(user.id, 1, accountId),
        markMessageAsSentAndAddWAMID(message, res),
      ]);
    }
  });

  try {
    const res = await Promise.all(sendPromises);
    revalidatePath(`/${accountId}/admin/send-message/${template.template_name}`);
    return { message: { status: 200, description: 'Message sent successfuly' } };
  } catch (e) {
    console.log(e);
    return { message: { status: 500, description: 'Error in sending message template' } };
  }
};

export const sendCarousel = async (allLeads: ILead[], template: any, accountId: string, messageBody: any, carouselParams: any) => {
  const user = await getUser();
  const account = await getAccount(accountId);
  const pb = await getPb();
  const sendPromises = allLeads.map(async (leadItem, idx: number) => {
    const phone_number = leadItem.phone_number;
    // add message to db
    const lead = await upsertLead(phone_number, accountId);
    const message = await addMessage(lead.id, template.template_name, 'agent', accountId, null, 'sent from wetarseel', template.id);
    const tempComponents = createIndividualCarouselTemplateComponent(template, lead, carouselParams, messageBody);
    const res = await send_template({
      recipient_number: lead?.phone_number,
      template: template?.template_name,
      phoneId: account.phone_id,
      components: tempComponents,
      templateLang: template?.template_body.language,
    });
    if ('error' in res) {
      await markMessageAsFailed(message, res.error.message);
    } else {
      await Promise.all([
        updateAccountRemainingLimit(accountId, 1),
        updateUserRemainingLimit(user.id, 1, accountId),
        markMessageAsSentAndAddWAMID(message, res),
      ]);
    }
  });
  try {
    const res = await Promise.all(sendPromises);
    revalidatePath(`/${accountId}/admin/send-message/${template.template_name}`);
    return { message: { status: 200, description: 'Message sent successfuly' } };
  } catch (e) {
    console.log(e);
    return { message: { status: 500, description: 'Error in sending message template' } };
  }
};

export const updateTemplateParamsActionCarousel = async (template: any, messageBody: any, carouselParams: any, campaign_name: string, accountId: string) => {
  try {
    const pb = await getPb();
    await pb.collection('templates').update(template.id, {
      params: messageBody,
      carousel_params: carouselParams,
    });
  } catch (error) {
    console.log(error);
    return { message: { status: 500, error: JSON.stringify(error) } };
  }
  redirect(`/${accountId}/campaign/create/template/${template.id}?updated=true&campaign_name=${campaign_name}`);
};

export const sendTemplateFromLiveChat = async (lead: ILead, template: ITemplateDatabase, accountId: string, userId: string) => {
  try {
    const { pb } = await server_component_pb();
    const account = await getAccount(accountId);
    const message = await addMessage(lead.id, template.template_name, 'agent', accountId, null, 'pending', template.id, null, null, userId);
    const tempComponents = createTemplateComponents(template, lead);
    const res = await send_template({
      recipient_number: lead?.phone_number,
      template: template?.template_name,
      phoneId: account.phone_id,
      components: tempComponents,
      templateLang: template?.template_body.language,
    });
    if ('error' in res) {
      await markMessageAsFailed(message, res.error.message);
      await increaseMessageLimitQuota(message);
    } else {
      await markMessageAsSentAndAddWAMID(message, res);
    }
    await Promise.all([updateAccountRemainingLimit(accountId, 1), updateUserRemainingLimit(userId, 1, accountId)]);
  } catch (error) {
    console.log(JSON.stringify(error, null, 2));
    throw error;
  }

  return true;
};
export const updateTemplateParamsAction = async (
  template: ITemplateDatabase,
  accountId: string,
  url: string,
  header: IHeader,
  body: IBody,
  campaign_name: string,
  prevData: any,
  formData: FormData
) => {
  const pb = await getPb();
  let matches: any = [];
  if (body.example?.body_text) {
    if (body.example.body_text.filter((item) => item == null).length > 0) {
      return { message: { status: 400, description: 'Please fill all the variables' } };
    }
    matches = body.example.body_text.filter((item) => item.match(/\{\{\d+\}\}/g) && item != undefined);
  }

  if (matches.length > 0) {
    return { message: { status: 400, description: 'Please fill all the variables' } };
  }
  let params = {
    header: header.example?.header_text,
    body: body.example?.body_text,
    defaultBody: [],
    defaultHeader: [],
  };
  for (const pair of formData.entries()) {
    if (pair[0].includes('body')) {
      //@ts-ignore
      params.defaultBody = [...params.defaultBody, pair[1]];
    } else if (pair[0].includes('header')) {
      //@ts-ignore
      params.defaultHeader = [...params.defaultHeader, pair[1]];
    }
  }
  try {
    await pb.collection('templates').update(template.id, {
      ...(header.format == 'IMAGE' && { updated_template: { HEADER: url } }),
      params,
    });
    return { message: { status: 200, description: 'Template updated' } };
  } catch (e) {
    console.log(e);
    return { message: { status: 500, description: 'Error in sending message template' } };
  }
};

export const updateTemplateImage = async (template: ITemplateDatabase, url: string) => {
  try {
    const pb = await getPb();
    await pb.collection('templates').update(template.id, {
      updated_template: { HEADER: url },
    });
  } catch (e) {
    throw e;
  }
};

interface NumberCode {
  code: string;
  emoji: string;
  phoneLength: number;
}

function getPBAUTHValue(cookie: string) {
  // Split the cookie string to separate individual cookies
  const cookies = cookie.split('; ');

  // Find the target cookie that starts with 'pb_auth='
  const targetCookie = cookies.find((c) => c.startsWith('pb_auth='));

  if (!targetCookie) {
    return null;
  }

  // Remove 'pb_auth=' prefix and return the URL decoded value
  const targetCookieValue = targetCookie.substring('pb_auth='.length);
  const decodedCookie = decodeURIComponent(targetCookieValue);

  return decodedCookie;
}

export const createUserAction = async (numberCode: NumberCode, prevState: any, formData: FormData) => {
  try {
    const schema = z
      .object({
        fullName: z.string().min(5, {
          message: 'Full Name must be at least 5 characters.',
        }),
        userName: z
          .string()
          .min(5, {
            message: 'User Name must be at least 5 characters.',
          })
          .refine((s) => !s.includes(' '), {
            message: 'No Spaces Allowed! in User Name',
          }),
        email: z.string().email(),
        password: z.string().min(8, {
          message: 'Password must be at least 8 characters.',
        }),
        phoneNumber: z
          .string()
          .refine((phoneNumber) => phoneNumber.length >= numberCode.phoneLength, {
            message: `Phone number must be at least ${numberCode.phoneLength} digits`,
          })
          .refine((phoneNumber) => /^\d+$/.test(phoneNumber), {
            message: 'Phone number must contain only numbers',
          }),
        confirmPassword: z.string(),
      })
      .refine((data) => data.password === data.confirmPassword, {
        message: "Passwords don't match",
        path: ['confirmPassword'],
      });

    const data = schema.parse({
      fullName: formData.get('fullName'),
      userName: formData.get('userName')?.toString().trim(),
      email: formData.get('email')?.toString().trim().toLowerCase(),
      phoneNumber: formData.get('phoneNumber'),
      password: formData.get('password'),
      confirmPassword: formData.get('confirmPassword'),
    });

    const pb = await getPb();

    const user = await pb.collection('users').getList(1, 10, {
      filter: `email = "${data.email}"`,
    });

    if (user.items.length == 0) {
      const response = await createUser({
        pb,
        user: {
          username: data.userName,
          phoneNumber: `${numberCode.code}${data?.phoneNumber}`,
          name: data.fullName,
          email: data.email,
          emailVisibility: true,
          avatar: null,
          passwordConfirm: data.confirmPassword,
          password: data.password,
          type: 'admin',
          roles: null,
        },
      });

      return response;
      // const res = await pb.collection("users").requestVerification(data.email)

      // const fullCookies = pb.authStore.exportToCookie({ httpOnly: false });
      // const pb_auth = getPBAUTHValue(fullCookies);
      // if (pb_auth) {
      //   const oneDay = 24 * 60 * 60 * 1000;
      //   cookies().set("pb_auth", pb_auth, { expires: Date.now() + oneDay });
      // }

      throw new Error('/auth');
    } else {
      return { message: 'Account already exists' };
    }
  } catch (error: any) {
    console.log(error);
    if (error?.message === '/auth') {
      redirect('/auth');
    }
    return JSON.parse(JSON.stringify(error));
  }
};

export const createBusiness = async (userId: string, prevState: any, formData: FormData) => {
  const companyName = formData.get('companyName') as string;
  const data = {
    name: companyName,
    //hardcoded superuser id
    pb_user_id: [userId, SUPERUSERID],
    show_recent_campaigns: true,
  };
  const pb = await getPb();
  let record = null;
  try {
    record = await pb.collection<Account>('accounts').create(data);
    await pb.collection('users').update(userId, { favorite: record.id });
    await pb.collection<IMessagingLimit>('messaging_limit').create({
      account: record.id,
      user: userId,
      remaining_limit: 0,
      assigned_limit: 0,
    });
  } catch (e) {
    console.log(e);
    return { message: JSON.stringify(e) };
  }
  redirect(`/${record.id}/dashboard`, RedirectType.replace);
};

export const updateLogo = async (userId: string, accountId: string, formData: any, type: string) => {
  const pb = await getPb();
  const file = formData.get('file') as File;
  try {
    if (type == 'user') {
      const result = await pb.collection('users').update(userId, { avatar: file });
    } else if (type == 'business') {
      const result = await pb.collection('accounts').update(accountId, { logo: file });
    }
    revalidatePath(`/${accountId}/profile`);
  } catch (error) {
    console.log(error);
    return error;
  }
};

export const removeLogo = async (userId: string, accountId: string, type: string) => {
  const pb = await getPb();
  try {
    if (type == 'user') {
      const result = await pb.collection('users').update(userId, { avatar: null });
    } else if (type == 'business') {
      const result = await pb.collection('accounts').update(accountId, { logo: null });
    }
    revalidatePath(`/${accountId}/profile`);
  } catch (error) {
    console.log(error);
    return error;
  }
};

export const updateBusiness = async (accountId: string, formData: any, business_location: string, city: string, address: string, TRN: string) => {
  const pb = await getPb();
  const currency = formData.get('currency');
  try {
    await pb.collection('accounts').update(accountId, { business_location, currency, TRN, city, address });
    revalidatePath(`/${accountId}/profile`);
  } catch (err) {
    console.log(err);
  }
};

export const updateBusinessByAdmin = async (prevState: any, formData: FormData) => {
  const pb = await getPb();
  const name = formData.get('name') as string;
  const lock = formData.get('lock') as string;
  const businessId = formData.get('businessId') as string;
  const accountId = formData.get('accountId') as string;

  try {
    await pb.collection('accounts').update(businessId, {
      name,
      // if lock is null then conver it to false else true
      lock: !!lock,
    });
    revalidatePath(`/${accountId}/business-settings`);
  } catch (err) {
    return { message: { status: 400 } };
  }
};

export const updateChatAgent = async (convoId: string, accountId: string, prevState: any, formData: FormData) => {
  const agent = formData.get('agent') as string;
  const pb = await getPb();
  let record = null;
  try {
    record = await pb.collection('conversations').update(convoId, {
      assigned_agent: agent,
    });
  } catch (e) {
    console.log(e);
    return { message: { status: 400 } };
  }
  revalidatePath(`/${accountId}/live-chat`);
  return { message: { status: 200 } };
};

export const isCampaignNameUnique = async (account_id: string, prevState: any, formData: FormData) => {
  const campaign_name = formData.get('campaign_name') as string;
  const pb = await getPb();
  const user = await getUser();
  let record = null;
  try {
    record = await pb.collection<ICampaign>('campaigns').getFullList({
      filter: `name = "${campaign_name}" && account = "${account_id}" && created_by = "${user.id}"`,
    });
  } catch (e) {
    console.log(e);
    return {
      message: `Campaign name "${campaign_name}" already exists. Select another unique name`,
    };
  }
  redirect(`create/template?campaign_name=${campaign_name}`, RedirectType.push);
};

export const createAgentAction = async (account_id: string, formData: any, numberCode: any, rolesArray: any, dashboard_options: any, viewAll_array: any) => {
  try {
    const pb = new PocketBase(pb_url);
    const account = await getAccount(account_id);
    const user = await createAgent({
      pb,
      user: {
        name: formData.userName,
        email: formData.email,
        emailVisibility: true,
        passwordConfirm: 'test1234',
        password: 'test1234',
        type: 'agent',
        roles: rolesArray,
        dashboard_options: dashboard_options,
        view_all: viewAll_array,
      },
      account_id,
    });

    await pb.collection<IMessagingLimit>('messaging_limit').create({
      account: account_id,
      user: user.id,
      remaining_limit: 0,
      assigned_limit: 0,
      limit_start_date: account.limit_start_date,
      limit_end_date: account.limit_end_date,
    });
    revalidatePath(`/${account_id}/agent`);
    redirect(`/${account_id}/agent`);
  } catch (error) {
    console.log(error);
    throw error;
  }
};

export const updateLeadAction = async (listName: string, leadsArray: ILead[], accountId: string) => {
  const list = await createList(listName, accountId);
  const pb = await getPb();
  const batch = pb.createBatch();
  leadsArray.forEach((lead) => {
    const { id } = lead;
    updateLeads({ batch, listId: list.id, leadId: id });
  });
  await batch.send();
  revalidatePath(`/${accountId}/list`);
  redirect(`/${accountId}/list?list_created=yes`);
};

export const updateList = async (listId: string, listName: string, removedLeadsArray: string[], addedLeadsArray: IExpandedLead[], accountId: string) => {
  try {
    // Update list name
    await updateListName(listId, listName);

    // Remove leads from the list and add leads to the list in parallel
    await Promise.all([...removedLeadsArray.map((id) => removeLeadsFromList(listId, id)), ...addedLeadsArray.map((lead) => addLeadToList(listId, lead.id))]);

    // Revalidate and redirect
    revalidatePath(`/${accountId}/list`);
  } catch (error) {
    console.error('Error updating list:', error);
    throw error;
  }
};

//upload country codes:
export const uploadCountryCodes = async (transformedLeads: any) => {
  const results = await Promise.all(
    transformedLeads.map(async (lead: any) => {
      try {
        await addCountryCode(lead.label, lead.code, lead.phone_code, lead.phone_length);
        return { status: 'success', lead };
      } catch (error: any) {
        return { status: 'failure', lead, error: error.message };
      }
    })
  );
};

export const getAgentsAndUsers = async (accountId: string) => {
  try {
    const pb = await getPb();
    const res = await pb.collection('accounts').getOne(accountId, {
      expand: 'pb_user_id',
    });
    const users = res!.expand!.pb_user_id;
    return users;
  } catch (e) {
    console.log(e);
  }
};
export const impersonate = async (userId: string) => {
  try {
    const pb = await getPb();
    // const res = await pb.collection('accounts').getOne(accountId, {
    //   expand: 'pb_user_id',
    // });
    // const admin = res!.expand!.pb_user_id.find((user: any) => user.type === 'admin');
    const cookieStore = cookies();
    cookieStore.set('impersonate', userId);
  } catch (e) {
    console.log(e);
  }
};

export const clearImpersonate = async () => {
  cookies().delete('impersonate');
};

export const getTemplateFromFacebook = async (accountId: string) => {
  const pb = await getPb();
  const account = await getAccount(accountId);
  const raw = await fetch(`https://graph.facebook.com/v19.0/${account.waba_id}/message_templates`, {
    method: 'GET',
    headers: {
      'Content-Type': 'application/json',
      Authorization: `Bearer ${account.access_token}`,
    },
  });
  const data = await raw.json();
  return data;
};

export const createInteractiveMessage = async (prevState: any, formData: FormData) => {
  const account = JSON.parse(formData.get('account') as string) as Account;
  const type = formData.get('type') as string;
  const header_type = formData.get('header_type') as string;
  const header_text = formData.get('header_text') as string;
  const body_text = formData.get('body_text') as string;
  const footer_text = formData.get('footer_text') as string;
  const button1 = formData.get('button1') as string;
  const button2 = formData.get('button2') as string;
  const button3 = formData.get('button3') as string;
  const interactive = {
    type,
    header: {
      type: header_type,
      text: header_text,
    },

    ...(body_text && { body: { text: body_text } }),
    ...(footer_text && { footer: { text: footer_text } }),
    action: {
      buttons: [
        {
          type: 'reply',
          reply: {
            id: button1.toLowerCase().split(' ').join('-'),
            title: button1,
          },
        },
        {
          type: 'reply',
          reply: {
            id: button2.toLowerCase().split(' ').join('-'),
            title: button2,
          },
        },
        {
          type: 'reply',
          reply: {
            id: button3.toLowerCase().split(' ').join('-'),
            title: button3,
          },
        },
      ],
    },
  };
  await actually_send_interactive_message(interactive, account, '************');
};

// actually send interactive message
//TODO: Add interactive type
export const actually_send_interactive_message = async (interactive: any, account: Account, recipient_number: string) => {
  const phoneId = account.phone_id;
  const res2 = await insertInteractiveMessage(interactive);
  const user = await upsertLead(recipient_number, account.id);
  const userId = user.id;
  const createdMessage = await addMessage(userId, 'Interactive message', 'agent', account.id, null, 'pending', null, res2?.id ?? null);
  const sendMessage = await send_interactive_message({
    recipient_number,
    interactive,
    phoneId,
  });

  if ('error' in sendMessage) {
    throw new Error(sendMessage.error.message);
  }
  if ('messaging_product' in sendMessage) {
    const res = await resAddWAMI(createdMessage, sendMessage);
    log.info('add whatsapp message id', res);
  }
};

export const redirectFunction = async (path: any, mapping: boolean) => {
  if (mapping) {
    redirect(`${path}?upload=yes`);
  } else {
    redirect(path);
  }
};

export const updateAccountAccessToken = async (account: Account) => {
  if (account.waba_id) {
    let data = await (
      await fetch(`${PATHS[process.env.NODE_ENV]}/api/get-phone-verification?waba_id=${account.waba_id}`, {
        cache: 'no-store',
      })
    ).json();
    data = data.data.filter((_data: IPhoneVerificationResponse) => _data.id == account.phone_id)[0] as IPhoneVerificationResponse;
    await updateAccountDetails(account.id, data);

    const expiry_date = dayjs(account.expires_at);
    const now = dayjs();
    const expires_in_diff = expiry_date.diff(now, 'days');
    // const expires_in = dayjs(account.expires_at).fromNow()
    console.log('token expires in ' + expires_in_diff + ' days');
    const pb = await getPb();
    if (expires_in_diff <= 15) {
      try {
        const res = await fetch(`${PATHS[process.env.NODE_ENV]}/api/refresh-token-update?fb_exchange_token=${account.access_token}`);
        const status = await res.json();
        console.log(status);
        let old_access_token = account.access_token;
        let access_token = status.access_token;
        let expires_in = status.expires_in;
        let expires_at = dayjs().add(expires_in, 'second').format();
        console.log(expires_at);
        await pb.collection('accounts').update(account.id, { expires_at, access_token });
        if (status?.error) {
          console.log(status);
        }
        try {
          const res = await fetch(`${PATHS[process.env.NODE_ENV]}/api/refresh-token-revoke?access_token=${old_access_token}`);
          const status = await res.json();
          if (status?.success == 'false') {
            console.log(status);
          }
        } catch (error) {
          console.log(error);
        }
      } catch (error) {
        console.log(error);
      }
    }
  }
};

export const updateAccountToPackage = async (prevState: any, formData: FormData) => {
  const accountId = formData.get('accountId') as string;
  const packageDetails = formData.get('packageDetails') as string;
  const accound_id = formData.get('account_id') as string;
  const [packageid, remaining_limit, period] = packageDetails.split('-');
  try {
    const res = await updateAccountWithPackageId(accountId, packageid, parseInt(remaining_limit), period);

    revalidatePath(`/${accound_id}`);
    return { message: { status: 200 } };
  } catch (error) {
    console.log(error);
    return { message: { status: 400, error: JSON.stringify(error) } };
  }
};

const sendCampaign = async ({
  campaign,
  sendType,
  list_id,
  template_name,
  account_id,
  template_id,
  user,
  start,
  date,
}: {
  campaign: any;
  sendType: string;
  list_id: any[];
  template_name: string;
  account_id: string;
  template_id: string;
  user: any;
  start: any;
  date: string;
}) => {
  await myQueue.add('createCampaign', { campaign, sendType, list_id, template_name, account_id, template_id, user, start, date });
  if (sendType === 'sendNow') {
    return {
      message: {
        status: 200,
        type: 'sendNow',
      },
    };
  } else {
    return {
      message: {
        status: 200,
        type: 'scheduled',
      },
    };
  }
};

export const createCampaign = async (prevState: any, formData: FormData) => {
  const pb = await getPb();
  const campaign_name = formData.get('campaign_name') as string;
  const template_name = formData.get('template_name') as string;
  const account_id = formData.get('account_id') as string;
  const list_id = JSON.parse(formData.get('list_id') as string) as string[];
  const template_id = formData.get('template_id') as string;
  const sendType = formData.get('sendType') as string;
  const date = formData.get('date') as string;
  const totalContacts = formData.get('totalContacts') as string;
  const next_retry_date = formData.get('next_retry_date') as string;
  let result = null;
  const user = await getUser();
  const start = new Date().getTime();

  try {
    const record = await pb.collection<ICampaign>('campaigns').getList(1, 50, {
      filter: `name = "${campaign_name}" && account = "${account_id}"`,
    });
    //if there is a campaign with the same name
    if (record.totalItems > 0) {
      //if there is a campaign with the same name and it is a draft
      if (record.items[0].type == 'Draft') {
        //update the draft campaign with the new data
        const campaign = await pb.collection('campaigns').update(
          record.items[0].id,
          {
            name: campaign_name,
            leads_list: list_id.flat(),
            account: account_id,
            template: template_id,
            created_by: user?.id,
            type: sendType == 'sendNow' ? 'Published' : 'Scheduled',
            status: sendType == 'sendNow' ? 'Sending' : 'Scheduled',
            scheduled_time: sendType == 'sendNow' ? null : date,
            draft_url: null,
            next_retry_date,
            retry_count: 0,
            total_messages_count: parseInt(totalContacts),
          },
          {
            expand: 'account',
          }
        );
        //sent campaign
        return await sendCampaign({ campaign, sendType, list_id, template_name, account_id, template_id, user, start, date });
      }
      return {
        message: {
          status: 403,
          error: `Campaign name "${campaign_name}" already exists. Select another unique name`,
        },
      };
    } else {
      //create new campaign
      const campaign = await pb.collection('campaigns').create(
        {
          name: campaign_name,
          leads_list: list_id.flat(),
          account: account_id,
          template: template_id,
          created_by: user?.id,
          type: sendType == 'sendNow' ? 'Published' : 'Scheduled',
          scheduled_time: sendType == 'sendNow' ? null : date,
          status: sendType == 'sendNow' ? 'Sending' : 'Scheduled',
          next_retry_date,
          retry_count: 0,
          total_messages_count: parseInt(totalContacts),
        },
        {
          expand: 'account',
        }
      );
      return await sendCampaign({ campaign, sendType, list_id, template_name, account_id, template_id, user, start, date });
    }
  } catch (e: any) {
    console.log(e);
    return {
      message: {
        status: 400,
        error: JSON.stringify(e?.response?.data, null, 2),
      },
    };
  }
};

export const toggleUserState = async (userId: string, accountId: string, lock: boolean) => {
  const pb = await getPb();

  try {
    await pb.collection('users').update(userId, {
      // if lock is null then conver it to false else true
      lock: !lock,
    });
    revalidatePath(`/${accountId}/business-settings`);
  } catch (err) {
    return { message: { status: 400 } };
  }
};

export const getTeam = async (teamId: string) => {
  const pb = await getPb();
  const team = await pb.collection('teams').getOne(teamId);
  return team;
};

export const addAgentToTeam = async (agentId: string, teamId: string | null, accountId: string) => {
  const pb = await getPb();
  if (!teamId) {
    return { message: { status: 400 } };
  }
  try {
    await pb.collection('users').update(agentId, {
      team: teamId,
    });
    revalidatePath(`/${accountId}/teams/create-team`);
  } catch (err) {
    return { message: { status: 400 } };
  }
};

export const removeAgent = async (agentId: string, selectedTeam: string, accountId: string) => {
  const pb = await getPb();
  try {
    await pb.collection('users').update(agentId, {
      team: null,
    });
    revalidatePath(`/${accountId}/teams/create-team`);
  } catch (err) {
    return { message: { status: 400 } };
  }
};

export const addTeamAction = async (prevState: any, formData: FormData) => {
  console.log('here');
  const teamName = formData.get('teamName') as string;
  const accountId = formData.get('accountId') as string;
  return await addTeam(teamName, accountId);
};

export const verifyOTP = async (prevState: any, formData: FormData) => {
  try {
    const otpId = formData.get('otpId') as string;
    const mfaId = (formData.get('mfaId') ?? '') as string;
    const otp = formData.get('otp') as string;

    const pb = new PocketBase(pb_url);
    if (mfaId !== '') {
      await pb.collection('users').authWithOTP(otpId, otp, { mfaId: mfaId, expand: 'roles' });
    } else {
      await pb.collection('users').authWithOTP(otpId, otp, { expand: 'roles' });
    }

    const cookie = pb.authStore.exportToCookie({ httpOnly: false });
    const cookieStore = cookies();
    cookieStore.set('pb_auth', decodeURIComponent(cookie.replace('pb_auth=', '')).replace(/; Path=.*/g, ''), {
      expires: new Date(new Date().getTime() + 1000 * 60 * 60 * 24 * 15),
    });
  } catch (e) {
    console.log(e);
    return { message: { status: 400 } };
  }

  redirect('/auth');
};

export const handleTransform = async (id: string, fileName: string, selectedMappings: Record<string, string>, account_id: string, listId?: string) => {
  const user = await getUser();
  myQueue.add('uploadContacts', { id, fileName, selectedMappings, account_id, listId, user });
};

export const uploadOneLead = async ({
  pb,
  account,
  listFilters,
  log,
  _listId,
  chunk,
  user,
}: {
  pb: PocketBase;
  account: string;
  listFilters: IExpandedList[] | null;
  log: any;
  _listId: string | null;
  chunk: ILead[];
  user: User;
}) => {
  let result: any[] = [];
  try {
    // use measurement to measure time spent
    const start = new Date().getTime();
    const filterString = chunk
      .map((lead) => {
        const phone_num = lead.phone_number.replace(/\D/g, '').trim();
        return `(phone_number = "${phone_num}" && account = "${account}")`;
      })
      .join(' || ');
    const resultList = await pb.collection<ILead>('leads').getFullList({
      filter: filterString,
      skipTotal: true,
      fields: 'id, phone_number, country, tags, name, status',
    });
    chunk.forEach(async (lead) => {
      const phone_num = lead.phone_number.replace(/\D/g, '').trim();
      const leadName = lead.name.length === 0 ? phone_num : lead.name;
      let phoneOutput = parsePhoneNumber(`+${phone_num}`, 'US');
      if (!phoneOutput?.country) {
        result.push({
          status: 'failure',
          lead,
          error: 'The phone number is invalid',
        });
      } else if (!phoneOutput.isValid()) {
        result.push({
          status: 'failure',
          lead,
          error: 'The phone number is invalid',
        });
      } else {
        let tagsArray = lead.tags ? lead.tags : [];
        const { tags, country, name, status, phone_number, ...rest } = lead;
        let existingRecord = resultList.find((result) => (result as ILead).phone_number === phone_num) ?? null;
        upsertNumberUpload(
          existingRecord as ILead,
          _listId,
          account!,
          phone_num,
          user,
          leadName,
          log.id,
          tagsArray,
          lead.shopify_id,
          lead.status,
          phoneOutput.country,
          listFilters,
          rest as unknown as Record<string, string>
        );
        // time taken
        const end = new Date().getTime();
        result.push({ status: 'success', lead });
      }
    });
  } catch (error: any) {
    console.log(error);
    // result = { status: 'failure', lead, error: error.message };
  }
  return result;
};

export const handleSyncShopifyCustomer = async ({ shop, customers, isBatch, batchSize, totalProcessed }: ISyncShopifyCustomerResponse) => {
  const pb = await getPb();
  try {
    const syncDate = dayjs().format('YYYY-MM-DD HH:mm:ss');
    console.log(`Starting Shopify sync for shop: ${shop}`);
    console.log(`Processing ${customers.length} customers, Batch: ${isBatch ? `${totalProcessed}/${batchSize}` : 'Full sync'}`);

    const newLeads = customers.map((customer: ISyncShopifyCustomer) => {
      const newLead = {} as ILead;
      if (!customer.firstName) {
        newLead.name = customer.phone;
      } else {
        newLead.name = `${customer.firstName} ${customer.lastName}`;
      }
      newLead.phone_number = customer.phone;
      newLead.status = 'New';
      newLead.tags = customer.tags;
      newLead.shopify_id = customer.id;
      return newLead;
    });

    console.log(`Transformed ${newLeads.length} customers to leads`);

    const rec = await pb.collection('shopify_accounts').getFirstListItem(`store_details.myshopifyDomain = "${shop}"`);
    console.log(`Found Shopify account: ${rec.id}`);

    const user = await pb.collection<User>('users').getFirstListItem(`email = "${rec.store_details.email}"`);
    console.log(`Found associated user: ${user.id}`);

    const log = await pb.collection<ILog>('logs').create({
      log_name: `Shopify Sync ${syncDate}`,
      account: rec.account,
      created_by: user.id,
      number_of_leads: newLeads.length,
      pending: 'false',
      log_data: newLeads.map((lead) => ({
        name: lead.name,
        phone_number: lead.phone_number,
        upload: lead.phone_number ? 'successful' : 'failed',
        shopify_id: lead.shopify_id,
        tags: lead.tags,
      })),
    });

    console.log(`Created sync log: ${log.id}`);

    const listName = isBatch ? `Shopify Sync Batch ${totalProcessed}/${batchSize} - ${syncDate}` : `Shopify Full Sync - ${syncDate}`;

    const list = await pb.collection('lists').create({
      name: listName,
      account: rec.account,
      created_by: user.id,
      list_filters: null,
      type: 'static',
    });

    console.log(`Created list: ${list.id} with name: ${listName}`);

    const result = await uploadOneLead({
      pb,
      account: rec.account,
      listFilters: null,
      chunk: newLeads.filter((lead) => lead.phone_number),
      _listId: list.id,
      log,
      user: user,
    });
    console.log(result);

    return {
      success: true,
      // listId: list.id,
      accountId: rec.account,
      processedLeads: newLeads.length,
      // logId: log.id,
    };
  } catch (error) {
    console.error('Shopify sync error:', {
      shop,
      error,
      timestamp: new Date().toISOString(),
      batchInfo: {
        isBatch,
        batchSize,
        totalProcessed,
      },
    });
    throw error;
  }
};

export const createShopifyCustomer = async ({ shop, customer }: { shop: string; customer: ICreateShopifyCustomer }) => {
  const pb = await getPb();
  try {
    const rec = await pb.collection('shopify_accounts').getFirstListItem(`store_details.myshopifyDomain = "${shop}"`);
    console.log(`Found Shopify account: ${rec.id}`);

    const user = await pb.collection<User>('users').getFirstListItem(`email = "${rec.store_details.email}"`);
    console.log(`Found associated user: ${user.id}`);

    const newLead = {} as ILead;
    if (!customer.first_name) {
      newLead.name = customer.phone;
    } else {
      newLead.name = `${customer.first_name} ${customer.last_name}`;
    }
    newLead.phone_number = customer.phone;
    newLead.status = 'New';
    newLead.shopify_id = customer.id.toString();

    const result = await uploadOneLead({
      pb,
      account: rec.account,
      listFilters: null,
      chunk: [newLead],
      _listId: null,
      log: null,
      user: user,
    });

    return {
      success: true,
      accountId: rec.account,
    };
  } catch (error) {
    console.error('Shopify sync error:', {
      shop,
      error,
      timestamp: new Date().toISOString(),
    });
    throw error;
  }
};
// Usage in your function:
export const blockLeadFromFacebook = async (phone_number: string, PHONE_ID: string, current_block_status: boolean) => {
  try {
    const _body = {
      messaging_product: 'whatsapp',
      block_users: [
        {
          user: phone_number,
        },
      ],
    };
    const raw = await fetch(`https://graph.facebook.com/${PHONE_ID}/block_users`, {
      method: current_block_status ? 'DELETE' : 'POST',
      headers: {
        Authorization: `Bearer ${process.env.CLOUD_API_ACCESS_TOKEN}`,
        'Content-Type': 'application/json', // <-- Add this line
      },
      body: JSON.stringify(_body),
    });
    const data: BlockUsersResponse = await raw.json();
    console.log(data);
    if ('failed_users' in data.block_users) {
      log.error(`Error in ${current_block_status ? 'unblocking' : 'blocking'} user`, {
        error: JSON.stringify(data),
      });
      const failedMessage = data.block_users.failed_users[0].errors[0].message;
      if (failedMessage == 'Re-engagement check failed') {
        return {
          message: {
            status: 400,
            description: `User can be ${current_block_status ? 'unblocked' : 'blocked'} only when 24 hour window is opened. User cannot be blocked right now.`,
          },
        };
      }
      return {
        message: {
          status: 400,
          description: `Something went wrong in ${current_block_status ? 'unblocking' : 'blocking'} the user`,
        },
      };
    }
    return {
      message: {
        status: 200,
        description: `User ${current_block_status ? 'Unblocked' : 'Blocked'}`,
      },
    };
    // return data;
  } catch (error) {
    log.error('Error in blocking user', {
      error: JSON.stringify(error),
    });
  }
};

export const blockUnBlockLeadFromContact = async ({ account, lead }: { account: Account; lead: ILead }) => {
  const pb = await getPb();
  const phone_id = account.phone_id;
  const convo = await pb.collection<IConversation>('conversations').getFirstListItem(`from.id = "${lead.id}"`);
  const res = await blockLeadFromFacebook(lead?.phone_number, phone_id, lead.blocked);
  return { convo, res };
};
