import admin from 'firebase-admin';
import { cert, getApps } from 'firebase-admin/app';

************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
// Initialize Firebase only if no app is already initialized
if (!getApps().length) {
  admin.initializeApp({
    credential: cert({
      projectId: 'wetarseelfirebase',
      clientEmail: '<EMAIL>',
      privateKey,
    }),
  });
}

/**
 * Sends a push notification to a specific device using Firebase Cloud Messaging
 * @param fcmToken The FCM token of the device to send the notification to
 * @param title The title of the notification
 * @param body The body content of the notification
 * @param data Optional additional data to send with the notification
 * @returns A promise that resolves with the message ID if successful, or rejects with an error
 */
const sendPushNotification = async (fcmToken: string, title: string, body: string, data?: Record<string, string>) => {
  // Validate FCM token
  if (!fcmToken || fcmToken.trim() === '') {
    console.warn('Push notification not sent: Empty FCM token');
    return null;
  }

  // Create the notification message
  const message = {
    token: fcmToken, // Device-specific token
    notification: {
      title: title || 'New Message', // Default title if none provided
      body: body || 'You have a new message', // Default body if none provided
    },
    // Add optional data payload if provided
    ...(data && { data }),
    // Add Android specific configuration
    android: {
      priority: 'high' as const, // Type assertion to match expected 'high' | 'normal'
      notification: {
        sound: 'default',
        clickAction: 'FLUTTER_NOTIFICATION_CLICK',
      },
    },
    // Add Apple specific configuration
    apns: {
      payload: {
        aps: {
          sound: 'default',
          badge: 1,
          contentAvailable: true,
        },
      },
    },
  };

  try {
    // Send the notification
    const response = await admin.messaging().send(message);
    console.log('Push Notification Sent Successfully:', response);
    return response;
  } catch (error) {
    // Log detailed error information
    console.error('Error sending push notification:', error);
    if (error instanceof Error) {
      console.error('Error details:', error.message);
    }
    // Re-throw the error to allow the caller to handle it
    throw error;
  }
};

export { sendPushNotification };
