// flowHelpers.test.ts
import { FlowBot } from './flowHelpers';
import { beforeEach, describe, expect, MockInstance, test, vi } from 'vitest';
import { data } from './flowMockData2';
import { type Edge, type Node } from '@xyflow/react';
import { Account } from './types';

describe('FlowBot', () => {
  let flowBot: FlowBot;
  let sendInteractiveOrTextMessageMock: MockInstance;
  let updateStateMock: MockInstance;
  let assignAgentMock: MockInstance;
  let removeStateMock: MockInstance;

  beforeEach(() => {
    flowBot = new FlowBot({
      conversationId: 'convo1',
      account: {
        id: 'account1',
        name: 'Account 1',
      } as any as Account,
      flow: {
        name: 'Flow 1',
        flow: {
          nodes: data.nodes as Node[],
          edges: data.edges,
        },
      },
      phoneNo: '**********',
      leadId: 'lead1',
      state: '',
      userMessage: '',
    });

    sendInteractiveOrTextMessageMock = vi.spyOn(flowBot, 'sendInteractiveOrTextMessage').mockImplementation((node) => {
      return Promise.resolve();
    });

    updateStateMock = vi.spyOn(flowBot, 'updateState').mockImplementation(() => {
      return Promise.resolve();
    });

    removeStateMock = vi.spyOn(flowBot, 'removeState').mockImplementation(() => {
      return Promise.resolve();
    });

    assignAgentMock = vi.spyOn(flowBot, 'assignAgent').mockImplementation(() => {
      return Promise.resolve();
    });
  });

  test('test if keyword matching for the message works', async () => {
    flowBot.userMessage = 'hi';
    flowBot.state = '';
    await flowBot.runFlow();

    const service2 = data.nodes.find((node) => node.id === 'service2');
    expect(sendInteractiveOrTextMessageMock).toHaveBeenCalledOnce();
    expect(updateStateMock).toHaveBeenCalledWith({ node: service2 });
    // expect(sendInteractiveOrTextMessageMock).toHaveBeenCalledWith();
  });

  test('messages working', async () => {
    flowBot.userMessage = 'deals';
    flowBot.state = 'Flow 1:service2';
    await flowBot.runFlow();

    expect(sendInteractiveOrTextMessageMock).toHaveBeenCalledOnce();
    expect(updateStateMock).toHaveBeenCalledTimes(0);
    expect(removeStateMock).toHaveBeenCalledOnce();
  });

  test('messages working 2', async () => {
    flowBot.userMessage = '2discounts';
    flowBot.state = 'Flow 1:service2';
    await flowBot.runFlow();

    expect(sendInteractiveOrTextMessageMock).toHaveBeenCalledOnce();
    expect(updateStateMock).toHaveBeenCalledTimes(0);
    expect(removeStateMock).toHaveBeenCalledOnce();
  });

  // test other flows
  test('test if option matching works', async () => {
    flowBot.userMessage = 'hi';
    flowBot.state = '';
    await flowBot.runFlow();
  });
});
