// @ts-ignore
import html2pdf from 'html2pdf.js';
import dayjs from 'dayjs';
import { Converter } from 'easy-currencies';

interface IInvoiceDataProps {
  invoiceNumber: string;
  billTo: {
    name: string;
    location: string;
  };
  invoiceDate: string;
  dueDate: string;
  billingMonths: number;
  unitAmount: number;
  taxAmount: number;
  otherCharges: number;
}

export async function generateInvoicePDF(data: IInvoiceDataProps, paymentStatus: string): Promise<FormData> {
  const converter = new Converter();

  const calculateSubTotal = async () => {
    if (data.billTo.location == 'Pakistan') {
      return await converter.convert(data.billingMonths * data.unitAmount, 'AED', 'PKR');
    }
    return data.billingMonths * data.unitAmount;
  };

  const calculateTotal = async () => {
    const subTotal = await calculateSubTotal();
    if (data.billTo.location == 'Pakistan') {
      // const sum = await converter.convert(data.billingMonths * data.unitAmount, 'AED', 'PKR');
      return subTotal + (await calculateTax()) + (await calculateOtherCharges());
    }
    return subTotal + data.taxAmount + data.otherCharges;
  };
  const calculateTax = async () => {
    if (data.billTo.location == 'Pakistan') {
      return await converter.convert(data.taxAmount, 'AED', 'PKR');
    }
    return data.taxAmount;
  };
  const calculateOtherCharges = async () => {
    if (data.billTo.location == 'Pakistan') {
      return await converter.convert(data.otherCharges, 'AED', 'PKR');
    }
    return data.otherCharges;
  };
  const calculateUnitAmount = async () => {
    if (data.billTo.location == 'Pakistan') {
      return await converter.convert(data.unitAmount, 'AED', 'PKR');
    }
    return data.unitAmount;
  };

  const total = await calculateTotal();
  const subTotal = await calculateSubTotal();
  const unit_amount = await calculateUnitAmount();
  const other_charges = await calculateOtherCharges();
  const tax_amount = await calculateTax();

  let invoiceContent;
  if (data.billTo.location == 'Pakistan') {
    if (paymentStatus == 'paid') {
      invoiceContent = `
      <div class="w-full max-w-4xl p-8 bg-white" style="font-family: Arial, sans-serif;">
        <div class="flex justify-between items-center mb-8">
          <div class="items-center">
            <img src=${'/assets/Tech-Inoviq.png'} alt="Company Logo" class="w-44 h-16 mr-4" />
            <div class="flex-col space-y-0">
              <h2 class="text-md font-bold">Tech Inoviq Solutions (SMC-PVT) Ltd.</h2>
                          <p class="text-sm text-gray-600">106-C,Jami Commercial Street 11</p>
              <p class="text-sm text-gray-600">DHA Phase 2 Ext</p>
              <p class="text-sm text-gray-600">Karachi, Pakistan</p>
              <p class="text-sm text-gray-600">***********</p>
              <p class="text-sm text-gray-600">NTN: 4747935
             </p>
            </div>
          </div>
  
            <div>
            <img src=${'/assets/paid_stamp.png'} alt="Company Logo" class="w-40 h-40 mr-4" />
          </div>
  
          <div class="text-right">
            <h1 class="text-2xl font-bold mb-2">TAX INVOICE</h1>
            <p class="text-sm text-gray-600 font-semibold"># ${data.invoiceNumber}</p>
            <p class="mt-2 text-md font-medium">
              Balance Due
              <br />
              <span class="text-xl font-semibold">PKR ${total.toFixed(2)}</span>
            </p>
          </div>
        </div>
  
        <div class="grid grid-cols-2 gap-8 mb-8">
          <div>
            <p class=" mb-2 text-sm">Bill To</p>
            <p class="text-sm font-bold">${data.billTo.name}</p>
  
          </div>
          <div class="text-right">
            <div class="grid grid-cols-2">
              <p class="text-sm font-semibold">Invoice Date:</p>
              <p class="text-sm">${data.invoiceDate}</p>
                         <p class="text-sm font-semibold">Terms:</p>
              <p class="text-sm">Due On Receipt
             </p>
              <p class="text-sm font-semibold">Due Date:</p>
              <p class="text-sm">${data.dueDate}</p>
            </div>
          </div>
        </div>
  
        <div class="mb-8">
          <table class="w-full" style="border-collapse: collapse;">
            <thead>
              <tr class="bg-gray-100">
                <th class="py-2 px-4 text-left">Description</th>
                <th class="py-2 px-4 text-right">Qty</th>
                <th class="py-2 px-4 text-right">Rate</th>
                <th class="py-2 px-4 text-right">Amount</th>
              </tr>
            </thead>
            <tbody>
              <tr class="border-b">
                <td class="py-2 px-4">WeTarseel Monthly Subscription</td>
                <td class="py-2 px-4 text-right">${data.billingMonths}</td>
                <td class="py-2 px-4 text-right">PKR ${unit_amount.toFixed(2)}</td>
                <td class="py-2 px-4 text-right">PKR ${subTotal.toFixed(2)}</td>
              </tr>
              <tr>
                <td colspan="3" class="py-2 px-4 text-right font-semibold">Tax</td>
                <td class="py-2 px-4 text-right">PKR ${tax_amount.toFixed(2)}</td>
              </tr>
              <tr>
                <td colspan="3" class="py-2 px-4 text-right font-semibold">Other Charges</td>
                <td class="py-2 px-4 text-right">PKR ${other_charges.toFixed(2)}</td>
              </tr>
              <tr class="font-bold">
                <td colspan="3" class="py-2 px-4 text-right">Total</td>
                <td class="py-2 px-4 text-right">PKR ${total.toFixed(2)}</td>
              </tr>
            </tbody>
          </table>
        </div>
  
         <div class="mt-8 text-sm text-gray-600">
    <p>Thanks for your business.</p>
  </div>
       <div class="mb-8">
      <h3 class="mb-4 text-sm">Bank Details:</h3>
      <div class="grid grid-cols-2 gap-y-0 text-sm">
        <p class="m-0">Account title: <strong>TECH INOVIQ SOLUTIONS (SMC-PVT) Ltd</strong></p>
        <p class="m-0">Bank Name: <strong>MEEZAN BANK</strong></p>
        <p class="m-0">Account number: <strong>**************</strong></p>
        <p class="m-0">IBAN: <strong>************************</strong></p>
        <p class="m-0">Swift: <strong>MEZNPKKA</strong></p>
      </div>
    </div>
      </div>
    `;
    } else {
      invoiceContent = `
        <div class="w-full max-w-4xl p-8 bg-white" style="font-family: Arial, sans-serif;">
          <div class="flex justify-between items-center mb-8">
            <div class="items-center">
              <img src=${'/assets/Tech-Inoviq.png'} alt="Company Logo" class="w-44 h-16 mr-4" />
              <div class="flex-col space-y-0">
          <h2 class="text-md font-bold">Tech Inoviq Solutions (SMC-PVT) Ltd.</h2>
                          <p class="text-sm text-gray-600">106-C,Jami Commercial Street 11</p>
              <p class="text-sm text-gray-600">DHA Phase 2 Ext</p>
              <p class="text-sm text-gray-600">Karachi, Pakistan</p>
              <p class="text-sm text-gray-600">***********</p>
              <p class="text-sm text-gray-600">NTN: 4747935
               </p>
              </div>
            </div>
    
            <div class="text-right">
              <h1 class="text-2xl font-bold mb-2">TAX INVOICE</h1>
              <p class="text-sm text-gray-600 font-semibold"># ${data.invoiceNumber}</p>
              <p class="mt-2 text-md font-medium">
                Balance Due
                <br />
                <span class="text-xl font-semibold">PKR ${total.toFixed(2)}</span>
              </p>
            </div>
          </div>
    
          <div class="grid grid-cols-2 gap-8 mb-8">
            <div>
              <p class=" mb-2 text-sm">Bill To</p>
              <p class="text-sm font-bold">${data.billTo.name}</p>
    
            </div>
            <div class="text-right">
              <div class="grid grid-cols-2">
                <p class="text-sm font-semibold">Invoice Date:</p>
                <p class="text-sm">${data.invoiceDate}</p>
                           <p class="text-sm font-semibold">Terms:</p>
                <p class="text-sm">Due On Receipt
               </p>
                <p class="text-sm font-semibold">Due Date:</p>
                <p class="text-sm">${data.dueDate}</p>
              </div>
            </div>
          </div>
    
          <div class="mb-8">
            <table class="w-full" style="border-collapse: collapse;">
              <thead>
                <tr class="bg-gray-100">
                  <th class="py-2 px-4 text-left">Description</th>
                  <th class="py-2 px-4 text-right">Qty</th>
                  <th class="py-2 px-4 text-right">Rate</th>
                  <th class="py-2 px-4 text-right">Amount</th>
                </tr>
              </thead>
              <tbody>
                <tr class="border-b">
                  <td class="py-2 px-4">WeTarseel Monthly Subscription</td>
                  <td class="py-2 px-4 text-right">${data.billingMonths}</td>
                  <td class="py-2 px-4 text-right">PKR ${unit_amount.toFixed(2)}</td>
                  <td class="py-2 px-4 text-right">PKR ${subTotal.toFixed(2)}</td>
                </tr>
                <tr>
                  <td colspan="3" class="py-2 px-4 text-right font-semibold">Tax</td>
                  <td class="py-2 px-4 text-right">PKR ${tax_amount.toFixed(2)}</td>
                </tr>
                <tr>
                  <td colspan="3" class="py-2 px-4 text-right font-semibold">Other Charges</td>
                  <td class="py-2 px-4 text-right">PKR ${other_charges.toFixed(2)}</td>
                </tr>
                <tr class="font-bold">
                  <td colspan="3" class="py-2 px-4 text-right">Total</td>
                  <td class="py-2 px-4 text-right">PKR ${total.toFixed(2)}</td>
                </tr>
              </tbody>
            </table>
          </div>
    
           <div class="mt-8 text-sm text-gray-600">
      <p>Thanks for your business.</p>
    </div>
         <div class="mb-8">
        <h3 class="mb-4 text-sm">Bank Details:</h3>
          <div class="grid grid-cols-2 gap-y-0 text-sm">
        <p class="m-0">Account title: <strong>TECH INOVIQ SOLUTIONS (SMC-PVT) Ltd</strong></p>
        <p class="m-0">Bank Name: <strong>MEEZAN BANK</strong></p>
        <p class="m-0">Account number: <strong>**************</strong></p>
        <p class="m-0">IBAN: <strong>************************</strong></p>
        <p class="m-0">Swift: <strong>MEZNPKKA</strong></p>
      </div>
      </div>
        </div>
      `;
    }
  } else {
    if (paymentStatus == 'paid') {
      invoiceContent = `
    <div class="w-full max-w-4xl p-8 bg-white" style="font-family: Arial, sans-serif;">
      <div class="flex justify-between items-center mb-8">
        <div class="items-center">
          <img src=${'/assets/Tech-Inoviq.png'} alt="Company Logo" class="w-44 h-16 mr-4" />
          <div class="flex-col space-y-0">
            <h2 class="text-md font-bold">Tech Inoviq Solutions FZ LLC</h2>
                        <p class="text-sm text-gray-600">Creative Tower Fujairah,</p>
            <p class="text-sm text-gray-600">Fujairah United Arab Emirates 4422</p>
            <p class="text-sm text-gray-600">+************</p>
            <p class="text-sm text-gray-600">TRN: *********200003
           </p>
          </div>
        </div>

          <div>
          <img src=${'/assets/paid_stamp.png'} alt="Company Logo" class="w-40 h-40 mr-4" />
        </div>

        <div class="text-right">
          <h1 class="text-2xl font-bold mb-2">TAX INVOICE</h1>
          <p class="text-sm text-gray-600 font-semibold"># ${data.invoiceNumber}</p>
          <p class="mt-2 text-md font-medium">
            Balance Due
            <br />
            <span class="text-xl font-semibold">AED ${total.toFixed(2)}</span>
          </p>
        </div>
      </div>

      <div class="grid grid-cols-2 gap-8 mb-8">
        <div>
          <p class=" mb-2 text-sm">Bill To</p>
          <p class="text-sm font-bold">${data.billTo.name}</p>

        </div>
        <div class="text-right">
          <div class="grid grid-cols-2">
            <p class="text-sm font-semibold">Invoice Date:</p>
            <p class="text-sm">${data.invoiceDate}</p>
                       <p class="text-sm font-semibold">Terms:</p>
            <p class="text-sm">Due On Receipt
           </p>
            <p class="text-sm font-semibold">Due Date:</p>
            <p class="text-sm">${data.dueDate}</p>
          </div>
        </div>
      </div>

      <div class="mb-8">
        <table class="w-full" style="border-collapse: collapse;">
          <thead>
            <tr class="bg-gray-100">
              <th class="py-2 px-4 text-left">Description</th>
              <th class="py-2 px-4 text-right">Qty</th>
              <th class="py-2 px-4 text-right">Rate</th>
              <th class="py-2 px-4 text-right">Amount</th>
            </tr>
          </thead>
          <tbody>
            <tr class="border-b">
              <td class="py-2 px-4">WeTarseel Monthly Subscription</td>
              <td class="py-2 px-4 text-right">${data.billingMonths}</td>
              <td class="py-2 px-4 text-right">AED ${data.unitAmount.toFixed(2)}</td>
              <td class="py-2 px-4 text-right">AED ${subTotal.toFixed(2)}</td>
            </tr>
            <tr>
              <td colspan="3" class="py-2 px-4 text-right font-semibold">Tax</td>
              <td class="py-2 px-4 text-right">AED ${data.taxAmount.toFixed(2)}</td>
            </tr>
            <tr>
              <td colspan="3" class="py-2 px-4 text-right font-semibold">Other Charges</td>
              <td class="py-2 px-4 text-right">AED ${data.otherCharges.toFixed(2)}</td>
            </tr>
            <tr class="font-bold">
              <td colspan="3" class="py-2 px-4 text-right">Total</td>
              <td class="py-2 px-4 text-right">AED ${total.toFixed(2)}</td>
            </tr>
          </tbody>
        </table>
      </div>

       <div class="mt-8 text-sm text-gray-600">
  <p>Thanks for your business.</p>
</div>
     <div class="mb-8">
    <h3 class="mb-4 text-sm">Bank Details:</h3>
    <div class="grid grid-cols-2 gap-y-0 text-sm">
      <p class="m-0">Account title: <strong>Tech Inoviq Solutions FZ LLC</strong></p>
      <p class="m-0">Bank Name: <strong>Mashreq Bank</strong></p>
      <p class="m-0">Account number: <strong>************</strong></p>
      <p class="m-0">IBAN: <strong>AE25 0330 0000 1910 0802 041</strong></p>
      <p class="m-0">Swift: <strong>BOMLAEAD</strong></p>
    </div>
  </div>
    </div>
  `;
    } else {
      invoiceContent = `
      <div class="w-full max-w-4xl p-8 bg-white" style="font-family: Arial, sans-serif;">
        <div class="flex justify-between items-center mb-8">
          <div class="items-center">
            <img src=${'/assets/Tech-Inoviq.png'} alt="Company Logo" class="w-44 h-16 mr-4" />
            <div class="flex-col space-y-0">
              <h2 class="text-md font-bold">Tech Inoviq Solutions FZ LLC</h2>
                          <p class="text-sm text-gray-600">Creative Tower Fujairah,</p>
              <p class="text-sm text-gray-600">Fujairah United Arab Emirates 4422</p>
              <p class="text-sm text-gray-600">+************</p>
              <p class="text-sm text-gray-600">TRN: *********200003
             </p>
            </div>
          </div>
  
          <div class="text-right">
            <h1 class="text-2xl font-bold mb-2">TAX INVOICE</h1>
            <p class="text-sm text-gray-600 font-semibold"># ${data.invoiceNumber}</p>
            <p class="mt-2 text-md font-medium">
              Balance Due
              <br />
              <span class="text-xl font-semibold">AED ${total.toFixed(2)}</span>
            </p>
          </div>
        </div>
  
        <div class="grid grid-cols-2 gap-8 mb-8">
          <div>
            <p class=" mb-2 text-sm">Bill To</p>
            <p class="text-sm font-bold">${data.billTo.name}</p>
  
          </div>
          <div class="text-right">
            <div class="grid grid-cols-2">
              <p class="text-sm font-semibold">Invoice Date:</p>
              <p class="text-sm">${data.invoiceDate}</p>
                         <p class="text-sm font-semibold">Terms:</p>
              <p class="text-sm">Due On Receipt
             </p>
              <p class="text-sm font-semibold">Due Date:</p>
              <p class="text-sm">${data.dueDate}</p>
            </div>
          </div>
        </div>
  
        <div class="mb-8">
          <table class="w-full" style="border-collapse: collapse;">
            <thead>
              <tr class="bg-gray-100">
                <th class="py-2 px-4 text-left">Description</th>
                <th class="py-2 px-4 text-right">Qty</th>
                <th class="py-2 px-4 text-right">Rate</th>
                <th class="py-2 px-4 text-right">Amount</th>
              </tr>
            </thead>
            <tbody>
              <tr class="border-b">
                <td class="py-2 px-4">WeTarseel Monthly Subscription</td>
                <td class="py-2 px-4 text-right">${data.billingMonths}</td>
                <td class="py-2 px-4 text-right">AED ${data.unitAmount.toFixed(2)}</td>
                <td class="py-2 px-4 text-right">AED ${subTotal.toFixed(2)}</td>
              </tr>
              <tr>
                <td colspan="3" class="py-2 px-4 text-right font-semibold">Tax</td>
                <td class="py-2 px-4 text-right">AED ${data.taxAmount.toFixed(2)}</td>
              </tr>
              <tr>
                <td colspan="3" class="py-2 px-4 text-right font-semibold">Other Charges</td>
                <td class="py-2 px-4 text-right">AED ${data.otherCharges.toFixed(2)}</td>
              </tr>
              <tr class="font-bold">
                <td colspan="3" class="py-2 px-4 text-right">Total</td>
                <td class="py-2 px-4 text-right">AED ${total.toFixed(2)}</td>
              </tr>
            </tbody>
          </table>
        </div>
  
         <div class="mt-8 text-sm text-gray-600">
    <p>Thanks for your business.</p>
  </div>
       <div class="mb-8">
      <h3 class="mb-4 text-sm">Bank Details:</h3>
      <div class="grid grid-cols-2 gap-y-0 text-sm">
        <p class="m-0">Account title: <strong>Tech Inoviq Solutions FZ LLC</strong></p>
        <p class="m-0">Bank Name: <strong>Mashreq Bank</strong></p>
        <p class="m-0">Account number: <strong>************</strong></p>
        <p class="m-0">IBAN: <strong>AE25 0330 0000 1910 0802 041</strong></p>
        <p class="m-0">Swift: <strong>BOMLAEAD</strong></p>
      </div>
    </div>
      </div>
    `;
    }
  }
  // <div class="mb-8">
  //   <h3 class="text-sm">Bank Details</h3>
  //   <div class="flex flex-col gap-y-0 text-sm">
  //   <div class="flex gap-2">
  //     <p class="m-0">Account title: <strong>Tech Inoviq Solutions FZ LLC</strong></p>
  //     <p class="m-0">Bank Name: <strong>Mashreq Bank</strong></p>
  //     </div>
  //         <div class="flex gap-2">
  //     <p class="m-0">Account number: <strong>************</strong></p>
  //     <p class="m-0">IBAN: <strong>AE25 0330 0000 1910 0802 041</strong></p>
  //           </div>

  //     <p class="m-0">Swift: <strong>BOMLAEAD</strong></p>
  //   </div>
  // </div>
  const options = {
    margin: 10,
    filename: `invoice_${dayjs().format('DD-MM-YYYY')}.pdf`,
    html2canvas: { scale: 2 },
    jsPDF: { unit: 'mm', format: 'a4', orientation: 'portrait' },
  };

  const blob = await html2pdf().from(invoiceContent).set(options).outputPdf('blob');
  const file = new File([blob], `invoice-${Date.now()}.pdf`, { type: 'application/pdf' });

  const formData = new FormData();
  formData.append('file', file);

  return formData;
}
