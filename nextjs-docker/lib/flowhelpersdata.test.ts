// flowHelpers.test.ts
import { FlowBot } from './flowHelpers';
import { beforeEach, describe, expect, MockInstance, test, vi } from 'vitest';
import { data } from './flowMockDataData';
import { type Edge, type Node } from '@xyflow/react';
import { Account } from './types';
import { flow } from 'lodash';

describe('FlowBot', () => {
  let flowBot: FlowBot;
  let sendInteractiveOrTextMessageMock: MockInstance;
  let sendTextMessageMock: MockInstance;
  let updateStateMock: MockInstance;
  let assignAgentMock: MockInstance;
  let removeStateMock: MockInstance;
  let sendImageMessageMock: MockInstance;

  beforeEach(() => {
    flowBot = new FlowBot({
      conversationId: 'convo1',
      account: {
        id: 'account1',
        name: 'Account 1',
      } as any as Account,
      flow: {
        name: 'Flow 1',
        flow: {
          nodes: data.nodes as Node[],
          edges: data.edges,
        },
      },
      phoneNo: '**********',
      leadId: 'lead1',
      state: '',
      userMessage: '',
    });

    sendInteractiveOrTextMessageMock = vi.spyOn(flowBot, 'sendInteractiveOrTextMessage').mockImplementation((stuff) => {
      console.log('sending ', stuff.node.data.text, ((stuff.node.data?.options as any[]) ?? []).map((a) => a.id).join(', '));
      return Promise.resolve();
    });
    sendTextMessageMock = vi.spyOn(flowBot, 'sendTextMessage').mockImplementation((stuff) => {
      console.log('sending ', stuff.message);
      return Promise.resolve();
    });
    sendImageMessageMock = vi.spyOn(flowBot, 'sendImageMessage').mockImplementation(() => {
      return Promise.resolve();
    });

    updateStateMock = vi.spyOn(flowBot, 'updateState').mockImplementation(({ node }) => {
      flowBot.state = `Flow 1:${node.id}`;
      return Promise.resolve();
    });

    removeStateMock = vi.spyOn(flowBot, 'removeState').mockImplementation(() => {
      return Promise.resolve();
    });

    assignAgentMock = vi.spyOn(flowBot, 'assignAgent').mockImplementation(() => {
      return Promise.resolve();
    });

    // Mock `revalidatePath`
    vi.mock('next/cache', () => ({
      revalidatePath: vi.fn(),
    }));
  });

  test('should create an instance of FlowBot', () => {
    expect(flowBot).toBeInstanceOf(FlowBot);
  });

  test('if keyword doesnt match, it should not start the flow', async () => {
    flowBot.userMessage = 'pooka';
    await flowBot.runFlow();

    expect(sendInteractiveOrTextMessageMock).toHaveBeenCalledTimes(0);
  });

  test('test normal flow', async () => {
    flowBot.userMessage = 'hi';
    await flowBot.runFlow();

    expect(sendInteractiveOrTextMessageMock).toHaveBeenCalledTimes(2);
  });

  test('save stuff and also send message', async () => {
    flowBot.userMessage = 'Oil /Minor Service';
    flowBot.state = 'Flow 1:Send Message13';
    await flowBot.runFlow();
    expect(sendInteractiveOrTextMessageMock).toHaveBeenCalledTimes(2);
  });

  test.skip('test if option less message works', async () => {
    flowBot.userMessage = 'tire';
    await flowBot.runFlow();

    expect(sendInteractiveOrTextMessageMock).toHaveBeenCalledTimes(1);
    expect(updateStateMock).toHaveBeenCalledTimes(1);
    expect(removeStateMock).toHaveBeenCalledTimes(0);

    sendInteractiveOrTextMessageMock.mockClear();
    updateStateMock.mockClear();
    flowBot.state = 'Flow 1:Send Message2';
    flowBot.userMessage = '225/50R17';
    await flowBot.runFlow();

    // expect(sendInteractiveOrTextMessageMock).toHaveBeenCalledTimes(1);
    // expect(updateStateMock).toHaveBeenCalledTimes(2);

    // sendInteractiveOrTextMessageMock.mockClear();
    // updateStateMock.mockClear();
    // flowBot.state = 'Flow 1:Send Message4';
    // flowBot.userMessage = 'patrol';
    // await flowBot.runFlow();

    // expect(sendInteractiveOrTextMessageMock).toHaveBeenCalledTimes(1);
    // expect(updateStateMock).toHaveBeenCalledTimes(2);

    // sendInteractiveOrTextMessageMock.mockClear();
    // updateStateMock.mockClear();
    // flowBot.state = 'Flow 1:Send Message6';
    // flowBot.userMessage = '19';
    // await flowBot.runFlow();

    // expect(sendInteractiveOrTextMessageMock).toHaveBeenCalledTimes(0);
    // expect(updateStateMock).toHaveBeenCalledTimes(1);
    // expect(removeStateMock).toHaveBeenCalledTimes(1);
  });
});
