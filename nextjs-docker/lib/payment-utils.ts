import { getBusinessInvoicesList } from '@/lib/pocket';
import dayjs from 'dayjs';
import { IInvoice } from './types';

/**
 * Checks if there are any overdue payments for a business account
 * @param accountId The account ID to check
 * @returns The overdue invoice if found, null otherwise
 */
export async function checkOverduePayment(accountId: string): Promise<{
  status: string;
  invoice: IInvoice;
} | null> {
  try {
    // Get all invoices for the business
    const invoices = await getBusinessInvoicesList(accountId);

    if (!invoices || invoices.length === 0) return null;

    const overdueInvoice = invoices.map((invoice) => {
      if (invoice.payment_status === 'over-due') {
        return {
          status: 'over-due',
          invoice,
        };
      }

      if (invoice.payment_status === 'due') {
        return {
          status: 'due',
          invoice,
        };
      }
    });
    return overdueInvoice[0] ?? null;
  } catch (error) {
    console.error('Error checking overdue payment:', error);
    return null;
  }
}
