import { describe, test, expect, vi, beforeEach, MockInstance } from 'vitest';
import { storeUserBankSession } from './pocket';
import type { FlowBot, WNode } from './flowHelpers';

// Create mock function using vi.hoisted to ensure it's available before module imports
const mockPost = vi.hoisted(() => vi.fn());

// Mock dependencies first
vi.mock('./pocket', () => ({
  storeUserBankSession: vi.fn(),
}));

// Mock axios
vi.mock('axios', () => ({
  default: {
    create: vi.fn(() => ({
      post: mockPost,
    })),
  },
}));

// Mock fetch globally (still used for some endpoints)
global.fetch = vi.fn();

import { mitfFlow } from './mitf/mitfFlow';

describe('handleBankMessages', () => {
  let mockCtx: FlowBot;
  let mockTargetNode: WNode;
  let fetchMock: MockInstance;
  let storeUserBankSessionMock: MockInstance;

  beforeEach(() => {
    // Reset all mocks
    vi.clearAllMocks();

    // Setup mock context
    mockCtx = {
      phoneNo: '+**********',
      leadId: 'lead123',
      userMessage: 'test message',
      sendTextMessage: vi.fn().mockResolvedValue(undefined),
      updateState: vi.fn().mockResolvedValue(undefined),
    } as any;

    mockTargetNode = {
      id: 'node1',
      data: {
        otp: '123456',
      },
    } as any;

    // Setup mocks
    fetchMock = vi.mocked(fetch);
    storeUserBankSessionMock = vi.mocked(storeUserBankSession);

    // Set environment variables
    process.env.MITF_ENDPOINT = 'http://localhost:3000';
    process.env.NEXT_PUBLIC_BUN_SERVER_URL = 'http://localhost:4000';
  });

  describe('Full Flow Integration (Production-like)', () => {
    test('should complete the banking flow using runFlow() as in production', async () => {
      const mockAccount = {
        id: 'account123',
        name: 'Test Account',
      } as any;

      const { FlowBot } = await import('./flowHelpers');

      // **Step 1: User sends initial banking message (triggers entire flow until OTP is needed)**
      console.log('Testing Step 1: Initial banking trigger - should progress through VerifyCustomer and GenerateOtp');

      let flowBot = new FlowBot({
        conversationId: 'conv123',
        leadId: 'lead123',
        flow: mitfFlow,
        account: mockAccount,
        phoneNo: '+**********',
        state: '', // No initial state - user is unverified
        userMessage: 'start banking',
      });

      // Mock methods
      flowBot.sendTextMessage = vi.fn().mockResolvedValue(undefined);
      flowBot.updateState = vi.fn().mockImplementation(async ({ node }) => {
        console.log(`Updating state to: ${node.id}`);
        flowBot.localState = node.id;
      });
      flowBot.removeState = vi.fn().mockResolvedValue(undefined);
      flowBot.pb = undefined;
      flowBot.init = vi.fn().mockResolvedValue(undefined);

      // Mock API calls - the flow should call VerifyCustomer and GenerateOtp, then stop at VerifyOtp
      mockPost
        .mockResolvedValueOnce({ status: 200, data: { success: true } }) // VerifyCustomer
        .mockResolvedValueOnce({ status: 200, data: { success: true, otpId: 'test-otp-id' } }); // GenerateOtp

      vi.stubEnv('NODE_ENV', 'test');

      await flowBot.runFlow();

      // Verify only 2 API calls were made (VerifyCustomer and GenerateOtp)
      expect(mockPost).toHaveBeenCalledTimes(2);
      expect(mockPost).toHaveBeenNthCalledWith(1, '/api/Authentication/VerifyCustomer', {
        phoneNumber: '+**********',
      });
      expect(mockPost).toHaveBeenNthCalledWith(2, '/api/Authentication/GenerateOtp', {
        phoneNumber: '+**********',
        otpChannel: '1',
      });

      expect(storeUserBankSessionMock).toHaveBeenCalledWith('lead123', true, expect.any(Date));

      // The flow should progress through VerifyCustomer, GenerateOtp, and WaitForOtp (where it stops)
      // So we should get: VerifyCustomer message + GenerateOtp message (no FullVerified message yet)
      expect((flowBot.sendTextMessage as any).mock.calls.length).toBe(2);
      expect(flowBot.sendTextMessage).toHaveBeenCalledWith({
        message: 'Your WhatsApp number has been verified for banking services.',
      });
      expect(flowBot.sendTextMessage).toHaveBeenCalledWith({
        message: 'An OTP has been sent to your WhatsApp number. Please enter the OTP to proceed.',
      });

      // State should progress: VerifyCustomer, then WaitForOtp (manual), then GenerateOtp (normal flow)
      expect(flowBot.updateState).toHaveBeenCalledTimes(3);
      expect(flowBot.updateState).toHaveBeenNthCalledWith(1, {
        node: expect.objectContaining({
          id: 'Autheåntication/VerifyCustomer',
          type: 'Authentication/VerifyCustomer',
        }),
      });
      expect(flowBot.updateState).toHaveBeenNthCalledWith(2, {
        node: expect.objectContaining({
          id: 'WaitForOtp',
          type: 'Save Data',
        }),
      });
      expect(flowBot.updateState).toHaveBeenNthCalledWith(3, {
        node: expect.objectContaining({
          id: 'Authentication/GenerateOtp',
          type: 'Authentication/GenerateOtp',
        }),
      });

      // Reset for next step
      vi.clearAllMocks();

      // Debug the final state
      console.log('Step 1 final state after all updates:', flowBot.localState);

      // **Step 2: User sends OTP (should trigger VerifyOtp and FullVerified)**
      console.log('Testing Step 2: User enters OTP - should progress through VerifyOtp to FullVerified');

      // Reset mocks but preserve the state that was established in step 1
      const step1FinalState = flowBot.localState; // Should be 'WaitForOtp'
      console.log('Step 1 final state:', step1FinalState);
      vi.clearAllMocks();

      flowBot = new FlowBot({
        conversationId: 'conv123',
        leadId: 'lead123',
        flow: mitfFlow,
        account: mockAccount,
        phoneNo: '+**********',
        state: step1FinalState, // Use the state from step 1 (should be 'WaitForOtp')
        userMessage: '123456', // User sends OTP
      });

      // Mock methods
      flowBot.sendTextMessage = vi.fn().mockResolvedValue(undefined);
      flowBot.updateState = vi.fn().mockResolvedValue(undefined);
      flowBot.removeState = vi.fn().mockResolvedValue(undefined);
      flowBot.pb = undefined;
      flowBot.init = vi.fn().mockResolvedValue(undefined);

      // Mock API call for VerifyOtp - the flow should progress WaitForOtp → VerifyOtp → FullVerified
      mockPost.mockResolvedValueOnce({ status: 200, data: { success: true, verified: true } });

      await flowBot.runFlow();

      // Debug what happened in step 2
      console.log('Step 2 - Messages sent count:', (flowBot.sendTextMessage as any).mock.calls.length);
      console.log('Step 2 - Messages sent:', (flowBot.sendTextMessage as any).mock.calls);
      console.log('Step 2 - Mock post calls:', mockPost.mock.calls.length);

      // Verify VerifyOtp API was called
      expect(mockPost).toHaveBeenCalledTimes(1);
      expect(mockPost).toHaveBeenCalledWith('/api/Authentication/VerifyOtp', {
        otpId: 'f8b1d3aa-4716-4a5e-bae0-731a2d7c83ae',
        otp: '123456',
      });

      // Verify both messages were sent (VerifyOtp success + FullVerified)
      expect(flowBot.sendTextMessage).toHaveBeenCalledTimes(2);
      expect(flowBot.sendTextMessage).toHaveBeenNthCalledWith(1, {
        message: 'Your OTP has been verified successfully. You can now proceed with your banking transaction.',
      });
      expect(flowBot.sendTextMessage).toHaveBeenNthCalledWith(2, {
        message: 'Congratulations! You are now fully verified for banking services. You can access all banking features.',
      });

      // Verify final state progression: WaitForOtp → VerifyOtp → FullVerified
      expect(flowBot.updateState).toHaveBeenCalledTimes(2);
      expect(flowBot.updateState).toHaveBeenNthCalledWith(1, {
        node: expect.objectContaining({
          id: 'Authentication/VerifyOtp',
          type: 'Authentication/VerifyOtp',
        }),
      });
      expect(flowBot.updateState).toHaveBeenNthCalledWith(2, {
        node: expect.objectContaining({
          id: 'FullVerified',
          type: 'FullVerified',
        }),
      });

      console.log('All banking flow steps completed successfully using runFlow() as in production!');
    });
  });
});
