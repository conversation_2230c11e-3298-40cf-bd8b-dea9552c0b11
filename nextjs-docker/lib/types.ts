import { ReactFlowJsonObject } from '@xyflow/react';
import IInteractive from './interactiveType';
import { ICardComponent, ICardState } from '@/app/[account_id]/templates/manage/[template_operation]/types';

export interface Account {
  id: string;
  name: string;
  access_token: string;
  waba_id: string;
  phone_id: string;
  reconnect: boolean;
  pb_user_id: string[];
  favorite: boolean;
  created: Date;
  logo: string;
  display_phone_number: string | null;
  expires_at: Date;
  remaining_limit: number;
  unutilized_limit: number;
  package_tier: string;
  business_location: string;
  currency: string;
  limit_start_date: Date;
  limit_end_date: Date;
  old_limit: string | null;
  current_limit: string | null;
  event: 'DOWNGRADE' | 'FLAGGED' | 'ONBOARDING' | 'UNFLAGGED' | 'UPGRADE' | null;
  code_verification_status: string | null;
  quality_rating: string;
  platform_type: string | null;
  lock: boolean;
  two_factor: boolean;
  show_recent_campaigns: boolean;
  bank_info: string;
  /**
   * Stripe customer ID for the account
   */
  stripe_customer_id: string;
  allow_failed_retry_campaigns: boolean;
  agent_creation_limit: number;
  expand?: {
    pb_user_id: User[];
  };
  subscription_active: boolean;
  TRN: string;
  address: string;
  city: string;
  banned: boolean;
}

export type IExpandedAccount = Account & {
  expand: {
    pb_user_id: Account;
    package_tier?: IPackageTier;
    team?: Team;
  };
};

export interface WALocation {
  latitude: number;
  longitude: number;
  address: string;
  name: string;
  url: string;
}

export interface IMessage {
  id: string;
  message: string;
  user: string;
  type: WAMessageTypeMedia & 'text';
  location: WALocation;
  file: string;
  from: 'agent' | 'user';
  referral: IReferral | null;
  created: Date;
  updated: Date;
  url?: string;
  convo_id: string;
  delivery_status: 'pending' | 'read' | 'sent from wetarseel' | 'sent' | 'failed' | 'delivered';
  campaign: string;
  error: string;
  error_code: number;
  error_data: string;
  retried_attempts: number;
  interactive_message: IInteractive;
  wamid?: string;
  replied_to: string;
  template: string;
  account: string;
  created_by: string;
  reaction?: {
    emoji: string;
    lead: {
      id: string;
      name: string;
    };
  };
}

export type IExpandedMessage = IMessage & {
  expand?: {
    template?: ITemplateDatabase;
    user?: ILead;
    campaign?: ICampaign;
    account: Account;
    interactive_message: {
      message: IInteractive;
    };
    replied_to: IMessage;
    created_by: User;
  };
};

export type IExpandedMessageAndRepliedList = IMessage & {
  expand?: {
    user?: IExpandedLead;
    campaign?: ICampaign;
    template?: ITemplateDatabase;
  };
};

export type IExpandedCampaign = ICampaign & {
  expand: {
    template: ITemplateDatabase;
    leads_list: IList[];
    account: Account;
    messages_via_campaign: IMessage[];
    created_by: User;
  };
};

export type ICampaignReport = {
  createdBy: String | undefined;
  createdById: String | undefined;
  createdAt: Date;
  type: String | undefined;
};

export interface IConversation {
  id: string;
  account: string;
  from: User;
  created: Date;
  updated: Date;
  unread_count: number;
  assigned_agent: string;
  message: string;
  /**
   * The template name on which the conversation is based on
   */
  template?: string;
  /**
   * The state of the conversation on the flow
   */
  state: string;
  chat_archive: boolean;
  chat_block: boolean;
}

export interface IConversationsWithMessages {
  convo_id: string;
  account: string;
  from: string;
  last_message: string;
  last_message_id: string;
  last_message_created: string;
  last_message_delivery_status: string;
  last_message_from: string;
  last_message_type: string | null;
  assigned_agent: string | null;
  chat_archive: boolean;
  chat_block: boolean;
  unread_count: number;
  campaign_history: string[];
  updated: Date;
}

export interface IExpandedConversationsWithMessages extends IConversationsWithMessages {
  tags: string;
  team_id: string;
  team_name: string;
  status: string;
  meta: { [key: string]: string | number };
  expand: {
    assigned_agent?: Partial<User>;
    from: Partial<ILead>;
  };
}
export interface IExpandedConversation extends IConversation {
  expand: {
    from: IExpandedLead;
    assigned_agent: User;
  };
}

export interface Team {
  id: string;
  name: string;
}

export interface IExpandedConversationAndExpandedMessage extends IConversation {
  expand: {
    from: IExpandedLead;
    assigned_agent: User;
    template: any;
    message: IMessage;
  };
}

export interface IImageWithUrl extends IImage {
  url: string;
  type?: string;
}

export interface IIVideoWithUrl extends IImage {
  url: string;
}

export type UserViewAll = 'lead-management' | 'live-chat' | 'templates' | 'campaigns' | 'rate_reviews' | 'flows' | 'dashboard' | 'reports';

export interface User {
  id: string;
  email: string;
  emailVisibility: boolean;
  name: string;
  /**
   * Use this if directly acessing user
   */
  phoneNumber: string;
  /**
   * Use this if getting from expand
   */
  phone_number: string;
  username: string;
  verified: boolean;
  /**
   * Field can be empty
   */
  avatar: string;
  collectionId: string;
  collectionName: string;
  created: Date;
  updated: Date;
  favorite: string;
  type: string;
  roles?: string[];
  dashboard_options?: string[];
  view_all?: UserViewAll[];
  /**
   * True if user is locked
   */
  lock: boolean;

  // firebase notification
  notification: string;

  team?: string;
  expand?: {
    roles: IRole[];
  };
}

// Interface for each Data item
export interface IPhoneVerificationResponse {
  verified_name: string;
  code_verification_status: string;
  display_phone_number: string;
  quality_rating: string;
  platform_type: string;
  last_onboarded_time: string;
  /** phone_id */
  id: string;
}

// Template Type
type IComponentBtnType = 'QUICK_REPLY' | 'URL' | 'PHONE_NUMBER';

export interface IComponent {
  type: 'HEADER' | 'BODY' | 'FOOTER' | 'BUTTONS' | 'TEXT';
  file?: File;
  fileUrl?: string;
  format: string;
  text: string;
  example?: {
    header_handle: string[];
    header_text: [];
    body_text: [];
  };
  //TODO: Need to fix this any
  cards: ICardState[];
  buttons: [
    {
      type: IComponentBtnType;
      btnType?: string;
      text: string;
      url?: string;
      phone_number?: string;
    },
  ];
}

export type WAMediaMessage = WAAudioMessage &
  WAVideoMessage &
  WAImageMessage &
  WADocumentMessage & {
    location: WALocation;
  };

// Interface for the main structure
export interface Template {
  allow_category_change: boolean;
  category: 'MARKETING' | 'UTILITY' | 'AUTHENTICATION';
  components: IComponent[];
  id: string;
  language: string;
  name: string;
  //TODO: this does not exists in Template
  flow: ReactFlowJsonObject;
}

export interface ITeamplateParams {
  body?: string[];
  defaultBody: string[];
  defaultHeader: string[];
  header?: string[];
}

export interface ITemplateDatabase {
  id: string;
  template_body: Template;
  reason: string;
  updated_template: { HEADER: string };
  template_name: string;
  account: Account;
  status: 'APPROVED' | 'PENDING' | 'REJECTED' | 'PENDING DELETION' | 'DELETED' | 'PAUSED';
  created: Date;
  updated: Date;
  language: string;
  flow: ReactFlowJsonObject;
  /**
   * Use this for template id given my meta
   */
  template_id: string;
  created_by: string;
  //TODO
  params: ITeamplateParams;
  previous_quality_score?: string;
  new_quality_score?: string;
  description: string;
  expand?: {
    created_by: User;
    account: Account;
  };
  type?: 'basic-template' | 'carousel-template' | 'utility-template';
  category: 'MARKETING' | 'AUTHENTICATION' | 'UTILITY';
  edit_count: number;
  template_edit_date: Date;
  previous_category?: string;
}

export interface ICarouselParams {
  body: string[];
  defaultBody: any[];
}

export interface IList {
  id: string;
  name: string;
  account: Account | string;
  created_by: string;
  updated_by: string;
  pending: boolean;
  list_filters: string;
  type: 'smart' | 'static' | '';
  created: Date;
  updated: string;
}

export interface IExpandedList extends IList {
  expand: {
    leads_via_list: ILead[];
    campaigns_via_leads_list: ICampaign[];
    created_by: User;
    list_filters: IListFilters;
  };
}

export interface IExpandedListAndLeadCount extends IExpandedList {
  leads_count: number;
}

export interface IExpandedAPISettings extends IApiSettings {
  expand: {
    account: Account;
  };
}

export type ICampaignType = 'Published' | 'Scheduled' | 'New' | 'Draft' | undefined;

export interface ICampaign {
  id: string;
  name: string;
  leads_list: string[];
  account: string;
  created_by: string;
  type: ICampaignType;
  template: string;
  total_messages_count: number;
  sent_count: number;
  delivered_count: number;
  read_count: number;
  reply_count: number;
  opt_out_count: number;
  scheduled_time: Date;
  next_retry_date: Date | null;
  retry_count: number;
  /** [empty string] if value is null */
  retry_status: string;
  /** [empty string] if value is null */
  retry_status_reason: string;
  draft_url?: string;
  status: 'Sending' | 'Stopped' | 'Completed' | 'Scheduled' | 'Retrying' | 'Paused';
  retry_performance: any[];
  updated: Date;
  created: Date;
  collectionId: string;
  collectionName: string;
}

export interface ILead {
  account: string;
  collectionId: string;
  collectionName: string;
  country: string;
  created: Date;
  id: string;
  name: string;
  phone_number: string;
  status: string;
  tags: string[];
  updated: Date;
  list: string[];
  log_id: string | undefined;
  active: boolean;
  campaign_history: string[];
  upload?: 'successful' | 'failed';
  meta: { [key: string]: string | number };
  opt_out: boolean;
  replied: string;
  created_by: string;
  updated_by: string;
  referral: IReferral | null;
  shopify_id: string;
  /**
   * Use this if editing list
   */
  checked?: boolean;
  blocked: boolean;
}

export interface ILeadReferral extends ILead {
  referral: IReferral;
}
export interface IExpandedLead extends ILead {
  expand: {
    campaign_history: ICampaign[];
    created_by: User;
    list: IList[];
    replied: ILeadRepliedCampaign[];
  };
}

export interface ILeadRepliedCampaign {
  id: string;
  campaign: string;
  lead: string;
  message: string;
  created: Date;
  updated: Date;
}

export interface IAllLeads {
  phone_number: string;
  name: string;
}

export type WAMessageTypeMedia = 'audio' & 'image' & 'document' & 'video' & 'location' & 'contacts' & 'interactive';

export interface ILogData {
  name: string;
  phone_number: string;
  status: string;
  tags: string[];
  rejection_reason: null | string;
  upload: 'successful' | 'failed';
  created_by: string;
  created: Date;
}

export interface ILog {
  id: string;
  log_name: string;
  account: string;
  number_of_leads: number;
  log_data: ILogData[];
  created: Date;
  pending: boolean;
  updated: Date;
  created_by: string;
  updated_by: string;
}

export type IExpandedLogs = ILog & {
  expand: {
    created_by: User;
  };
};

export type WAMessageType = 'text' | 'audio' | 'image' | 'application' | 'document' | 'video' | 'location' | 'contacts' | 'reaction' | 'unsupported';

export type WARecieveMessage = WATextMessage & WAMediaMessage & { business_phone_number_id: string };

export interface WAMessage {
  business_phone_number_id: string;
  from: string;
  id: string;
  timestamp: string;
  type: WAMessageTypeMedia & 'text';
  contacts: [Contact];
  template: ITemplateDatabase;
  button: {
    payload: string;
    text: string;
  };
  interactive: {
    type: 'list_reply' | 'button_reply';
    list_reply: {
      id: string;
      title: string;
      description: string;
    };
    button_reply: {
      id: string;
      title: string;
    };
  };
  context: {
    /**
     * Number of business
     */
    from: string;
    /**
     * WAMI of the message to which the user replied
     */
    id: string;
    /**
     * Is message forwarded
     */
    forwarded?: boolean;
  };
  errors: WAMessageError[];
  reaction: IReaction;
  referral: IReferral;
}

export interface IReferral {
  ctwa_clid: string;
  headline: string;
  media_type: 'image' | 'video';
  source_id: string;
  source_type: 'ad';
  source_url: string;
  /**body present when mediya_type is image */
  body: string;
  /**thumbnail_url present when media_type is video **/
  thumbnail_url: string;
  /**video_url present when media_type is video **/
  video_url: string;
  /**image_url present when mediya_type is image */
  image_url: string;
}
export interface IReaction {
  message_id: string;
  emoji: string;
}

export interface WAMessageError {
  code: number;
  title: string;
  message: string;
  error_data: {
    details: string;
  };
}
export interface WATextMessage extends WAMessage {
  text: {
    body: string;
  };
}

export interface WAStatuses {
  timestamp: number;
  recipient_id: number;
  errors?: [
    {
      title: string;
      message: string;
      error_data: {
        details: string;
      };
      href: string;
      code: number;
    },
  ];
  conversation?: {
    origin: {
      type: string;
    };
    id: string;
    expiration_timestamp: number;
  };
  pricing?: {
    billable: boolean;
    pricing_model: string;
    category: string;
  };
  id: string;
  status: string;
}

export interface WAAudioMessage extends WAMessage {
  audio: WAMedia;
}

export interface WAImageMessage extends WAMessage {
  image: WAMedia;
}

export interface WADocumentMessage extends WAMessage {
  document: WAMedia;
}

export interface WAVideoMessage extends WAMessage {
  video: WAMedia;
}

export interface WAMedia {
  caption: string;
  filename: string;
  id: string;
  mime_type: string;
}

export interface Contact {
  profile: {
    name: string;
  };
  wa_id: string;
}

export interface IMessageEcho {
  from: string;
  to: string;
  id: string;
  timestamp: string;
  type: WAMessageType;
  text?: {
    body: string;
  };
  // Other message types can be added as needed (image, audio, etc.)
}

export interface IHistoryMessage {
  from: string;
  id: string;
  timestamp: string;
  type: WAMessageType | 'media_placeholder';
  text?: {
    body: string;
  };
  history_context: {
    status: 'read' | 'delivered' | 'sent' | 'failed';
    from_me: boolean;
  };
}

export interface IHistoryThread {
  id: string;
  messages: WhatsAppHistoryMessage[];
}

export interface IHistoryChunk {
  metadata: {
    phase: number;
    chunk_order: number;
    progress: number;
  };
  threads: IHistoryThread[];
}

export interface IContactSync {
  type: 'contact';
  contact: {
    full_name: string;
    first_name: string;
    phone_number: string;
  };
  action: 'add' | 'remove';
  metadata: {
    timestamp: string;
    version: number;
  };
}

export interface IWhatsappHook {
  object: string;
  entry: [
    {
      id: string;
      changes: [
        {
          field:
            | 'message_template_quality_update'
            | 'phone_number_quality_update'
            | 'message_template_status_update'
            | 'smb_app_state_sync'
            | 'history'
            | 'smb_message_echoes'
            | string;
          value: {
            display_phone_number: string;
            current_limit: string;
            old_limit: string;
            messaging_product: string;
            metadata: {
              display_phone_number: string;
              phone_number_id: string;
            };
            previous_quality_score: string;
            new_quality_score: string;
            message_template_id: string;
            message_template_name: string;
            message_template_language: string;
            new_category: string;
            previous_category: string;
            event?: 'APPROVED' | 'REJECTED' | 'FLAGGED' | 'DOWNGRADE' | 'ONBOARDING' | 'UNFLAGGED' | 'UPGRADE' | 'PAUSED';
            reason?: string | 'NONE';
            other_info: {
              description: string;
            };
            statuses?: WAStatuses[];
            contacts?: [Contact];
            messages: [WATextMessage | WAAudioMessage | WAImageMessage | WADocumentMessage | WAVideoMessage];
            // Added for smb_app_state_sync event
            state_sync?: IContactSync[];
            // Added for history event
            history?: IHistoryChunk[];
            // Added for smb_message_echoes event
            message_echoes?: IMessageEcho[];
          };
        },
      ];
    },
  ];
}

// SMB App State Sync Types
export interface SMBAppStateSyncWebhook {
  object: 'whatsapp_business_account';
  entry: Array<{
    id: string; // WABA_ID
    changes: Array<{
      value: {
        messaging_product: 'whatsapp';
        metadata: {
          display_phone_number: string;
          phone_number_id: string;
        };
        state_sync: Array<SMBContactStateSync>;
      };
      field: 'smb_app_state_sync';
    }>;
  }>;
}

export interface SMBContactStateSync {
  type: 'contact';
  contact: {
    full_name?: string; // Not included when contact is removed
    first_name?: string; // Not included when contact is removed
    phone_number: string;
  };
  action: 'add' | 'remove';
  metadata: {
    timestamp: string; // Unix timestamp
  };
}

export interface WAMediaReturn {
  url: string;
  mime_type: string;
  id: string;
  file_size: number;
}

export interface ISendMessageResponse {
  messaging_product: string;
  contacts: {
    input: string;
    wa_id: string;
  }[];
  messages: {
    id: string;
    message_status: string;
  }[];
}

export interface ISendMessageErrorResponse {
  error: {
    message: string;
    type: string;
    code: number;
    error_data: {
      messaging_product: 'whatsapp';
      details: string;
    };
    fbtrace_id: string;
  };
}

export interface IRole {
  name: string;
  parent_role: string;
  created: Date;
  updated: Date;
}

export interface InteractiveMessage {
  type: string;
  header?: {
    type?: string;
    text?: string;
  };
  body?: {
    text?: string;
  };
  footer?: {
    text?: string;
  };
  action: {
    button?: string;
    sections?: {
      title: string;
      rows: {
        id: string;
        title: string;
      }[];
    }[];
    buttons?: {
      type: string;
      reply: {
        id: string;
        title: string;
      };
    }[];
  };
}

export interface IImage {
  id: string;
  file: File | string;
  url: string;
  name: string;
  created_by: string;
  account: string;
  created: Date;
  updated: Date;
  expand: {
    created_by: User;
  };
}

export interface IPackageTier {
  id: string;
  name: string;
  type: 'platinum' | 'gold' | 'silver' | 'free';
  total_limit: number;
  period: 'daily' | 'monthly' | 'yearly';
  created: Date;
  updated: Date;
}

export interface IMessagingLimit {
  id: string;
  account: string;
  user: string;
  assigned_limit: number;
  remaining_limit: number;
  period: 'daily' | 'monthly' | 'yearly';
  limit_start_date: Date;
  limit_end_date: Date;
  created: Date;
  updated: Date;
}

export interface IMessagingLimitPartial {
  user: string;
  assigned_limit: number;
  remaining_limit: number;
  period: 'daily' | 'monthly' | 'yearly';
  limit_start_date: Date;
  limit_end_date: Date;
}

export type IExpandedMessagingLimit = IMessagingLimit & {
  expand?: {
    user: User;
  };
};

export interface IAccountLimitHistory {
  date: Date;
  message_limit: number;
  remaining_messages: number;
  agents_admin_data: IMessagingLimitPartial[];
}

export interface IAgentMessagingLimit {
  id: string;
  account: string;
  user: string;
  total_limit: number;
  remaining_limit: number;
  period: 'daily' | 'monthly' | 'yearly';
  limit_start_date: Date;
  limit_end_date: Date;
  created: Date;
  updated: Date;
}

export type IExpandedAgentMessagingLimit = IAgentMessagingLimit & {
  expand?: {
    user: User;
  };
};

export interface ICountryCurrencyMap {
  [country: string]: string[];
}

export interface IDuplicateData {
  leadName: string;
  leadId: string;
  optOut: boolean;
  status: boolean;
  multipleList: { listName: string; listId: string }[];
}

export interface IApiSettings {
  id: string;
  account: string;
  key: string;
  created: Date;
  updated: Date;
  expires_at: Date;
  status: 'active' | 'inactive';
}

export interface ICountry {
  id: string;
  name: string;
  phone_code: number;
  desciption: string;
  iso_codes: string[];
  phone_length: number;
  created: Date;
  updated: Date;
}

export interface IAutomation {
  id: string;
  isActive: boolean;
  name: string;
  description: string;
  account: string;
  template?: string;
  flow: Partial<ReactFlowJsonObject>;
  created: Date;
  updated: Date;
}

export interface ISavedMessages {
  id: string;
  account: string;
  messages: string[];
  created: Date;
  updated: Date;
}

export interface IExpandedSavedMessages {
  id: string;
  account: string;
  messages: string[];
  created: Date;
  updated: Date;
  expand: {
    messages: IMessage[];
  };
}

export interface IDocument {
  id: string;
  name: string;
  file: string;
  url: string;
  account: string;
  created_by: string;
  created: Date;
  updated: Date;
  type: string | null;
  expand: {
    created_by: User;
  };
}

export type ITrigger = 'On Message';
export type ICondition = 'Office Hours';
export type paymentStatus = 'due' | 'paid' | 'over-due';
export type paymentMethod = 'on-system' | 'off-system';
export type _paymentStatus = 'paid' | 'failed';
export type IAction = 'Send Message' | 'Assign agent' | 'Save Data' | 'button' | 'Office Hours' | 'Data Node' | 'VerifyCustomer' | 'RequestOTP' | 'VerifyOtp';

export interface Meta {
  key: string;
  values: string[];
}

export interface IInvoice {
  id: string;
  wt_id: number;
  amount_due: number;
  tax: number;
  charges: number;
  unit_amount: number;
  payment_status: paymentStatus;
  invoice_limit: number;
  billing_months: number;
  renewal_date: Date;
  auto_renewal: boolean;
  account_id: string;
  created: Date;
  completed: boolean;
  invoice_valid_from: Date;
  is_first_invoice: boolean;
  expand?: {
    account_id: Account;
    invoice_pdf: IDocument;
  };
}

export interface IPayment {
  id: string;
  account_id: string;
  invoice_id: string;
  amount_paid: number;
  tax: number;
  charges: number;
  unit_amount: number;
  payment_status: _paymentStatus;
  created: Date;
  payment_ref: string;
}

export interface ITransactionLog {
  id: string;
  account_id: string;
  payment_id: string;
  payment_amount: number;
  payment_method: paymentMethod;
  created: Date;
  error: string;
  payment_ref: string;
}
export interface Country {
  name: string;
  region: string;
  timezones: {
    [key: string]: string;
  };
  iso: {
    'alpha-2': string;
    'alpha-3': string;
    numeric: string;
  };
  phone: string[];
  emoji: string;
  image: string;
  phoneLength: number;
}

export type AgentWithConvos = User & { conversations: number };
export interface IFilterCondition {
  id: string;
  field: string;
  operator: string;
  value: string;
}

export interface IFilterGroup {
  id: string;
  type: 'and' | 'or';
  conditions: IFilterCondition[];
}

export interface IListFilters {
  id: string;
  parse_string: string;
  list_filter_object: IFilterGroup[];
  account: string;
  created_by: string;
  created: Date;
  updated: Date;
}

export interface IShopifyOrder {
  id: string;
  account: string;
  invoice_pdf: string;
  expand: {
    account: Account;
    invoice_pdf: IDocument;
  };
  response_object: any;
}

export interface IThirdParty {
  id: string;
  account: string;
  invoice_template: string;
  logo: string;
}

export type IExpandedThirdParty = IThirdParty & {
  expand?: {
    invoice_template: ITemplateDatabase;
  };
};

export interface IPendingContacts {
  id: string;
  account: string;
  pending_leads: number;
  created: Date;
  updated: Date;
  total_items: number;
  listId: string;
  meta_info: Record<string, string>;
  csv_file: string;
}

export type IConvoFromSQL = {
  convo_id: string;
  account: string;
  from: string;
  assigned_agent: string | null;
  chat_archive: boolean;
  unread_count: number;
  updated: Date;
  last_message: string;
  last_message_id: string;
  last_message_created: string;
  last_message_delivery_status: string;
  last_message_from: string;
  last_message_type: string | null;
  created: Date;
  id: string;
  name: string;
  phone_number: string;
  tags: string;
  status: string;
  country: string;
  log_id: string | undefined;
  list: string[];
  active: boolean;
  campaign_history: string[];
  meta: { [key: string]: string | number };
  created_by: string;
  updated_by: string;
  opt_out: boolean;
  replied: string;
  agent_name: string;
  team_id: string;
  team_name: string;
  chat_block: boolean;
};

export interface ISyncShopifyCustomer {
  id: string;
  firstName: string;
  lastName: string;
  email: string;
  phone: string;
  defaultAddress: {
    address1: string;
    address2: string | null;
    city: string;
    province: string | null;
    country: string;
    zip: string;
    phone: string | null;
  };
  tags: string[];
  lifetimeDuration: string;
  createdAt: Date;
  updatedAt: Date;
}

export interface ISyncShopifyCustomerResponse {
  shop: string;
  customers: any[];
  isBatch: boolean;
  batchSize: number;
  totalProcessed: number;
}

export interface ICreateShopifyCustomer {
  id: number;
  email: string;
  created_at: string;
  updated_at: string;
  first_name: string;
  last_name: string;
  state: 'enabled' | 'disabled' | 'invited' | 'declined';
  note: string;
  verified_email: boolean;
  multipass_identifier: string | null;
  tax_exempt: boolean;
  currency: string;
  phone: string;
  addresses: ShopifyAddress[];
  tax_exemptions: string[];
  admin_graphql_api_id: string;
  default_address: ShopifyAddress;
}

export interface ShopifyAddress {
  id: number;
  customer_id: number;
  first_name: string;
  last_name: string;
  company: string;
  address1: string;
  address2: string;
  city: string;
  province: string;
  country: string;
  zip: string;
  phone: string;
  name: string;
  province_code: string | null;
  country_code: string;
  country_name: string;
  default: boolean;
}

// Success response types
type BlockUserAdded = {
  input: string;
  wa_id: string;
};

type BlockUsersSuccess = {
  messaging_product: 'whatsapp';
  block_users: {
    added_users: BlockUserAdded[];
  };
};

// Error response types
type BlockUserErrorDetail = {
  message: string;
  code: number;
  error_data: {
    details: string;
  };
};

type BlockUserFailed = {
  input: string;
  errors: BlockUserErrorDetail[];
};

type BlockUsersError = {
  messaging_product: 'whatsapp';
  block_users: {
    added_users: BlockUserAdded[];
    failed_users: BlockUserFailed[];
  };
  errors: {
    message: string;
    type: string;
    code: number;
    error_data: {
      details: string;
    };
  };
};

// Union type for the response
export type BlockUsersResponse = BlockUsersSuccess | BlockUsersError;

export interface WhatsAppHistoryMessage {
  from: string;
  to?: string;
  id: string;
  timestamp: string;
  type: string;
  history_context: {
    status: 'DELIVERED' | 'ERROR' | 'PENDING' | 'PLAYED' | 'READ' | 'SENT';
  };
  [key: string]: any; // for dynamic message type content (e.g. text, image, etc.)
}
