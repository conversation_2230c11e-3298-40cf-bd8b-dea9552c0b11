export const data = {
  edges: [
    {
      id: 'xy-edge__On Message1On Message1-Send Message2Send Message2',
      source: 'On Message1',
      sourceHandle: 'On Message1',
      target: 'Send Message2',
      targetHandle: 'Send Message2',
    },
  ],
  nodes: [
    {
      data: {
        keywords: [
          {
            label: 'hi',
            value: 'hi',
          },
        ],
        label: 'On Message',
      },
      id: 'On Message1',
      measured: {
        height: 114,
        width: 322,
      },
      position: {
        x: 200,
        y: 200,
      },
      selected: false,
      type: 'On Message',
    },
    {
      data: {
        label: 'Send Message',
        options: [
          {
            id: 'Send Message2_1',
            label: 'Option 1',
          },
        ],
        selectedImage: {
          id: 'Send Message2_51',
          url: 'https://node.taskmate.ae/api/files/9wv2tekahneayd0/uhzs9g5al53lz75/pikachu_dhxhxWmjdY.png',
        },
        text: 'hi from pikachu',
      },
      dragging: false,
      height: 378,
      id: 'Send Message2',
      measured: {
        height: 378,
        width: 269,
      },
      position: {
        x: 237.33337727391472,
        y: 399.9798991762884,
      },
      selected: true,
      type: 'Send Message',
    },
    {
      data: {
        label: 'Button',
        parentId: 'Send Message2',
      },
      dragging: false,
      extent: 'parent',
      height: 80,
      id: 'Send Message2_1',
      measured: {
        height: 80,
        width: 174,
      },
      parentId: 'Send Message2',
      position: {
        x: 4.3434276857703935,
        y: 287.7979371889313,
      },
      selected: false,
      type: 'button',
      width: 174,
    },
  ],
  viewport: {
    x: 12.892840993452353,
    y: -204.67808453779804,
    zoom: 0.8839275310636711,
  },
};
