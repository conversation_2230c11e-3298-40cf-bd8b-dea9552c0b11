// flowHelpers.test.ts
import { FlowBot } from './flowHelpers';
import { beforeEach, describe, expect, MockInstance, test, vi } from 'vitest';
import { data } from './flowMockData3';
import { type Edge, type Node } from '@xyflow/react';
import { Account } from './types';

describe('FlowBot', () => {
  let flowBot: FlowBot;
  let sendInteractiveOrTextMessageMock: MockInstance;
  let updateStateMock: MockInstance;
  let assignAgentMock: MockInstance;
  let removeStateMock: MockInstance;
  let saveDataMock: MockInstance;

  beforeEach(() => {
    flowBot = new FlowBot({
      conversationId: 'convo1',
      account: {
        id: 'account1',
        name: 'Account 1',
      } as any as Account,
      flow: {
        name: 'Flow 1',
        flow: {
          nodes: data.nodes as Node[],
          edges: data.edges,
        },
      },
      phoneNo: '**********',
      leadId: 'lead1',
      state: '',
      userMessage: '',
    });

    sendInteractiveOrTextMessageMock = vi.spyOn(flowBot, 'sendInteractiveOrTextMessage').mockImplementation(() => {
      return Promise.resolve();
    });

    updateStateMock = vi.spyOn(flowBot, 'updateState').mockImplementation(() => {
      return Promise.resolve();
    });

    removeStateMock = vi.spyOn(flowBot, 'removeState').mockImplementation(() => {
      return Promise.resolve();
    });

    saveDataMock = vi.spyOn(flowBot, 'saveData').mockImplementation((key, value) => {
      return Promise.resolve();
    });

    assignAgentMock = vi.spyOn(flowBot, 'assignAgent').mockImplementation(() => {
      return Promise.resolve();
    });
  });

  test('should create an instance of FlowBot', () => {
    expect(flowBot).toBeInstanceOf(FlowBot);
  });

  test('test if save data works', async () => {
    flowBot.userMessage = '1';
    flowBot.state = 'Flow 1:service3';
    await flowBot.runFlow();

    expect(sendInteractiveOrTextMessageMock).toHaveBeenCalledTimes(0);
    expect(updateStateMock).toHaveBeenCalledTimes(0);
    expect(removeStateMock).toHaveBeenCalledTimes(1);

    expect(saveDataMock).toHaveBeenCalledWith('test', '1');
    // expect(sendInteractiveOrTextMessageMock).toHaveBeenCalledWith();
  });
});
