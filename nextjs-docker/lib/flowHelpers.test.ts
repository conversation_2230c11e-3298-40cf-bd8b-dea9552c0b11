// flowHelpers.test.ts
import { FlowBot } from './flowHelpers';
import { beforeEach, describe, expect, MockInstance, test, vi } from 'vitest';
import { data } from './flowMockData';
import { type Edge, type Node } from '@xyflow/react';
import { Account } from './types';

describe('FlowBot', () => {
  let flowBot: FlowBot;
  let sendInteractiveOrTextMessageMock: MockInstance;
  let updateStateMock: MockInstance;
  let assignAgentMock: MockInstance;

  beforeEach(() => {
    flowBot = new FlowBot({
      conversationId: 'convo1',
      account: {
        id: 'account1',
        name: 'Account 1',
      } as any as Account,
      flow: {
        name: 'Flow 1',
        flow: {
          nodes: data.nodes as Node[],
          edges: data.edges,
        },
      },
      phoneNo: '**********',
      leadId: 'lead1',
      state: '',
      userMessage: '',
    });

    sendInteractiveOrTextMessageMock = vi.spyOn(flowBot, 'sendInteractiveOrTextMessage').mockImplementation(() => {
      return Promise.resolve();
    });

    updateStateMock = vi.spyOn(flowBot, 'updateState').mockImplementation(() => {
      return Promise.resolve();
    });

    assignAgentMock = vi.spyOn(flowBot, 'assignAgent').mockImplementation((node) => {
      // console.trace();
      return Promise.resolve();
    });
  });

  test('should create an instance of FlowBot', () => {
    expect(flowBot).toBeInstanceOf(FlowBot);
  });

  test('test if keyword matching for the message works', async () => {
    flowBot.userMessage = 'deal';
    flowBot.state = '';
    await flowBot.runFlow();

    const service3 = data.nodes.find((node) => node.id === 'service3');
    expect(sendInteractiveOrTextMessageMock).toHaveBeenCalledOnce();
    expect(updateStateMock).toHaveBeenCalledWith({ node: service3 });
    // expect(sendInteractiveOrTextMessageMock).toHaveBeenCalledWith();
  });

  test('messages working', async () => {
    const service5 = data.nodes.find((node) => node.id === 'service5');
    flowBot.userMessage = 'massage';
    await flowBot.runFlow();

    expect(sendInteractiveOrTextMessageMock).toHaveBeenCalledOnce();
    expect(assignAgentMock).toHaveBeenCalledTimes(0);
    expect(updateStateMock).toHaveBeenCalledWith({ node: service5 });
  });

  // test other flows
  test('test if option matching works', async () => {
    flowBot.userMessage = 'hi';
    flowBot.state = '';
    await flowBot.runFlow();
  });

  test('Test if assign agent works', async () => {
    flowBot.state = 'Flow 1:service3';
    await flowBot.runFlow();
    expect(assignAgentMock).toHaveBeenCalledTimes(1);
  });
});
