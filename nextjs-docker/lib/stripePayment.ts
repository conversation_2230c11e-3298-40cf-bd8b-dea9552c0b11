import Stripe from 'stripe';
import { IInvoice, IExpandedAccount } from './types';
import { createPayment, getAccount, invoiceCompleted } from './pocket';
import dayjs from 'dayjs';

// Initialize Stripe with the secret key
const stripe = new Stripe(process.env.STRIPE_SECRET_KEY!, {});

/**
 * Process automatic payment for an invoice using stored payment method
 * 
 * @param invoice - The invoice to process payment for
 * @returns Object containing success status and message
 */
export const processAutomaticPayment = async (invoice: IInvoice): Promise<{ success: boolean; message: string }> => {
  try {
    // Get the account details to retrieve customer information
    const account = await getAccount(invoice.account_id);
    
    if (!account) {
      return { success: false, message: 'Account not found' };
    }
    
    // Check if the account has a Stripe customer ID
    if (!account.stripe_customer_id) {
      return { 
        success: false, 
        message: 'No payment method found for this account. Customer needs to add a payment method first.' 
      };
    }
    
    // Calculate amount in cents (Stripe requires amounts in smallest currency unit)
    const amountInCents = Math.round(invoice.amount_due * 100);
    
    // Create a payment intent
    const paymentIntent = await stripe.paymentIntents.create({
      amount: amountInCents,
      currency: account.currency?.toLowerCase() || 'aed',
      customer: account.stripe_customer_id,
      payment_method_types: ['card'],
      off_session: true, // This is an automatic payment
      confirm: true, // Confirm the payment immediately
      description: `Invoice #WT-${invoice.wt_id} - Automatic renewal payment`,
      metadata: {
        invoice_id: invoice.id,
        account_id: invoice.account_id,
        wt_id: invoice.wt_id.toString()
      }
    });
    
    // If payment is successful
    if (paymentIntent.status === 'succeeded') {
      // Record the payment in our system
      await createPayment(
        'automatic-stripe', 
        invoice.account_id, 
        [invoice], 
        'success', 
        undefined, 
        dayjs().toDate(), 
        undefined, 
        paymentIntent.id
      );
      
      // Mark the invoice as completed
      await invoiceCompleted(invoice.id);
      
      return { 
        success: true, 
        message: `Payment processed successfully. Payment ID: ${paymentIntent.id}` 
      };
    } else if (paymentIntent.status === 'requires_action') {
      // Payment requires additional action (like 3D Secure)
      return { 
        success: false, 
        message: 'Payment requires additional authentication. Customer needs to complete the payment manually.' 
      };
    } else {
      // Payment failed for some other reason
      return { 
        success: false, 
        message: `Payment failed with status: ${paymentIntent.status}` 
      };
    }
  } catch (error: any) {
    // Handle Stripe errors
    const errorMessage = error.message || 'Unknown error occurred during payment processing';
    
    // Record the failed payment
    try {
      await createPayment(
        'automatic-stripe', 
        invoice.account_id, 
        [invoice], 
        'failed', 
        errorMessage
      );
    } catch (recordError) {
      console.error('Failed to record payment failure:', recordError);
    }
    
    return { 
      success: false, 
      message: `Payment processing error: ${errorMessage}` 
    };
  }
};
