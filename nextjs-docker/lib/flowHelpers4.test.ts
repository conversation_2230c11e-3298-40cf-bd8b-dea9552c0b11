// flowHelpers.test.ts
import { FlowBot } from './flowHelpers';
import { beforeEach, describe, expect, MockInstance, test, vi } from 'vitest';
import { data } from './flowMockData4';
import { type Edge, type Node } from '@xyflow/react';
import { Account } from './types';

describe('FlowBot', () => {
  let flowBot: FlowBot;
  let sendInteractiveOrTextMessageMock: MockInstance;
  let updateStateMock: MockInstance;
  let assignAgentMock: MockInstance;
  let removeStateMock: MockInstance;
  let saveDataMock: MockInstance;
  let sendImageMessageMock: MockInstance;

  beforeEach(() => {
    flowBot = new FlowBot({
      conversationId: 'convo1',
      account: {
        id: 'account1',
        name: 'Account 1',
      } as any as Account,
      flow: {
        name: 'Flow 1',
        flow: {
          nodes: data.nodes as Node[],
          edges: data.edges,
        },
      },
      phoneNo: '**********',
      leadId: 'lead1',
      state: '',
      userMessage: '',
    });

    sendInteractiveOrTextMessageMock = vi.spyOn(flowBot, 'sendInteractiveOrTextMessage');

    sendImageMessageMock = vi.spyOn(flowBot, 'sendImageMessage').mockImplementation(() => {
      return Promise.resolve();
    });

    updateStateMock = vi.spyOn(flowBot, 'updateState').mockImplementation(() => {
      return Promise.resolve();
    });

    removeStateMock = vi.spyOn(flowBot, 'removeState').mockImplementation(() => {
      return Promise.resolve();
    });

    saveDataMock = vi.spyOn(flowBot, 'saveData').mockImplementation((key, value) => {
      console.log(key, value);
      return Promise.resolve();
    });

    assignAgentMock = vi.spyOn(flowBot, 'assignAgent').mockImplementation(() => {
      return Promise.resolve();
    });
  });

  test('should create an instance of FlowBot', () => {
    expect(flowBot).toBeInstanceOf(FlowBot);
  });

  test('test if image message works', async () => {
    flowBot.userMessage = 'hi';
    await flowBot.runFlow();

    expect(sendInteractiveOrTextMessageMock).toHaveBeenCalledTimes(1);
    expect(sendImageMessageMock).toHaveBeenCalledWith({
      message: 'hi from pikachu',
      url: 'https://node.taskmate.ae/api/files/9wv2tekahneayd0/uhzs9g5al53lz75/pikachu_dhxhxWmjdY.png',
    });
    expect(updateStateMock).toHaveBeenCalledTimes(1);
    expect(removeStateMock).toHaveBeenCalledTimes(0);
  });
});
