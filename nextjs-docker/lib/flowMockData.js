export const data = {
  edges: [
    {
      id: 'xy-edge__select_keywords2select_keywords2-service3service3',
      source: 'select_keywords2',
      sourceHandle: 'select_keywords2',
      target: 'service3',
      targetHandle: 'service3',
    },
    {
      id: 'xy-edge__service3service3-assign_agent2assign_agent2',
      source: 'service3',
      sourceHandle: 'service3',
      target: 'assign_agent2',
      targetHandle: 'assign_agent2',
    },
    {
      id: 'xy-edge__select_keywords4select_keywords4-service5service5',
      source: 'select_keywords4',
      sourceHandle: 'select_keywords4',
      target: 'service5',
      targetHandle: 'service5',
    },
    {
      id: 'xy-edge__service5service5-assign_agent13assign_agent13',
      source: 'service5',
      sourceHandle: 'service5',
      target: 'assign_agent13',
      targetHandle: 'assign_agent13',
    },
  ],
  nodes: [
    {
      data: {
        agents: [
          {
            id: '3a1mzo45a0k7gk5',
            name: 'FAST-agent',
          },
          {
            id: 'hmxhlm228a8gw1r',
            name: 'salmanagent',
          },
          {
            id: 'ha2pu69xolvqwcs',
            name: '<PERSON><PERSON><PERSON> Test 01',
          },
          {
            id: 't7i2vzrwyrkds8f',
            name: 'testhanif',
          },
        ],
        label: 'Assign Agent',
        selectedAgent: 'ha2pu69xolvqwcs',
      },
      dragging: false,
      id: 'assign_agent2',
      measured: {
        height: 86,
        width: 320,
      },
      position: {
        x: 356.2055283402992,
        y: 330.4811303848885,
      },
      selected: false,
      type: 'Assign agent',
    },
    {
      data: {
        keywords: [
          {
            __isNew__: true,
            label: 'price',
            value: 'price',
          },
          {
            __isNew__: true,
            label: 'deal',
            value: 'deal',
          },
        ],
        label: 'Select Keywords',
      },
      dragging: false,
      id: 'select_keywords2',
      measured: {
        height: 108,
        width: 320,
      },
      position: {
        x: 148,
        y: -180.8938696151115,
      },
      selected: false,
      type: 'On Message',
    },
    {
      data: {
        label: 'Send Message',
        text: 'Please check our website for the deals',
      },
      dragging: false,
      id: 'service3',
      measured: {
        height: 162,
        width: 248,
      },
      position: {
        x: 341,
        y: -32.8938696151115,
      },
      selected: false,
      type: 'Send Message',
    },
    {
      data: {
        keywords: [
          {
            value: 'massage',
          },
        ],
        label: 'Select Keywords',
        selectedKeyword: ['massage'],
      },
      dragging: false,
      id: 'select_keywords4',
      measured: {
        height: 108,
        width: 320,
      },
      position: {
        x: 606.8062218537368,
        y: -281.4890042140023,
      },
      selected: false,
      type: 'On Message',
    },
    {
      data: {
        label: 'Send Message',
        options: [
          {
            id: 1,
            label: 'Option 1',
          },
          {
            id: 2,
            label: 'Option 2',
          },
          {
            id: 3,
            label: 'Option 3',
          },
          {
            id: 4,
            label: 'Option 4',
          },
          {
            id: 5,
            label: 'Option 5',
          },
          {
            id: 6,
            label: 'Option 6',
          },
        ],
        text: 'We have these people who can give massage',
      },
      dragging: false,
      id: 'service5',
      measured: {
        height: 702,
        width: 248,
      },
      position: {
        x: 681.8209909085722,
        y: -117.74892879516749,
      },
      selected: false,
      type: 'Send Message',
    },
    {
      data: {
        label: 'Button',
        text: 'Shokat',
      },
      extent: 'parent',
      height: 80,
      id: 'service5_1',
      measured: {
        height: 80,
        width: 174,
      },
      parentId: 'service5',
      position: {
        x: 10,
        y: 162,
      },
      selected: false,
      type: 'button',
      width: 174,
    },
    {
      data: {
        label: 'Button',
        text: 'Banu',
      },
      extent: 'parent',
      height: 80,
      id: 'service5_2',
      measured: {
        height: 80,
        width: 174,
      },
      parentId: 'service5',
      position: {
        x: 10,
        y: 242,
      },
      selected: false,
      type: 'button',
      width: 174,
    },
    {
      data: {
        label: 'Button',
        text: 'ChooChoo',
      },
      extent: 'parent',
      height: 80,
      id: 'service5_3',
      measured: {
        height: 80,
        width: 174,
      },
      parentId: 'service5',
      position: {
        x: 10,
        y: 322,
      },
      selected: false,
      type: 'button',
      width: 174,
    },
    {
      data: {
        label: 'Button',
        text: 'Puka Paka',
      },
      extent: 'parent',
      height: 80,
      id: 'service5_4',
      measured: {
        height: 80,
        width: 174,
      },
      parentId: 'service5',
      position: {
        x: 10,
        y: 402,
      },
      selected: false,
      type: 'button',
      width: 174,
    },
    {
      data: {
        label: 'Button',
        text: 'Some random dude',
      },
      extent: 'parent',
      height: 80,
      id: 'service5_5',
      measured: {
        height: 80,
        width: 174,
      },
      parentId: 'service5',
      position: {
        x: 10,
        y: 482,
      },
      selected: false,
      type: 'button',
      width: 174,
    },
    {
      data: {
        label: 'Button',
        text: 'Tail wala',
      },
      extent: 'parent',
      height: 80,
      id: 'service5_6',
      measured: {
        height: 80,
        width: 174,
      },
      parentId: 'service5',
      position: {
        x: 10,
        y: 562,
      },
      selected: false,
      type: 'button',
      width: 174,
    },
    {
      data: {
        agents: [
          {
            id: '3a1mzo45a0k7gk5',
            name: 'FAST-agent',
          },
          {
            id: 'hmxhlm228a8gw1r',
            name: 'salmanagent',
          },
          {
            id: 'ha2pu69xolvqwcs',
            name: 'Murtaza Test 01',
          },
          {
            id: 't7i2vzrwyrkds8f',
            name: 'testhanif',
          },
        ],
        label: 'Assign Agent',
        selectedAgent: '3a1mzo45a0k7gk5',
      },
      dragging: true,
      id: 'assign_agent13',
      measured: {
        height: 86,
        width: 320,
      },
      position: {
        x: 1265.947341594504,
        y: 181.77837961169888,
      },
      selected: true,
      type: 'Assign agent',
    },
  ],
  viewport: {
    x: -248.07506610330393,
    y: 234.31057577209333,
    zoom: 0.7955364837549237,
  },
};
