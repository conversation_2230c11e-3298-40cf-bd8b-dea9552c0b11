export const data = {
  nodes: [
    {
      id: 'On Message4',
      position: {
        x: -2313.601959113287,
        y: -5188.443796819642,
      },
      type: 'On Message',
      data: {
        label: 'On Message',
        keywords: [
          {
            value: 'oil',
            label: 'oil',
          },
          {
            value: 'where are you located?',
            label: 'where are you located?',
          },
        ],
      },
      measured: {
        width: 322,
        height: 136,
      },
      selected: false,
      dragging: false,
    },
    {
      id: 'Send Message5',
      position: {
        x: -1893.2661236189888,
        y: -5110.942153784318,
      },
      type: 'Send Message',
      data: {
        label: 'Send Message',
        text: "Hi\n\nThank you for reaching out Petromin Express, UAE's Top Certified Autocare Service Provider.\n\n🚗 Get Exclusive Discounts on all services at Petromin Express! \n\n🔧 Oil Change Services | 🚘 Tyre Services | 🛠️ Mechanical & Electrical Services | 🚿 Car Wash | ❄️ AC / Battery Services & Much More! We’ve got your car care needs covered!\n\nWhy Choose Petromin Express: We Fix It Right, The First Time\n1.⁠ ⁠Service Warranty\n2.⁠ ⁠Genuine Parts\n3.⁠ ⁠Trained Technicians\n\nService Stations Across UAE:\nDubai | Sharjah | Abu Dhabi | Ras Al Khaimah",
      },
      height: 260,
      measured: {
        width: 434,
        height: 260,
      },
      selected: true,
      dragging: false,
    },
    {
      id: 'On Message6',
      position: {
        x: -2368.4651428544043,
        y: -4955.853932835664,
      },
      type: 'On Message',
      data: {
        label: 'On Message',
        keywords: [
          {
            value: 'can i get more info on this?',
            label: 'can i get more info on this?',
          },
        ],
      },
      measured: {
        width: 322,
        height: 136,
      },
      selected: false,
      dragging: false,
    },
    {
      id: 'On Message7',
      position: {
        x: -2263.4654289079062,
        y: -4741.216673151882,
      },
      type: 'On Message',
      data: {
        label: 'On Message',
        keywords: [
          {
            value: 'can you check the price of a product?',
            label: 'can you check the price of a product?',
          },
        ],
      },
      measured: {
        width: 322,
        height: 136,
      },
      selected: false,
      dragging: false,
    },
    {
      id: 'Send Message8',
      position: {
        x: -255.64193083320095,
        y: -5198.20145360208,
      },
      type: 'Send Message',
      data: {
        label: 'Send Message',
        options: [
          {
            id: 'Send Message8_1',
            label: 'Option 1',
          },
          {
            id: 'Send Message8_2',
            label: 'Option 2',
          },
          {
            id: 'Send Message8_3',
            label: 'Option 3',
          },
          {
            id: 'Send Message8_4',
            label: 'Option 4',
          },
          {
            id: 'Send Message8_5',
            label: 'Option 5',
          },
          {
            id: 'Send Message8_6',
            label: 'Option 6',
          },
        ],
        text: 'Select the branch you would prefer to visit ',
      },
      height: 800,
      measured: {
        width: 434,
        height: 800,
      },
      selected: false,
      dragging: false,
    },
    {
      id: 'Send Message8_1',
      position: {
        x: 10,
        y: 260,
      },
      type: 'button',
      extent: 'parent',
      parentId: 'Send Message8',
      width: 174,
      height: 80,
      data: {
        label: 'Button',
        parentId: 'Send Message8',
        text: 'Al Quoz Dubai',
      },
      measured: {
        width: 174,
        height: 80,
      },
      selected: false,
    },
    {
      id: 'Send Message8_2',
      position: {
        x: 10,
        y: 350,
      },
      type: 'button',
      extent: 'parent',
      parentId: 'Send Message8',
      width: 174,
      height: 80,
      data: {
        label: 'Button',
        parentId: 'Send Message8',
        text: 'Mussafah Abu Dhabi',
      },
      measured: {
        width: 174,
        height: 80,
      },
      selected: false,
    },
    {
      id: 'Send Message8_3',
      position: {
        x: 10,
        y: 440,
      },
      type: 'button',
      extent: 'parent',
      parentId: 'Send Message8',
      width: 174,
      height: 80,
      data: {
        label: 'Button',
        parentId: 'Send Message8',
        text: 'Umm Ramool Dubai',
      },
      measured: {
        width: 174,
        height: 80,
      },
      selected: false,
    },
    {
      id: 'Send Message8_4',
      position: {
        x: 10,
        y: 530,
      },
      type: 'button',
      extent: 'parent',
      parentId: 'Send Message8',
      width: 174,
      height: 80,
      data: {
        label: 'Button',
        parentId: 'Send Message8',
        text: 'Al Qusais Dubai',
      },
      measured: {
        width: 174,
        height: 80,
      },
      selected: false,
    },
    {
      id: 'Send Message8_5',
      position: {
        x: 10,
        y: 620,
      },
      type: 'button',
      extent: 'parent',
      parentId: 'Send Message8',
      width: 174,
      height: 80,
      data: {
        label: 'Button',
        parentId: 'Send Message8',
        text: 'Sharjah',
      },
      measured: {
        width: 174,
        height: 80,
      },
      selected: false,
    },
    {
      id: 'Send Message8_6',
      position: {
        x: 10,
        y: 710,
      },
      type: 'button',
      extent: 'parent',
      parentId: 'Send Message8',
      width: 174,
      height: 80,
      data: {
        label: 'Button',
        parentId: 'Send Message8',
        text: 'Ras al Khaimah',
      },
      measured: {
        width: 174,
        height: 80,
      },
      selected: false,
    },
    {
      id: 'Send Message13',
      position: {
        x: -1787.009164521159,
        y: -4679.716159240535,
      },
      type: 'Send Message',
      data: {
        label: 'Send Message',
        text: 'Select from below on-going offers',
        options: [
          {
            id: 'Send Message13_1',
            label: 'Option 1',
          },
          {
            id: 'Send Message13_2',
            label: 'Option 2',
          },
          {
            id: 'Send Message13_3',
            label: 'Option 3',
          },
        ],
      },
      height: 530,
      measured: {
        width: 434,
        height: 530,
      },
      selected: false,
      dragging: false,
    },
    {
      id: 'Send Message13_1',
      position: {
        x: 10,
        y: 260,
      },
      type: 'button',
      extent: 'parent',
      parentId: 'Send Message13',
      width: 174,
      height: 80,
      data: {
        label: 'Button',
        parentId: 'Send Message13',
        text: 'Oil /Minor Service',
      },
      measured: {
        width: 174,
        height: 80,
      },
      selected: false,
    },
    {
      id: 'Send Message13_2',
      position: {
        x: 10,
        y: 350,
      },
      type: 'button',
      extent: 'parent',
      parentId: 'Send Message13',
      width: 174,
      height: 80,
      data: {
        label: 'Button',
        parentId: 'Send Message13',
        text: 'Brakepad Service',
      },
      measured: {
        width: 174,
        height: 80,
      },
      selected: false,
    },
    {
      id: 'Send Message13_3',
      position: {
        x: 10,
        y: 440,
      },
      type: 'button',
      extent: 'parent',
      parentId: 'Send Message13',
      width: 174,
      height: 80,
      data: {
        label: 'Button',
        parentId: 'Send Message13',
        text: 'Tyre',
      },
      measured: {
        width: 174,
        height: 80,
      },
      selected: false,
    },
    {
      id: 'Send Message17',
      position: {
        x: -937.0891343811613,
        y: -5109.4857706174325,
      },
      type: 'Send Message',
      data: {
        label: 'Send Message',
        text: "Minor / Oil Change Service Package:\n\nChoose from our affordable service packages:\nPetromin Engine Oil & Filter\n✅ AED 99 (5,000 KMs - 4 Ltr with Filter)\n✅ AED 136 (10,000 KMs - 4 Ltr with Filter)\n\nMulti Brand Oil Change Promotions: Choose any Engine Oil (Castrol, Nissan, Toyota, Lexus & more)\n✅ AED 119 (10,000 KMs - 4 Ltr)\n\nWhat's Included?\n1. Oil Change\n2. Top-Up Radiator Fluid\n3. Brake Fluid Check-Up\n4. Air Filter Check-Up\n5. Top-Up Battery Fluid\n6. Windshield Water Reservoir Refill\n7. Tire Pressure Check-Up\n8. Wiper Blades Check-Up\n9. Lights Check-Up\n10. Window Cleaning\n\n*🚗 Pick & Drop Service Available in Dubai for your convenience.\n\n📞 Need more info? Call/WhatsApp us at 054 730 7073.\n\n✨ Drive Safe, Drive Confidently with Petromin Express!\n\n🔧 Your Trusted Vehicle Care Partner\n*Terms & Conditions Apply.",
      },
      height: 260,
      measured: {
        width: 434,
        height: 260,
      },
      selected: false,
      dragging: false,
    },
    {
      id: 'Send Message18',
      position: {
        x: -926,
        y: -4826.5,
      },
      type: 'Send Message',
      data: {
        label: 'Send Message',
        text: '🚗 Brake Pad Services Starting at Just 199 AED! 🚗\n\nKeep your car safe and running smoothly with our professional brake pad services at Petromin Express.\n\nSpecial Offers\nSedan (Japanese/Korean)/ Hatchback - 199 AED + VAT (Including Labor & Parts)\nSedan (German/American)\t\t   - 350 AED + VAT (Including Labor & Parts)\nSUV / CrossOver\t\t\t   - 450 AED + VAT (Including Labor & Parts)\n\n📍 Visit your nearest Petromin service station today for top-notch service and unbeatable prices.\n\n📞 Need more info? Call/WhatsApp us at 054 730 7073.\n\n✨ Drive Safe, Drive Confidently with Petromin Express!\n\n🔧 Your Trusted Vehicle Care Partner\n*Terms & Conditions Apply.',
      },
      height: 260,
      measured: {
        width: 434,
        height: 260,
      },
      selected: false,
      dragging: false,
    },
    {
      id: 'Send Message19',
      position: {
        x: -930.2067628875996,
        y: -4541.085363318615,
      },
      type: 'Send Message',
      data: {
        label: 'Send Message',
        text: 'Buy 4 Tyres of any size or brand and enjoy the following services for FREE:\n✅ Nitrogen Inflation\n✅ Alignment Check-Up\n✅ Tire Fitting & Balancing\n✅ Tire Sensor Configuration\n✅ 30,000 KM Tire Rotation\n✅ All Major Tyre Brands / Expert Technicians / Reliable Quality Service\n\n📞 Need more info? Call/WhatsApp us at 054 730 7073.\n\n✨ Drive Safe, Drive Confidently with Petromin Express!\n\n🔧 Your Trusted Vehicle Care Partner\n*Terms & Conditions Apply.',
      },
      height: 260,
      measured: {
        width: 434,
        height: 260,
      },
      selected: false,
      dragging: false,
    },
    {
      id: 'Send Message20',
      position: {
        x: 328.89085411465044,
        y: -4904.256730511825,
      },
      type: 'Send Message',
      data: {
        label: 'Send Message',
        text: 'Click on below link to talk to live agent\nhttps://wa.me/971547307073',
      },
      height: 260,
      measured: {
        width: 434,
        height: 260,
      },
      selected: false,
      dragging: false,
    },
    {
      id: 'On Message20',
      position: {
        x: -2406.719097906128,
        y: -4493.4011759970335,
      },
      type: 'On Message',
      data: {
        label: 'On Message',
        keywords: [
          {
            value: 'hello',
            label: 'hello',
          },
          {
            value: 'price',
            label: 'price',
          },
          {
            value: 'services',
            label: 'services',
          },
          {
            value: 'hi',
            label: 'hi',
          },
        ],
      },
      measured: {
        width: 322,
        height: 136,
      },
      selected: false,
      dragging: false,
    },
    {
      id: 'Send Message21',
      position: {
        x: -1797.6893825294412,
        y: -3954.955144506315,
      },
      type: 'Send Message',
      data: {
        label: 'Send Message',
        text: 'Please share the complete Tyre Size (Width/Height RimSize) Example: 225/50R17\n',
      },
      height: 260,
      measured: {
        width: 434,
        height: 260,
      },
      selected: false,
      dragging: false,
    },
    {
      id: 'Save Data22',
      position: {
        x: -1252.8151592444879,
        y: -4058.313499005309,
      },
      type: 'Save Data',
      data: {
        label: 'Save Data',
        text: 'tyre_size',
      },
      measured: {
        width: 217,
        height: 88,
      },
      selected: false,
      dragging: false,
    },
    {
      id: 'Data Node23',
      position: {
        x: -1289.1980496092215,
        y: -3786.869410169433,
      },
      type: 'Data Node',
      data: {
        label: 'Data Node',
      },
      measured: {
        width: 322,
        height: 44,
      },
      selected: false,
      dragging: false,
    },
    {
      id: 'Save Data24',
      position: {
        x: -1342.6033076660765,
        y: -4245.070486994211,
      },
      type: 'Save Data',
      data: {
        label: 'Save Data',
        text: 'Service',
      },
      measured: {
        width: 217,
        height: 88,
      },
      selected: false,
      dragging: false,
    },
    {
      id: 'Save Data25',
      position: {
        x: 468.20057507846604,
        y: -4525.171276279757,
      },
      type: 'Save Data',
      data: {
        label: 'Save Data',
        text: 'Station',
      },
      measured: {
        width: 217,
        height: 88,
      },
      selected: false,
      dragging: false,
    },
  ],
  edges: [
    {
      source: 'On Message4',
      sourceHandle: 'On Message4',
      target: 'Send Message5',
      targetHandle: 'Send Message5',
      id: 'xy-edge__On Message4On Message4-Send Message5Send Message5',
    },
    {
      source: 'On Message6',
      sourceHandle: 'On Message6',
      target: 'Send Message5',
      targetHandle: 'Send Message5',
      id: 'xy-edge__On Message6On Message6-Send Message5Send Message5',
    },
    {
      source: 'On Message7',
      sourceHandle: 'On Message7',
      target: 'Send Message5',
      targetHandle: 'Send Message5',
      id: 'xy-edge__On Message7On Message7-Send Message5Send Message5',
    },
    {
      source: 'Send Message13_2',
      sourceHandle: 'Send Message13_2',
      target: 'Send Message18',
      targetHandle: 'Send Message18',
      id: 'xy-edge__Send Message13_2Send Message13_2-Send Message18Send Message18',
    },
    {
      source: 'Send Message17',
      sourceHandle: 'Send Message17',
      target: 'Send Message8',
      targetHandle: 'Send Message8',
      id: 'xy-edge__Send Message17Send Message17-Send Message8Send Message8',
    },
    {
      source: 'Send Message18',
      sourceHandle: 'Send Message18',
      target: 'Send Message8',
      targetHandle: 'Send Message8',
      id: 'xy-edge__Send Message18Send Message18-Send Message8Send Message8',
    },
    {
      source: 'Send Message5',
      sourceHandle: 'Send Message5',
      target: 'Send Message13',
      targetHandle: 'Send Message13',
      id: 'xy-edge__Send Message5Send Message5-Send Message13Send Message13',
    },
    {
      source: 'On Message20',
      sourceHandle: 'On Message20',
      target: 'Send Message5',
      targetHandle: 'Send Message5',
      id: 'xy-edge__On Message20On Message20-Send Message5Send Message5',
    },
    {
      source: 'Save Data22',
      sourceHandle: 'a',
      target: 'Data Node23',
      targetHandle: 'Data Node23',
      id: 'xy-edge__Save Data22a-Data Node23Data Node23',
    },
    {
      source: 'Send Message21',
      sourceHandle: 'Send Message21',
      target: 'Save Data22',
      targetHandle: 'a',
      id: 'xy-edge__Send Message21Send Message21-Save Data22a',
    },
    {
      source: 'Send Message13_3',
      sourceHandle: 'Send Message13_3',
      target: 'Send Message21',
      targetHandle: 'Send Message21',
      id: 'xy-edge__Send Message13_3Send Message13_3-Send Message21Send Message21',
    },
    {
      source: 'Data Node23',
      sourceHandle: 'Data Node23',
      target: 'Send Message19',
      targetHandle: 'Send Message19',
      id: 'xy-edge__Data Node23Data Node23-Send Message19Send Message19',
    },
    {
      source: 'Send Message19',
      sourceHandle: 'Send Message19',
      target: 'Send Message8',
      targetHandle: 'Send Message8',
      id: 'xy-edge__Send Message19Send Message19-Send Message8Send Message8',
    },
    {
      source: 'Send Message8_1',
      sourceHandle: 'Send Message8_1',
      target: 'Send Message20',
      targetHandle: 'Send Message20',
      id: 'xy-edge__Send Message8_1Send Message8_1-Send Message20Send Message20',
    },
    {
      source: 'Send Message8_2',
      sourceHandle: 'Send Message8_2',
      target: 'Send Message20',
      targetHandle: 'Send Message20',
      id: 'xy-edge__Send Message8_2Send Message8_2-Send Message20Send Message20',
    },
    {
      source: 'Send Message8_3',
      sourceHandle: 'Send Message8_3',
      target: 'Send Message20',
      targetHandle: 'Send Message20',
      id: 'xy-edge__Send Message8_3Send Message8_3-Send Message20Send Message20',
    },
    {
      source: 'Send Message8_4',
      sourceHandle: 'Send Message8_4',
      target: 'Send Message20',
      targetHandle: 'Send Message20',
      id: 'xy-edge__Send Message8_4Send Message8_4-Send Message20Send Message20',
    },
    {
      source: 'Send Message8_5',
      sourceHandle: 'Send Message8_5',
      target: 'Send Message20',
      targetHandle: 'Send Message20',
      id: 'xy-edge__Send Message8_5Send Message8_5-Send Message20Send Message20',
    },
    {
      source: 'Send Message8_6',
      sourceHandle: 'Send Message8_6',
      target: 'Send Message20',
      targetHandle: 'Send Message20',
      id: 'xy-edge__Send Message8_6Send Message8_6-Send Message20Send Message20',
    },
    {
      source: 'Send Message13_1',
      sourceHandle: 'Send Message13_1',
      target: 'Save Data24',
      targetHandle: 'a',
      id: 'xy-edge__Send Message13_1Send Message13_1-Save Data24a',
    },
    {
      source: 'Send Message13_2',
      sourceHandle: 'Send Message13_2',
      target: 'Save Data24',
      targetHandle: 'a',
      id: 'xy-edge__Send Message13_2Send Message13_2-Save Data24a',
    },
    {
      source: 'Send Message13_3',
      sourceHandle: 'Send Message13_3',
      target: 'Save Data24',
      targetHandle: 'a',
      id: 'xy-edge__Send Message13_3Send Message13_3-Save Data24a',
    },
    {
      source: 'Send Message13_1',
      sourceHandle: 'Send Message13_1',
      target: 'Send Message17',
      targetHandle: 'Send Message17',
      id: 'xy-edge__Send Message13_1Send Message13_1-Send Message17Send Message17',
    },
    {
      source: 'Send Message8_1',
      sourceHandle: 'Send Message8_1',
      target: 'Save Data25',
      targetHandle: 'a',
      id: 'xy-edge__Send Message8_1Send Message8_1-Save Data25a',
    },
    {
      source: 'Send Message8_2',
      sourceHandle: 'Send Message8_2',
      target: 'Save Data25',
      targetHandle: 'a',
      id: 'xy-edge__Send Message8_2Send Message8_2-Save Data25a',
    },
    {
      source: 'Send Message8_3',
      sourceHandle: 'Send Message8_3',
      target: 'Save Data25',
      targetHandle: 'a',
      id: 'xy-edge__Send Message8_3Send Message8_3-Save Data25a',
    },
    {
      source: 'Send Message8_4',
      sourceHandle: 'Send Message8_4',
      target: 'Save Data25',
      targetHandle: 'a',
      id: 'xy-edge__Send Message8_4Send Message8_4-Save Data25a',
    },
    {
      source: 'Send Message8_5',
      sourceHandle: 'Send Message8_5',
      target: 'Save Data25',
      targetHandle: 'a',
      id: 'xy-edge__Send Message8_5Send Message8_5-Save Data25a',
    },
    {
      source: 'Send Message8_6',
      sourceHandle: 'Send Message8_6',
      target: 'Save Data25',
      targetHandle: 'a',
      id: 'xy-edge__Send Message8_6Send Message8_6-Save Data25a',
    },
  ],
  viewport: {
    x: 1798.3913930294907,
    y: 3472.36135527397,
    zoom: 0.6531994981024177,
  },
};
