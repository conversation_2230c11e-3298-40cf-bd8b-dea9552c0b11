import {
  addLocationMessage,
  addMediaMessage,
  addMessage,
  addReactionMessage,
  getPb,
  getRepliedToMessage,
  setLeadAsOptOut,
  setOptedOutCountOfCampaign,
  upsertLead,
} from '@/lib/pocket';
import {
  type Account,
  type Contact,
  IAutomation,
  ICampaign,
  IConversation,
  IExpandedConversation,
  IExpandedConversationAndExpandedMessage,
  IExpandedConversationsWithMessages,
  IExpandedMessage,
  ILead,
  ILeadRepliedCampaign,
  IMessage,
  IReaction,
  IReferral,
  ITemplateDatabase,
  type WARecieveMessage,
} from '@/lib/types';
import { readMessage } from '@/lib/wa';
import { ReactFlowJsonObject } from '@xyflow/react';
import { Logger } from 'next-axiom';
import type PocketBase from 'pocketbase';
import { FlowBot } from './flowHelpers';
import { sendPushNotification } from './firebase';
import { mitfFlow } from './mitf/mitfFlow';

export const recieveMessage = async (message: WARecieveMessage, contacts: [Contact] | undefined, log: Logger) => {
  const pb = await getPb();
  const { business_phone_number_id, id, from, type, reaction, text, interactive, button, context, referral } = message;

  const account = await pb.collection('accounts').getFirstListItem<Account>(`phone_id="${business_phone_number_id}"`); //get bsuiness from bsuniess phone id
  const [lead] = await Promise.all([
    upsertLead(from, account.id, contacts, referral),
    // readMessage(id, business_phone_number_id)
  ]);

  try {
    //get the convo
    let convo;
    let messageAdded = false;
    try {
      convo = await pb.collection<IExpandedConversationAndExpandedMessage>('conversations').getFirstListItem(`from = "${lead.id}"`, {
        expand: 'template,message,assigned_agent',
      });
    } catch (e) {
      await addMessageToOurDb({ context, type, lead, text, account, button, reaction, message, interactive, log, referral });
      messageAdded = true;
      convo = await pb.collection<IExpandedConversationAndExpandedMessage>('conversations').getFirstListItem(`from = "${lead.id}"`, {
        expand: 'template,message,assigned_agent',
      });
    }

    const { state, template: template_id, id: conversation_id } = convo;
    const lastMessage = convo.expand?.message;

    const template: ITemplateDatabase = (convo as IExpandedConversationAndExpandedMessage).expand?.template;

    try {
      await countReply({ lastMessage, template, type, text, button, reaction, pb, lead, log });
    } catch (error) {
      console.log('error in count reply', error);
    }
    try {
      if (!messageAdded) {
        await addMessageToOurDb({ context, type, lead, text, account, button, reaction, message, interactive, log, referral });
      }
    } catch (error) {
      console.log('error in add message to our db', error);
    }
    try {
      await runTemplates({ template_id, pb, conversation_id, account, state, lead, type, interactive, text });
    } catch (error) {
      console.log('error in run templates', error);
    }

    // send firebase notification
    try {
      // Get the FCM token for the assigned agent
      const fcmToken = convo?.expand?.assigned_agent?.notification ?? '';
      // Get the sender's name (lead name)
      const senderName = convo?.expand?.from?.name ?? lead.name ?? 'Customer';

      // Prepare message content based on message type
      let messageContent = '';

      if (type === 'text') {
        messageContent = text.body;
      } else if (type === 'button') {
        messageContent = `Button: ${button.text}`;
      } else if (type === 'interactive') {
        if (interactive.type === 'list_reply') {
          messageContent = `Selected: ${interactive.list_reply.title}`;
        } else {
          messageContent = `Button: ${interactive.button_reply.title}`;
        }
      } else if (type === 'reaction') {
        messageContent = `Reacted with: ${reaction.emoji}`;
      } else if (type === 'image') {
        messageContent = 'Sent an image';
      } else if (type === 'video') {
        messageContent = 'Sent a video';
      } else if (type === 'audio') {
        messageContent = 'Sent an audio message';
      } else if (type === 'document') {
        messageContent = 'Sent a document';
      } else if (type === 'location') {
        messageContent = 'Shared a location';
      } else {
        messageContent = 'Sent a message';
      }

      // Only send notification if we have a valid FCM token
      if (fcmToken && fcmToken.trim() !== '') {
        await sendPushNotification(fcmToken, senderName, messageContent);
        log.info(`Push notification sent to agent for message from ${senderName}`);
      }
    } catch (error) {
      // Log the error for debugging
      console.error('Failed to send push notification:', error);
      log.error(`Failed to send push notification: ${error}`);
    }
    // Get which template conversation is this person in
  } catch (e: any) {
    if (e?.result) {
      const { result } = e;
      if (result?.error) {
        console.log(result.error);
      }
    } else {
      console.log(e);
    }
  }
};

const addMessageToOurDb = async ({
  context,
  type,
  lead,
  text,
  account,
  button,
  reaction,
  message,
  interactive,
  log,
  referral,
}: {
  context: { id: string };
  type: never;
  lead: ILead;
  text: { body: string };
  account: { id: string };
  button: { text: string };
  reaction: IReaction;
  message: WARecieveMessage;
  interactive: any;
  log: Logger;
  referral: IReferral | null;
}) => {
  // if last message is a template message, then get the campaign
  // Get the previous message in this conversation, add the reply count to the campaign

  let repliedToMessage: IMessage | undefined;
  if (context?.id) {
    repliedToMessage = await getRepliedToMessage(context?.id);
  }

  if (type === 'text') {
    await addMessage(lead.id, text.body, 'user', account.id, null, 'sent from wetarseel', null, null, repliedToMessage?.id, null, referral);
    // await addToRepliedList(user.id)
    // revalidatePath(`/${account.id}/list`)
  } else if (type === 'button') {
    if (button.text.toLowerCase() == 'stop promotions') {
      try {
        await setLeadAsOptOut(lead.id);
      } catch (error) {
        console.log(error);
        log.error(`error in marking lead ${lead.id} as opt out`);
      }
      if (repliedToMessage?.campaign) {
        try {
          await setOptedOutCountOfCampaign(repliedToMessage?.campaign);
        } catch (error) {
          log.error(`error in increasing opt out count campaign ${repliedToMessage?.campaign}`);
          console.log(error);
        }
      }
    }
    await addMessage(lead.id, button.text, 'user', account.id, null, 'sent from wetarseel', null, null, repliedToMessage?.id);
  } else if (type === 'location') {
    await addLocationMessage(lead.id, message, type, 'user', account.id);
  } else if (type === 'interactive') {
    if (interactive.type === 'list_reply') {
      await addMessage(lead.id, interactive.list_reply.title, 'user', account.id, null, 'sent from wetarseel', null, null, repliedToMessage?.id);
    } else {
      await addMessage(lead.id, interactive.button_reply.title, 'user', account.id, null, 'sent from wetarseel', null, null, repliedToMessage?.id);
    }
  } else if (type == 'reaction') {
    try {
      await addReactionMessage(reaction.emoji, reaction.message_id);
    } catch (error) {
      console.log(error);
      log.error(JSON.stringify(error));
    }
  } else {
    await addMediaMessage(lead.id, message, type, 'user', account.id);
  }
};

const countReply = async ({
  lastMessage,
  template,
  type,
  text,
  button,
  reaction,
  pb,
  lead,
  log,
}: {
  log: Logger;
  lastMessage: IMessage | undefined;
  template: ITemplateDatabase;
  type: never;
  text: { body: string };
  button: { text: string };
  reaction: IReaction;
  pb: PocketBase;
  lead: ILead;
}) => {
  if (lastMessage && lastMessage.campaign && lastMessage.message === template.template_name) {
    try {
      let messageText = '';
      if (type == 'text') {
        messageText = text.body;
      } else if (type == 'button') {
        messageText = button.text;
      } else if (type == 'reaction') {
        messageText = 'Reacted with: ' + reaction.emoji;
      } else if (type == 'unsuppported') {
        messageText = 'Unsupported message';
      }
      if (messageText != 'Stop promotions') {
        const record = await pb.collection<ILeadRepliedCampaign>('leads_replied_campaign').create({
          campaign: lastMessage.campaign,
          lead: lead.id,
          message: messageText,
        });
        await pb.collection<ILead>('leads').update(lead.id, { 'replied+': record.id });
        await pb.collection<ICampaign>('campaigns').update(lastMessage.campaign, { 'reply_count+': 1 });
      }
    } catch (error) {
      console.log(error);
      log.error(`Error in increasing reply count in campaign ${lastMessage?.campaign}`);
      log.error(JSON.stringify(error));
    }
  }
};

const runTemplates = async ({
  template_id,
  pb,
  conversation_id,
  account,
  state,
  lead,
  type,
  interactive,
  text,
}: {
  template_id?: string;
  pb: PocketBase;
  conversation_id: string;
  account: Account;
  state: string;
  lead: ILead;
  type: string;
  interactive: { button_reply: { title: string } };
  text: { body: string };
}) => {
  let blankFlows: IAutomation[] = await pb
    .collection<IAutomation>('automations')
    .getFullList({ filter: `account = "${account.id}" && template = null && isActive = true` });

  let BlankBot = [];
  let Bot;
  const getUserMessage = ({ interactive, type, text }: Record<string, any>) => {
    if (type === 'interactive') {
      if (interactive.type === 'list_reply') {
        console.log(interactive);
        return interactive.list_reply.title;
      }
      return interactive.button_reply.title;
    }
    return text?.body ?? '';
  };
  if (blankFlows.length > 0) {
    for (let i = 0; i < blankFlows.length; i++) {
      const nBlankBot = new FlowBot({
        conversationId: conversation_id,
        leadId: lead.id,
        flow: blankFlows?.[i],
        account,
        phoneNo: lead.phone_number,
        state,
        userMessage: getUserMessage({ interactive, type, text }),
      });
      await nBlankBot.init();
      BlankBot.push(nBlankBot);
    }
  }
  if (template_id) {
    let flows: IAutomation[] = await pb
      .collection<IAutomation>('automations')
      .getFullList({ filter: `account = "${account.id}" && template = "${template_id}" && isActive = true` });

    if (flows.length > 0) {
      Bot = new FlowBot({
        conversationId: conversation_id,
        flow: flows[0],
        account,
        phoneNo: lead.phone_number,
        state,
        leadId: lead.id,
        userMessage: type === 'interactive' ? interactive.button_reply.title : text.body,
      });
      await Bot.init();
    }
  }

  // Hard check for bank
  if (account?.bank_info === 'mitf') {
    Bot = new FlowBot({
      conversationId: conversation_id,
      flow: mitfFlow,
      account,
      phoneNo: lead.phone_number,
      state,
      leadId: lead.id,
      userMessage: type === 'interactive' ? interactive.button_reply.title : text.body,
    });
  }
  if (typeof Bot === 'undefined' || Bot.isEmptyFlow()) {
    // if no flow automation exist for this template, attempt to find flows in the blank template
    for (let i = 0; i < BlankBot.length; i++) {
      if (typeof BlankBot[i] === 'undefined' || BlankBot[i].isEmptyFlow()) {
        console.log('No flow automation found for this template');
      } else {
        await BlankBot[i].runFlow();
      }
    }
  } else {
    await Bot.runFlow();
  }
};
