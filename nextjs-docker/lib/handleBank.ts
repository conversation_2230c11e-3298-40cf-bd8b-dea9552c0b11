import type { FlowBot, WNode } from './flowHelpers';
import axios from 'axios';
import { storeUserBankSession } from './pocket';

const instance = axios.create({
  baseURL: process.env.MITF_ENDPONT,
  timeout: 1000,
  headers: { 'Content-Type': 'application/json' },
});

async function handleBankMessages({
  ctx,
  target_type,
  target_node,
  actionPerformed,
}: {
  ctx: FlowBot;
  target_type: string;
  target_node: WNode;
  actionPerformed: boolean;
}): Promise<boolean> {
  if (target_type === 'Authentication/VerifyCustomer') {
    try {
      // Get the user's phone number
      const phoneNumber = ctx.phoneNo;

      // Call the MITF API to verify the customer
      const response = await instance.post('/api/Authentication/VerifyCustomer', {
        phoneNumber,
      });

      // Store the user bank session data
      await storeUserBankSession(
        ctx.leadId,
        true,
        new Date(Date.now() + 24 * 60 * 60 * 1000) // Session expires in 24 hours
      );

      // Send a confirmation message to the user
      await ctx.sendTextMessage({
        message: 'Your WhatsApp number has been verified for banking services.',
      });

      return true; // Action was performed successfully
    } catch (error) {
      console.log('Error verifying customer:', error);
      // If verification failed, send an error message
      await ctx.sendTextMessage({
        message: 'Sorry, we could not verify your WhatsApp number for banking services.',
      });
      return true; // Action was performed (even if it failed)
    }
  }

  if (target_type === 'Authentication/GenerateOtp') {
    try {
      // Get the user's phone number
      const phoneNumber = ctx.phoneNo;
      // Call the MITF API to generate OTP
      const response = await instance.post('/api/Authentication/GenerateOtp', {
        phoneNumber,
        otpChannel: '1',
      });

      // Process the response
      await ctx.sendTextMessage({
        message: 'An OTP has been sent to your WhatsApp number. Please enter the OTP to proceed.',
      });

      // Automatically move to WaitForOtp state so the user can input the OTP
      const nextNode = { id: 'WaitForOtp', type: 'Save Data' } as WNode;
      await ctx.updateState({ node: nextNode });

      return true; // Action was performed successfully
    } catch (error) {
      console.error('Error generating OTP:', error);
      await ctx.sendTextMessage({
        message: 'An error occurred while generating OTP. Please try again later.',
      });
      return true; // Action was performed (even if it failed)
    }
  }

  if (target_type === 'Authentication/VerifyOtp') {
    try {
      // Get the user's phone number and OTP
      const phoneNumber = ctx.phoneNo;
      const otp = target_node.data.otp || ctx.userMessage; // Use the OTP from the node data or the user message

      // Call the MITF API to verify OTP
      const response = await instance.post('/api/Authentication/VerifyOtp', {
        otpId: 'f8b1d3aa-4716-4a5e-bae0-731a2d7c83ae',
        otp,
      });

      // Process the response
      // Send a confirmation message to the user
      await ctx.sendTextMessage({
        message: 'Your OTP has been verified successfully. You can now proceed with your banking transaction.',
      });

      return true; // Action was performed successfully
    } catch (error) {
      console.error('Error verifying OTP:', error);
      await ctx.sendTextMessage({
        message: 'Sorry, the OTP you provided is invalid or has expired. Please try again.',
      });
      return true; // Action was performed (even if it failed)
    }
  }

  if (target_type === 'FullVerified') {
    // Send a final confirmation message
    await ctx.sendTextMessage({
      message: 'Congratulations! You are now fully verified for banking services. You can access all banking features.',
    });
    return true; // Action was performed successfully
  }

  return actionPerformed; // Return the original value if no banking action was performed
}

export default handleBankMessages;
