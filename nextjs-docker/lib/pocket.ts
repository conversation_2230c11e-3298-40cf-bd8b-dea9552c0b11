'use server';

import { ICarouselTemplateObject, IHeader, ITemplateObject } from '@/app/[account_id]/templates/manage/[template_operation]/types';
import { Option } from '@/components/ui/multiple-selector';
import pricingList from '@/lib/pricingList.json';
import { server_component_pb } from '@/state/pb/server_component_pb';
import dayjs from 'dayjs';
import utc from 'dayjs/plugin/utc';
import parsePhoneNumber from 'libphonenumber-js';
import { Logger } from 'next-axiom';
import { revalidatePath } from 'next/cache';
import { redirect } from 'next/navigation';
import { phone } from 'phone';
import PocketBase, { BatchService } from 'pocketbase';
import { downloadAndReturnFile } from './filedownload';
import type {
  Account,
  AgentWithConvos,
  Contact,
  IAccountLimitHistory,
  IApiSettings,
  IAutomation,
  ICampaign,
  IConversation,
  ICountry,
  IDocument,
  IExpandedAccount,
  IExpandedAPISettings,
  IExpandedCampaign,
  IExpandedConversation,
  IExpandedConversationsWithMessages,
  IExpandedLead,
  IExpandedList,
  IExpandedListAndLeadCount,
  IExpandedLogs,
  IExpandedMessage,
  IExpandedMessageAndRepliedList,
  IExpandedMessagingLimit,
  IExpandedSavedMessages,
  IExpandedThirdParty,
  IHistoryMessage,
  IImage,
  IInvoice,
  ILead,
  IList,
  IListFilters,
  ILog,
  IMessage,
  IMessagingLimit,
  IMessagingLimitPartial,
  IPackageTier,
  IPhoneVerificationResponse,
  IReferral,
  ISendMessageResponse,
  IShopifyOrder,
  ITemplateDatabase,
  IThirdParty,
  ITransactionLog,
  Meta,
  Team,
  User,
  WALocation,
  WAMedia,
  WAMediaMessage,
  WAMediaReturn,
  WAMessageTypeMedia,
  WhatsAppHistoryMessage,
  SMBAppStateSyncWebhook,
  SMBContactStateSync,
} from './types';
import { canadianAreaCodes, extractUniqueTags, parseAndEvaluate, uploadMediaToMeta, USERNAME } from './utils';
import { get_media_url } from './wa';
const fs = require('fs');

dayjs.extend(utc);

let pb: PocketBase | null = null;
const log = new Logger();

export const getPb = async () => {
  if (!pb) {
    pb = new PocketBase(process.env.NEXT_PUBLIC_PB_URL);
    pb.autoCancellation(false);
    await pb.collection('_superusers').authWithPassword('<EMAIL>', 'Jojo.33443344');
  }
  if (!pb.authStore.isValid) {
    await pb.collection('_superusers').authWithPassword('<EMAIL>', 'Jojo.33443344');
  }
  return pb;
};

export const getUser = async () => {
  const { pb, cookies } = await server_component_pb();
  const user = pb.authStore.record as User;
  return user;
};

export const isNewUser = async (phoneNumber: string) => {
  const pb = await getPb();
  const resultList = await pb.collection('leads').getList(1, 50, {
    filter: `phone_number = "${phoneNumber}"`,
  });
  return resultList.items.length === 0;
};

export const deleteContactById = async (id: string) => {
  const pb = await getPb();
  const deleted = await pb.collection('leads').delete(id);
  return deleted;
};

export const deleteAgentById = async (id: string, account_id: string) => {
  const pb = await getPb();
  const deleteMessageLimitRecord = await pb.collection('messaging_limit').getFirstListItem(`user.id = "${id}"`);
  await pb.collection('messaging_limit').delete(deleteMessageLimitRecord.id);
  const deleted = await pb.collection('users').delete(id);
  revalidatePath(`${account_id}/agent`);
  return deleted;
};

export const toggleLeadState = async (id: string, account_id: string, active: boolean) => {
  try {
    const pb = await getPb();
    await pb.collection('leads').update(id, { active });
    revalidatePath(`${account_id}/lead-management`);
  } catch (error) {
    throw error;
  }
};

export async function getFileNameFromUrl(url: string, filename: string) {
  const response = await fetch(url);
  const blob = await response.blob();
  const file = new File([blob], filename, { type: blob.type });
  return file;
}

export const getAccount = async (accountId: string) => {
  try {
    const pb = await getPb();
    const resultList = await pb.collection('accounts').getFirstListItem<IExpandedAccount>(`id="${accountId}"`, {
      expand: 'package_tier,pb_user_id',
    });

    return resultList;
  } catch (e) {
    redirect('/business/select');
  }
};

/**
 * Update an account with a Stripe customer ID
 *
 * @param accountId - The ID of the account to update
 * @param stripeCustomerId - The Stripe customer ID to save
 * @returns The updated account record
 */
export const updateAccountStripeCustomerId = async (accountId: string, stripeCustomerId: string) => {
  try {
    const pb = await getPb();
    const result = await pb.collection('accounts').update(accountId, {
      stripe_customer_id: stripeCustomerId,
    });
    return result;
  } catch (error) {
    console.error('Error updating account with Stripe customer ID:', error);
    throw error;
  }
};

export const getAccountByWabaId = async (wabaId: string) => {
  try {
    const pb = await getPb();
    const resultList = await pb.collection<Account>('accounts').getFullList({
      filter: `waba_id="${wabaId}"`,
    });

    return resultList;
  } catch (e) {}
};

// export const create_lead_and_add_to_list = async (account: any, _data: any) => {
//   try {
//     function isNumeric(str: string) {
//       const regex = /^\d+$/;
//       return regex.test(str);
//     }

//     const { phone_number, list_name, name } = _data;
//     const pb = await getPb();

//     const _account = await pb.collection<IExpandedAccount>('accounts').getOne(account.id, {
//       expand: 'pb_user_id',
//     });

//     const bussiness_owner = _account.expand.pb_user_id.filter((user) => user.type == 'admin' && user.username != USERNAME);

//     const user_id = bussiness_owner[0].id;

//     const list = await pb.collection('lists').getList(1, 50, {
//       filter: `name="${list_name}"`,
//     });

//     //checking if the list exists or not
//     if (list.items.length === 0) {
//       return { message: `No list named ${list_name} was found`, status: 404 };
//     }

//     const phoneNumber = phone_number
//       .replace(/^\+/, '')
//       .replace(/ /g, '')
//       .replace(/[\s-]/g, '')
//       .replace(/[\[\]\(\)\{\}]/g, '');

//     const resultList = await pb.collection('leads').getList(1, 50, {
//       filter: `phone_number = "${phoneNumber}" && account = "${account.id}"`,
//     });

//     const numberCountry = phone(`+${phoneNumber}`, { validateMobilePrefix: false }).countryIso2;
//     if (!numberCountry || !isNumeric(phoneNumber)) {
//       return { message: 'The phone number is invalid', status: 404 };
//     }

//     const data = {
//       phone_number: phoneNumber,
//       account: account.id,
//       name: phoneNumber,
//       created_by: user_id,
//       status: 'New',
//       tags: [] as string[],
//       country: numberCountry,
//       log_id: '',
//       active: true,
//     };

//     if (name) {
//       data.name = name;
//     }

//     //adding lead to db
//     let lead_data: any;
//     if (resultList.items.length === 0) {
//       lead_data = await pb.collection('leads').create(data);
//     } else {
//       const existingRecord = resultList.items[0];
//       lead_data = await pb.collection('leads').update(existingRecord.id, data);
//     }

//     //adding lead to list
//     if (lead_data) {
//       await pb.collection('leads').update(lead_data.id, { 'list+': list.items[0].id });
//     }
//     return { lead: lead_data, message: 'Lead created and added in list successfully!!', status: 200 };
//   } catch (e) {
//     console.log(e);
//   }
// };

export const create_lead_and_add_to_list = async (account: any, _data: any) => {
  try {
    const { phone_number, list_name, name } = _data;
    const phone_num = phone_number.replace(/^\+/, '').trim();

    let phoneOutput = parsePhoneNumber(`+${phone_num}`, 'US');

    const pb = await getPb();

    const _account = await pb.collection<IExpandedAccount>('accounts').getOne(account.id, {
      expand: 'pb_user_id',
    });

    const bussiness_owner = _account.expand.pb_user_id.filter((user) => user.type == 'admin' && user.username != USERNAME);

    const user_id = bussiness_owner[0].id;

    let list = await pb.collection('lists').getList(1, 1, {
      filter: `name="${list_name}"`,
    });

    if (list.items.length === 0) {
      list = await pb.collection('lists').create({
        name: list_name,
        account: account.id,
        created_by: user_id,
      });
    }

    const resultList = await pb.collection('leads').getList(1, 1, {
      filter: `phone_number = "${phone_num}" && account = "${account.id}"`,
    });

    if (!phoneOutput?.country || !phoneOutput.isValid()) {
      return { message: 'The phone number is invalid', status: 404 };
    }

    const data = {
      phone_number: phone_num,
      account: account.id,
      name: name,
      created_by: user_id,
      status: 'New',
      tags: [] as string[],
      country: phoneOutput.country,
      log_id: '',
      active: true,
      'list+': list.items[0].id,
    };

    if (resultList.items.length === 0) {
      const lead_data = await pb.collection<ILead>('leads').create(data);
      return { lead: lead_data, message: 'Lead created and added to list successfully!', status: 200 };
    }
    const existingRecord = resultList.items[0];
    const lead_data = await pb.collection<ILead>('leads').update(existingRecord.id, data);
    return { lead: lead_data, message: 'Lead created and added to list successfully!', status: 200 };
  } catch (e) {
    console.log(JSON.stringify(e, null, 2));
    return { message: 'An error has occured', status: 404 };
  }
};

export const uploadImage = async (prevData: any, formData: any) => {
  'use server';
  try {
    const user = await getUser();
    const fileToUpload = formData.get('file');
    const accountId = formData.get('accountId');

    // mutate data
    const pb = await getPb();
    const _file = await pb.collection('images').create({
      file: fileToUpload,
      name: fileToUpload.name,
      account: accountId,
      created_by: user.id,
    });

    const url = pb.files.getURL(_file, _file.file);
    const res = await pb.collection<IImage>('images').update(_file.id, {
      url,
    });
    console.log('uploaded');
    revalidatePath(`/${accountId}/`);
    return { message: { status: 200, description: 'Image uploaded successfuly' } };
  } catch (e) {
    console.log(e);
    return { message: { status: 500, description: 'Error in uploading image' } };
  }
};

export const uploadFileFromLiveChat = async (prevData: any, formData: any) => {
  'use server';
  const user = await getUser();
  const fileToUpload = formData.get('file');
  const accountId = formData.get('accountId');
  const collectionName = formData.get('collectionName');
  const data = {
    file: fileToUpload,
    name: fileToUpload.name,
    account: accountId,
    created_by: user.id,
  };
  try {
    // mutate data
    const pb = await getPb();
    const _file = await pb.collection(collectionName).create(data);

    const url = pb.files.getURL(_file, _file.file as string);
    const res = await pb.collection(collectionName).update(
      _file.id,
      {
        url,
      },
      {
        expand: 'created_by',
      }
    );
    return { message: { status: 200, description: 'Upload successfuly', data: res } };
  } catch (e) {
    console.log(e);
    console.log(JSON.stringify(e, null, 2));
    return { message: { status: 500, description: 'Error in uploading file', data: data } };
  }
};

export const uploadPdfDocument = async (accountId: string, myFile: any, type?: string) => {
  const pb = await getPb();
  const user = await getUser();
  const fileToUpload = myFile.get('file');
  const _file = await pb.collection<IDocument>('documents').create({
    file: fileToUpload,
    name: fileToUpload.name,
    account: accountId,
    created_by: user ? user?.id : null,
    type: type ?? null,
  });

  const url = pb.files.getURL(_file, _file.file);
  const res = await pb.collection<IDocument>('documents').update(_file.id, {
    url,
  });
  revalidatePath(`/${accountId}/`);
  return res;
};

export const getBusinessInvoices = async (accountId: string) => {
  try {
    const pb = await getPb();
    const invoices = await pb.collection<IDocument>('documents').getFullList({
      filter: `account = "${accountId}" && type="invoice"`,
      sort: '-created',
    });
    return invoices;
  } catch (err) {
    console.log(err);
  }
};

export const upsertTemplate = async ({
  accountId,
  templateObject,
  edit,
  templateType,
  template_id,
}: {
  accountId: string;
  templateObject: ITemplateObject;
  edit: boolean;
  templateType: string;
  template_id: string;
}) => {
  try {
    const errorCodes = [100, 200, 192, 131009, 80008, 368, 200002];

    const pb = await getPb();
    const account = await getAccount(accountId);
    const wabaId = account.waba_id;
    const header = templateObject.components?.[0] as IHeader;
    let myFile = null;
    let fileUrl = null;
    let headerFileUrl = null;
    if (header?.format === 'IMAGE' || header?.format === 'VIDEO' || header?.format === 'DOCUMENT') {
      myFile = header?.file instanceof FormData ? (header.file.get('file') as File) : null;
      headerFileUrl = header?.fileUrl ?? null;

      //if user has selected a new image from choose file button, then upload the new file to facebook
      if (myFile) {
        //upload file to meta
        fileUrl = await uploadMediaToMeta(myFile, account);
      }
      //if editing and fileUrl already exists AND user has not selected any image, then upload the existing file to facebook
      else if (headerFileUrl) {
        const fileNameFromUrl = await getFileNameFromUrl(headerFileUrl, 'image');
        //upload file to meta
        fileUrl = await uploadMediaToMeta(fileNameFromUrl, account);
      }

      delete templateObject.components[0].file;
      delete templateObject.components[0].text;
      delete templateObject.components[0].fileUrl;
      templateObject.components[0].example = {
        header_handle: [`${fileUrl.h}`],
      };
    } else {
      delete templateObject.components[0].file;
      delete templateObject.components[0].fileUrl;
    }

    templateObject.components = templateObject.components.filter((component: any) => {
      return !(component.type === 'FOOTER' && component.text === '');
    });

    // remove header if text is empty
    if (header?.type === 'HEADER' && header?.format == 'TEXT') {
      if (header?.text === '') {
        templateObject.components.shift();
      }
    }
    const user = await getUser();

    // const raw = await fetch(
    if (edit) {
      console.log(JSON.stringify(templateObject));
      const raw = await fetch(`https://graph.facebook.com/v21.0/${template_id}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${account.access_token}`,
        },
        body: JSON.stringify(templateObject),
      });
      const response = await raw.json();
      if (response.error || errorCodes.includes(response?.error?.code)) {
        console.log(response, 'response error');
        return response;
      } else {
        //if no error and header type is image
        if (header?.format == 'IMAGE') {
          //if user has selected a new image from choose file button, then upload the new file to facebook
          if (myFile) {
            //upload file to meta
            const _file = await pb.collection('images').create({
              file: myFile,
              name: (myFile as File).name,
              account: accountId,
            });
            header.fileUrl = pb.files.getURL(_file, _file.file);
          } else {
            header.fileUrl = headerFileUrl;
          }
        }
        if (header?.format == 'VIDEO') {
          //if editing and fileUrl already exists AND user has selected a new image from choose file buttom, then create new image in pb
          if (myFile) {
            const _file = await pb.collection('videos').create({
              file: myFile,
              name: (myFile as File).name,
              account: accountId,
            });
            header.fileUrl = pb.files.getURL(_file, _file.file);
          } else {
            header.fileUrl = headerFileUrl;
          }
        }
        if (header?.format == 'DOCUMENT') {
          //if editing and fileUrl already exists AND user has selected a new image from choose file buttom, then create new image in pb
          if (myFile) {
            const _file = await pb.collection('documents').create({
              file: myFile,
              name: (myFile as File).name,
              account: accountId,
            });
            header.fileUrl = pb.files.getURL(_file, _file.file);
          } else {
            header.fileUrl = headerFileUrl;
          }
        }
        const id = (await pb.collection<ITemplateDatabase>('templates').getFirstListItem(`template_id="${template_id}"`)).id;
        await pb.collection('templates').update(id, {
          status: 'PENDING',
          template_body: templateObject,
          'edit_count+': 1,
          template_edit_date: dayjs().toDate().toUTCString(),
          updated_by: user.id,
        });
        revalidatePath(`/${accountId}/templates`);

        return response;
      }
    } else {
      //send template to meta
      const raw = await fetch(`https://graph.facebook.com/v21.0/${wabaId}/message_templates`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${account.access_token}`,
        },
        body: JSON.stringify(templateObject),
      });
      const response = await raw.json();
      if (response.error || errorCodes.includes(response?.error?.code)) {
        console.log(response, 'response error');
        return response;
      } else {
        //if no error and header type is image
        if (header?.format == 'IMAGE') {
          //if user has selected a new image from choose file button, then upload the new file to facebook
          if (myFile) {
            //upload file to meta
            const _file = await pb.collection('images').create({
              file: myFile,
              name: (myFile as File).name,
              account: accountId,
            });
            header.fileUrl = pb.files.getURL(_file, _file.file);
          } else {
            header.fileUrl = headerFileUrl;
          }
        }
        if (header?.format == 'VIDEO') {
          //if editing and fileUrl already exists AND user has selected a new image from choose file buttom, then create new image in pb
          if (myFile) {
            const _file = await pb.collection('videos').create({
              file: myFile,
              name: (myFile as File).name,
              account: accountId,
            });
            header.fileUrl = pb.files.getURL(_file, _file.file);
          } else {
            header.fileUrl = headerFileUrl;
          }
        }
        if (header?.format == 'DOCUMENT') {
          //if editing and fileUrl already exists AND user has selected a new image from choose file buttom, then create new image in pb
          if (myFile) {
            const _file = await pb.collection('documents').create({
              file: myFile,
              name: (myFile as File).name,
              account: accountId,
            });
            header.fileUrl = pb.files.getURL(_file, _file.file);
          } else {
            header.fileUrl = headerFileUrl;
          }
        }
        templateObject.id = response.id;
        await pb.collection('templates').create({
          template_name: templateObject.name,
          account: accountId,
          template_body: templateObject,
          status: response.status,
          template_id: response.id,
          created_by: user.id,
          type: templateType,
          category: templateType == 'utility-template' ? 'UTILITY' : 'MARKETING',
        });
        revalidatePath(`/${accountId}/templates`);
        return response;
      }
    }
  } catch (error) {
    console.log(error, 'error in catch');
    throw error;
  }
};

export const upsertTemplateCarousel = async ({
  accountId,
  templateObject,
  edit,
  templateType,
  template_id,
  _form,
}: {
  accountId: string;
  templateObject: ICarouselTemplateObject;
  edit: boolean;
  templateType: string;
  template_id: string;
  _form: FormData;
}) => {
  try {
    const errorCodes = [100, 200, 192, 131009, 80008, 368, 200002];
    const pb = await getPb();
    const account = await getAccount(accountId);
    const wabaId = account.waba_id;

    let fileArray = [];
    let headerAssetArray = [];
    const cards = templateObject.components?.[1]?.cards ?? [];

    for (let index = 0; index < cards.length; index++) {
      const card = cards[index];
      const header = card.components[0];
      let myFile = null;
      let fileUrl = null;
      let headerFileUrl = null;

      // myFile = header?.file instanceof FormData ? (header.file.get('file') as File) : null;
      myFile = _form.get(`file${index}`) as File;
      headerFileUrl = header?.fileUrl ?? null;
      const fileData = new FormData();

      //if user has selected a new image from choose file button, then upload the new file to facebook
      if (myFile) {
        //upload file to meta
        fileUrl = await uploadMediaToMeta(myFile, account);

        fileData.append('file', myFile);
        fileData.append('type', myFile.type);
        fileData.append('messaging_product', 'whatsapp');

        fileArray.push(myFile);

        const mediaResponse = await fetch(`https://graph.facebook.com/v20.0/${account.phone_id}/media`, {
          method: 'POST',
          headers: {
            Authorization: `Bearer ${account.access_token}`,
          },
          body: fileData,
        });
        const mediaData = await mediaResponse.json();

        headerAssetArray.push(mediaData.id);
      }
      //if editing and fileUrl already exists AND user has not selected any image, then upload the existing file to facebook
      else if (headerFileUrl) {
        const fileNameFromUrl = await getFileNameFromUrl(headerFileUrl, 'image');
        //upload file to meta
        fileUrl = await uploadMediaToMeta(fileNameFromUrl, account);

        fileData.append('file', fileNameFromUrl);
        fileData.append('type', fileNameFromUrl.type);
        fileData.append('messaging_product', 'whatsapp');

        fileArray.push(fileNameFromUrl);

        const mediaResponse = await fetch(`https://graph.facebook.com/v20.0/${account.phone_id}/media`, {
          method: 'POST',
          headers: {
            Authorization: `Bearer ${account.access_token}`,
          },
          body: fileData,
        });
        const mediaData = await mediaResponse.json();

        headerAssetArray.push(mediaData.id);
      }

      delete templateObject.components[1].cards[index].components[0].file;
      delete templateObject.components[1].cards[index].components[0].fileUrl;

      templateObject.components[1].cards[index].components[0].example = {
        header_handle: [`${fileUrl.h}`],
      };
    }

    const user = await getUser();

    let response: any;
    if (edit) {
      const raw = await fetch(`https://graph.facebook.com/v21.0/${template_id}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${account.access_token}`,
        },
        body: JSON.stringify(templateObject),
      });
      response = await raw.json();
    } else {
      const raw = await fetch(`https://graph.facebook.com/v21.0/${wabaId}/message_templates`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${account.access_token}`,
        },
        body: JSON.stringify(templateObject),
      });
      response = await raw.json();
    }

    if (response.error || errorCodes.includes(response?.error?.code)) {
      console.log(response, 'response error');
      return response;
    } else {
      for (let index = 0; index < cards.length; index++) {
        if (templateObject.components[1].cards[index].components?.[0]?.format === 'IMAGE') {
          const _file = await pb.collection('images').create({
            file: fileArray[index],
            name: fileArray[index].name,
            account: accountId,
          });
          let fileUrl = pb.files.getURL(_file, _file.file);
          templateObject.components?.[1]?.cards?.[index]?.components?.forEach((comp: any) => {
            if (comp.type === 'HEADER') {
              comp.fileUrl = fileUrl;
            }
          });
        }
        if (templateObject.components?.[1]?.cards?.[index]?.components?.[0]?.format === 'VIDEO') {
          const _file = await pb.collection('videos').create({
            file: fileArray[index],
            name: fileArray[index].name,
            account: accountId,
          });
          let fileUrl = pb.files.getURL(_file, _file.file);
          templateObject.components?.[1]?.cards?.[index]?.components?.forEach((comp: any) => {
            if (comp.type === 'HEADER') {
              comp.fileUrl = fileUrl;
            }
          });
        }
      }

      if (!edit) {
        templateObject.id = response.id;
        await pb.collection('templates').create({
          template_name: templateObject.name,
          account: accountId,
          template_body: templateObject,
          status: response.status,
          template_id: response.id,
          created_by: user.id,
          header_asset_id: headerAssetArray,
          type: 'carousel-template',
          category: 'MARKETING',
        });
      } else {
        const id = (await pb.collection<ITemplateDatabase>('templates').getFirstListItem(`template_id="${template_id}"`)).id;
        await pb.collection('templates').update(id, {
          status: 'PENDING',
          template_body: templateObject,
          updated_by: user.id,
          header_asset_id: headerAssetArray,
        });
      }

      revalidatePath(`/${accountId}/templates`);
      return response;
    }
  } catch (error) {
    throw error;
  }
};

export const updateHeaderAssetId = async (templateId: any, header_asset_id_array: string[]) => {
  try {
    const pb = await getPb();
    const result = await pb.collection('templates').update(templateId, { header_asset_id: header_asset_id_array });
    return result;
  } catch (error) {
    console.log(error);
    return error;
  }
};

export const getFilesForDialogs = async (accountId: string, collectionName: string) => {
  const pb = await getPb();
  const user = await getUser();
  const _files = [];

  try {
    const filterCondition = user.type != 'admin' ? ` && created_by = "${user.id}"` : '';
    const resultList = await pb.collection<IImage>(collectionName).getFullList({
      filter: `account = "${accountId}" ${filterCondition}`,
      expand: 'created_by',
      sort: '-created',
    });

    for (const file of resultList) {
      if (!file.url) {
        file.url = pb.files.getURL(file, file.file as string);
      }
      _files.push({ ...file });
    }
  } catch (error) {
    console.log(error);
  }

  return {
    files: _files,
  };
};

export const getAccountImage = async (accountId: string) => {
  try {
    const pb = await getPb();
    const resultList = await pb.collection<Account>('accounts').getOne(accountId, {
      fields: 'logo, id, collectionId',
    });
    const url = pb.files.getURL(resultList, resultList.logo as string);
    return url;
  } catch (error) {
    console.log({ error });
    return null;
  }
};

export const getImages = async (accountId: string) => {
  const pb = await getPb();
  const resultList = await pb.collection<IImage>('images').getList(1, 50, {
    filter: `account = "${accountId}"`,
  });
  return resultList.items.map((item) => {
    return { ...item, url: pb.files.getURL(item, item.file as string) };
  });
};

export const getListById = async (listId: string) => {
  const pb = await getPb();
  const resultList = await pb.collection<IList>('lists').getOne(listId);
  return resultList;
};

export const getSmartListById = async (listId: string) => {
  const pb = await getPb();
  const resultList = await pb.collection<IExpandedList>('lists').getOne(listId, {
    expand: 'list_filters',
  });
  return resultList;
};
export const getAllLeadsByListIds = async (listIds: string[]) => {
  try {
    const filter = listIds.map((id) => `id="${id}"`).join(' || ');
    const pb = await getPb();
    const resultList = await pb.collection<IExpandedList>('lists').getFullList({
      filter,
    });
    let result = [] as any; //it should be IExpandedList but it is giving error;
    for (const list of resultList) {
      let leads = await pb.collection<ILead>('leads').getFullList({
        filter: `list ~ "${list.id}"`,
      });
      result.push({ ...list, expand: { leads_via_list: leads } });
    }
    return result;
  } catch (e) {
    console.log(e);
    return e;
  }
};
// get templates based on accountId
export const getAllTemplatesFromDb = async (account: Account, user: User) => {
  const pb = await getPb();

  // If the user is not an admin and has access to view all templates, then filter the templates based on the user id
  const filterCondition = user.type != 'admin' && !user.view_all?.includes('templates');

  const filters = [`template_body != null`, `account.waba_id = "${account.waba_id}"`, `status != "DELETED"`];

  if (filterCondition) {
    filters.push(`created_by = "${user.id}"`);
  }
  const resultList = await pb.collection<ITemplateDatabase>('templates').getFullList({
    filter: filters.join(' && '),
    sort: '-created',
    expand: 'created_by,account',
  });
  return resultList;
};

// get approved templates based on accountId
export const getApprovedTemplatesFromDb = async (account: Account) => {
  const pb = await getPb();
  const user = await getUser();
  const userFilterCondition = `${user.type != 'admin' && !user.view_all?.includes('templates') ? ` && created_by = "${user.id}"` : ''}`;
  const filterCondition = `account.waba_id = "${account.waba_id}" && status = "APPROVED"${userFilterCondition}`;
  const resultList = await pb.collection<ITemplateDatabase>('templates').getFullList({
    filter: filterCondition,
    sort: '-created',
    expand: 'created_by,account',
  });
  return resultList;
};

export const getTemplateFromDbByTemplateId = async (templateId: string) => {
  const pb = await getPb();
  const resultList = await pb.collection<ITemplateDatabase>('templates').getOne(templateId);
  return resultList;
};

export const getPendingDeletionTemplates = async () => {
  try {
    const pb = await getPb();
    const resultList = await pb.collection<ITemplateDatabase>('templates').getFullList({
      filter: `status = "PENDING DELETION"`,
    });
    return resultList;
  } catch (error) {
    throw error;
  }
};

export const checkExistingName = async (account: Account, templateName: string) => {
  const pb = await getPb();
  try {
    //template alread exists
    const res = await pb
      .collection('templates')
      .getFirstListItem(`account.waba_id = "${account.waba_id}" && template_name = "${templateName}" && status != "DELETED"`);
    return true;
  } catch (error) {
    // doesnt exists
    return false;
  }
};

export const getCampaignMessages = async (accountId: string) => {
  const pb = await getPb();
  const resultList = await pb.collection<IExpandedCampaign>('campaigns').getFullList({
    filter: `account = "${accountId}"`,
    expand: 'messages_via_campaign',
  });
  return resultList;
};
// get campaigns based on accountId
export const getCampaign = async (accountId: string) => {
  const pb = await getPb();
  const user = await getUser();
  const userFilterCondition = `${user.type != 'admin' && !user.view_all?.includes('campaigns') ? ` && created_by = "${user.id}"` : ''}`;
  const filterCondition = `account = "${accountId}"${userFilterCondition}`;
  const resultList = await pb.collection<IExpandedCampaign>('campaigns').getFullList({
    filter: filterCondition,
    expand: 'template,leads_list,created_by',
    sort: '-created',
  });
  return resultList;
};

//** Get campaigns if retry count is less than 3 and current retry time is right now and status is not failed */
export const getRetryCampaigns = async () => {
  try {
    const pb = await getPb();
    const currentDate = dayjs.utc();
    const currentDateSubOneMinute = currentDate.subtract(1, 'minute');
    // get campaings where retry date is greater than equal to current date -1 and less than equal current date
    // 2024-01-01 00:00:00.000Z to 2024-01-01 00:01:00.000Z
    const resultList = await pb.collection<IExpandedCampaign>('campaigns').getFullList({
      filter: `retry_count < 3  && next_retry_date <= '${currentDate.format('YYYY-MM-DD HH:mm:00[.000Z]')}' && next_retry_date >= '${currentDateSubOneMinute.format('YYYY-MM-DD HH:mm:00[.000Z]')}' && retry_status != 'failed' && type = 'Published'`,
      expand: 'account',
    });
    return resultList;
  } catch (error) {
    throw error;
  }
};

export const setLeadAsOptIn = async (accountId: string, leadId: string) => {
  try {
    const pb = await getPb();
    await pb.collection('leads').update(leadId, { opt_out: false });
  } catch (err) {
    throw err;
  }
  revalidatePath(`/${accountId}/lead-management`);
  redirect(`/${accountId}/lead-management`);
};

export const getLeadReplyByCampaign = async (campaignId: string, leadId: string) => {
  try {
    const pb = await getPb();
    const result = await pb.collection('leads_replied_campaign').getFullList({
      filter: `campaign = "${campaignId}" && lead = "${leadId}"`,
    });
    if (result.length) {
      return result[0].message;
    } else {
      return 'Not Replied';
    }
  } catch (error) {
    throw error;
  }
};

export const getCampaignsByLists = async (campaigns: ICampaign[] | undefined, leadId: string) => {
  try {
    if (!campaigns) return [];
    const campaigsArray = await Promise.all(
      campaigns.map(async (item) => {
        const reply = await getLeadReplyByCampaign(item.id, leadId);
        return {
          name: item.name,
          id: item.id,
          date_and_time: item.created,
          leadReply: reply,
        };
      })
    );

    return campaigsArray;
  } catch (error) {
    throw error;
  }
};

export const getMessagesByAccount = async (accountId: string) => {
  const pb = await getPb();
  const resultList = await pb.collection<IExpandedMessage>('messages').getFullList({
    filter: `from = "agent" && created_by !=null && account = "${accountId}" && delivery_status != "failed" && delivery_status !="pending" && template != null`,
    expand: 'created_by,template, campaign,user',
    sort: '-created',
  });
  return resultList;
};

export const getCampaignMessagesById = async (campaignId: string, delivery_status: string | null = null) => {
  try {
    const pb = await getPb();
    const filterCondition = delivery_status ? `campaign = "${campaignId}" && delivery_status = "${delivery_status}"` : `campaign = "${campaignId}"`;
    const resultList = await pb.collection<IExpandedMessageAndRepliedList>('messages').getFullList({
      filter: filterCondition,
      expand: 'campaign,user,user.replied,template',
    });
    return resultList;
  } catch (error) {
    throw error;
  }
};

export const getScheduledCampagins = async () => {
  const currentDate = dayjs.utc();
  const currentDateSubOneMinute = currentDate.subtract(1, 'minute');
  const pb = await getPb();
  const res = await pb.collection<IExpandedCampaign>('campaigns').getFullList({
    filter: `type = "Scheduled"  && scheduled_time <= '${currentDate.format('YYYY-MM-DD HH:mm:59[.000Z]')}' && scheduled_time >= '${currentDateSubOneMinute.format('YYYY-MM-DD HH:mm:00[.000Z]')}'`,
    expand: 'leads_list,user,template,account,created_by',
  });
  return res;
};

export const setCampaignAsPublished = async (id: string) => {
  const pb = await getPb();
  const res = await pb.collection<ICampaign>('campaigns').update(id, { type: 'Published' });
  return res;
};
// get first template based on name
export const getTemplateFromDbByTemplateName = async ({ account, template_name }: { account: Account | null; template_name: string }) => {
  try {
    const pb = await getPb();
    const filter = account ? `template_name = "${template_name}" && account.waba_id = "${account.waba_id}"` : `template_name = "${template_name}"`;
    const resultList = await pb.collection<ITemplateDatabase>('templates').getFirstListItem(filter);
    return resultList;
  } catch (e: any) {
    console.log(e);
    throw e;
  }
};

export const getMessagesByTemplateId = async (template_id: string, account_id: string) => {
  try {
    const pb = await getPb();
    const res = await pb.collection<IExpandedMessage>('messages').getFullList({
      filter: `template = "${template_id}" && account = "${account_id}"`,
      expand: 'user',
    });
    return res;
  } catch (error) {
    console.log(error);
  }
};
/**
 * Get last unsent messages
 */
export const getUnsentMessages = async () => {
  const pb = await getPb();
  try {
    const resultList = await pb.collection<IExpandedMessage>('messages').getFullList({
      filter: `(campaign = null && delivery_status = "pending") || (campaign != null && delivery_status = "pending" && campaign.stopped = false)`,
      expand: 'template,user,campaign,account',
      sort: '-created',
    });
    return resultList;
  } catch (error) {
    return [];
  }
};

/**
 * Finds or creates a conversation record for the user
 * @param from the lead id of the person who is messaging us
 * @param accountId the account id of the business
 * @returns the conversation record
 */
export const upsertConversation = async (from: string, accountId: string) => {
  const pb = await getPb();
  try {
    const resultList = await pb.collection('conversations').getFirstListItem(`from = "${from}" && account="${accountId}"`);
    return resultList;
  } catch (e) {
    const result = await pb.collection('conversations').create({
      from,
      unread_count: 0,
      account: accountId,
    });
    return result;
  }
};

export const updateRecordByIdEditContact = async (
  id: string,
  accountId: string,
  formData: FormData,
  from: string,
  country: string,
  phone_number: string,
  tags: Option[],
  default_number: string,
  deletedListIds: string[],
  addNewList: string[]
) => {
  const pb = await getPb();
  const user = await getUser();
  const name = formData.get('name') as string;
  const status = formData.get('status') as string;
  const data = {
    name,
    country,
    phone_number,
    status,
    tags: tags.map((tag) => tag.value),
    'list+': addNewList,
    'list-': deletedListIds,
    updated_by: user.id,
  };
  try {
    //the number user inputs is the same as the lead number which exists in db
    if (default_number == phone_number) {
      const record = await pb.collection<ILead>('leads').update(id, data);
      await addLeadToSmartList(accountId, record);
    } else {
      //check if the number user inputs already exists in db
      const res = await pb.collection('leads').getList(1, 50, {
        filter: `phone_number = "${phone_number.replace(/^\+/, '')}" && account = "${accountId}"`,
      });
      if (res.totalItems > 0) {
        return { message: 'Phone number already exists', status: 400 };
      } else {
        const record = await pb.collection<ILead>('leads').update(id, data);
        await addLeadToSmartList(accountId, record);
      }
    }
  } catch (error) {
    console.log(error);
    return { message: 'Error updating lead', status: 404 };
  }
  revalidatePath(`/${accountId}/lead-management`);
  revalidatePath(`/${accountId}/edit-contact`);
  revalidatePath(`/${accountId}/view-contact`);
  return { message: 'Lead updated successfully!', status: 200 };
};

export const updateRecordByIdRightBar = async (id: string, accountId: string, formData: FormData, tags: Option[], meta: any) => {
  const pb = await getPb();
  const user = await getUser();
  const status = formData.get('status') as string;
  let record = null;
  const data = {
    status,
    tags: tags.map((tag) => tag.value),
    updated_by: user.id,
    meta,
  };
  try {
    record = await pb.collection<ILead>('leads').update(id, data);
    await addLeadToSmartList(accountId, record);
  } catch (error) {
    console.log(error);
    return { message: 'Error updating lead', status: 404 };
  }
  // revalidatePath(`/${accountId}/live-chat`);
  return { message: 'Lead updated successfully!', status: 200, record: record };
};

/**
 * This is adding a text type message
 * This function adds a message to the conversation, and revalidates the path to update the UI
 * Increase the undread count of the conversation, and create a conversation if not already there
 * @param userId lead id
 * @param message message text
 * @param from agent or user
 * @param accountId account id of the business
 * @returns
 */
export const addMessage = async (
  userId: string,
  message: string,
  from: 'agent' | 'user',
  account_id: string,
  campaign: string | null = null,
  delivery_status: 'pending' | 'read' | 'sent' | 'failed' | 'sent from wetarseel' = 'sent from wetarseel',
  template_id: string | null = null,
  interactive_message: string | null = null,
  replied_to: string | null = null,
  created_by: string | null = null,
  referral: IReferral | null = null,
  system: string | null = null
) => {
  const pb = await getPb();
  const loggedInUser = await getUser();
  const conversation = await upsertConversation(userId, account_id);
  const data = {
    user: `${userId}`,
    message: `${message}`,
    account: account_id,
    from: from,
    campaign,
    delivery_status,
    template: template_id,
    interactive_message,
    replied_to,
    //if recieve message is excuted, created_by is null
    created_by: created_by ?? loggedInUser?.id,
    convo_id: conversation.id ?? '',
    referral,
    system,
  };

  // add 1 to the undread_count in conversation
  if (from == 'user') {
    const record = await pb.collection<IMessage>('messages').create(data, { expand: 'created_by' });
    const conversationData: Partial<IConversation> = {
      unread_count: Number(conversation.unread_count) + 1,
      assigned_agent: created_by ?? loggedInUser?.id,
      message: record.id,
    };
    pb.collection('conversations').update(conversation.id, conversationData);

    return record;
  } else {
    const record = await pb.collection<IMessage>('messages').create(data, { expand: 'created_by' });
    const conversationData: Partial<IConversation> = {
      assigned_agent: created_by ?? loggedInUser?.id,
      message: record.id,
      template: template_id ?? undefined,
    };
    await pb.collection('conversations').update(conversation.id, conversationData);

    return record;
  }
};

export const getRepliedToMessage = async (wamid: String) => {
  try {
    const pb = await getPb();
    const record = await pb.collection<IMessage>('messages').getFirstListItem(`wamid="${wamid}"`);
    return record;
  } catch (error) {
    console.log(error);
  }
};

export const getLeadDataById = async (id: string) => {
  const pb = await getPb();
  try {
    const res = await pb.collection<IExpandedLead>('leads').getOne(id, {
      expand: 'list, campaign_history',
    });
    return res;
  } catch (error) {
    throw error;
  }
};

/**
 * This is adding a media type message
 * This function adds a message to the conversation, and revalidates the path to update the UI
 * Increase the undread count of the conversation, and create a conversation if not already there
 * @param userId lead id
 * @param waevent event of whatsapp
 * @param from agent or user
 * @param accountId account id of the business
 * @returns
 */
export const addMediaMessage = async (userId: string, waevent: WAMediaMessage, type: WAMessageTypeMedia, from: 'agent' | 'user', accountId: string) => {
  const pb = await getPb();
  const waeventData: WAMedia = waevent?.[type];
  if (!waeventData) {
    return Promise.resolve();
  }
  const id = waeventData.id;

  const media_url: WAMediaReturn = await get_media_url(id);
  const file = await downloadAndReturnFile(media_url.url, media_url.mime_type, media_url.id);
  const data = {
    user: `${userId}`,
    message: `${type}`,
    account: accountId,
    from: from,
    file,
    type,
  };

  return await addMediaMessageToPb(userId, accountId, from, data);
};

export const addLocationMessage = async (
  userId: string,
  waevent: {
    location: WALocation;
  },
  type: 'location',
  from: 'agent' | 'user',
  accountId: string
) => {
  const waeventData = waevent?.[type];

  const data = {
    user: `${userId}`,
    message: `${type}`,
    from: from,
    type,
    account: accountId,
    location: waeventData,
  };

  return await addMediaMessageToPb(userId, accountId, from, data);
};

export const addMediaMessageToPb = async (userId: string, accountId: string, from: string, data: Record<string, any>) => {
  const pb = await getPb();
  if (!data?.message) {
    delete data.message;
  }
  let record;
  try {
    record = await pb.collection<IMessage>('messages').create(data, {
      expand: 'created_by',
    });
  } catch (e) {
    console.log(JSON.stringify(e, null, 2));
    throw e;
  }
  const conversation = await upsertConversation(userId, accountId);

  if (from === 'user') {
    // add 1 to the undread_count in conversation
    const conversationData = {
      unread_count: Number(conversation.unread_count) + 1,
      message: record.id,
    };
    await pb.collection('conversations').update(conversation.id, conversationData);
  }
  const record2 = await pb.collection<IMessage>('messages').update(record.id, { convo_id: conversation.id }, { expand: 'created_by' });
  record2.url = pb.files.getURL(record, record.file);
  return record2;
};

// get all conversations
export const getConversations = async (accountId: string) => {
  const pb = await getPb();
  const resultList = await pb.collection<ILead>('leads').getFullList({
    sort: '-created',
    filter: `account = "${accountId}"`,
  });
  return resultList;
};

export const getConversationMessagesById = async (accountId: string) => {
  try {
    const pb = await getPb();
    const messages = await pb.collection<IExpandedMessage>('messages').getFullList({
      filter: `account = "${accountId}"`,
      sort: '-created',
      expand: 'interactive_message, replied_to, created_by,template',
      skipTotal: true,
    });
    return messages.map((item) => {
      if (item.file) {
        return { ...item, url: pb.files.getURL(item, item.file) };
      }
      return item;
    });
  } catch (error) {
    console.log(error);
    throw error;
  }
};

export const getConversationById = async (convoId: string) => {
  try {
    const pb = await getPb();
    const conversation = await pb.collection<IExpandedConversation>('conversations').getOne(convoId, {
      expand: 'assigned_agent, from.campaign_history',
    });
    return conversation;
  } catch (e) {
    console.log(e);
    throw e;
  }
};

export const getSavedMessages = async (accountId: string, userId: string) => {
  try {
    const pb = await getPb();
    const record = await pb.collection<IExpandedSavedMessages>('saved_messages').getFirstListItem(`account = "${accountId}"`, {
      expand: 'messages',
    });
    return record.expand.messages.filter((item) => item.created_by === userId);
  } catch (e) {
    return null;
  }
};

export const deleteSavedMessage = async (id: string, accountId: string) => {
  try {
    const pb = await getPb();
    const record = await pb.collection<IExpandedSavedMessages>('saved_messages').getFirstListItem(`account = "${accountId}"`, {
      expand: 'messages',
    });
    const data = {
      account: accountId,
      'messages-': id,
    };
    await pb.collection<IExpandedSavedMessages>('saved_messages').update(record.id, data, {
      expand: 'messages',
    });
  } catch (e) {
    throw e;
  }
};

export const saveMessagePocket = async (accountId: string, convoId: string, message_id: string) => {
  const pb = await getPb();
  try {
    //get the record id of the save_messages collection. Usually one account has one record in saved_messags.
    const record = await pb.collection<IExpandedSavedMessages>('saved_messages').getFirstListItem(`account = '${accountId}'`);
    //if record is found then update the message column
    if (record) {
      const data = {
        account: accountId,
        'messages+': message_id,
      };
      const res = await pb.collection<IExpandedSavedMessages>('saved_messages').update(record.id, data, {
        expand: 'messages',
      });
      return res;
    }
  } catch (e) {
    //else create record
    const data = {
      account: accountId,
      messages: message_id,
    };
    const res = await pb.collection<IExpandedSavedMessages>('saved_messages').create(data, {
      expand: 'messages',
    });
    return res;
  }
};

export const saveFlow = async ({
  accountId,
  selectedAutomation,
  flow,
  name,
  description,
}: {
  accountId: string;
  selectedAutomation?: IAutomation;
  flow: any;
  name: string;
  description: string;
}) => {
  const pb = await getPb();
  let result = null;
  if (selectedAutomation) {
    result = await pb.collection<IAutomation>('automations').update(selectedAutomation.id, {
      name,
      description,
      flow,
    });
  } else {
    result = await pb.collection<IAutomation>('automations').create({
      name,
      description,
      account: accountId,
      flow: flow,
    });
  }
  return result;
};

export const getConversation = async (phoneNumber: string) => {
  const pb = await getPb();
  const resultList = await pb.collection('leads').getList(1, 50, {
    filter: `phone_number = "${phoneNumber}"`,
    sort: '-created',
  });
  if (resultList.items.length === 0) {
    return null;
  } else {
    const conversation = await pb.collection('messages').getList(1, 50, {
      filter: `user = "${resultList.items[0].id}"`,
    });
    return conversation;
  }
};

export const getConversationsWithaReply = async (accountId: string) => {
  try {
    const resultList = await (await fetch(`${process.env.NEXT_PUBLIC_BUN_SERVER_URL}/get-conversations?accountId=${accountId}`)).json();
    return resultList;
    // const pb = await getPb();
    // const user = await getUser();
    // const viewAllConvos = user.view_all?.includes('live-chat');
    // let filterCondition = `account = "${accountId}"`;
    // const isAdminOrViewAll = user.type == 'admin' || viewAllConvos;
    // if (!isAdminOrViewAll) {
    //   //added assigned_agent = null so that all unassigned chats also show up for agent
    //   filterCondition += ` && (assigned_agent = "${user.id}" || assigned_agent = null)`;
    // }
    // const resultList = await pb.collection<IExpandedConversationsWithMessages>('conversations_with_messages').getFullList({
    //   filter: filterCondition,
    //   sort: '-unread_count, -last_message_created',
    //   expand: 'campaign_history, assigned_agent, from',
    //   skipTotal: true,
    // });
    // return resultList;
  } catch (error) {
    throw error;
  }
};

export const getMessageFromConversations = async (convoId: string) => {
  try {
    const start2 = performance.now();
    const resultList = await (await fetch(`${process.env.NEXT_PUBLIC_BUN_SERVER_URL}/get-conversation-messages?convoId=${convoId}`)).json();
    const end2 = performance.now();
    console.log(`getMessageFromConversations query took ${end2 - start2} ms for convoId: ${convoId}`);
    return resultList;
    // const pb = await getPb();
    // const user = await getUser();
    // const viewAllConvos = user.view_all?.includes('live-chat');
    // let filterCondition = `account = "${accountId}"`;
    // const isAdminOrViewAll = user.type == 'admin' || viewAllConvos;
    // if (!isAdminOrViewAll) {
    //   //added assigned_agent = null so that all unassigned chats also show up for agent
    //   filterCondition += ` && (assigned_agent = "${user.id}" || assigned_agent = null)`;
    // }
    // const resultList = await pb.collection<IExpandedConversationsWithMessages>('conversations_with_messages').getFullList({
    //   filter: filterCondition,
    //   sort: '-unread_count, -last_message_created',
    //   expand: 'campaign_history, assigned_agent, from',
    //   skipTotal: true,
    // });
    // return resultList;
  } catch (error) {
    throw error;
  }
};

// export const getConversationsWithaReplyFull = async (accountId: string) => {
//   try {
//     const pb = await getPb();
//     const user = await getUser();
//     const viewAllConvos = user.view_all?.includes('live-chat');
//     let filterCondition = `account = "${accountId}"`;
//     const isAdminOrViewAll = user.type == 'admin' || viewAllConvos;
//     if (!isAdminOrViewAll) {
//       //added assigned_agent = null so that all unassigned chats also show up for agent
//       filterCondition += ` && (assigned_agent = "${user.id}" || assigned_agent = null)`;
//     }
//     const resultList = await pb.collection<IExpandedConversationsWithMessages>('conversations_with_messages').getFullList({
//       filter: filterCondition,
//       sort: '-unread_count, -last_message_created',
//       expand: 'campaign_history, assigned_agent, from',
//       skipTotal: true,
//     });
//     return resultList;
//   } catch (error) {
//     throw error;
//   }
// };

// export const getConversationsWithaReplyFull = async (accountId: string) => {
//   try {
//     const pb = await getPb();
//     const user = await getUser();
//     const viewAllConvos = user.view_all?.includes('live-chat');
//     let filterCondition = `account = "${accountId}"`;
//     const isAdminOrViewAll = user.type == 'admin' || viewAllConvos;
//     if (!isAdminOrViewAll) {
//       //added assigned_agent = null so that all unassigned chats also show up for agent
//       filterCondition += ` && (assigned_agent = "${user.id}" || assigned_agent = null)`;
//     }
//     const resultList = await pb.collection<IExpandedConversationsWithMessages>('conversations_with_messages').getFullList({
//       filter: filterCondition,
//       sort: '-unread_count, -last_message_created',
//       expand: 'campaign_history, assigned_agent, from',
//       skipTotal: true,
//     });
//     return resultList;
//   } catch (error) {
//     throw error;
//   }
// };

export const getQuickConvos = async (accountId: string) => {
  try {
    const pb = await getPb();
    const user = await getUser();
    const viewAllConvos = user.view_all?.includes('live-chat');
    let filterCondition = `account = "${accountId}"`;
    const isAdminOrViewAll = user.type == 'admin' || viewAllConvos;
    if (!isAdminOrViewAll) {
      //added assigned_agent = null so that all unassigned chats also show up for agent
      filterCondition += ` && (assigned_agent = "${user.id}" || assigned_agent = null)`;
    }
    // special check for nuttino
    let resultList;
    resultList = await pb.collection<IExpandedConversationsWithMessages>('last_user_message').getFullList({
      filter: `account = "${accountId}"`,
      skipTotal: true,
    });

    return resultList;
  } catch (error) {
    throw error;
  }
};

/**
 * Set the undread count of the conversation to 0
 * @param accountId Account id of the business
 * @param userId ILead id of the user
 * @param skipRevalidation Skip revalidation of the path
 * @returns nothing
 */
export const revalidateConversation = async ({
  accountId,
  convoId,
  userId,
  unread_count = 0,
  skipRevalidation = false,
  wabaid,
  loggedInUser,
}: {
  accountId: string;
  convoId: string | null;
  userId: string | null;
  unread_count?: number;
  skipRevalidation: boolean;
  wabaid?: string;
  loggedInUser: User;
}) => {
  const log = new Logger();
  if (userId === null && convoId === null) {
    return;
  }
  try {
    const pb = await getPb();
    const conversation = userId ? await upsertConversation(userId, accountId) : null;
    const conversationData = {
      unread_count,
      ...(skipRevalidation && loggedInUser.type != 'admin' && { assigned_agent: loggedInUser.id }),
    };
    const updatedConversation: IConversation | null = conversation ? await pb.collection('conversations').update(conversation.id, conversationData) : null;
    if (!skipRevalidation) {
      revalidatePath(`/${accountId}/live-chat`);
    }
    return updatedConversation;
  } catch (error) {
    log.error('conversation', { error: JSON.stringify(error) });
    await log.flush();
    console.log(error);
  }
};

export const getAccounts = async () => {
  const pb = await getPb();
  const resultList = await pb.collection('accounts').getList();
  return resultList;
};

export const updateAccessTokenOfAccount = async (accountId: string, waba_id: string, access_token: string, expires_at: number) => {
  try {
    const pb = await getPb();
    const account = await getAccount(accountId);
    // for (const eachAccount of accountList) {
    //   if (eachAccount.waba_id === waba_id) {
    //     console.log('Error');
    //     return {
    //       message: `Error! A business with name ${eachAccount.name} already exists!`,
    //     };
    //   }
    // }

    //save the expiry_date
    let d = new Date(0);
    d.setUTCSeconds(expires_at);
    const data = { ...account, waba_id, access_token, expires_at: d };
    const record = await pb.collection('accounts').update(accountId, data);
    return record;
  } catch (e) {
    return e;
  }
};

export const updateAccountDetails = async (accountId: string, data: IPhoneVerificationResponse) => {
  try {
    const pb = await getPb();
    //data.data =
    // [
    //   {
    //     verified_name: 'Test Number',
    //     code_verification_status: 'NOT_VERIFIED',
    //     display_phone_number: '******-131-4313',
    //     quality_rating: 'GREEN',
    //     platform_type: 'CLOUD_API',
    //     throughput: { level: 'STANDARD' },
    //     webhook_configuration: { application: 'https://beta.app.wetarseel.ai/api/whatsapp' },
    //     id: '***************'
    //   }
    // ]

    let { code_verification_status, display_phone_number, platform_type, quality_rating, id } = data;

    const record = await pb.collection('accounts').update(accountId, {
      code_verification_status,
      display_phone_number: display_phone_number.replace(/\s/g, ''),
      platform_type,
      quality_rating,
      phone_id: id,
    });
    return record;
  } catch (e) {
    return e;
  }
};

export const updateAccount = async (accountId: string, data: any) => {
  try {
    const pb = await getPb();
    const record = await pb.collection('accounts').update(accountId, data);
    return record;
  } catch (e) {
    return e;
  }
};

//TODO: expand by user id. Get all the users so that we can append it.
export const getUserAccountList = async (userId: string, isSuperUser: boolean) => {
  const pb = await getPb();
  let resultList;
  if (isSuperUser) {
    resultList = await pb.collection<Account>('accounts').getFullList({
      expand: 'users_via_favorite.favorite',
    });
  } else {
    resultList = await pb.collection<Account>('accounts').getFullList({
      filter: `pb_user_id.id ?= '${userId}'`,
      expand: 'users_via_favorite.favorite',
    });
  }
  return resultList;
};

export const getAccountsWhereLimitEnds = async (filter: string) => {
  const pb = await getPb();
  const resultList = await pb.collection('accounts').getFullList<IExpandedAccount>({
    filter,
    expand: 'package_tier',
  });
  return resultList;
};

export const getAllAccounts = async () => {
  const pb = await getPb();
  const resultList = await pb.collection('accounts').getFullList<IExpandedAccount>({
    expand: 'package_tier',
    sort: '-created, -updated',
  });

  return resultList;
};

export const updateRecord = async (phoneNumber: string, data: any) => {
  const pb = await getPb();
  const resultList = await pb.collection('leads').getList(1, 50, {
    filter: `phone_number = "${phoneNumber}"`,
  });
  if (resultList.items.length === 0) {
    return null;
  } else {
    const record = await pb.collection('leads').update(resultList.items[0].id, data);
    return record;
  }
};

export const getAllLeads = async (accountId: string, type?: string) => {
  const pb = await getPb();
  const user = await getUser();
  let userFilterCondition: string;
  userFilterCondition = `${user.type != 'admin' && !user.view_all?.includes('lead-management') ? ` && created_by = "${user.id}"` : ''}`;
  const filterCondition = `account = "${accountId}"${userFilterCondition}`;
  const resultList = await pb.collection<IExpandedLead>('leads').getFullList({
    filter: filterCondition,
    expand: 'created_by',
    sort: '-created,name',
  });
  return resultList;
};

export const getReport = async (accountId: string) => {
  const result = await (await fetch(`${process.env.NEXT_PUBLIC_BUN_SERVER_URL}/get-report?accountId=${accountId}`)).json();
  return result;
};

export const getAllLeadsFromLeadManagement = async (
  accountId: string,
  page: string,
  perPage: string,
  name: string,
  country: string,
  phone_number: string,
  tags: string,
  active: string,
  optout: string,
  status: string,
  meta: string
) => {
  const pb = await getPb();
  const user = await getUser();
  const userFilterCondition = `${user.type !== 'admin' && !user.view_all?.includes('lead-management') ? ` && created_by = "${user.id}"` : ''}`.trim();
  const filterCondition =
    `account = "${accountId}"${userFilterCondition} ${name} ${country} ${phone_number} ${tags} ${active} ${optout} ${status} ${meta}`.trim();
  const resultList = await pb.collection<IExpandedLead>('leads').getList(Number(page), Number(perPage), {
    filter: filterCondition,
    expand: 'created_by',
    sort: '-created,name',
  });
  return resultList;
};

export const getLeadsByListId = async (listId: string, meta: string = '') => {
  const pb = await getPb();
  const user = await getUser();
  const userFilterCondition = `${user.type != 'admin' && !user.view_all?.includes('lead-management') ? ` && created_by = "${user.id}" ${meta}` : meta}`;
  const filterCondition = `list ~ "${listId}"${userFilterCondition}`;
  const resultList = await pb.collection<ILead>('leads').getFullList({
    filter: filterCondition,
  });
  return resultList;
};

export const getLeadsByListIds = async (listIds: string[]) => {
  const pb = await getPb();
  const filter = listIds.map((id) => `list~"${id}"`).join(' || ');
  let leads = await pb.collection<ILead>('leads').getFullList({
    filter,
  });
  return leads;
};

export const getAllLists = async (accountId: string) => {
  const pb = await getPb();
  const user = await getUser();
  const userFilterCondition = `${user.type != 'admin' && !user.view_all?.includes('lead-management') ? ` && created_by = "${user.id}"` : ''}`;
  const filterCondition = `account = "${accountId}"${userFilterCondition}`;
  const resultList = await pb.collection<IExpandedListAndLeadCount>('lists_with_count').getFullList({
    filter: filterCondition,
    sort: '-updated,-created',
    // expand: "created_by"
    expand: 'created_by',
  });
  return resultList;
};

export const createContact = async (
  id: string,
  accountId: string,
  formData: FormData,
  country: string,
  phone_number: string,
  tags: Option[],
  addNewList: string[]
) => {
  const pb = await getPb();
  const user = await getUser();
  const name = formData.get('name') as string;
  const status = formData.get('status') as string;
  const data = {
    name,
    account: accountId,
    active: true,
    country,
    phone_number,
    status,
    tags: tags.map((tag) => tag.value),
    'list+': addNewList,
    created_by: user.id,
    meta: {},
  };
  try {
    const res = await pb.collection('leads').getList(1, 50, {
      filter: `phone_number = "${phone_number.replace(/^\+/, '')}" && account = "${accountId}"`,
    });
    if (res.totalItems > 0) {
      return { message: 'Phone number already exists', status: 400 };
    } else {
      const record = await pb.collection<ILead>('leads').create(data);
      try {
        await addLeadToSmartList(accountId, record);
      } catch (error) {
        console.log('cannot find filters');
      }
    }
  } catch (error) {
    console.log(error);
    return { message: 'Error creating lead', status: 404 };
  }

  revalidatePath(`/${accountId}/lead-management`);

  return { message: 'Lead created successfully!', status: 200 };
};

export const addLeadToSmartList = async (accountId: string, data: { id: string; country: string; tags: string[]; name: string; status: string }) => {
  try {
    let listArrayToAdd: string[] = [];
    let listArrayToDelete: string[] = [];
    const pb = await getPb();
    const listFilters = await pb.collection<IExpandedList>('lists').getFullList({
      // filter: `account = "${accountId}" && created_by = "${userId}" && list_filters != null`,
      filter: `account = "${accountId}" && list_filters != null`,
      expand: 'list_filters',
    });
    listFilters.forEach((listFilter) => {
      if (
        parseAndEvaluate(listFilter.expand.list_filters.parse_string, {
          country: data.country,
          tags: data.tags,
          name: data.name,
          status: data.status,
        })
      ) {
        listArrayToAdd.push(listFilter.id);
      } else {
        listArrayToDelete.push(listFilter.id);
      }
    });
    await pb.collection('leads').update(data.id, { 'list-': listArrayToDelete, 'list+': listArrayToAdd });
  } catch (error) {
    console.log(error);
  }
};

export const addLeadToSmartListOnBulkImport = (listFilters: IExpandedList[], data: { country: string; tags: string[]; name: string; status: string }) => {
  try {
    let listArrayToAdd: string[] = [];
    let listArrayToDelete: string[] = [];

    listFilters.forEach((listFilter) => {
      if (
        parseAndEvaluate(listFilter.expand.list_filters.parse_string, {
          country: data.country,
          tags: data.tags,
          name: data.name,
          status: data.status,
        })
      ) {
        listArrayToAdd.push(listFilter.id);
      } else {
        listArrayToDelete.push(listFilter.id);
      }
    });
    return { listArrayToDelete, listArrayToAdd };
  } catch (error) {
    throw error;
    console.log(error);
  }
};

export const addToPricingList = async () => {
  try {
    const pb = await getPb();
    const country_codes = await getCountryCodes();
    for (let record of pricingList) {
      let iso_code = [];
      const countryData = country_codes?.filter((item) => item.name == record.Market);
      if (countryData?.length) {
        iso_code.push(countryData[0].iso_codes[0]);
      }
      const data = {
        country_name: record.Market,
        iso_codes: iso_code,
        marketing: record.Marketing,
        utility: record.Utility,
        authentication: record.Authentication,
        authentication_international: record.Authentication_International,
        service: record.Service,
      };
      await pb.collection('pricing').create(data);
    }
  } catch (error) {
    console.log(error);
    return error;
  }
};

export const addCountryCode = async (name: string, code: string, phone_code: string, phone_length: string) => {
  try {
    const pb = await getPb();
    const data = {
      name: name,
      iso_code: code,
      phone_code: phone_code,
      phone_length: (() => {
        try {
          const parsed = JSON.parse(phone_length);
          if (Array.isArray(parsed)) {
            return parsed.map(String);
          } else {
            return [String(parsed)];
          }
        } catch (e) {
          return [phone_length];
        }
      })(),

      // phone_length:JSON.parse(phone_length).map(String)
    };
    const result = await pb.collection('country').create(data);
    return result;
  } catch (error) {
    console.log(error);
    return error;
  }
};

export const addCountryCode2 = async (name: string, code: string, phone_code: string, phone_length: string) => {
  try {
    const pb = await getPb();
    const data = {
      name: name,
      iso_codes: [code],
      phone_code: phone_code,
      phone_length: parseInt(phone_length),
      // phone_length: (() => {
      //   try {
      //     const parsed = JSON.parse(phone_length);
      //     if (Array.isArray(parsed)) {
      //       return parsed.map(String);
      //     } else {
      //       return [String(parsed)];
      //     }
      //   } catch (e) {
      //     return [phone_length];
      //   }
      // })()

      // phone_length:JSON.parse(phone_length).map(String)
    };
    const result = await pb.collection('country').create(data);
    return result;
  } catch (error) {
    console.log(error);
    return error;
  }
};

export const upsertNumberUpload = async (
  existingRecord: ILead | null,
  list: string | null,
  account: string,
  phoneNumber: string,
  user: User,
  // batch: BatchService,
  name: string,
  log_id: string,
  tagsArray: string[],
  shopify_id: string | null,
  status: string,
  country: string,
  listFilters: IExpandedList[] | null,
  meta: Record<string, string> = {}
) => {
  try {
    const pb = await getPb();
    //get lead by phone number and account id
    // const resultList = await pb.collection<ILead>('leads').getList(1, 5, {
    //   filter: `phone_number = "${phoneNumber}" && account = "${account.id}"`,
    // });

    const data = {
      phone_number: phoneNumber,
      account: account,
      name,
      created_by: user.id,
      status: status,
      tags: tagsArray,
      shopify_id,
      country,
      log_id,
      active: true,
      meta,
    };

    let record;
    if (!existingRecord) {
      //if no lead found
      try {
        //get listArrayId to add
        const res = listFilters ? addLeadToSmartListOnBulkImport(listFilters, data) : { listArrayToAdd: [], listArrayToDelete: [] };
        //spread and create lead
        record = pb.collection('leads').create({ ...data, list: list ? [list, ...res.listArrayToAdd] : [...res.listArrayToAdd] });
      } catch (error) {
        console.log(error);
      }
    } else {
      // lead record found
      try {
        //add lead to smart list and remove from exisitn lists based on smart filters
        const res = listFilters ? addLeadToSmartListOnBulkImport(listFilters, existingRecord) : { listArrayToAdd: [], listArrayToDelete: [] };
        record = pb.collection('leads').update(existingRecord.id, {
          ...data,
          'list+': list ? [list, ...res.listArrayToAdd] : [...res.listArrayToAdd],
          'list-': res.listArrayToDelete,
        });
      } catch (e) {
        console.log(e);
      }
    }
    return record;
  } catch (error: any) {
    console.error('Error in upsertNumberUpload:', error);
    throw new Error(`Failed to upsert number: ${error.message}`);
  }
};

export const updateLogData = async (logId: string, number_of_leads: number, successfullUploads: any) => {
  const pb = await getPb();
  await pb.collection('logs').update(logId, {
    number_of_leads: number_of_leads,
    log_data: successfullUploads,
  });
};

export const upsertLead = async (phoneNumber: string, account_id: string, contacts: [Contact] | undefined = undefined, referral: IReferral | null = null) => {
  const pb = await getPb();

  try {
    const resultList = await pb.collection('leads').getFirstListItem<ILead>(`phone_number = "${phoneNumber}" && account = "${account_id}"`);
    return resultList;
  } catch (error) {
    let phoneResult = phone(`+${phoneNumber}`);
    let { countryIso2, countryIso3 } = phoneResult;

    if (phoneNumber.startsWith('1') && phoneNumber.length >= 11) {
      const areaCode = phoneNumber.slice(1, 4);
      if (canadianAreaCodes.has(areaCode)) {
        countryIso2 = 'CA';
        countryIso3 = 'CAN';
      } else if (!countryIso2) {
        countryIso2 = 'US';
        countryIso3 = 'USA';
      }
    }

    const data = {
      phone_number: `${phoneNumber}`,
      account: account_id,
      name: contacts?.[0]?.profile?.name ?? '',
      status: 'New',
      active: true,
      tags: referral?.source_url ? (referral.source_type == 'ad' ? ['Meta Ad'] : ['Meta Ad', 'Web']) : [],
      referral: referral,
      country: countryIso2 ?? countryIso3,
    };

    const record = await pb.collection<ILead>('leads').create(data);
    await addLeadToSmartList(account_id, record);
    return record;
  }
};

export const getAccountFromUser = async (userId: string) => {
  const pb = await getPb();
  const resultList = await pb.collection<Account>('accounts').getList(1, 50, {
    filter: `pb_user_id.id ?= '${userId}'`,
  });
  return resultList.items[0];
};

export const markMessageAsSentAndAddWAMID = async (message: IMessage, messageReponse: ISendMessageResponse) => {
  try {
    const pb = await getPb();
    const [res, res2] = await Promise.all([
      pb.collection('messages').update(message.id, { delivery_status: 'sent from wetarseel', wamid: messageReponse.messages[0].id }, { expand: 'created_by' }),
      pb.collection('message_logs').create({
        message_id: message.id,
        wamid: messageReponse.messages[0].id,
        message_body: message,
      }),
    ]);
    return res;
  } catch (error) {
    throw error;
  }
};

export const markMessageAsFailed = async (message: IMessage, error_message: string) => {
  try {
    const pb = await getPb();
    const res = await pb.collection('messages').update(message.id, { delivery_status: 'failed', error: error_message }, { expand: 'created_by' });
    return res;
  } catch (error) {
    throw error;
  }
};
export const markMessageAsFailedByWhatsappRoute = async (
  wamid: string,
  error:
    | {
        code: number;
        title: string;
        message: string;
        error_data: { details: string };
      }
    | undefined
) => {
  try {
    const pb = await getPb();
    const res = await pb.collection<IMessage>('messages').getFirstListItem(`wamid="${wamid}"`);

    // await addToFailedList(res.user)
    // revalidatePath(`/${res.account}/list`)
    const res2 = await pb.collection('messages').update(res.id, {
      delivery_status: 'failed',
      error: error?.message ?? 'Error',
      error_code: error?.code ?? 500,
      error_data: error?.error_data.details ?? '',
    });
    //Remove campaign from lead if campaign failed to send
    const leadId = res.user;
    await pb.collection<ILead>('leads').update(leadId, {
      'campaign_history-': res.campaign,
    });
    return res;
  } catch (error) {
    throw error;
  }
};

export const increaseMessageLimitQuota = async (messageRecord: IMessage) => {
  try {
    const pb = await getPb();
    const userId = messageRecord.created_by;
    const accountId = messageRecord.account;
    const userLimit = await getMessageLimitsByUser(userId);
    if (userLimit) {
      const res = await pb.collection<IMessagingLimit>('messaging_limit').update(userLimit.id, { 'remaining_limit+': 1 });
      const res2 = pb.collection<Account>('accounts').update(accountId, { 'remaining_limit+': 1 });
      return res2;
    } else {
      throw new Error('User Limit not found');
    }
  } catch (error) {
    console.log(error);
    log.error('Error in increasing message limit quota', {
      error: JSON.stringify(error),
    });
  }
};

export const resendMessage = async (formData: FormData) => {
  const id = formData.get('id') as string;
  const campaignId = formData.get('campaignId') as string;
  const accountId = formData.get('accountId') as string;
  const user = await getUser();
  const pb = await getPb();
  await Promise.all([
    pb.collection('messages').update(id, { delivery_status: 'pending' }),
    updateAccountRemainingLimit(accountId, 1),
    updateUserRemainingLimit(user.id, 1, accountId),
  ]);
  revalidatePath(`${campaignId}`);
  return true;
};

export const batchRetryFailedMessages = async (messages: IExpandedMessageAndRepliedList[], accountId: string, userId: string) => {
  const pb = await getPb();
  const totalContacts = messages.length;
  if (totalContacts > 0) {
    try {
      await Promise.all([updateAccountRemainingLimit(accountId, totalContacts), updateUserRemainingLimit(userId, totalContacts, accountId)]);
    } catch (error) {
      console.log('Error in updating messaging limit');
      throw new Error('Error in updating messaging limit');
    }

    try {
      for (const message of messages) {
        await pb.collection('messages').update(message.id, {
          delivery_status: 'pending',
          'retried_attempts+': 1,
        });
      }
    } catch (error) {
      console.log('Error in retrying messages');
      throw new Error('Error in retrying messages');
    }
  }
};

export const resAddWAMI = async (message: IMessage, messageReponse: ISendMessageResponse) => {
  try {
    const pb = await getPb();
    const res = await pb.collection('messages').update(message.id, { wamid: messageReponse.messages[0].id }, { expand: 'created_by' });
    return res;
  } catch (error) {
    throw error;
  }
};

export const markMessageSentByWhatsapp = async (wamid: string, status: string) => {
  try {
    const pb = await getPb();
    const res = await pb.collection<IMessage>('messages').getFirstListItem(`wamid="${wamid}"`);
    if (res?.campaign) {
      await pb.collection('campaigns').update(res.campaign, { 'sent_count+': 1 });
    }

    return pb.collection('messages').update(res.id, { delivery_status: status }, { expand: 'created_by' });
  } catch (error) {
    throw error;
  }
};

export const markMessageAsDelivered = async (wamid: string, delivered: string) => {
  try {
    const pb = await getPb();
    let res;
    try {
      res = await pb.collection<IMessage>('messages').getFirstListItem(`wamid="${wamid}"`);
    } catch (error) {
      console.error('cannot find wamid', wamid);
    }
    // await addToDeliveredList(res.user)
    // revalidatePath(`/${res.account}/list`)
    if (res?.campaign) {
      await pb.collection('campaigns').update(res.campaign, { 'delivered_count+': 1 });
    }

    if (res?.id) {
      return pb.collection('messages').update(res.id, { delivery_status: delivered }, { expand: 'created_by' });
    }
  } catch (error) {
    throw error;
  }
};

export const markMessageAsRead = async (wamid: string, read: string) => {
  try {
    const pb = await getPb();
    const res = await pb.collection('messages').getFirstListItem(`wamid="${wamid}"`);
    if (res?.campaign) {
      await pb.collection('campaigns').update(res.campaign, { 'read_count+': 1 });
    }
    return pb.collection('messages').update(res.id, { delivery_status: read });
  } catch (error) {
    throw error;
  }
};

export const markTemplateAsApprovedByTemplateName = async (template_name: string) => {
  try {
    const pb = await getPb();
    const id = (await pb.collection<ITemplateDatabase>('templates').getFirstListItem(`template_name="${template_name}"`)).id;
    return pb.collection('templates').update(id, { status: 'APPROVED' });
  } catch (error) {
    throw error;
  }
};

export const markTemplateAsApproved = async (template_id: string) => {
  try {
    const pb = await getPb();
    const id = (await pb.collection<ITemplateDatabase>('templates').getFirstListItem(`template_id="${template_id}"`)).id;
    return pb.collection('templates').update(id, { status: 'APPROVED' });
  } catch (error) {
    throw error;
  }
};

export const markTemplateAsRejected = async (template_id: string, reason: string) => {
  try {
    const pb = await getPb();
    const id = (await pb.collection<ITemplateDatabase>('templates').getFirstListItem(`template_id="${template_id}"`)).id;
    return pb.collection('templates').update(id, { status: 'REJECTED', reason });
  } catch (error) {
    throw error;
  }
};

export const markTemplateAsPaused = async (template_id: string, reason: string, description: string) => {
  try {
    const pb = await getPb();
    const id = (await pb.collection<ITemplateDatabase>('templates').getFirstListItem(`template_id="${template_id}"`)).id;
    //TODO: remove id
    console.log(id);
    return pb.collection('templates').update(id, { status: 'PAUSED', reason, description });
  } catch (error) {
    throw error;
  }
};

export const updateTemplateQuality = async (template_id: string, previous_quality_score: string, new_quality_score: string) => {
  try {
    const pb = await getPb();
    const id = (await pb.collection<ITemplateDatabase>('templates').getFirstListItem(`template_id="${template_id}"`)).id;
    return pb.collection('templates').update(id, { previous_quality_score, new_quality_score });
  } catch (error) {
    throw error;
  }
};

export const updateBusinessLimitStatus = async (display_phone_number: string, event: string, current_limit: string, old_limit: string) => {
  try {
    const pb = await getPb();
    const id = (await pb.collection<Account>('account').getFirstListItem(`display_phone_number="${display_phone_number}"`)).id;
    return pb.collection('account').update(id, { display_phone_number, event, current_limit, old_limit });
  } catch (error) {
    throw error;
  }
};
//Deleting by name on facebook
//Deleting a template by name deletes all templates that match that name (meaning templates with the same name but different languages will also be deleted).
export const deleteTemplateOnFacebook = async (template_name: string, account_id: string) => {
  try {
    const pb = await getPb();
    const account = await getAccount(account_id);
    const id = (await pb.collection<ITemplateDatabase>('templates').getFirstListItem(`template_name="${template_name}"`)).id;
    const raw = await fetch(`https://graph.facebook.com/v21.0/${account.waba_id}/message_templates?name=${template_name}`, {
      method: 'DELETE',
      headers: {
        'Content-Type': 'application/json',
        Authorization: `Bearer ${account.access_token}`,
      },
    });
    revalidatePath(`/${account_id}/templates`);
    return pb.collection('templates').update(id, { status: 'PENDING DELETION' });
  } catch (error) {
    console.log(error);
    log.error('Error in deleting template on facebook', { error: JSON.stringify(error) });
    throw error;
  }
};

//delete in our database
export const deleteTemplate = async (id: string) => {
  try {
    const pb = await getPb();
    return pb.collection('templates').update(id, {
      status: 'DELETED',
    });
  } catch (error) {
    console.log(error);
    log.error('Error in marking template as deleted in database', { error: JSON.stringify(error) });
    throw error;
  }
};
export const createList = async (listName: string, accountId: string) => {
  const pb = await getPb();
  const user = await getUser();
  const list = await pb.collection('lists').create({ name: listName, account: accountId, created_by: user.id });
  return list;
};

export const addToFailedList = async (userId: string) => {
  //list+ is the id of failed list. list- is the id of delivered list
  //add to failed list, remove from delivered list
  try {
    const pb = await getPb();
    await pb.collection('leads').update(userId, {
      'list+': '0rpzegffdxjqiyt',
      'list-': 'mzy17506lpb5e94',
    });
  } catch (error) {
    console.log(error);
  }
};

export const addToDeliveredList = async (userId: string) => {
  //list+ is the id of delivered list. list- is the id of failed list
  //add to delivered list, remove from failed list
  try {
    const pb = await getPb();
    await pb.collection('leads').update(userId, {
      'list+': 'mzy17506lpb5e94',
      'list-': '0rpzegffdxjqiyt',
    });
  } catch (error) {
    console.log(error);
  }
};

export const addToRepliedList = async (userId: string) => {
  //list+ is the id of responded list. list- is the id of failed list and delivered list.
  //add to responded list, remove from failed list and delivered list
  try {
    const pb = await getPb();
    await pb.collection('leads').update(userId, {
      'list+': 'dvoaqfxphoxazzl',
      'list-': ['0rpzegffdxjqiyt', 'mzy17506lpb5e94'],
    });
  } catch (error) {
    console.log(error);
  }
};

export const createLog = async (account: Account, logName: string, number_of_leads: number, log_data: ILead[]) => {
  const pb = await getPb();
  const user = await getUser();
  const log = await pb.collection('logs').create({
    log_name: logName,
    account: account.id,
    number_of_leads: number_of_leads,
    log_data: log_data,
    created_by: user.id,
  });
  return log;
};

export const updateLeadWithCampaign = async (id: string, campaign_id: string) => {
  const pb = await getPb();
  const res = await pb.collection('leads').update(id, { 'campaign_history+': campaign_id });
};

export const updateLeads = async ({ batch, listId, leadId }: { batch: BatchService; listId: string; leadId: string }) => {
  batch.collection('leads').update(leadId, {
    'list+': listId,
  });
};

export const removeLeadsFromList = async (listId: string, id: string) => {
  const pb = await getPb();
  await pb.collection('leads').update(id, { 'list-': listId });
};

export const addLeadToList = async (listId: string, id: string) => {
  const pb = await getPb();
  await pb.collection('leads').update(id, { 'list+': listId });
};

export const getLogsByAccountId = async (accountId: string, user: User) => {
  try {
    const pb = await getPb();
    const filterCondition = `account = "${accountId}"${user.type != 'admin' ? ` && created_by = "${user.id}"` : ''}`;
    const resultList = await pb.collection<ILog>('logs').getList(1, 100, {
      filter: filterCondition,
      sort: '-created',
    });
    return resultList.items;
  } catch (error) {
    console.log(error);
    throw error;
  }
};

export const getLogsById = async (logId: string) => {
  try {
    const pb = await getPb();
    const resultList = await pb.collection<IExpandedLogs>('logs').getOne(logId, {
      expand: 'created_by',
    });
    return resultList;
  } catch (error) {
    console.log(error);
    throw error;
  }
};
export const updateListName = async (list_id: string, listName: string) => {
  const pb = await getPb();
  const user = await getUser();
  const updatedName = await pb.collection('lists').update(list_id, { name: listName, updated_by: user.id });
  return updatedName;
};

export const deleteList = async (list_id: string, account_id: string) => {
  const pb = await getPb();
  const listDeleted = await pb.collection('lists').delete(list_id);
  revalidatePath(`/${account_id}/list`);
  return listDeleted;
};

export const activeToTrue = async () => {
  const pb = await getPb();
  const allLeads = await pb.collection('leads').getFullList();
  for (let lead of allLeads) {
    await pb.collection('leads').update(lead.id, { active: true });
  }
};

export const handleSetFavorite = async (userId: string, accountId: string) => {
  const pb = await getPb();
  const result = await pb.collection<User>('users').update(userId, { favorite: [accountId] });
  // revalidatePath(`business/select`);
  return result.favorite;
};

export const updateAnyCollectionByIds = async (
  ids: string[],
  collectionName: string,
  fields: {
    [key: string]: string;
  }
) => {
  try {
    const pb = await getPb();
    const result = await Promise.all(ids.map(async (item) => await pb.collection(collectionName).update(item, fields)));
    return result;
  } catch (error) {
    return error;
  }
};

export const getAnyCollectionIdsByCollectionName = async (collectionName: string, filter: string, fields: string, expand: string = '') => {
  try {
    const pb = await getPb();
    const result = await pb.collection<IMessage>(collectionName).getFullList({ filter: filter, fields, expand });
    return result.map((result) => result.id);
  } catch (error) {
    return JSON.stringify(error);
  }
};

export const updateShopifySessionId = async ({ store_details, apiKey, prevApiKey }: { store_details: any; apiKey: string; prevApiKey: string }) => {
  const pb = await getPb();
  //get apikey from api_settings to confirm if it is valid or not
  const result = await pb.collection('api_settings').getFirstListItem(`key="${prevApiKey}"`);
  console.log(result.account);
  let shopify_account_record_id = null;
  try {
    const shopify_account = await pb.collection('shopify_accounts').getFirstListItem(`key="${prevApiKey}"`);
    shopify_account_record_id = shopify_account.id;
    await pb.collection('shopify_accounts').update(shopify_account.id, { store_details, account: result.account });
  } catch (error) {
    console.log('shopify account not found', error);
    //if not found then create a new record
    const shopify_account = await pb.collection('shopify_accounts').create({ key: prevApiKey, store_details, account: result.account });
    shopify_account_record_id = shopify_account.id;
  }
  let _store_details = store_details;
  //if apkikey is empty then clear the record
  if (apiKey.length == 0) {
    _store_details = null;
  }
  await pb.collection('shopify_accounts').update(shopify_account_record_id, { key: prevApiKey, store_details: _store_details });
  return result;
};

export const exportToFile = async () => {
  try {
    const pb = await getPb();
    const records = await pb.collection<Account>('accounts').getFullList({
      filter: 'pb_user_id.type ?~ "admin" && waba_id != null',
      expand: 'pb_user_id',
      perPage: 1000, // Adjust as needed
    });

    // Prepare data with comma delimiters
    const lines = records.map((record) =>
      [
        record?.expand?.pb_user_id?.[0].email || 'NA',
        record?.expand?.pb_user_id?.[0].phoneNumber || 'NA',
        record?.expand?.pb_user_id?.[0].type || 'NA',
        record.name || 'NA',
        record.phone_id || 'NA',
        record.display_phone_number || 'NA',
      ].join(',')
    );

    // Add header
    const header = 'email,phone_number,type,Business Name,phone_id,display_phone_number,';
    const fileContent = [header, ...lines].join('\n');

    // Write to a file
    fs.writeFileSync('accounts_export.txt', fileContent);
    console.log('Export completed: accounts_export.csv');
  } catch (error) {
    console.error('Error exporting data:', error);
  }
};

export const getRoles = async () => {
  const pb = await getPb();
  const roles = await pb.collection('roles').getFullList();
  return roles;
};

export const getAgents = async (account_id: string, team_id?: string) => {
  try {
    const pb = await getPb();
    const account = await pb.collection<IExpandedAccount>('accounts').getOne(account_id, {
      expand: 'pb_user_id',
    });
    let agentsByAccountId = account.expand?.pb_user_id.filter((item) => {
      return ['admin', 'agent'].includes(item.type);
    });
    if (team_id) {
      agentsByAccountId = agentsByAccountId.filter((item) => item.team == team_id);
    }
    // remove super admin from agents list
    agentsByAccountId = agentsByAccountId.filter((item) => item.username !== 'Super-User');
    return agentsByAccountId;
  } catch (error) {
    console.log(error);
    throw error;
  }
};

export const agentsWithConvos = async (account_id: string, team_id?: string) => {
  const agents = await getAgents(account_id, team_id);

  let agentWithConvos: AgentWithConvos[] = [];
  for (let agent of agents) {
    agentWithConvos.push({
      ...agent,
      conversations: 0,
    });
  }

  return agentWithConvos;
};

export const getTeams = async (account_id: string) => {
  const pb = await getPb();
  const res = await pb.collection<Team>('teams').getFullList({ filter: `account = "${account_id}"` });
  return res;
};

export const getAssignedAgent = async (convoId: string) => {
  try {
    const pb = await getPb();
    const res = await pb.collection<IExpandedConversation>('conversations').getOne(convoId, {
      expand: 'assigned_agent',
    });
  } catch (error) {
    console.log(error);
    throw error;
  }
};

export const getUserById = async (userId: string) => {
  try {
    const pb = await getPb();
    const user = await pb.collection<User>('users').getOne(userId);
    return user;
  } catch (error) {
    console.log(error);
    throw new Error(`User not found for ${userId}`);
  }
};

export const updateAgent = async (
  userId: string,
  account_id: string,
  formData: any,
  numberCode: any,
  rolesArray: any,
  dashboard_options: any,
  viewAll_array: any
) => {
  const pb = await getPb();
  await pb.collection('users').update(userId, {
    roles: rolesArray,
    dashboard_options: dashboard_options,
    view_all: viewAll_array,
  });
  revalidatePath(`/${account_id}/agent`);
  redirect(`/${account_id}/agent`);
};

export const requestPasswordReset = async (email: string) => {
  try {
    const pb = await getPb();
    const user = await pb.collection('users').getFirstListItem(`email="${email}"`);
    const response = await pb.collection('users').requestPasswordReset(email);
    return { success: true };
  } catch (error) {
    console.error('Error requesting password reset:', error);
    return { success: false };
  }
};

export const changePassword = async (token: string, password: string, confirmPassword: string, userId: string) => {
  const pb = await getPb();

  try {
    const respone = await pb.collection('users').confirmPasswordReset(token, password, confirmPassword);
    return { success: true };
  } catch (error) {
    console.log(error);
    return { success: false };
  }
};

export const verifyUser = async (token: string) => {
  try {
    const pb = await getPb();
    await pb.collection('users').confirmVerification(token);
    return { success: true };
  } catch (error) {
    console.log(error);
    return { success: false };
  }
};

export const getVerificationMail = async (email: any) => {
  const pb = await getPb();
  try {
    const response = await pb.collection('users').requestVerification(email);
    return { success: true };
  } catch (error) {
    console.log(error);
    return { success: false };
  }
};

export const insertInteractiveMessage = async (interactive: any) => {
  try {
    const pb = await getPb();
    const res = await pb.collection('interactive_messages').create({ message: interactive });
    return res;
  } catch (e) {
    console.log(e);
  }
};

export const deleteLog = async (account_id: string, logId: string) => {
  let record = null;
  try {
    const pb = await getPb();
    record = await pb.collection('logs').delete(logId);
    revalidatePath(`${account_id}/lead-management`);
  } catch (error) {
    console.log(error);
    return { success: false };
  }
  if (record) {
    redirect(`/${account_id}/lead-management`);
  }
};

export const archiveLead = async (value: boolean, convoId: string, account_id: string) => {
  try {
    const pb = await getPb();
    await pb.collection('conversations').update(convoId, { chat_archive: value });
    revalidatePath(`${account_id}/live-chat`);
    return { success: true };
  } catch (error) {
    console.log(error);
  }
};

export const blockUnBlockLeadWeTarseel = async (lead_id: string, archive: boolean, value: boolean, convoId: string, account_id: string) => {
  try {
    const pb = await getPb();
    await Promise.all([
      pb.collection('conversations').update(convoId, { chat_block: value, chat_archive: archive }),
      pb.collection('leads').update(lead_id, { blocked: value }),
    ]);
    revalidatePath(`${account_id}/live-chat`);
    return { success: true };
  } catch (error) {
    console.log(error);
  }
};

export const getCountryCodes = async () => {
  try {
    const pb = await getPb();
    const result = await pb.collection<ICountry>('country').getFullList();
    return result;
  } catch (error) {
    console.log('error');
  }
};

export const getPricingData = async () => {
  try {
    const pb = await getPb();
    const result = await pb.collection('pricing').getFullList();
    return result;
  } catch (error) {
    console.log('error');
  }
};

export const updatePricing = async (data: any, account_id: string) => {
  try {
    const pb = await getPb();
    const pricing_list = await getPricingData();
    for (let item of data) {
      const country_data = pricing_list?.filter((pricing_item) => pricing_item.country_name == item.Market || pricing_item.meta_group_name == item.Market);
      if (country_data) {
        for (let country of country_data) {
          const response = await pb.collection('pricing').update(country.id, {
            marketing: isNaN(parseFloat(item.Marketing)) ? 0 : parseFloat(item.Marketing),
            utility: isNaN(parseFloat(item.Utility)) ? 0 : parseFloat(item.Utility),
            authentication: isNaN(parseFloat(item.Authentication)) ? 0 : parseFloat(item.Authentication),
            authentication_international: isNaN(parseFloat(item['Authentication-International'])) ? 0 : parseFloat(item['Authentication-International']),
            service: isNaN(parseFloat(item.Service)) ? 0 : parseFloat(item.Service),
          });
        }
      }
    }
  } catch (error) {
    console.log(error);
    throw error;
  }
  redirect(`/${account_id}/business-settings`);
};

export const generatePastInvoice = async (
  unit_amount: number,
  tax: number,
  charges: number,
  businessId: string,
  billingMonths: number,
  start_date: any,
  accountId: string,
  renewal: string,
  isFirstInvoice: boolean
) => {
  try {
    const pb = await getPb();
    const wt_id = await getLatest_WT_ID();
    const result = await pb.collection('invoice').create({
      amount_due: (unit_amount * billingMonths + charges + tax).toFixed(2),
      unit_amount: unit_amount.toFixed(2),
      tax: tax.toFixed(2),
      charges: charges.toFixed(2),
      payment_status: 'due',
      billing_months: billingMonths,
      invoice_limit: 15,
      account_id: businessId,
      invoice_valid_from: start_date,
      wt_id: wt_id + 1,
      completed: renewal == 'yes' ? false : true,
      is_first_invoice: isFirstInvoice,
    });
  } catch (error) {
    console.log(error);
    throw error;
  }

  revalidatePath(`/${accountId}/business-settings/${businessId}/subscription`);
  redirect(`/${accountId}/business-settings/${businessId}/subscription`);
};

export const generateInvoice = async (
  lastInvoiceId: string,
  fees: number,
  tax: number,
  charges: number,
  businessId: string,
  billingMonths: number,
  dueDateLimit: number,
  lastInvoiceRenewalDate: Date,
  from: string,
  isfirstTimeInvoice: boolean,
  accountId?: string
) => {
  try {
    const pb = await getPb();
    const wt_id = await getLatest_WT_ID();
    const result = await pb.collection('invoice').create({
      amount_due: (fees * billingMonths + charges + tax).toFixed(2),
      unit_amount: fees.toFixed(2),
      tax: tax.toFixed(2),
      charges: charges.toFixed(2),
      payment_status: 'due',
      billing_months: billingMonths,
      invoice_limit: dueDateLimit,
      account_id: businessId,
      invoice_valid_from: lastInvoiceRenewalDate ? dayjs(lastInvoiceRenewalDate).add(1, 'day') : dayjs(),
      is_first_invoice: isfirstTimeInvoice,
      wt_id: wt_id + 1,
      //if it is first time invoice then renewal date will be null, as it will be updated after user pays the subscription amount but
      //if it is not the first invoice then renewal date will be current date plus billing duration
      renewal_date: isfirstTimeInvoice ? null : dayjs(lastInvoiceRenewalDate).add(billingMonths, 'month'),
      // renewal_date: isfirstTimeInvoice ? null : dayjs().add(billingMonths, 'month'),
    });

    if (lastInvoiceId) {
      await invoiceCompleted(lastInvoiceId);
    }

    if (accountId) {
      await pb.collection('accounts').update(accountId, { subscription_active: true });
    }
  } catch (err) {
    throw err;
  }
  if (accountId) {
    revalidatePath(`/${accountId}/business-settings/${businessId}/subscription`);
    redirect(`/${accountId}/business-settings/${businessId}/subscription`);
  }
};

export const updateInvoice = async (
  accountId: string,
  businessId: string,
  invoice_id: string,
  unit_amount: number,
  tax: number,
  charges: number,
  billingMonths: number,
  dueDateLimit: number
) => {
  try {
    const pb = await getPb();
    const result = await pb.collection('invoice').update(invoice_id, {
      amount_due: unit_amount * billingMonths + tax + charges,
      tax: tax,
      unit_amount: unit_amount,
      other_charges: charges,
      billing_months: billingMonths,
      invoice_limit: dueDateLimit,
      auto_renewal: false,
      invoice_pdf: null,
    });
  } catch (err) {
    throw err;
  }
  revalidatePath(`/${accountId}/business-settings/${businessId}/subscription`);
  redirect(`/${accountId}/business-settings/${businessId}/subscription`);
};

export const changeInvoiceStatus = async (invoiceId: string, status: string) => {
  try {
    const pb = await getPb();
    const result = await pb.collection('invoice').update(invoiceId, { payment_status: status });
  } catch (err) {
    console.log(err);
  }
};

export const enableAutoRenewal = async (invoiceId: string) => {
  try {
    const pb = await getPb();
    const result = await pb.collection('invoice').update(invoiceId, { auto_renewal: true });
  } catch (err) {
    console.log(err);
  }
};

export const disableAutoRenewal = async (invoiceId: string) => {
  try {
    const pb = await getPb();
    const result = await pb.collection('invoice').update(invoiceId, { auto_renewal: false });
  } catch (err) {
    console.log(err);
  }
};

export const invoiceCompleted = async (invoiceId: string) => {
  try {
    const pb = await getPb();
    const result = await pb.collection('invoice').update(invoiceId, { completed: true });
  } catch (err) {
    console.log(err);
    throw err;
  }
};

export const getLatest_WT_ID = async () => {
  try {
    const pb = await getPb();
    const res = await pb.collection<IInvoice>('invoice').getFullList({ sort: '-created' });
    let wt_id = 0;
    if (res.length > 0) {
      wt_id = res[0].wt_id;
    }
    return wt_id;
  } catch (error) {
    console.log(error);
    throw error;
  }
};

export const cancelSubscription = async (businessId: string, accountId: string) => {
  try {
    const pb = await getPb();
    await pb.collection('accounts').update(businessId, { subscription_active: false });
  } catch (err) {
    console.log(err);
    throw err;
  }
  revalidatePath(`/${accountId}/business-settings/${businessId}`);
  redirect(`/${accountId}/business-settings/${businessId}`);
};

export const getInvoiceById = async (invoiceId: string) => {
  try {
    const pb = await getPb();
    const invoice = pb.collection<IInvoice>('invoice').getOne(invoiceId);
    return invoice;
  } catch (err) {
    throw err;
    console.log(err);
  }
};

export const getBussinessCurrency = async (accountId: string) => {
  try {
    const pb = await getPb();
    const account = await pb.collection<IExpandedAccount>('accounts').getOne(accountId);
    if (account.currency.length == 0) {
      return null;
    }
    return account.currency;
  } catch (error) {
    return null;
  }
};

export const getAmountDue = async (businessId: string) => {
  try {
    const pb = await getPb();
    let totalAmount: number = 0;
    let pendingInvoiceArray: IInvoice[] = [];
    const invoiceList = await pb.collection<IInvoice>('invoice').getFullList({ filter: `account_id = "${businessId}"`, sort: '-created' });
    invoiceList.forEach((invoice) => {
      if (dayjs(invoice.invoice_valid_from).diff(dayjs(), 'day') <= 0) {
        if (invoice.payment_status == 'due' || invoice.payment_status == 'over-due') {
          pendingInvoiceArray.push(invoice);
          totalAmount = totalAmount + invoice.amount_due;
        }
      }
    });
    return { totalAmount, pendingInvoiceArray };
  } catch (error) {
    console.log(error);
    throw error;
  }
};

export const getLastSubscriptionDetails = async (business_id: string) => {
  try {
    const pb = await getPb();
    const details = await pb.collection('invoice').getFullList<IInvoice>({ filter: `account_id="${business_id}" && payment_status="paid"`, sort: '-created' });
    return details[0];
  } catch (error) {
    console.log(error);
    throw error;
  }
};

export const getAllSubscriptionDetails = async (business_id: string) => {
  try {
    const pb = await getPb();
    const details = await pb
      .collection<IInvoice>('invoice')
      .getFullList({ filter: `account_id="${business_id}"`, sort: '-invoice_valid_from', expand: 'account_id' });
    return details;
  } catch (error) {
    console.log(error);
    throw error;
  }
};

export const getUncreatedInvoiceDocuments = async (business_id: string) => {
  try {
    const pb = await getPb();
    const invoices = await pb
      .collection<IInvoice>('invoice')
      .getFullList({ filter: `account_id="${business_id}" && invoice_pdf=null`, sort: '-created', expand: 'account_id' });
    return invoices;
  } catch (error) {
    console.log(error);
    throw error;
  }
};

export const updateInvoiceDocumentUrl = async (invoiceId: string, fileId: string) => {
  try {
    const pb = await getPb();
    await pb.collection('invoice').update(invoiceId, { invoice_pdf: fileId });
  } catch (err) {
    console.log(err);
    throw err;
  }
};

export const updateShopifyInvoiceDocumentUrl = async (invoiceId: string, fileId: string) => {
  try {
    const pb = await getPb();
    await pb.collection('shopify_orders').update(invoiceId, { invoice_pdf: fileId });
  } catch (err) {
    console.log(err);
    throw err;
  }
};

/**
 * Creates or updates payment records and transaction logs for invoices
 *
 * This function handles the entire payment processing workflow:
 * 1. Updates invoice status and renewal dates
 * 2. Creates or updates payment records
 * 3. Creates transaction logs for audit purposes
 * 4. Handles page revalidation and redirection after payment
 *
 * @param paymentMethod - The method used for payment (e.g., 'on-system', 'off-system', 'automatic-stripe', 'saved-method')
 * @param businessId - The ID of the business/account making the payment
 * @param invoiceArray - Array of invoices being paid
 * @param status - Payment status ('success' or 'failed')
 * @param paymentError - Error message if payment failed (optional)
 * @param paymentDate - Date when payment was made (optional, defaults to current date)
 * @param accountId - Account ID for redirection after payment (optional)
 * @param payRef - Payment reference/ID from payment processor (optional)
 */
export const createPayment = async (
  paymentMethod: string,
  businessId: string,
  invoiceArray: IInvoice[],
  status: string,
  paymentError?: string,
  paymentDate?: Date,
  accountId?: string,
  payRef?: string
) => {
  try {
    const pb = await getPb();
    const currentDate = dayjs(); // Current date for transaction records

    // Handle successful payments
    if (status == 'success') {
      // Process each invoice in the array
      for (let invoice of invoiceArray) {
        // Handle first-time invoices differently
        if (invoice.is_first_invoice) {
          if (paymentMethod == 'off-system') {
            // For manual/off-system payments, use the provided payment date
            await pb.collection('invoice').update(invoice.id, {
              // Set renewal date based on provided payment date
              renewal_date: dayjs(paymentDate).add(invoice.billing_months, 'month'),
              payment_status: 'paid',
              invoice_pdf: null,
            });
          } else {
            // For automatic/on-system payments, use current date
            await pb.collection('invoice').update(invoice.id, {
              // Set renewal date based on current date
              renewal_date: dayjs().add(invoice.billing_months, 'month'),
              payment_status: 'paid',
              invoice_pdf: null,
            });
          }
        } else {
          // For recurring invoices (not first-time)
          await pb.collection('invoice').update(invoice.id, {
            // Set renewal date based on invoice valid from date
            renewal_date: dayjs(invoice.invoice_valid_from).add(invoice.billing_months, 'month'),
            payment_status: 'paid',
            invoice_pdf: null,
          });
        }

        // Check if a payment record already exists for this invoice
        const paymentExists = await pb.collection('payment').getFullList({ filter: `invoice_id="${invoice.id}"` });

        if (paymentExists.length > 0) {
          // Update existing payment record
          const payment = await pb.collection('payment').update(paymentExists[0].id, {
            payment_status: 'paid',
            payment_ref: payRef,
          });

          // Create transaction log for audit purposes
          await pb.collection('transaction_log').create({
            account_id: businessId,
            payment_amount: invoice.amount_due,
            payment_id: payment.id,
            transaction_date: paymentDate ?? currentDate, // Use provided date or current date
            error: null,
            payment_method: paymentMethod,
            payment_ref: payRef,
          });
        } else {
          // Create new payment record
          const payment = await pb.collection('payment').create({
            account_id: businessId,
            invoice_id: invoice.id,
            amount_paid: invoice.amount_due,
            payment_status: 'paid',
            unit_amount: invoice.unit_amount,
            tax: invoice.tax,
            charges: invoice.charges,
            payment_ref: payRef,
          });

          // Create transaction log for audit purposes
          await pb.collection('transaction_log').create({
            account_id: businessId,
            payment_amount: invoice.amount_due,
            payment_id: payment.id,
            transaction_date: paymentDate ?? currentDate, // Use provided date or current date
            error: null,
            payment_method: paymentMethod,
            payment_ref: payRef,
          });
        }
      }
    } else {
      // Handle failed payments
      for (let invoice of invoiceArray) {
        // Check if a payment record already exists for this invoice
        const paymentExists = await pb.collection('payment').getFullList({ invoice_id: invoice.id });

        if (paymentExists.length > 0) {
          // Update existing payment record to failed status
          const payment = await pb.collection('payment').update(paymentExists[0].id, {
            payment_status: 'failed',
          });

          // Create transaction log with error information
          await pb.collection('transaction_log').create({
            account_id: businessId,
            payment_amount: invoice.amount_due,
            payment_id: payment.id,
            transaction_date: paymentDate ?? currentDate, // Use provided date or current date
            error: paymentError, // Include error message
            payment_method: paymentMethod,
          });
        } else {
          // Create new payment record with failed status
          const payment = await pb.collection('payment').create({
            account_id: businessId,
            invoice_id: invoice.id,
            amount_paid: invoice.amount_due,
            payment_status: 'failed',
            unit_amount: invoice.unit_amount,
            tax: invoice.tax,
            charges: invoice.charges,
            payment_ref: payRef,
          });

          // Create transaction log with error information
          await pb.collection('transaction_log').create({
            account_id: businessId,
            payment_amount: invoice.amount_due,
            payment_id: payment.id,
            transaction_date: paymentDate ?? currentDate, // Use provided date or current date
            error: paymentError, // Include error message
            payment_method: paymentMethod,
            payment_ref: payRef,
          });
        }
      }
    }
  } catch (error) {
    console.log(error); // Log the error
    throw error; // Re-throw to allow caller to handle
  }

  // Handle redirections based on payment context

  // If paymentDate is explicitly provided, it means this payment was done manually by an admin
  if (paymentDate) {
    // Revalidate and redirect to the subscription page for the business
    revalidatePath(`/${accountId}/business-settings/${businessId}/subscription`);
    redirect(`/${accountId}/business-settings/${businessId}/subscription`);
  }

  // If no payment error and no explicit payment date, it was done by the business owner through payment gateway
  if (!paymentError) {
    // Revalidate and redirect to the billing page
    revalidatePath(`/${businessId}/billing`);
    // redirect(`/${businessId}/billing`);
  }
};

export const getAccountLimit = async (accountId: string) => {
  try {
    const pb = await getPb();
    const result = await pb.collection<IExpandedAccount>('accounts').getOne(accountId, {
      expand: 'package_tier',
    });
    return result;
  } catch (error) {
    throw error;
  }
};

export const getUserLimits = async (accountId: string) => {
  try {
    const pb = await getPb();
    const result = await pb.collection<IExpandedMessagingLimit>('messaging_limit').getFullList({
      filter: `account = "${accountId}" && user.username != "${USERNAME}"`,
      expand: 'user',
    });
    return result;
  } catch (error) {
    console.log('error');
    throw error;
  }
};

export const getAllMetaKeys = async (accountId: string): Promise<Meta[]> => {
  const pb = await getPb();
  const user = await getUser();
  const userFilterCondition = `${user.type != 'admin' && !user.view_all?.includes('lead-management') ? ` && created_by = "${user.id}"` : ''}`;
  const filterCondition = `tags.0 != null && account = "${accountId}"${userFilterCondition}`;
  const result = await pb.collection<ILead>('leads').getFullList({
    filter: filterCondition,
  });

  const finalMetakeys = new Set<string>();
  const finalMetaTuple = new Set<string>();
  result.forEach((lead) => {
    if (lead.meta) {
      Object.entries(lead.meta).forEach(([key, value]) => {
        if (key !== '') {
          finalMetakeys.add(key);
          finalMetaTuple.add(JSON.stringify({ key, value }));
        }
      });
    }
  });

  const finalAnswer: Meta[] = Array.from(finalMetakeys).map((key) => {
    return {
      key,
      values: Array.from(finalMetaTuple)
        .filter((tuple) => JSON.parse(tuple).key === key)
        .map((tuple) => JSON.parse(tuple).value.toString()),
    };
  });

  return finalAnswer;
};

export const getAllTags = async (accountId: string) => {
  const pb = await getPb();
  const user = await getUser();
  const userFilterCondition = `${user.type != 'admin' && !user.view_all?.includes('lead-management') ? ` && created_by = "${user.id}"` : ''}`;
  const filterCondition = `tags.0 != null && account = "${accountId}"${userFilterCondition}`;
  const result = await pb.collection<ILead>('leads').getFullList({
    filter: filterCondition,
    fields: 'tags',
  });
  const tagOptions = extractUniqueTags(result);
  return tagOptions as Option[];
};

export const updateTableLimits = async (
  accountId: string,
  limitDataForBusiness: { unutilized_limit: number },
  limitDataForMessageTable: {
    id: string;
    assignedLimit: number;
    remainingLimit: number;
  }[]
) => {
  const pb = await getPb();
  const user = await getUser();
  const res = await pb.collection<Account>('accounts').update(accountId, limitDataForBusiness);
  const result = await Promise.all(
    limitDataForMessageTable.map(
      async (data) =>
        await pb.collection<IMessagingLimit>('messaging_limit').update(data.id, {
          assigned_limit: data.assignedLimit,
          remaining_limit: data.remainingLimit,
        })
    )
  );
};

export const getMessageLimitsByUser = async (userId: string, accountId: string | undefined = undefined) => {
  try {
    const pb = await getPb();
    let filter = '';
    if (accountId) {
      filter = `user = "${userId}" && account = "${accountId}"`;
    } else {
      filter = `user = "${userId}"`;
    }
    const res = await pb.collection<IMessagingLimit>('messaging_limit').getFirstListItem(filter);
    return res;
  } catch (error) {
    return null;
  }
};

export const updateAccountRemainingLimit = async (accountId: string, totalContacts: number) => {
  const pb = await getPb();
  // const accountLimit = await getAccountLimit(accountId)
  // const remaining_limit = accountLimit.remaining_limit - totalContacts
  const res = await pb.collection<Account>('accounts').update(accountId, { 'remaining_limit-': totalContacts });
};

export const updateUserRemainingLimit = async (userId: string, totalContacts: number, accountId: string | undefined = undefined) => {
  try {
    const pb = await getPb();
    const userLimit = await getMessageLimitsByUser(userId, accountId);
    if (userLimit) {
      // const remaining_limit = userLimit.remaining_limit - totalContacts;
      const res = await pb.collection<IMessagingLimit>('messaging_limit').update(userLimit.id, { 'remaining_limit-': totalContacts });
    } else {
      throw new Error('User limit not found');
    }
  } catch (error) {
    throw error;
  }
};

export const getPackageTier = async () => {
  const pb = await getPb();
  const res = await pb.collection<IPackageTier>('packages_tier').getFullList();
  return res;
};

export const updateAccountWithPackageId = async (accountId: string, packageId: string, remaining_limit: number, period: string) => {
  const pb = await getPb();
  let limit_start_date = dayjs();
  let limit_end_date = dayjs();
  if (period == 'monthly') {
    limit_end_date = limit_start_date.add(1, 'month');
  }
  const res = await pb.collection<Account>('accounts').update(accountId, {
    package_tier: packageId,
    remaining_limit,
    unutilized_limit: 0,
    limit_start_date,
    limit_end_date,
  });
  const allAdmins = await pb.collection<IMessagingLimit>('messaging_limit').getFullList({ filter: `account = "${accountId}" && user.type = "admin"` });
  allAdmins.map(async (item) => {
    const res2 = await pb.collection<Account>('messaging_limit').update(item.id, {
      remaining_limit: item.assigned_limit, //set this to remaining limit if resetting properly
      assigned_limit: item.assigned_limit, //set this to remaining_limit if resetting properly
      limit_start_date,
      limit_end_date,
      period,
    });
  });

  const allAgents = await pb.collection<IMessagingLimit>('messaging_limit').getFullList({ filter: `account = "${accountId}" && user.type = "agent"` });
  allAgents.map(async (item) => {
    const res2 = await pb.collection<Account>('messaging_limit').update(item.id, {
      remaining_limit: item.assigned_limit, //change this to 0 if resetting properly
      assigned_limit: item.assigned_limit, //change this to 0 if resetting properly
      limit_start_date,
      limit_end_date,
      period,
    });
  });
};

export const getCampaignById = async (campaignId: string) => {
  const pb = await getPb();
  const resultList = await pb.collection<IExpandedCampaign>('campaigns').getOne(campaignId, {
    expand: 'template,leads_list,created_by',
  });
  return resultList;
};

export const setLeadAsOptOut = async (lead_id: string) => {
  try {
    const pb = await getPb();
    await pb.collection('leads').update(lead_id, { opt_out: true });
    return;
  } catch (error) {
    throw error;
  }
};

export const setOptedOutCountOfCampaign = async (campaign_id: string) => {
  try {
    const pb = await getPb();
    await pb.collection('campaigns').update(campaign_id, { 'opt_out_count+': 1 });
  } catch (error) {
    throw error;
  }
};

export const addToAccountLimitHistory = async (
  accountId: string,
  message_limit: number,
  remaining_messages: number,
  agents_admin_data: IMessagingLimitPartial[]
) => {
  try {
    const pb = await getPb();
    await pb.collection<IAccountLimitHistory>('account_limit_history').create({
      date: dayjs(),
      account: accountId,
      message_limit,
      remaining_messages,
      agents_admin_data,
    });
  } catch (error) {
    throw error;
  }
};

export const getLeadIdsByMessages = async (campaign_id: string) => {
  try {
    const pb = await getPb();
    const filter = `campaign= "${campaign_id}" && (delivery_status = 'read' || delivery_status = 'delivered')`;
    const result = await pb.collection<IMessage>('messages').getFullList({ filter });
    return result.map((result) => {
      return { userId: result.user, created_at: result.created };
    });
  } catch (error) {
    return JSON.stringify(error);
  }
};

export const getRepliedMessage = async (campaign_id: string) => {
  try {
    const pb = await getPb();
    //send campaign_id here
    const resultData = (await getLeadIdsByMessages(campaign_id)) as { userId: string; created_at: Date }[];
    let filter = resultData
      .map((item) => {
        return `user = "${item.userId}"`;
      })
      .join(` || `);

    filter = '(' + filter + `) && from = "user" && created >= "${resultData[0].created_at}" && message != "Stop promotions"`;
    const messageRecords = await pb.collection<IMessage>('messages').getFullList({ filter, fields: 'user, message' });

    messageRecords.map(async (item) => {
      try {
        const record = await pb.collection('leads_replied_campaign').create({
          campaign: campaign_id,
          lead: item.user,
          message: item.message,
        });
        await pb.collection<ICampaign>('campaigns').update(campaign_id, { 'reply_count+': 1 });
        await pb.collection<ILead>('leads').update(item.user, { 'replied+': record.id });
      } catch (error) {}
    });

    return resultData;
  } catch (error) {
    return error;
  }
};

export const createReTargetCampaign = async (
  campaignName: string,
  accountId: string,
  selectedLeads: { userId: string; campaignName: string; templateId: string }[],
  templateId: string
) => {
  const pb = await getPb();
  const user = await getUser();
  const records = await pb.collection<IList>('lists').getList(1, 50, {
    filter: `name ~ "${campaignName}-retarget-list-"`,
  });
  const listName = `${campaignName}-retarget-list-${records.totalItems + 1}`;
  const listRecord = await pb.collection<IList>('lists').create({
    name: listName,
    account: accountId,
    created_by: user.id,
  });

  selectedLeads.map(async (item) => {
    await pb.collection<ILead>('leads').update(item.userId, {
      'list+': listRecord.id,
    });
  });
  const listId = listRecord.id;
  redirect(
    `/${accountId}/campaign/create/${templateId}/summary?campaign_name=${campaignName} Retarget ${records.totalItems + 1}&list=${listId}&duplicate=true&retarget=true`
  );
};

export const getApiSettings = async (accountId: string) => {
  const pb = await getPb();
  const result = await pb.collection<IApiSettings>('api_settings').getFullList({
    filter: `account = "${accountId}"`,
  });
  return result;
};

export const getAccountByToken = async (token: string) => {
  const pb = await getPb();
  try {
    const result = await pb.collection<IExpandedAPISettings>('api_settings').getFirstListItem(`key="${token}"`, {
      expand: 'account',
    });
    return result;
  } catch (error) {
    return null;
  }
};

export const getLeadByPhone = async (phoneNumber: string, accountId: string) => {
  const pb = await getPb();
  try {
    const result = await pb.collection<ILead>('leads').getFirstListItem(`phone_number="${phoneNumber}" && account="${accountId}"`);
    return result;
  } catch (e) {
    return null;
  }
};

export const addLead = async (phoneNumber: string, accountId: string) => {
  const pb = await getPb();
  const data = {
    phone_number: phoneNumber,
    account: accountId,
    name: phoneNumber,
    status: 'New',
    active: true,
  };
  const result = await pb.collection<ILead>('leads').create(data);
  return result;
};

export const saveMetaToUser = async (leadId: string, meta: Record<string, string>) => {
  const pb = await getPb();
  // get meta
  let _meta = (await pb.collection<ILead>('leads').getOne(leadId)).meta ?? {};
  const result = await pb.collection('leads').update(leadId, { meta: { ..._meta, ...meta } });
  return result;
};

/**
 * Stores user bank session data in the user_bank_session table
 * @param userId The ID of the user
 * @param isBankUser Whether the user is a bank user
 * @param sessionExpiry When the session expires (optional)
 * @param sessionToken Session token for the user (optional)
 * @returns The created or updated record
 */
export const storeUserBankSession = async (
  userId: string,
  isBankUser: boolean,
  sessionExpiry?: Date,
  sessionToken?: string
) => {
  const pb = await getPb();
  try {
    // Check if a record already exists for this user
    let record;
    try {
      record = await pb.collection('user_bank_session').getFirstListItem(`user_id="${userId}"`);
      // Update existing record
      return await pb.collection('user_bank_session').update(record.id, {
        is_bank_user: isBankUser,
        session_expiry: sessionExpiry || null,
        session_token: sessionToken || null,
      });
    } catch (error) {
      // Record doesn't exist, create a new one
      return await pb.collection('user_bank_session').create({
        user_id: userId,
        is_bank_user: isBankUser,
        session_expiry: sessionExpiry || null,
        session_token: sessionToken || null,
      });
    }
  } catch (error) {
    console.error('Error storing user bank session:', error);
    throw error;
  }
};

export const bulkDeleteList = async (listIds: string[], accountId: string) => {
  try {
    const filter = listIds.map((id) => `leads_list.id="${id}"`).join(' || ') + ` && account.id = "${accountId}"`;
    const pb = await getPb();
    const res = await pb.collection<ICampaign>('campaigns').getList(1, 50, {
      filter,
    });
    if (res.totalItems == 0) {
      listIds.forEach(async (id) => {
        await pb.collection<IList>('lists').delete(id);
      });
      revalidatePath(`/${accountId}/list`);
      return { duplicate: false };
    }
    return { duplicate: true };
  } catch (error) {
    console.log(error);
    throw error;
  }
};

export const updateCampaign = async (id: string, date: string, account_id: string) => {
  try {
    const user = await getUser();
    const pb = await getPb();
    const res = await pb.collection('campaigns').update(id, {
      scheduled_time: date,
      updated_by: user.id,
    });
    revalidatePath(`${account_id}/campaign`);
    return res;
  } catch (error) {
    throw error;
  }
};
export const updateCampaignRetryCount = async (id: string, next_retry_date: Date) => {
  try {
    const pb = await getPb();
    const res = await pb.collection('campaigns').update(id, {
      'retry_count+': 1,
      next_retry_date: dayjs(next_retry_date).add(1, 'day').toDate().toUTCString(),
    });
    return res;
  } catch (error) {
    throw error;
  }
};

export const updateCampaignRetryFailed = async (id: string, reason: string) => {
  try {
    const pb = await getPb();
    const res = await pb.collection('campaigns').update(id, {
      retry_status: 'failed',
      retry_status_reason: reason,
    });
    return res;
  } catch (error) {
    throw error;
  }
};

export const getRecentCampaigns = async (accountId: string) => {
  // get the most recent 3 campaigns
  try {
    const user = await getUser();
    const pb = await getPb();
    const userFilterCondition = `${user.type != 'admin' && !user.view_all?.includes('campaigns') ? ` && created_by = "${user.id}"` : ''}`;
    const filterCondition = `account = "${accountId}"${userFilterCondition} && type = "Published"`;
    const res = await pb.collection<ICampaign>('campaigns').getList(1, 3, {
      filter: filterCondition,
      sort: '-created',
    });
    return res;
  } catch (error) {
    throw error;
  }
};

export const saveHours = async (accountId: string, openHours: string, closeHours: string) => {
  const pb = await getPb();
  // if entry doesn't exist, create or update
  try {
    const res1 = await pb.collection('office_settings').getFirstListItem(`account="${accountId}"`);
    const res2 = await pb.collection('office_settings').update(accountId, {
      open_hours: openHours,
      close_hours: closeHours,
    });
    return res2;
  } catch {
    const res = await pb.collection('office_settings').create({
      account: accountId,
      open_hours: openHours,
      close_hours: closeHours,
    });
    return res;
  }
};

export const createAutomation = async (automation: Partial<IAutomation>) => {
  const pb = await getPb();
  const res = await pb.collection<IAutomation>('automations').create(automation);
  return res;
};

export const getAutomation = async (id: string) => {
  try {
    const pb = await getPb();
    const res = await pb
      .collection<
        {
          expand: {
            template: ITemplateDatabase;
          };
        } & IAutomation
      >('automations')
      .getOne(id, {
        expand: 'template',
      });
    return res;
  } catch (err) {
    return undefined;
  }
};

export const toggleAutomation = async (automationId: string, accountId: string, status: boolean) => {
  console.log('toggleAutomation', automationId, accountId, status);
  const pb = await getPb();
  const res = await pb.collection<IAutomation>('automations').update(automationId, { isActive: status });
  revalidatePath(`/${accountId}/flows`);
  return res;
};

export const getAutomations = async (accountId: string) => {
  const pb = await getPb();
  const res = await pb.collection<IAutomation>('automations').getFullList({
    filter: `account = "${accountId}"`,
  });
  return res;
};

export const addTemplates = async (templates: ITemplateDatabase[], accountId: string) => {
  const pb = await getPb();
  const account = await getAccount(accountId);
  const wabaId = account.waba_id;
  for (let template of templates) {
    try {
      const templateObject = template.template_body;
      if (template.id) {
        //@ts-ignore
        delete template.id;
        //@ts-ignore
        delete templateObject.id;
        //@ts-ignore
        delete template.template_id;
        //@ts-ignore
        if (template.type == 'carousel-template') {
          const cards = templateObject.components?.[1]?.cards;
          for (const _card of cards) {
            const currentCard = _card.components[0];
            if (currentCard.fileUrl && currentCard.example) {
              const fileNameFromUrl = await getFileNameFromUrl(currentCard.fileUrl, 'image');
              //upload file to meta
              const fileUrl = await uploadMediaToMeta(fileNameFromUrl, account);
              delete currentCard.fileUrl;
              currentCard.example.header_handle = [`${fileUrl.h}`];
            }
          }
        } else {
          //@ts-ignore
          if (templateObject.components[0].fileUrl && templateObject.components[0].example) {
            const fileNameFromUrl = await getFileNameFromUrl(templateObject.components[0].fileUrl, 'image');
            //upload file to meta
            const fileUrl = await uploadMediaToMeta(fileNameFromUrl, account);
            delete templateObject.components[0].fileUrl;

            templateObject.components[0].example.header_handle = [`${fileUrl.h}`];
          }
        }
      }
      template.status = 'PENDING';
      // const record = await pb.collection<ITemplateDatabase>('templates').create(template);

      // const templateToSend = record.template_body;
      const raw = await (
        await fetch(`https://graph.facebook.com/v21.0/${wabaId}/message_templates`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            Authorization: `Bearer ${account.access_token}`,
          },
          body: JSON.stringify(templateObject),
        })
      ).json();

      if (raw.error) {
        throw raw.error;
      }
      template.status = raw.status;
      template.template_id = raw.id;

      const record = await pb.collection<ITemplateDatabase>('templates').create(template);
    } catch (e) {
      console.log(e);
    }
  }
  revalidatePath(`/${accountId}/templates/migrate-templates`);
  return { message: 'ok ' };
};

export const getAllInvoicesList = async () => {
  try {
    const pb = await getPb();
    const invoices = await pb.collection<IInvoice>('invoice').getFullList({ expand: 'account_id', sort: '-invoice_valid_from' });
    return invoices;
  } catch (err) {
    console.log(err);
  }
};

export const getBusinessInvoicesList = async (account_id: string) => {
  try {
    const pb = await getPb();
    const invoices = await pb.collection<IInvoice>('invoice').getFullList({
      filter: `account_id = "${account_id}"`,
      expand: 'invoice_pdf,account',
      sort: '-invoice_valid_from',
    });
    return invoices;
  } catch (err) {
    console.log(err);
    throw err;
  }
};

export const getTransactionsList = async (account_id: string) => {
  try {
    const pb = await getPb();
    const transactions = await pb.collection<ITransactionLog>('transaction_log').getFullList({
      filter: `account_id = "${account_id}"`,
      sort: '-created',
    });
    return transactions;
  } catch (error) {
    console.log(error);
    throw error;
  }
};

export const addReactionMessage = async (emoji: string, wamid: string) => {
  try {
    const pb = await getPb();
    const res = await pb.collection<IMessage>('messages').getFirstListItem(`wamid="${wamid}"`);
    if (res?.message) {
      await pb.collection('messages').update(res.id, { reaction: { emoji } });
    }
  } catch (error) {
    throw error;
  }
};

export const addTeam = async (teamName: string, accountId: string) => {
  const pb = await getPb();
  const res = await pb.collection('teams').create({
    name: teamName,
    account: accountId,
  });
  console.log(res);
  revalidatePath(`/${accountId}/teams/create-team`);
  return res;
};
export const createListFilter = async (parse_string: string, list_filter_object: string, list_name: string, accountId: string) => {
  try {
    const user = await getUser();
    const pb = await getPb();
    const res = await pb.collection<IListFilters>('list_filters').create({
      account: accountId,
      created_by: user.id,
      parse_string,
      list_filter_object,
    });
    const list = await pb.collection<IList>('lists').create({
      name: list_name,
      account: accountId,
      created_by: user.id,
      list_filters: res.id,
      type: 'smart',
    });
    revalidatePath(`/${accountId}/list`);
  } catch (error) {
    throw error;
  }
  return;
};
export const updateListFilter = async (
  parse_string: string,
  list_filter_object: string,
  listId: string,
  listName: string,
  listFilterId: string,
  removedLeadsArray: string[],
  addedLeadsArray: IExpandedLead[],
  accountId: string
) => {
  try {
    const user = await getUser();
    const pb = await getPb();
    const res = await pb.collection<IListFilters>('list_filters').update(listFilterId, {
      updated_by: user.id,
      parse_string,
      list_filter_object,
    });

    await updateListName(listId, listName);

    // Remove leads from the list and add leads to the list in parallel
    await Promise.all([...removedLeadsArray.map((id) => removeLeadsFromList(listId, id)), ...addedLeadsArray.map((lead) => addLeadToList(listId, lead.id))]);

    revalidatePath(`/${accountId}/list`);
  } catch (error) {
    throw error;
  }
  return;
};

export const createShopifyOrder = async (account_id: string, response_object: any) => {
  try {
    const pb = await getPb();
    const result = await pb.collection<IShopifyOrder>('shopify_orders').create({
      response_object: response_object,
      account: account_id,
    });
    return result;
  } catch (error) {
    console.log(error);
    throw error;
  }
};

export const getShopifyInvoiceTemplate = async (account_id: string) => {
  try {
    const pb = await getPb();
    const res = await pb.collection<IExpandedThirdParty>('third_party').getFullList({ filter: `account="${account_id}"`, expand: 'invoice_template' });
    return res[0];
  } catch (error) {
    console.log(error);
    throw error;
  }
};

export const getShopifyOrderDetails = async (invoice_id: string) => {
  try {
    const pb = await getPb();
    const shopifyOrder = await pb.collection<IShopifyOrder>('shopify_orders').getFullList({ filter: `id="${invoice_id}"`, expand: 'invoice_pdf,account' });
    const third_party = await pb.collection<IThirdParty>('third_party').getFullList({ filter: `account="${shopifyOrder[0].account}"` });
    const logo_url = pb.files.getURL(third_party[0], third_party[0].logo, { thumb: '50x50' });
    const accountDetails = shopifyOrder[0].expand?.account;
    return { logo_url, accountDetails, orderDetails: shopifyOrder[0] };
  } catch (error) {
    console.log(error);
    throw error;
  }
};

export const createDraftCampaign = async (campaign_name: string, list_id: string[], account_id: string, template_id: string, draft_url: string) => {
  const pb = await getPb();
  const user = await getUser();
  const res = await pb.collection<ICampaign>('campaigns').getList(1, 1, { filter: `name="${campaign_name}" && account="${account_id}"` });
  if (res.totalItems > 0) {
    if (res.items[0].type == 'Draft') {
      //if the campaign is already draft, the nupdate the drafted campaign
      await pb.collection<ICampaign>('campaigns').update(res.items[0].id, {
        name: campaign_name,
        leads_list: list_id.flat(),
        account: account_id,
        template: template_id,
        created_by: user?.id,
        type: 'Draft',
        draft_url,
        scheduled_time: null,
        next_retry_date: null,
        retry_count: 0,
      });
    } else {
      throw new Error(`Campaign with name ${campaign_name} already exists.`);
    }
  } else {
    //mo drafted campaign found or any campaign found with the same name, the ncreate
    await pb.collection<ICampaign>('campaigns').create({
      name: campaign_name,
      leads_list: list_id.flat(),
      account: account_id,
      template: template_id,
      created_by: user?.id,
      type: 'Draft',
      draft_url,
      scheduled_time: null,
      next_retry_date: null,
      retry_count: 0,
    });
  }
  revalidatePath(`/${account_id}/campaign`);
  redirect(`/${account_id}/campaign`);
};

export const deleteCampaign = async (campaign_id: string, account_id: string) => {
  const pb = await getPb();
  await pb.collection<ICampaign>('campaigns').delete(campaign_id);
  revalidatePath(`/${account_id}/campaign`);
  redirect(`/${account_id}/campaign`);
};

export const setCampaignStatus = async (campaign_id: string, status: string, account_id: string) => {
  const pb = await getPb();
  await pb.collection<ICampaign>('campaigns').update(campaign_id, {
    status: status,
  });

  // if (isStopped) {
  await (await fetch(`${process.env.NEXT_PUBLIC_BUN_SERVER_URL}/update-campaign-status?campaignId=${campaign_id}`)).json();
  // }
  revalidatePath(`/${account_id}/campaign`);
};

export const updateMetaTags = async (meta: Record<string, any>, lead_id: string, accountId: string) => {
  const pb = await getPb();
  try {
    const record = await pb.collection<ILead>('leads').update(lead_id, {
      meta,
    });
    return record;
  } catch (error) {
    throw error;
  }
};

export const updateTemplateCategory = async ({
  message_template_id,
  new_category,
  previous_category,
}: {
  message_template_id: string;
  new_category: string;
  previous_category: string | undefined;
}) => {
  try {
    const pb = await getPb();
    const template = await pb.collection<ITemplateDatabase>('templates').getFirstListItem(`template_id="${message_template_id}"`);
    let type = 'basic-template';
    if (new_category == 'UTILITY') {
      type = 'utility-template';
    }
    if (new_category == 'MARKETING') {
      type = 'basic-template';
    }
    if (!previous_category) {
      previous_category = template.category;
    }
    return pb.collection('templates').update(template.id, { previous_category, category: new_category, type });
  } catch (error) {
    throw error;
  }
};

export const getuserFavorite = async (userId: string) => {
  try {
    const pb = await getPb();
    const result = await pb.collection<User>('users').getOne(userId, {
      fields: 'favorite',
    });
    return result.favorite;
  } catch (error) {
    console.log('error');
    return null;
  }
};

export const storeHistoricalMessages = async (phone_number_id: string, threadId: string, sortedMessages: WhatsAppHistoryMessage[]) => {
  try {
    const pb = await getPb();

    // Find the account using phone_number_id
    const account = await pb.collection<Account>('accounts').getFirstListItem(`phone_id = "${phone_number_id}"`);
    if (!account) throw new Error(`Account not found for phone_number_id: ${phone_number_id}`);

    // The threadId is the WhatsApp user's phone number
    const userPhoneNumber = threadId.replace(/[^\d]/g, '');

    // Create or get the lead
    const lead = await upsertLead(userPhoneNumber, account.id);

    // Check if conversation exists (by account and from = lead.id)
    let conversation: IConversation;
    try {
      conversation = await pb.collection<IConversation>('conversations').getFirstListItem(`account = "${account.id}" && from = "${lead.id}"`);
    } catch {
      // Create new conversation if it doesn't exist
      conversation = await pb.collection<IConversation>('conversations').create({
        account: account.id,
        from: lead.id,
        unread_count: 0,
        archived: false,
        blocked: false,
        last_message_created: new Date(parseInt(sortedMessages[0]?.timestamp || Date.now().toString()) * 1000).toISOString(),
      });
    }

    // Prepare batch for message insertion
    const batch = pb.createBatch();

    for (const message of sortedMessages) {
      if (message.type === 'media_placeholder') continue;

      const isFromUser = message.from === threadId;

      const messageData: any = {
        convo_id: conversation.id,
        account: account.id,
        from: isFromUser ? 'user' : 'agent',
        type: message.type,
        delivery_status: message.history_context.status.toLowerCase() || 'sent',
        read: message.history_context.status === 'READ',
        delivered: ['DELIVERED', 'READ', 'PLAYED'].includes(message.history_context.status),
        sent: message.history_context.status !== 'ERROR',
        created: new Date(parseInt(message.timestamp) * 1000).toISOString(),
        wamid: message.id,
        replied_to: message.context?.id || null,
      };

      // Handle message content
      if (message.type === 'text' && message.text) {
        messageData.message = message.text.body || '';
      } else if (['image', 'video', 'audio', 'document', 'sticker'].includes(message.type) && message[message.type]) {
        const mediaField = message[message.type];
        messageData.media_id = mediaField.id || null;
        messageData.media_mime_type = mediaField.mime_type || null;
        messageData.caption = mediaField.caption || null;
        if (message.type === 'document') messageData.filename = mediaField.filename || null;
        if (message.type === 'audio') messageData.voice = mediaField.voice || false;
        messageData.message = messageData.caption || `[${message.type}]`;
      } else if (message.type === 'location' && message.location) {
        messageData.location = JSON.stringify(message.location);
        messageData.message = message.location.name || `[Location: ${message.location.latitude}, ${message.location.longitude}]`;
      } else if (message.type === 'contacts' && message.contacts && message.contacts.length > 0) {
        messageData.message = `[Contact: ${message.contacts[0].name.formatted_name}]`;
        messageData.contacts = JSON.stringify(message.contacts);
      } else if (message.type === 'interactive' && message.interactive) {
        messageData.interactive_message = JSON.stringify(message.interactive);
        messageData.message = message.interactive.body?.text || '[Interactive Message]';
      } else if (message.type === 'button' && message.button) {
        messageData.message = message.button.text || '[Button Reply]';
        messageData.button_payload = message.button.payload;
      } else if (message.type === 'reaction' && message.reaction) {
        messageData.message = `[Reaction: ${message.reaction.emoji}]`;
        messageData.reaction_to_message = message.reaction.message_id;
      } else {
        messageData.message = `[${message.type}]`;
      }

      if (message.history_context.status === 'ERROR') {
        messageData.error_message = 'Historical message delivery failed';
      }

      batch.collection('messages').create(messageData);
    }

    await batch.send();

    // Update conversation's last message timestamp if messages were added
    if (sortedMessages.length > 0) {
      const lastMessage = sortedMessages[sortedMessages.length - 1];
      await pb.collection('conversations').update(conversation.id, {
        last_message_created: new Date(parseInt(lastMessage.timestamp) * 1000).toISOString(),
        unread_count: 0,
      });
    }

    return {
      success: true,
      conversation_id: conversation.id,
      messages_count: sortedMessages.filter((m) => m.type !== 'media_placeholder').length,
    };
  } catch (error: any) {
    log.error('Error in storeHistoricalMessages:', error);
    throw error;
  }
};

/**
 * Stores or updates contact information from SMB app state sync
 * - Finds the account using phone_number_id
 * - Creates or updates contact based on action (add/remove)
 * - Handles contact name updates and removals
 */
export const storeContact = async (phone_number_id: string, contactStateSync: SMBContactStateSync) => {
  try {
    const pb = await getPb();

    // Find the account using phone_number_id
    const account = await pb.collection<Account>('accounts').getFirstListItem(`phone_id = "${phone_number_id}"`);
    if (!account) {
      throw new Error(`Account not found for phone_number_id: ${phone_number_id}`);
    }

    const { contact, action, metadata } = contactStateSync;
    const phoneNumber = contact.phone_number.replace(/[^\d]/g, '');

    if (action === 'add') {
      // Create or update contact (add/edit action)
      try {
        // Check if lead already exists
        const existingLead = await pb.collection<ILead>('leads').getFirstListItem(`phone_number = "${phoneNumber}" && account = "${account.id}"`);

        // Update existing lead with new contact information
        const updateData: any = {
          updated: new Date().toISOString(),
        };

        if (contact.full_name) {
          updateData.name = contact.full_name;
        } else if (contact.first_name) {
          updateData.name = contact.first_name;
        }

        await pb.collection('leads').update(existingLead.id, updateData);

        return {
          success: true,
          action: 'updated',
          lead_id: existingLead.id,
          phone_number: phoneNumber,
        };
      } catch (error) {
        // Lead doesn't exist, create new one
        const leadData: any = {
          phone_number: phoneNumber,
          account: account.id,
          active: true,
          opt_out: false,
          status: 'new',
          country: 'Unknown', // We don't have country info from contact sync
          tags: [],
          list: [],
          meta: {},
          created: new Date(parseInt(metadata.timestamp) * 1000).toISOString(),
          updated: new Date(parseInt(metadata.timestamp) * 1000).toISOString(),
        };

        if (contact.full_name) {
          leadData.name = contact.full_name;
        } else if (contact.first_name) {
          leadData.name = contact.first_name;
        } else {
          leadData.name = phoneNumber; // Fallback to phone number
        }

        const newLead = await pb.collection('leads').create(leadData);

        return {
          success: true,
          action: 'created',
          lead_id: newLead.id,
          phone_number: phoneNumber,
        };
      }
    } else if (action === 'remove') {
      // Handle contact removal
      try {
        const existingLead = await pb.collection<ILead>('leads').getFirstListItem(`phone_number = "${phoneNumber}" && account = "${account.id}"`);

        // Mark as inactive or delete based on business logic
        // For now, we'll mark as inactive to preserve conversation history
        await pb.collection('leads').update(existingLead.id, {
          active: false,
          updated: new Date(parseInt(metadata.timestamp) * 1000).toISOString(),
        });

        return {
          success: true,
          action: 'deactivated',
          lead_id: existingLead.id,
          phone_number: phoneNumber,
        };
      } catch (error) {
        // Contact doesn't exist in our system, nothing to remove
        return {
          success: true,
          action: 'not_found',
          phone_number: phoneNumber,
          message: 'Contact not found in system',
        };
      }
    }

    throw new Error(`Unknown action: ${action}`);
  } catch (error: any) {
    log.error('Error in storeContact:', error);
    throw error;
  }
};
