import { IAutomation } from '../types';

export const mitfFlow: Partial<IAutomation> = {
  name: 'MITF Flow',
  description: 'Handles the MITF flow for banking transactions',
  account: 'mitf',
  flow: {
    edges: [
      {
        source: 'StartTrigger',
        sourceHandle: 'StartTrigger1',
        target: 'Authentication/VerifyCustomer',
        targetHandle: 'Authentication/VerifyCustomer1',
        id: 'starttrigger-Authentication-VerifyCustomer',
      },
      {
        source: 'Authentication/VerifyCustomer',
        sourceHandle: 'Authentication/VerifyCustomer1',
        target: 'Authentication/GenerateOtp',
        targetHandle: 'Authentication/GenerateOtp1',
        id: 'authentication-verifycustomer-generateotp',
      },
      {
        source: 'Authentication/GenerateOtp',
        sourceHandle: 'Authentication/GenerateOtp1',
        target: 'WaitForOtp',
        targetHandle: 'WaitForOtp1',
        id: 'authentication-generateotp-waitforotp',
      },
      {
        source: 'WaitForOtp',
        sourceHandle: 'WaitForOtp1',
        target: 'Authentication/VerifyOtp',
        targetHandle: 'Authentication/VerifyOtp1',
        id: 'waitforotp-verifyotp',
      },
      {
        source: 'Authentication/VerifyOtp',
        sourceHandle: 'Authentication/VerifyOtp1',
        target: 'FullVerified',
        targetHandle: 'FullVerified1',
        id: 'authentication-verifyotp-fullverified',
      },
    ],
    nodes: [
      {
        id: 'StartTrigger',
        type: 'On Message',
        position: { x: 0, y: 0 },
        data: {
          label: 'Banking Trigger',
          description: 'Triggers when user wants to start banking',
          keywords: [{ value: 'banking' }, { value: 'bank' }, { value: 'verify' }, { value: 'start' }],
        },
      },
      {
        id: 'Authentication/VerifyCustomer',
        type: 'Authentication/VerifyCustomer',
        position: { x: 200, y: 0 },
        data: {
          label: 'Verify Customer',
          description: 'Verifies the customer for banking services',
        },
      },
      {
        id: 'Authentication/GenerateOtp',
        type: 'Authentication/GenerateOtp',
        position: { x: 400, y: 0 },
        data: {
          label: 'Generate OTP',
          description: 'Generates OTP for the customer',
        },
      },
      {
        id: 'WaitForOtp',
        type: 'Save Data',
        position: { x: 500, y: 0 },
        data: {
          label: 'Wait for OTP',
          description: 'Waits for user to input OTP',
        },
      },
      {
        id: 'Authentication/VerifyOtp',
        type: 'Authentication/VerifyOtp',
        position: { x: 600, y: 0 },
        data: {
          label: 'Verify OTP',
          description: 'Verifies the OTP entered by the customer',
          waitForUserInput: true,
        },
      },
      {
        id: 'FullVerified',
        type: 'FullVerified',
        position: { x: 800, y: 0 },
        data: {
          label: 'Full Verified',
          description: 'Customer is fully verified and can access banking services',
        },
      },
    ],
    viewport: {
      x: 0,
      y: 0,
      zoom: 1,
    },
  },
  created: new Date(),
  updated: new Date(),
};
