export const data = {
  edges: [
    {
      id: 'xy-edge__on_message1on_message1-service2service2',
      source: 'on_message1',
      sourceHandle: 'on_message1',
      target: 'service2',
      targetHandle: 'service2',
    },
    {
      id: 'xy-edge__service2_1service2_1-service3service3',
      selected: false,
      source: 'service2_1',
      sourceHandle: 'service2_1',
      target: 'service3',
      targetHandle: 'service3',
    },
    {
      id: 'xy-edge__service2_2service2_2-service8service8',
      source: 'service2_2',
      sourceHandle: 'service2_2',
      target: 'service8',
      targetHandle: 'service8',
    },
  ],
  nodes: [
    {
      data: {
        label: 'On Message',
      },
      dragging: false,
      id: 'on_message1',
      measured: {
        height: 42,
        width: 110,
      },
      position: {
        x: 341,
        y: 198,
      },
      selected: false,
      type: 'On Message',
    },
    {
      data: {
        label: 'Send Message',
        options: [
          {
            id: 1,
            label: 'Option 1',
          },
          {
            id: 2,
            label: 'Option 2',
          },
        ],
        text: 'Okay thanks for contact what u r looking for',
      },
      dragging: false,
      id: 'service2',
      measured: {
        height: 342,
        width: 246,
      },
      position: {
        x: 364,
        y: 346,
      },
      selected: false,
      type: 'Send Message',
    },
    {
      data: {
        label: 'Send Message',
        options: [],
        text: 'Visit https://jstylesalon.pk/deals-menu-mens-hair-salon-in-dha',
      },
      dragging: false,
      id: 'service3',
      measured: {
        height: 162,
        width: 246,
      },
      position: {
        x: 842,
        y: 343,
      },
      selected: false,
      type: 'Send Message',
    },
    {
      data: {
        label: 'Button',
        text: 'deals',
      },
      extent: 'parent',
      height: 80,
      id: 'service2_1',
      measured: {
        height: 80,
        width: 174,
      },
      parentId: 'service2',
      position: {
        x: 10,
        y: 162,
      },
      selected: false,
      type: 'button',
      width: 174,
    },
    {
      data: {
        label: 'Button',
        text: '2discounts',
      },
      extent: 'parent',
      height: 80,
      id: 'service2_2',
      measured: {
        height: 80,
        width: 174,
      },
      parentId: 'service2',
      position: {
        x: 10,
        y: 242,
      },
      selected: true,
      type: 'button',
      width: 174,
    },
    {
      data: {
        label: 'Send Message',
        text: 'Webiste :',
      },
      dragging: false,
      id: 'service8',
      measured: {
        height: 162,
        width: 246,
      },
      position: {
        x: 824,
        y: 21.31231689453125,
      },
      selected: false,
      type: 'Send Message',
    },
  ],
  viewport: {
    x: -70.66785057386801,
    y: -1.7724389359531756,
    zoom: 0.7371346086455505,
  },
};
