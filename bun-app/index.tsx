import { FlowProducer, MetricsTime, Queue, Worker } from "bullmq";
import { Database } from "bun:sqlite";
import { Convert } from "easy-currencies";
import { Logger } from "next-axiom";
import { renderToReadableStream } from "react-dom/server";
import { QNAME } from "./constant.ts";
import { calculateMilliseconds } from "./functions/shopify/index.ts";
import { createJobHandlers } from "./handlers/jobHandlers.ts";
import { audioFromBlobToBuffer } from "./lib/bun-ffmpeg/audio.ts";
import { getConversationsWithLastMessage } from "./lib/queries.ts";
import { handleApiRequest } from "./mitf/api";
import type { ICheckout } from "./lib/shopifyTypes.ts";
import { Report } from "./reports/report.ts";
import { getQueueStats } from "./services/queue.ts";
import { redis } from "./services/redis.ts";
import { setupWebSocket } from "./services/websocket.ts";
import subscription from "./subscription.ts";
import { Template } from "./template.tsx";
// import { audio, audioFromBlob, audioFromBlobToBuffer } from "bun-ffmpeg";

const db = new Database(process.env.SQLITE_DB_PATH, { readonly: true });
const forex_rates = await Convert().from("USD").fetch();
// report.generate();
// redis://default:a5gcersboVQDY9cgz8CUDwUxf5hwyAktol0Hm2nbqnLJhDvofM9Gg0Wmoyh6raMD@************:5433/0

const connection = redis;

const flowProducer = new FlowProducer({
  connection,
});

const myQueue = new Queue(QNAME, {
  connection,
  defaultJobOptions: {
    attempts: 1,
    removeOnComplete: {
      count: 20, //keep up to 20 jobs
    },
  },
});

interface TemplateComponent {
  type: string;
  parameters: Array<{
    type: string;
    text?: string;
    image_url?: string;
  }>;
}

// Initialize jobHandlers with required dependencies
const jobHandlers = createJobHandlers(flowProducer, myQueue);

const worker = new Worker(
  QNAME,
  async (job) => {
    const log = new Logger();
    try {
      const { name } = job;

      const handler = jobHandlers[name as keyof typeof jobHandlers];
      if (handler) {
        await handler(job.data);
      } else {
        console.log(`No handler found for job type: ${name}`);
      }
    } catch (error) {
      console.log("error", error);
    }
    log.flush();
  },
  {
    connection,
    concurrency: 1,
    metrics: {
      maxDataPoints: MetricsTime.ONE_WEEK,
    },
  }
);

// await myQueue.obliterate({ force: true });
// Upserting repeatable jobs in the queue

// Daily job for maintenance tasks
await myQueue.upsertJobScheduler(
  "repeat-every-day",
  {
    every: 1000 * 1 * 60 * 60 * 24, // 1 day
  },
  {
    name: "every-day",
  }
);

// Hourly job to check for unanswered messages and send follow-ups
//disabled so it does not send the messages

/*await myQueue.upsertJobScheduler(
  "check-follow-up-messages",
  {
    every: 1000 * 60 * 60, // 1 hour
  },
  {
    name: "followUpMessages",
  }
);*/

worker.on("error", (error) => {
  console.log("error", error);
});

const gracefulShutdown = async (signal: any) => {
  console.log(`Received ${signal}, closing server...`);
  await worker.close();
  // Other asynchronous closings
  process.exit(0);
};

process.on("SIGINT", () => gracefulShutdown("SIGINT"));

process.on("SIGTERM", () => gracefulShutdown("SIGTERM"));

// uncomment this while developing
if (process.env.NODE_ENV === "development") {
  console.log("Building client...");
  await Bun.build({
    entrypoints: ["./client.tsx"],
    outdir: "./out",
  });
}

const server = Bun.serve({
  port: 3006,
  idleTimeout: 255,
  static: {
    "/client/websocket.js": new Response(
      // @ts-expect-error a
      await Bun.file("./client/websocket.js").bytes(),
      {
        headers: {
          "Content-Type": "text/javascript",
        },
      }
    ),
    "/out/client.js": new Response(
      // @ts-expect-error a
      await Bun.file("./out/client.js").bytes(),
      {
        headers: {
          "Content-Type": "text/javascript",
        },
      }
    ),
  },

  async fetch(req, _server) {
    const url = new URL(req.url);

    // Handle CORS preflight
    if (req.method === "OPTIONS") {
      return new Response(null, {
        status: 200,
        headers: {
          "Access-Control-Allow-Origin": "*",
          "Access-Control-Allow-Methods": "GET, POST, OPTIONS",
          "Access-Control-Allow-Headers": "Content-Type",
        },
      });
    }

    if (url.pathname === "/") {
      // const delayedJobs = await myQueue.getJobs(["delayed"]);
      // const now = Date.now();
      // const THIRTY_DAYS_MS = 1000 * 60 * 60 * 24 * 30;

      // if (delayedJobs.length > 0) {
      //   for (const job of delayedJobs) {
      //     if (
      //       job.name == "sendCampaign" &&
      //       job.data?.campaign.status === "Paused"
      //     ) {
      //       await setCampaignStatus(job.data.campaign.id, "Stopped");
      //       const delayMs = job.timestamp + (job.delay ?? 0) - now;
      //       if (Math.abs(delayMs - THIRTY_DAYS_MS) < 1000 * 60) {
      //         // allow 1 min tolerance
      //         await job.remove();
      //       }
      //     }
      //   }
      // }
      // const job = await Job.create(myQueue, "wall", { color: "red" });

      // const completed = await myQueue.getJobs(["delayed"]);

      // const myJob = completed.find((job) => job.id == "6094");
      // myJob.changeDelay(1000 * 20);
      // console.log(myJob.name);
      // myJob.remove();

      // returns the oldest 100 jobs
      // await myQueue.obliterate();

      const queueStats = await getQueueStats();
      const stream = await renderToReadableStream(
        <Template queueStats={queueStats} />
      );
      return new Response(stream, {
        headers: {
          "Content-Type": "text/html",
        },
      });
      return new Response("Hello World", {
        headers: {
          "Content-Type": "text/html",
        },
      });
    }
    if (url.pathname === "/subscription-test") {
      await subscription();
      return new Response("Subscription done");
    }
    if (url.pathname === "/update-campaign") {
      const searchParams = url.searchParams;
      const campaignId = searchParams.get("campaignId");
      const newDate = searchParams.get("newDate");

      // Validate required parameters
      if (!campaignId) {
        return new Response("Campaign ID is required", {
          status: 400,
          headers: {
            "Access-Control-Allow-Origin": "*",
            "Access-Control-Allow-Methods": "GET, POST, OPTIONS",
            "Access-Control-Allow-Headers": "Content-Type",
          },
        });
      }

      if (!newDate) {
        return new Response("New date is required", {
          status: 400,
          headers: {
            "Access-Control-Allow-Origin": "*",
            "Access-Control-Allow-Methods": "GET, POST, OPTIONS",
            "Access-Control-Allow-Headers": "Content-Type",
          },
        });
      }

      const delay = Number(new Date(newDate)) - Number(new Date());
      try {
        // Fetch all delayed jobs
        const delayedJobs = await myQueue.getJobs(["delayed"]);
        // Find the job you want (e.g., by matching some data)
        const targetJob = delayedJobs.find(
          (job) => job.data.campaign.id === campaignId
        );

        if (!targetJob) {
          console.log("No matching delayed job found.");
          return new Response("Campaign not found", {
            status: 404,
            headers: {
              "Access-Control-Allow-Origin": "*",
              "Access-Control-Allow-Methods": "GET, POST, OPTIONS",
              "Access-Control-Allow-Headers": "Content-Type",
            },
          });
        }
        const state = await targetJob.getState();
        if (state === "delayed") {
          await targetJob.changeDelay(delay);
        }
        return new Response("Time changed", {
          headers: {
            "Access-Control-Allow-Origin": "*",
            "Access-Control-Allow-Methods": "GET, POST, OPTIONS",
            "Access-Control-Allow-Headers": "Content-Type",
          },
        });
        // Change the delay
        // await targetJob.changeDelay(newDelay);
        // console.log(`Updated delay for job ${targetJob.id} to ${newDelay}ms`);
      } catch (error) {
        console.error("Error finding or updating job:", error);
        return new Response("Internal Server Error", {
          status: 500,
          headers: {
            "Access-Control-Allow-Origin": "*",
            "Access-Control-Allow-Methods": "GET, POST, OPTIONS",
            "Access-Control-Allow-Headers": "Content-Type",
          },
        });
      }
    }
    // if (url.pathname === "/update-campaign-status") {
    //   const searchParams = url.searchParams;
    //   const campaignId = searchParams.get("campaignId");
    //   const status = searchParams.get("status") as
    //     | "Stopped"
    //     | "Paused"
    //     | "Sending"
    //     | "Completed";

    //   try {
    //     // Fetch all delayed jobs
    //     const delayedJobs = await myQueue.getJobs(["delayed"]);

    //     if (status === "Stopped") {
    //       const targetJob = delayedJobs.find(
    //         (job) =>
    //           job.data.campaign_id === campaignId && job.name === "sendCampaign"
    //       );
    //       const targetJob2 = delayedJobs.find(
    //         (job) =>
    //           job.data.campaign_id === campaignId &&
    //           job.name === "publishCampaign"
    //       );
    //       // Remove the job if it matches the campaignId
    //       if (targetJob) {
    //         await targetJob.remove();
    //       }
    //       if (targetJob2) {
    //         await targetJob2.remove();
    //       }

    //       return new Response("Job removed");
    //     }
    //     // If the job is paused, we can add delay
    //     if (status === "Paused") {
    //       const targetJob = delayedJobs.find(
    //         (job) =>
    //           job.data.campaign_id === campaignId && job.name === "sendCampaign"
    //       );
    //       // Pause by delaying the job for 2 month (60 days)
    //       const oneMonthMs = 1000 * 60 * 60 * 24 * 60;
    //       await targetJob.changeDelay(oneMonthMs);
    //       await targetJob.update({
    //         ...targetJob.data,
    //         campaign: { ...targetJob.data.campaign, status },
    //       });
    //       return new Response("Job paused");
    //     }
    //     // If the job is sending, we can change the delay
    //     if (status === "Sending") {
    //       const targetJob = delayedJobs.find(
    //         (job) =>
    //           job.data.campaign_id === campaignId && job.name === "sendCampaign"
    //       );
    //       const targetJob2 = delayedJobs.find(
    //         (job) =>
    //           job.data.campaign_id === campaignId &&
    //           job.name === "publishCampaign"
    //       );
    //       if (targetJob) {
    //         await targetJob.changeDelay(0);
    //         await targetJob.update({
    //           ...targetJob.data,
    //           campaign: { ...targetJob.data.campaign, status },
    //         });
    //       }
    //       if (targetJob2) {
    //         await targetJob.changeDelay(0);
    //         await targetJob.update({
    //           ...targetJob.data,
    //           campaign: { ...targetJob.data.campaign, status: "Pubslished" },
    //         });
    //       }
    //       // Resume by changing the delay to 0

    //       return new Response("Job resumed");
    //     }
    //     if (status === "Completed") {
    //       const targetJob = delayedJobs.find(
    //         (job) =>
    //           job.data.campaign_id === campaignId && job.name === "sendCampaign"
    //       );
    //       // Mark the job as completed
    //       await targetJob.update({
    //         ...targetJob.data,
    //         campaign: { ...targetJob.data.campaign, status },
    //       });
    //       return new Response("Job marked as completed");
    //     }
    //   } catch (error) {
    //     console.error("Error finding or updating job:", error);
    //   }
    // }
    if (url.pathname === "/get-conversations") {
      const searchParams = url.searchParams;
      const accountId = searchParams.get("accountId") as string;
      const conversations = getConversationsWithLastMessage(db, accountId);
      return new Response(JSON.stringify(conversations), {
        headers: {
          "Content-Type": "application/json",
        },
      });
    }
    if (url.pathname === "/get-report") {
      const searchParams = url.searchParams;
      const accountId = searchParams.get("accountId");
      if (accountId) {
        const report = new Report(db, accountId, forex_rates);
        const reportContent = await report.getReportForToday();
        return new Response(JSON.stringify(reportContent), {
          headers: {
            "Content-Type": "application/json",
          },
        });
      }
    }
    if (url.pathname === "/update-lead-with-campaign") {
      const { id, campaign_id } = await req.json();
      const query = "SELECT * FROM leads WHERE id = $id";
      const conversations = db.query(query).all({ $id: id });
      return new Response(JSON.stringify(conversations), {
        headers: {
          "Content-Type": "application/json",
        },
      });
    }

    // Handle MITF API requests
    if (url.pathname.startsWith("/api/UserManagement/")) {
      return handleApiRequest(req);
    }

    if (url.pathname === "/shopify-checkouts") {
      try {
        //Get the body from the request
        const bodyText = await req.text();
        let checkout = JSON.parse(bodyText) as ICheckout;
        // Get the header from the request
        const shopifyHmac = req.headers.get("x-shopify-hmac-sha256");
        const event_id = req.headers.get("x-shopify-event-id") || "";
        const topic = req.headers.get("x-shopify-topic") || "";
        const shopify_domain = req.headers.get("x-shopify-shop-domain") || "";
        const hmac = req.headers.get("x-shopify-hmac-sha256") || "";
        const secret = process.env.SHOPIFY_API_SECRET;
        console.log({ topic, event_id, id: checkout.id });
        if (!secret) {
          console.error("SHOPIFY_API_SECRET is not set.");
          return new Response("Server configuration error.", { status: 500 });
        }
        if (!hmac) {
          console.error("HMAC is not set.");
          return new Response("Server configuration error.", { status: 500 });
        }
        if (!shopify_domain) {
          console.error("Shopify domain is not set.");
          return new Response("Server configuration error.", { status: 500 });
        }

        const calculatedHmacDigest = new Bun.CryptoHasher("sha256", secret)
          .update(bodyText)
          .digest("base64");

        const hmacValid = calculatedHmacDigest === shopifyHmac;
        if (!hmacValid) {
          console.error("HMAC validation failed");
          return new Response("HMAC validation failed", { status: 401 });
        } else {
          const id = checkout.id;

          if (topic === "checkouts/update") {
            // Remove all previous pending jobs with jobId including checkout.id
            const existingJobs = (await myQueue.getJobs(["delayed"])).filter(
              (job) => job.name === "checkoutUpdatesShopify"
            );
            for (const job of existingJobs) {
              if (job.id == `checkout-${id}`) {
                // Remove the job if it matches the checkout.id
                await job.remove();
              }
            }
            checkout.shopify_domain = shopify_domain;
            checkout.event_id = event_id;
            // Add the latest job with 2 minutes delay
            const delayInMilliseconds = calculateMilliseconds(0, 2, 0);
            await myQueue.add("checkoutUpdatesShopify", checkout, {
              delay: delayInMilliseconds,
              jobId: `checkout-${id}`,
            });
          } else if (topic === "orders/create") {
            //create shopfy order in db
          }
        }
        return new Response(JSON.stringify(checkout), {
          headers: {
            "Content-Type": "application/json",
          },
        });
      } catch (error) {
        //@ts-ignore
        console.log(error);
        return new Response("Not Found", { status: 404 });
      }
    }
    if (url.pathname === "/convert-codec") {
      // Check if ffmpeg is available in the system
      const ffmpegPath = await Bun.which("ffmpeg");
      if (!ffmpegPath) {
        return new Response("ffmpeg not found in $PATH", { status: 500 });
      }

      const formdata = await req.formData();
      const file = formdata.get("file") as Blob;
      interface AudioConvertOptions {
        codec: string;
        bitrate: string;
        channels: number;
        sampleRate: number;
        onError: (error: unknown) => void;
      }

      const options: AudioConvertOptions = {
        codec: "libopus", // use libvorbis for ogg output
        bitrate: "192k",
        channels: 2,
        sampleRate: 44100,
        onError: (error: unknown) =>
          console.error("Error processing audio:", error),
      };

      // Convert and return as file
      try {
        const convertedBuffer = await audioFromBlobToBuffer(
          file,
          options,
          "ogg"
        );
        return new Response(convertedBuffer, {
          headers: {
            "Content-Type": "audio/ogg",
            "Content-Disposition": 'attachment; filename="converted.ogg"',
          },
        });
      } catch (err) {
        return new Response("Audio conversion failed", { status: 500 });
      }
    }
    return new Response("Not Found", {
      status: 404,
    });
  },
  websocket: {
    message(ws, message) {},
    open(ws) {
      setupWebSocket(ws);
    },
  },
});

console.log(`Server running at http://localhost:${server.port}`);
