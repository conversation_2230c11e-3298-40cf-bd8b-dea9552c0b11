{"scripts": {"dev": "bun run --inspect --watch index.tsx"}, "dependencies": {"@axiomhq/js": "^1.3.1", "@types/bun": "^1.2.10", "@types/papaparse": "^5.3.15", "@xyflow/react": "^12.6.0", "bullmq": "^5.51.0", "classnames": "^2.5.1", "clsx": "^2.1.1", "date-fns": "^3.6.0", "dayjs": "^1.11.13", "easy-currencies": "^1.8.3", "export-to-csv": "^1.4.0", "fetch-to-curl": "^0.6.0", "html2pdf.js": "^0.10.3", "ioredis": "^5.6.1", "jsep": "^1.4.0", "libphonenumber-js": "^1.12.7", "lodash": "^4.17.21", "next-axiom": "^1.9.1", "papaparse": "^5.5.2", "phone": "^3.1.59", "pocketbase": "^0.24.0", "react": "latest", "react-aria": "^3.39.0", "react-dom": "latest", "react-hot-toast": "^2.5.2", "stripe": "^18.0.0", "tailwind-merge": "^2.6.0"}, "devDependencies": {"@biomejs/biome": "^1.9.4", "@iconify/react": "^5.2.1", "@types/js-cookie": "^3.0.6", "@types/node": "20.11.25", "@types/react": "18.2.64", "@types/react-dom": "^18.3.6", "@types/react-window": "^1.8.8", "autoprefixer": "^10.4.21", "dotenv": "^16.5.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-prettier": "^5.2.6", "prettier": "^3.5.3", "sass": "^1.87.0", "tailwindcss": "^3.4.17", "typescript": "^5.8.3"}}