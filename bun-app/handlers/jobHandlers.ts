import { FlowProducer, Queue } from "bullmq";
import <PERSON> from "papaparse";
import { QNAME } from "../constant.ts";
import {
  calculateMilliseconds,
  getLead,
  getPhoneNumber,
  getShopifyAccountAndUser,
  upsertShopifyCheckout,
} from "../functions/shopify/index.ts";
import deleteOldTemplates, { justSend } from "../helper.ts";
import {
  createShopifyCustomer,
  finishUploadingLeads,
  prepareUploadLeads,
  uploadOneLead,
} from "../lib/actions.ts";
import {
  addMessage,
  createLeadForShopify,
  getCampaignMessagesById,
  getLeadsByListIds,
  getMessageLimitsByUser,
  getPb,
  getTemplateFromDbByTemplateName,
  setCampaignAsPublished,
  setCampaignStatus,
  updateAccountRemainingLimit,
  updateCampaignRetryCount,
  updateCampaignRetryFailed,
  updateCampaignRetryPerformance,
  updateUserRemainingLimit,
  upsertConversation,
  upsertLead,
} from "../lib/pocket";
import type { ICheckout } from "../lib/shopifyTypes.ts";
import {
  type ILead,
  type IMessagingLimit,
  type IPendingContacts,
  type User,
} from "../lib/types";
import { send_template } from "../lib/wa.ts";
import resetLimits from "../resetLimits.ts";
import subscription from "../subscription.ts";
import { sendCampaign } from "../worker-functions/sendCampaign.ts";

// Handler functions for worker jobs

/**
 * Handles retrying to send a previously failed message
 * Retrieves the message by ID, attempts to send it again,
 * and increments the retry counter in the database
 * @param data Object containing messageId
 */
export async function handleSendRetry(data: any) {
  const pb = await getPb();
  const { message: myMessage } = data;
  //@ts-ignore
  const message = await justSend({
    message: myMessage,
  });

  await pb.collection("messages").update(message.id, {
    "retried_attempts+": 1,
  });
}

/**
 * Handles sending a message to a lead using a template
 * Creates or retrieves a lead record, adds a new message to the database,
 * and sends the message through the messaging service
 * @param data Object containing lead details, template, and accountId
 */
export async function handleSend(data: any) {
  const { lead: leadItem, template, accountId } = data;
  const phone_number = leadItem.phone_number;
  // add message to db
  const lead = await upsertLead(phone_number, accountId);
  const message = await addMessage(
    lead.id,
    template.template_name,
    "agent",
    accountId,
    null,
    "pending",
    template.id
  );
  await justSend({ message });
}

/**
 * Handles the creation of a new campaign
 * Processes campaign data, identifies target leads, creates jobs for each lead,
 * and schedules the campaign execution with appropriate delay if needed
 * @param data Campaign configuration data including campaign, lists, templates, etc.
 * @param flowProducer BullMQ FlowProducer instance for creating job flows
 */
export async function handleCreateCampaign(
  data: any,
  flowProducer: FlowProducer
) {
  const {
    campaign,
    sendType,
    list_id,
    template_name,
    account_id,
    template_id,
    user,
    start,
    date,
  } = data;
  let totalContacts = 0;
  const seenLeadIds = new Set<string>();

  const leads = await getLeadsByListIds(list_id.flat());
  const activeLead = leads.filter(
    (lead) => lead.active && !lead.opt_out && !lead.blocked
  );
  for (const _lead of activeLead) {
    if (!seenLeadIds.has(_lead.id)) {
      totalContacts += 1;
      seenLeadIds.add(_lead.id);
    }
  }
  const jobs = Array.from(seenLeadIds).map((leadId) => {
    return {
      name: "sendCampaign",
      data: {
        lead_id: leadId,
        campaign,
        template_name,
        account_id,
        template_id,
        created_by: campaign.created_by,
      },
    };
  });
  console.log(
    "Send campaign is being sent with total contacts of ",
    totalContacts
  );
  await Promise.all([
    updateAccountRemainingLimit(account_id, totalContacts),
    updateUserRemainingLimit(campaign.created_by, totalContacts, account_id),
  ]);
  const delay =
    sendType === "sendNow" ? 0 : Number(new Date(date)) - Number(new Date());
  flowProducer.add({
    queueName: QNAME,
    name: "publishCampaign",
    data: {
      campaign,
      totalContacts,
      created_by: campaign.created_by,
      account_id,
    },
    children: jobs.map((job) => ({
      queueName: QNAME,
      opts: {
        delay,
      },
      ...job,
    })),
  });
  const end = new Date().getTime();
  console.log(`Time taken in create campaign: ${campaign.id}`, end - start);
}

/**
 * Handles the publishing of a campaign after it's been created
 * Updates account and user messaging limits, marks the campaign as published,
 * and sets up automatic retry for failed messages if enabled
 * @param data Object containing campaign, totalContacts, created_by, account_id
 * @param myQueue BullMQ Queue instance for scheduling retry jobs
 */
export async function handlePublishCampaign(data: any, myQueue: Queue) {
  const { campaign, totalContacts, created_by, account_id } = data;
  console.log("Publishing campaign", campaign.id, totalContacts);

  let updatedCampaign = await setCampaignStatus(campaign.id, "Completed");
  updatedCampaign = await updateCampaignRetryPerformance(updatedCampaign);

  // retry after 24 hours
  if (updatedCampaign.expand.account.allow_failed_retry_campaigns) {
    if (updatedCampaign.retry_count < 3) {
      myQueue.add(
        "retryCampaign",
        { campaign: updatedCampaign },
        {
          delay: 1000 * 60 * 60 * 24,
        }
      );
    }
  }
}

/**
 * Daily job handler that performs routine maintenance tasks
 * Deletes old templates, resets messaging limits, and handles subscription updates
 * @param data Job data (not used in this handler)
 */
export async function handleEveryDay(data: any) {
  console.log("every-day-job", data);
  await deleteOldTemplates();
  await resetLimits();
  await subscription();
}

/**
 * Handles retrying failed messages in a campaign
 * Retrieves failed messages, checks messaging limits, and creates new jobs
 * to retry sending those messages if limits allow
 * @param data Object containing campaign
 * @param flowProducer BullMQ FlowProducer instance for creating job flows
 */
export async function handleRetryCampaign(
  data: any,
  flowProducer: FlowProducer
) {
  const { campaign } = data;
  const failedMessages = await getCampaignMessagesById(campaign.id, "failed");

  const userId = campaign.created_by;
  const accountId = campaign.account;
  let messagingLimit = (await getMessageLimitsByUser(
    userId,
    accountId
  )) as IMessagingLimit;

  const totalContacts = failedMessages.length;
  if (
    (messagingLimit?.remaining_limit ?? 0) >= totalContacts &&
    totalContacts > 0
  ) {
    const jobs = Array.from(failedMessages).map((message) => {
      return {
        name: "sendRetry",
        data: {
          message: message,
        },
      };
    });
    let updatedCampaign = await updateCampaignRetryCount(
      campaign.id,
      campaign.next_retry_date ?? new Date()
    );
    updatedCampaign = await setCampaignStatus(campaign.id, "Retrying");
    console.log(
      "retry campaign is being sent with total contacts of ",
      totalContacts
    );
    await Promise.all([
      updateAccountRemainingLimit(accountId, totalContacts),
      updateUserRemainingLimit(campaign.created_by, totalContacts, accountId),
    ]);
    flowProducer.add({
      queueName: QNAME,
      name: "publishCampaign",
      data: {
        campaign: updatedCampaign,
        totalContacts,
        created_by: updatedCampaign.created_by,
        account_id: updatedCampaign.account,
      },
      children: jobs.map((job) => ({
        queueName: QNAME,
        ...job,
      })),
    });
  } else {
    await updateCampaignRetryFailed(campaign.id, "Message limit exceeded");
  }
}

/**
 * Handles the upload of contacts from a CSV file
 * Processes the CSV data, maps fields according to user selections,
 * and creates batch jobs to handle the upload of large contact lists
 * @param data Upload data including file info, field mappings, and account details
 * @param flowProducer BullMQ FlowProducer instance for creating upload job flows
 */
export async function handleUploadContacts(
  data: any,
  flowProducer: FlowProducer
) {
  const { id, fileName, selectedMappings, account_id, listId, user } = data;
  const pb = await getPb();
  const pendingContact = (await pb
    .collection<IPendingContacts>("pending_contacts")
    .getOne(id)) as IPendingContacts;
  const csvFile = await fetch(
    pb.files.getURL(pendingContact, pendingContact.csv_file)
  );
  const text = await csvFile.text();
  const { data: leads, meta } = Papa.parse<ILead>(text, {
    header: true,
    skipEmptyLines: true,
  });
  const newLeads = leads.map((lead) => {
    const newLead: any = {};
    Object.entries(selectedMappings).forEach(([originalHeader, newHeader]) => {
      newLead[newHeader as string] = (lead as any)[originalHeader];
    });
    if (!newLead.name) {
      newLead.name = newLead.phone_number;
    }
    if (!newLead.status) {
      newLead.status = "New";
    }
    if (newLead.tags) {
      newLead.tags = newLead.tags.split(";").map((item: string) => {
        return item.trim();
      });
    }
    return newLead;
  });
  const { log, _listId, _transformedLeads, listFilters, start } =
    await prepareUploadLeads(
      account_id,
      newLeads,
      fileName,
      newLeads.length,
      listId,
      user,
      pb
    );

  // tell pending contacts how many leads will be uploaded
  await pb.collection<IPendingContacts>("pending_contacts").update(id, {
    total_items: newLeads.length,
    pending_leads: newLeads.length,
    listId: _listId,
    logId: log.id,
    account: account_id,
    meta_info: [],
  });

  // create flowproducer jobs
  // create slices of 1000 of _transformedLeads
  const leadChunks: ILead[][] = [];
  const BATCH = 40;
  for (let i = 0; i < _transformedLeads.length; i += BATCH) {
    leadChunks.push(_transformedLeads.slice(i, i + BATCH));
  }

  const jobs = leadChunks.map((chunk) => {
    return {
      name: "uploadOneContact",
      data: {
        chunk,
        accountId: account_id,
        listFilters,
        pendingId: id,
        _listId,
        user,
        log,
      },
    };
  });

  flowProducer.add({
    queueName: QNAME,
    name: "uploadContactsDone",
    data: {
      log,
      start,
      _listId,
      account_id,
      pendingId: id,
    },
    children: jobs.map((job) => ({
      queueName: QNAME,
      ...job,
    })),
  });
}

/**
 * Handles uploading a batch of contacts to the system
 * Processes a chunk of leads from a larger import job,
 * saving them to the database and creating related records
 * @param data Object containing chunk of leads and related metadata
 */
export async function handleUploadOneContact(data: any) {
  const pb = await getPb();
  const {
    chunk,
    accountId,
    _listId,
    listFilters,
    pendingId,
    user,
    log,
  }: {
    chunk: ILead[];
    accountId: string;
    _listId: string;
    listFilters: any;
    pendingId: string;
    user: User;
    log: any;
  } = data;
  const result = await uploadOneLead({
    pb,
    account: accountId,
    listFilters,
    chunk,
    _listId,
    log,
    user,
  });
  result.forEach(async (r) => {
    await pb.collection("pending_logs").create({
      log: r,
      pending_id: pendingId,
    });
  });
}

/**
 * Finalizes the contact upload process after all chunks have been processed
 * Gathers logs from all individual upload operations, updates the log record,
 * and schedules cleanup of temporary log entries
 * @param data Object containing log references, list ID, and pending ID
 * @param myQueue BullMQ Queue instance for scheduling cleanup jobs
 */
export async function handleUploadContactsDone(data: any, myQueue: Queue) {
  const { log, start, _listId, pendingId } = data;
  const pb = await getPb();
  const pendingLogs = (await pb.collection("pending_logs").getFullList({
    filter: `pending_id = "${pendingId}"`,
  })) as { log: any }[];
  const results = pendingLogs.map((item) => item.log);
  await finishUploadingLeads({ log, _listId, start, results: results });
  myQueue.add(
    "delete1log",
    { pendingId },
    {
      delay: 1000 * 60,
    }
  );
}

/**
 * Clears the state of a conversation
 * Resets a conversation state to empty, typically used after
 * a conversation flow has completed or needs to be reset
 * @param data Object containing convoId
 */
export async function handleClearState(data: any) {
  const pb = await getPb();
  const { convoId } = data;
  await pb.collection("conversations").update(convoId, { state: "" });
}

/**
 * Incrementally deletes log entries for completed contact uploads
 * Performs batch deletion of pending logs to avoid performance issues
 * with large-scale deletions, and schedules itself to continue until all logs are cleared
 * @param data Object containing pendingId reference
 * @param myQueue BullMQ Queue instance to schedule additional cleanup jobs
 */
export async function handleDelete1Log(data: any, myQueue: Queue) {
  const { pendingId } = data;
  const pb = await getPb();
  const pendingLogs = await pb
    .collection("pending_logs")
    .getList(1, 5, { filter: `pending_id = "${pendingId}"` });
  if (pendingLogs.items.length === 0) {
    return;
  }
  pendingLogs.items.forEach(async (item: any) => {
    await pb.collection("pending_logs").delete(item.id);
  });
  myQueue.add("delete1log", { pendingId });
}

/**
 * Handles checking for unanswered user messages and sending follow-up messages
 * Identifies conversations where the last message is from a user and no agent
 * has replied for 20 hours, then sends an automated "are you interested?" message
 * @param data Object containing optional accountId to limit the check to a specific account
 */
// export async function handleFollowUpMessages(data: any) {
//   const log = new Logger();
//   const pb = await getPb();
//   const { accountId } = data || {};

//   try {
//     // Use the Database instance to query conversations
//     const db = new Database(process.env.SQLITE_DB_PATH || "", { readonly: true });

//     // Import the query function
//     const { getConversationsWithLastMessage } = await import("../lib/queries.ts");

//     // Get conversations with their last messages using our SQL query
//     const conversations = getConversationsWithLastMessage(db, accountId);

//     log.info(`Checking ${conversations.length} conversations for follow-up messages`);

//     // Define the conversation type for better type safety
//     interface Conversation {
//       convo_id: string;
//       from: string;
//       account: string;
//       last_message_from: string;
//       last_message_created: string;
//       chat_archive: boolean;
//       last_message: string;
//     }

//     // Cast the conversations to the correct type
//     const typedConversations = conversations as Conversation[];

//     // Filter conversations where the last message is from a user
//     const userLastMessageConvos = typedConversations.filter(convo =>
//       convo.last_message_from === "user" && !convo.chat_archive
//     );

//     log.info(`Found ${userLastMessageConvos.length} conversations with last message from user`);

//     // Check each conversation for time since last message
//     for (const convo of userLastMessageConvos) {
//       const lastMessageTime = dayjs(convo.last_message_created);
//       const currentTime = dayjs();
//       const hoursSinceLastMessage = currentTime.diff(lastMessageTime, 'hour');

//       // If 20 hours have passed since the last message
//       if (hoursSinceLastMessage >= 20 && hoursSinceLastMessage <= 24) {
//         log.info(`Sending follow-up message for conversation ${convo.convo_id} (${hoursSinceLastMessage} hours since last message)`);
//         console.log(`Sending follow-up message for conversation ${convo.last_message} (${hoursSinceLastMessage} hours since last message)`);
//         // Send a follow-up message
//         await addMessage(
//           convo.from,
//           "Are you interested?",
//           "agent",
//           convo.account,
//           null,
//           "pending"
//         );

//         // Get the message we just created (should be the latest one)
//         const messages = await pb.collection("messages").getList(1, 1, {
//           filter: `convo_id = "${convo.convo_id}" && from = "agent"`,
//           sort: "-created"
//         });

//         if (messages.items.length > 0) {
//           // Send the message
//           await justSend({ message: { id: messages.items[0].id } });
//           log.info(`Follow-up message sent for conversation ${convo.convo_id}`);
//         }
//       }
//     }

//     log.info("Follow-up message check completed");
//   } catch (error) {
//     console.log(error);
//     log.error(`Error in handleFollowUpMessages: ${error}`);
//   }

//   log.flush();
// }

/**
 * Handles the creation of a Shopify customer
 * Processes the provided shop and customer data, and creates a new customer
 * in the Shopify store using the provided information
 * @param data Object containing shop and customer details
 */
export async function handleCreateShopifyCustomer(data: any) {
  const { shop, customer } = data;
  const pb = await getPb();
  const result = await createShopifyCustomer({
    shop,
    customer,
  });
}

/**
 * Handles updates to Shopify checkouts
 * Processes the provided checkout data, determines the appropriate customer address,
 * validates the phone number, creates or updates a lead, and manages the checkout status.
 * If the checkout is not completed, schedules a delayed job for further processing.
 * @param checkout Shopify checkout object containing details about the transaction
 * @param myQueue BullMQ Queue instance for scheduling delayed jobs
 */
export async function handleCheckoutUpdatesShopify(
  checkout: ICheckout,
  myQueue: Queue
) {
  const { shopify_domain, id, event_id } = checkout;
  const { account, user } = await getShopifyAccountAndUser({
    shopify_domain,
  });

  //if shipping_address is an array means it is empty. THen look for billing_address
  // phone number, first naeme and last name are not available

  let shipping_address = checkout.shipping_address;
  let billing_address = checkout.billing_address;
  let customer_address = null;
  //if shipping_address is an array and billing_address is not an array, then use billing_address
  if (Array.isArray(shipping_address) && !Array.isArray(billing_address)) {
    customer_address = billing_address;
  } else if (
    !Array.isArray(shipping_address) &&
    Array.isArray(billing_address)
  ) {
    //If shipping_address is not an array and billing_address is an array, then use shipping_address
    customer_address = shipping_address;
  } else if (
    !Array.isArray(shipping_address) &&
    !Array.isArray(billing_address)
  ) {
    //If both are not arrays, then use shipping_address
    customer_address = shipping_address;
  }

  if (customer_address) {
    let phone_number = customer_address.phone;
    let country_code = customer_address.country_code;
    const phoneNumber = getPhoneNumber({
      phone_number,
      country_code,
    });
    //if phone number is valid then create lead, and upsert checkout
    if (phoneNumber?.isValid()) {
      phone_number = phoneNumber.number;

      let lead_id =
        checkout.lead_id ??
        (await getLead({
          phone_number,
          account_id: account.id,
        }));
      const name =
        customer_address.first_name + " " + customer_address.last_name;
      if (!lead_id) {
        lead_id = await createLeadForShopify({
          phone_number,
          account_id: account.id,
          name,
          user_id: user.id,
          country_code,
        });
      }
      //if checkout.completed has a date, then dont add to job and instead update the checkout object in db
      if (checkout.completed_at) {
        await upsertShopifyCheckout({
          checkout_id: checkout.id.toString(),
          account_id: account.id,
          lead_id,
          checkout_status: "completed",
          checkout,
        });
      } else {
        //else upsert the checkout object in db and add to job
        await upsertShopifyCheckout({
          checkout_id: checkout.id.toString(),
          account_id: account.id,
          lead_id,
          checkout_status: "in_process",
          checkout,
        });

        checkout.lead_id = lead_id;
        checkout.customer_name = name;
        checkout.customer_phone_number = phone_number;
        checkout.checkout_account_of = account;

        console.log("adding job to delayedqueye");
        // Add the latest job
        const delayInMilliseconds = calculateMilliseconds(0, 1, 0);
        await myQueue.add("delayedJobShopify", checkout, {
          delay: delayInMilliseconds,
        });
      }
    }
  }
}

/**
 * Handles updates to Shopify checkouts
 * Processes the provided checkout data, determines the appropriate customer address,
 * validates the phone number, creates or updates a lead, and manages the checkout status.
 * If the checkout is not completed, schedules a delayed job for further processing.
 * @param checkout Shopify checkout object containing details about the transaction
 * @param myQueue BullMQ Queue instance for scheduling delayed jobs
 */
export async function handleDelayedJobShopify(checkout: ICheckout) {
  const {
    customer_name,
    customer_phone_number,
    checkout_account_of: account,
  } = checkout;
  const lead_id =
    checkout.lead_id ??
    (await getLead({
      phone_number: customer_phone_number,
      account_id: account.id,
    }));

  await upsertConversation(lead_id || "", account.id);
  let template_name = "shopify_abandoned_checkout";
  const template = await getTemplateFromDbByTemplateName({
    account,
    template_name,
  });
  await addMessage(
    lead_id || "",
    template.template_name,
    "agent",
    account.id,
    null,
    "pending",
    template.id
  );
  const tempComponents = [
    {
      type: "body",
      parameters: [
        customer_name,
        "50% OFF",
        checkout.abandoned_checkout_url,
      ]?.map((param) => {
        return {
          type: "text",
          text: param,
        };
      }),
    },
  ];
  const res = await send_template({
    recipient_number: customer_phone_number,
    template: template?.template_name,
    phoneId: account.phone_id,
    components: tempComponents,
    templateLang: template?.template_body.language,
  });
  await upsertShopifyCheckout({
    checkout_id: checkout.id.toString(),
    account_id: account.id,
    lead_id: null,
    checkout_status: "abandoned",
    checkout,
  });
  return new Response(JSON.stringify(res, null, 2), { status: 200 });
}

/**
 * Creates a collection of job handlers with their required dependencies
 * This factory function wires up all job handlers with the dependencies they need
 * (FlowProducer and Queue instances) so they can be easily registered with the worker
 *
 * @param flowProducer BullMQ FlowProducer instance for handlers that need to create job flows
 * @param myQueue BullMQ Queue instance for handlers that need to schedule additional jobs
 * @returns An object mapping job names to their handler functions with dependencies injected
 */
export function createJobHandlers(flowProducer: FlowProducer, myQueue: Queue) {
  return {
    sendRetry: handleSendRetry,
    send: handleSend,
    sendCampaign: sendCampaign,
    createCampaign: (data: any) => handleCreateCampaign(data, flowProducer),
    publishCampaign: (data: any) => handlePublishCampaign(data, myQueue),
    "every-day": handleEveryDay,
    retryCampaign: (data: any) => handleRetryCampaign(data, flowProducer),
    uploadContacts: (data: any) => handleUploadContacts(data, flowProducer),
    uploadOneContact: handleUploadOneContact,
    uploadContactsDone: (data: any) => handleUploadContactsDone(data, myQueue),
    clearState: handleClearState,
    delete1log: (data: any) => handleDelete1Log(data, myQueue),
    createShopifyCustomer: (data: any) => handleCreateShopifyCustomer(data),
    checkoutUpdatesShopify: (data: any) =>
      handleCheckoutUpdatesShopify(data, myQueue),
    delayedJobShopify: (data: any) => handleDelayedJobShopify(data),

    // followUpMessages: handleFollowUpMessages
  };
}
