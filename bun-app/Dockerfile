# use the official Bun image
# see all versions at https://hub.docker.com/r/oven/bun/tags
FROM oven/bun:canary-debian AS base
WORKDIR /usr/src/bun-app

# install dependencies into temp directory
# this will cache them and speed up future builds
# Add ffmpeg for audio processing
RUN apt-get update && apt-get install -y ffmpeg

FROM base AS install
RUN mkdir -p /temp/prod
COPY package.json bun.lock /temp/prod/
RUN cd /temp/prod && bun install --frozen-lockfile --production

# copy node_modules from temp directory
# then copy all (non-ignored) project files into the image
FROM base AS prerelease
COPY --from=install /temp/prod/node_modules node_modules
COPY . .

# [optional] tests & build
ENV NODE_ENV=production
RUN bun build ./client.tsx --outdir ./out

ARG NEXT_PUBLIC_AXIOM_TOKEN
ARG NEXT_PUBLIC_AXIOM_DATASET
ARG NEXT_PUBLIC_APP_ID
ARG NEXT_PUBLIC_PB_URL
ARG NEXT_PUBLIC_APP_URL
ARG NEXT_PUBLIC_QNAME
ARG FIREBASE_PRIVATE_KEY
ARG SQLITE_DB_PATH

# run the app
USER bun
EXPOSE 3006/tcp
ENTRYPOINT [ "bun", "run", "index.tsx" ]