import { Logger } from "next-axiom";

import dayjs from "dayjs";
import {
  deleteTemplate,
  getPendingDeletionTemplates,
  increaseMessageLimitQuota,
  markMessageAsFailed,
  resAddWAMI,
} from "./lib/pocket";
import {
  type ICarouselParams,
  type IExpandedMessage,
  type ILead,
  type ITemplateDatabase,
} from "./lib/types";
import { send_template } from "./lib/wa";

export const justSend = async ({ message }: { message: IExpandedMessage }) => {
  const log = new Logger();
  let m = message;

  const lead = m.expand?.user;
  const template = m.expand?.template;
  if (!template || !lead || !m.expand?.account?.id) {
    console.log("Template or lead or account not found");
    log.error("error in send template");
    log.flush();
    return m as IExpandedMessage;
  }
  let tempComponents;
  if (
    ["basic-template", "utility-template", ""].includes(template?.type ?? "")
  ) {
    tempComponents = createTemplateComponents(template, lead);
  } else {
    tempComponents = createCarouselTemplateComponent(
      template,
      lead,
      m.expand?.account.access_token
    );
  }
  const account = m.expand?.account;

  const res = await send_template({
    recipient_number: lead?.phone_number,
    template: template?.template_name,
    phoneId: account!.phone_id,
    components: tempComponents,
    templateLang: template?.template_body.language,
  });

  if ("error" in res) {
    //If meta returns error then increase account and user message count by 1
    await Promise.all([
      markMessageAsFailed(m, res.error.message),
      increaseMessageLimitQuota(m),
    ]);
  } else {
    await resAddWAMI(m, res);
  }
  return m;
};

const createTemplateComponents = (template: ITemplateDatabase, lead: ILead) => {
  //check if template is there
  const headerComponent = template?.template_body.components.find(
    (item) => item.type == "HEADER"
  );
  const bodyComponent = template?.template_body.components.find(
    (item) => item.type == "BODY"
  );
  const fileHeader =
    template?.updated_template?.HEADER ?? headerComponent?.fileUrl;
  const templateParams = template?.params;
  const bodyParams = templateParams?.body as string[];
  let headerParams = templateParams?.header?.[0];

  if (headerComponent?.example?.header_text) {
    if (headerParams == "custom") {
      headerParams = templateParams.defaultHeader[0];
    } else if (headerParams == "name") {
      //if header value is a number then use default header
      if (!isNaN(Number(lead?.[headerParams]))) {
        headerParams = templateParams.defaultHeader[0];
      } else {
        headerParams = lead?.[headerParams] ?? templateParams.defaultHeader[0];
      }
    } else {
      //@ts-ignore
      headerParams = lead?.[headerParams] ?? templateParams.defaultHeader[0];
    }
  }

  const tempComponents: any = [
    {
      type: "body",
      //check if body params event exists
      //map the vody params and get their values correspending to lead. Will always recive keys.
      //if {name, phone_number} is undefined for user then use default value
      ...(bodyComponent?.example && {
        parameters: bodyParams.map((item, index) => {
          return {
            type: "text",
            //@ts-ignore
            text:
              item == "custom"
                ? templateParams.defaultBody[index]
                : (lead?.[item as keyof ILead] ??
                  templateParams.defaultBody[index]),
          };
        }),
      }),
    },
  ];

  if (headerComponent && headerComponent.format == "IMAGE") {
    tempComponents.push({
      type: "header",
      parameters: [{ type: "image", image: { link: fileHeader } }],
    });
  } else if (headerComponent && headerComponent.format == "VIDEO") {
    tempComponents.push({
      type: "header",
      parameters: [{ type: "video", video: { link: fileHeader } }],
    });
  } else if (headerComponent && headerComponent.format == "DOCUMENT") {
    tempComponents.push({
      type: "header",
      parameters: [{ type: "document", document: { link: fileHeader } }],
    });
  } else if (headerComponent) {
    tempComponents.push({
      type: "header",
      parameters: [
        headerComponent.example && {
          type: "text",
          text: headerParams,
        },
      ],
    });
  }
  return tempComponents;
};

export const createCarouselTemplateComponent = (
  template: any,
  lead: ILead,
  access_token: any
) => {
  const carouselParams: ICarouselParams[] = template.carousel_params;
  const messageBodyParams: ICarouselParams = template.params;
  let mainComponent: Array<{
    type: string;
    parameters?: Array<{ type: string; text?: string; payload?: string }>;
    cards?: Array<{ card_index: number; components: any[] }>;
  }> = [];
  let carouselComponent = {
    type: "carousel",
    cards: [] as { type: string; text: string }[],
  } as any;
  let _messageBodyParameter = {
    type: "body",
    parameters: [] as { type: string; text: string }[],
  };
  if ("example" in template.template_body.components[0]) {
    let messageParams: string;
    messageBodyParams?.body.forEach((item, bodyIndex) => {
      if (item == "custom") {
        messageParams = messageBodyParams.defaultBody[bodyIndex];
      } else if (item == "name") {
        //if header value is a number then use default header
        if (!isNaN(Number(lead?.[item]))) {
          messageParams = messageBodyParams.defaultBody[bodyIndex];
        } else {
          messageParams =
            lead?.[item] ?? messageBodyParams.defaultBody[bodyIndex];
        }
      } else {
        //@ts-ignore
        messageParams =
          lead?.[item as keyof ILead] ??
          messageBodyParams.defaultBody[bodyIndex];
      }
      _messageBodyParameter.parameters.push({
        type: "text",
        text: messageParams,
      });
    });

    mainComponent.push(_messageBodyParameter);
  }
  const cards = template.template_body.components[1].cards;
  for (let index = 0; index < cards.length; index++) {
    let cardComponent: any = [];

    if ("example" in cards[index].components[1]) {
      let cardComponentParameter = {
        type: "body",
        parameters: [] as { type: string; text: string }[],
      };
      let cardParams: string;
      const carouselParamsEntry = carouselParams?.find(
        (item: any) => item.cardIndex === index
      );
      carouselParamsEntry?.body.forEach((item, bodyIndex) => {
        if (item == "custom") {
          cardParams = carouselParamsEntry.defaultBody[bodyIndex];
        } else if (item == "name") {
          //if header value is a number then use default header
          if (!isNaN(Number(lead?.[item]))) {
            cardParams = carouselParamsEntry.defaultBody[bodyIndex];
          } else {
            cardParams =
              lead?.[item] ?? carouselParamsEntry.defaultBody[bodyIndex];
          }
        } else {
          //@ts-ignore
          cardParams =
            lead?.[item as keyof ILead] ??
            carouselParamsEntry.defaultBody[bodyIndex];
        }
        cardComponentParameter.parameters.push({
          type: "text",
          text: cardParams,
        });
      });

      cardComponent.push(cardComponentParameter);
    }

    const headerComponent = {
      type: "header",
      parameters: [
        {
          type: cards[index].components[0].format.toLowerCase(),
          [cards[index].components[0].format.toLowerCase()]: {
            id: template.header_asset_id[index],
          },
        },
      ],
    };
    cardComponent.push(headerComponent);
    for (
      let btnIndex = 0;
      btnIndex < cards[index].components[2].buttons.length;
      btnIndex++
    ) {
      if (cards[index].components[2].buttons[btnIndex].type == "QUICK_REPLY") {
        cardComponent.push({
          type: "button",
          sub_type: "quick_reply",
          index: btnIndex,
          parameters: [
            {
              type: "payload",
              payload: cards[index].components[2].buttons[btnIndex].text,
            },
          ],
        });
      }

      if (cards[index].components[2].buttons[btnIndex].type == "URL") {
        cardComponent.push({
          type: "button",
          sub_type: "quick_reply",
          index: btnIndex,
          parameters: [
            {
              type: "text",
              text: cards[index].components[2].buttons[btnIndex].text,
            },
          ],
        });
      }
    }
    carouselComponent.cards.push({
      card_index: index,
      components: cardComponent,
    });
  }
  mainComponent.push(carouselComponent);
  return mainComponent;
};

const deleteOldTemplates = async () => {
  const pendingDeletionTemplates =
    (await getPendingDeletionTemplates()) as ITemplateDatabase[];
  await Promise.all(
    pendingDeletionTemplates.map(async (item) => {
      const currentDate = dayjs(new Date());
      const updatedAtTime = dayjs(item.updated);
      const diffInDays = currentDate.diff(updatedAtTime, "day");
      const deletionDaysPassed = diffInDays > 30;
      if (deletionDaysPassed) {
        await deleteTemplate(item.id);
      }
      return item.id;
    })
  );
};

export default deleteOldTemplates;
