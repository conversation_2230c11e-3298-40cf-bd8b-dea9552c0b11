import dayjs from "dayjs";
import utc from "dayjs/plugin/utc";
import { Logger } from "next-axiom";
import { phone } from "phone";
import PocketBase from "pocketbase";
// import { MockPocketBase as PocketBase } from '../tests/mocks/pocketbase-mock';
import type {
  Account,
  Contact,
  IAccountLimitHistory,
  ICampaign,
  IConversation,
  IExpandedAccount,
  IExpandedCampaign,
  IExpandedList,
  IExpandedMessageAndRepliedList,
  IExpandedMessagingLimit,
  IInvoice,
  ILead,
  IMessage,
  IMessagingLimit,
  IMessagingLimitPartial,
  IReferral,
  ISendMessageResponse,
  ITemplateDatabase,
  User,
} from "./types";
import { canadianAreaCodes, parseAndEvaluate, USERNAME } from "./utils";

interface ITemplateObject {
  name: string;
  category: string;
  allow_category_change: boolean;
  language: string;
  components: any;
  id: string;
}

export const getUser = async () => {
  const user = {} as User;
  return user;
};

dayjs.extend(utc);

let pb: PocketBase | null = null;
const log = new Logger();

export let data: any = {};
export const setData = (newData: Record<string, Record<string, any>[]>) => {
  data = newData;
};

export const getPb = async () => {
  if (!pb) {
    pb = new PocketBase(process.env.NEXT_PUBLIC_PB_URL);
    // pb = new PocketBase(data);
    pb.autoCancellation(false);
    await pb
      .collection("_superusers")
      .authWithPassword("<EMAIL>", "Jojo.********");
  }
  if (!pb.authStore.isValid) {
    await pb
      .collection("_superusers")
      .authWithPassword("<EMAIL>", "Jojo.********");
  }
  return pb;
};

export const getAccount = async (accountId: string) => {
  try {
    const pb = await getPb();
    const resultList = await pb
      .collection("accounts")
      .getFirstListItem<IExpandedAccount>(`id="${accountId}"`, {
        expand: "package_tier,pb_user_id",
      });

    return resultList as IExpandedAccount;
  } catch (e) {}
};

export const getPendingDeletionTemplates = async () => {
  try {
    const pb = await getPb();
    const resultList = await pb
      .collection<ITemplateDatabase>("templates")
      .getFullList({
        filter: `status = "PENDING DELETION"`,
      });
    return resultList;
  } catch (error) {
    throw error;
  }
};

export const getCampaignMessages = async (accountId: string) => {
  const pb = await getPb();
  const resultList = await pb
    .collection<IExpandedCampaign>("campaigns")
    .getFullList({
      filter: `account = "${accountId}"`,
      expand: "messages_via_campaign",
    });
  return resultList;
};
// get campaigns based on accountId
export const getCampaign = async (accountId: string) => {
  const pb = await getPb();
  const user = await getUser();
  const userFilterCondition = `${user.type != "admin" && !user.view_all?.includes("campaigns") ? ` && created_by = "${user.id}"` : ""}`;
  const filterCondition = `account = "${accountId}"${userFilterCondition}`;
  const resultList = await pb
    .collection<IExpandedCampaign>("campaigns")
    .getFullList({
      filter: filterCondition,
      expand: "template,leads_list,created_by",
      sort: "-created",
    });
  return resultList;
};

export const getCampaignMessagesById = async (
  campaignId: string,
  delivery_status: string | null = null
) => {
  try {
    const pb = await getPb();
    const filterCondition = delivery_status
      ? `campaign = "${campaignId}" && delivery_status = "${delivery_status}"`
      : `campaign = "${campaignId}"`;
    const resultList = await pb
      .collection<IExpandedMessageAndRepliedList>("messages")
      .getFullList({
        filter: filterCondition,
        expand: "campaign,user,user.replied,template,account",
      });
    return resultList as IExpandedMessageAndRepliedList[];
  } catch (error) {
    throw error;
  }
};

export const setCampaignAsPublished = async (id: string) => {
  const pb = await getPb();
  const res = await pb
    .collection<ICampaign>("campaigns")
    .update(id, { type: "Published" });
  return res;
};

export const setCampaignStatus = async (id: string, status: string) => {
  const pb = await getPb();
  const updatedCampaign = await pb
    .collection<IExpandedCampaign>("campaigns")
    .update(
      id,
      {
        status: status,
      },
      { expand: "account" }
    );
  return updatedCampaign;
};

export const updateCampaignRetryPerformance = async (campaign: ICampaign) => {
  const pb = await getPb();
  const retry_performance = campaign.retry_performance ?? [];
  retry_performance.push({
    retry_attempt: campaign.retry_count,
    delivered_count: campaign.delivered_count,
    sent_count: campaign.sent_count,
    read_count: campaign.read_count,
    timestamp: new Date().toISOString(),
  });
  const updatedCampaign = await pb
    .collection<IExpandedCampaign>("campaigns")
    .update(
      campaign.id,
      {
        retry_performance,
      },
      { expand: "account" }
    );
  return updatedCampaign;
};
// get first template based on name
export const getTemplateFromDbByTemplateName = async ({
  account,
  template_name,
}: {
  account: Account | null;
  template_name: string;
}) => {
  try {
    const pb = await getPb();
    const filter = account
      ? `template_name = "${template_name}" && account.waba_id = "${account.waba_id}"`
      : `template_name = "${template_name}"`;
    const resultList = await pb
      .collection<ITemplateDatabase>("templates")
      .getFirstListItem(filter);
    return resultList;
  } catch (e: any) {
    console.log(e);
    throw e;
  }
};
/**
 * This is adding a text type message
 * This function adds a message to the conversation, and revalidates the path to update the UI
 * Increase the undread count of the conversation, and create a conversation if not already there
 * @param userId lead id
 * @param message message text
 * @param from agent or user
 * @param accountId account id of the business
 * @returns
 */
export const addMessage = async (
  userId: string,
  message: string,
  from: "agent" | "user",
  account_id: string,
  campaign: string | null = null,
  delivery_status:
    | "pending"
    | "read"
    | "sent"
    | "failed"
    | "sent from wetarseel" = "sent from wetarseel",
  template_id: string | null = null,
  interactive_message: string | null = null,
  replied_to: string | null = null,
  created_by: string | null = null,
  referral: IReferral | null = null
) => {
  const pb = await getPb();
  const conversation: IConversation = (await upsertConversation(
    userId,
    account_id
  )) as IConversation;
  const data = {
    user: `${userId}`,
    message: `${message}`,
    account: account_id,
    from: from,
    campaign,
    delivery_status,
    template: template_id,
    interactive_message,
    replied_to,
    //if recieve message is excuted, created_by is null
    created_by: created_by,
    convo_id: conversation.id ?? "",
    referral,
  };

  // add 1 to the undread_count in conversation
  if (from == "user") {
    const record = await pb
      .collection<IMessage>("messages")
      .create(data, { expand: "campaign,template,account,user,created_by" });
    const conversationData: Partial<IConversation> = {
      unread_count: Number(conversation.unread_count) + 1,
      assigned_agent: created_by!,
      message: record.id,
    };
    pb.collection("conversations").update(conversation.id, conversationData);

    return record;
  } else {
    const record = await pb
      .collection<IMessage>("messages")
      .create(data, { expand: "campaign,template,account,user,created_by" });
    const conversationData: Partial<IConversation> = {
      assigned_agent: created_by!,
      message: record.id,
      template: template_id ?? undefined,
    };
    await pb
      .collection("conversations")
      .update(conversation.id, conversationData);

    return record;
  }
};

export const getAccountsWhereLimitEnds = async (filter: string) => {
  const pb = await getPb();
  const resultList = await pb
    .collection("accounts")
    .getFullList<IExpandedAccount>({
      filter,
      expand: "package_tier",
    });
  return resultList;
};

export const getLeadsByListIds = async (listIds: string[]) => {
  const pb = await getPb();
  const filter = listIds.map((id) => `list~"${id}"`).join(" || ");
  let leads = await pb.collection<ILead>("leads").getFullList({
    filter,
  });
  return leads as ILead[];
};

export const upsertNumberUpload = async (
  existingRecord: ILead | null,
  list: string | null,
  account: string,
  phoneNumber: string,
  user: User,
  // batch: BatchService,
  name: string,
  tagsArray: string[],
  shopify_id: string | null,
  status: string,
  country: string,
  listFilters: IExpandedList[] | null,
  meta: Record<string, string> = {}
) => {
  try {
    const pb = await getPb();
    //get lead by phone number and account id
    // const resultList = await pb.collection<ILead>('leads').getList(1, 5, {
    //   filter: `phone_number = "${phoneNumber}" && account = "${account.id}"`,
    // });

    const data = {
      phone_number: phoneNumber,
      account: account,
      name,
      created_by: user.id,
      status: status,
      tags: tagsArray,
      shopify_id,
      country,
      active: true,
      meta,
    };

    let record;
    if (!existingRecord) {
      //if no lead found
      try {
        //get listArrayId to add
        const res = listFilters
          ? addLeadToSmartListOnBulkImport(listFilters, data)
          : { listArrayToAdd: [], listArrayToDelete: [] };
        //spread and create lead
        record = pb.collection("leads").create({
          ...data,
          list: list ? [list, ...res.listArrayToAdd] : [...res.listArrayToAdd],
        });
      } catch (error) {
        console.log(error);
      }
    } else {
      // lead record found
      try {
        //add lead to smart list and remove from exisitn lists based on smart filters
        const res = listFilters
          ? addLeadToSmartListOnBulkImport(listFilters, existingRecord)
          : { listArrayToAdd: [], listArrayToDelete: [] };
        record = pb.collection("leads").update(existingRecord.id, {
          ...data,
          "list+": list
            ? [list, ...res.listArrayToAdd]
            : [...res.listArrayToAdd],
          "list-": res.listArrayToDelete,
        });
      } catch (e) {
        console.log(e);
      }
    }
    return record;
  } catch (error: any) {
    console.error("Error in upsertNumberUpload:", error);
    throw new Error(`Failed to upsert number: ${error.message}`);
  }
};

export const updateLogData = async (
  logId: string,
  number_of_leads: number,
  successfullUploads: any
) => {
  const pb = await getPb();
  await pb.collection("logs").update(logId, {
    number_of_leads: number_of_leads,
    log_data: successfullUploads,
    pending: false,
  });
};

export const upsertLead = async (
  phoneNumber: string,
  account_id: string,
  contacts: [Contact] | undefined = undefined,
  referral: IReferral | null = null
) => {
  const pb = await getPb();

  try {
    const resultList = await pb
      .collection("leads")
      .getFirstListItem<ILead>(
        `phone_number = "${phoneNumber}" && account = "${account_id}"`
      );
    return resultList as unknown as ILead;
  } catch (error) {
    let phoneResult = phone(`+${phoneNumber}`);
    let { countryIso2, countryIso3 } = phoneResult;

    if (phoneNumber.startsWith("1") && phoneNumber.length >= 11) {
      const areaCode = phoneNumber.slice(1, 4);
      if (canadianAreaCodes.has(areaCode)) {
        countryIso2 = "CA";
        countryIso3 = "CAN";
      } else if (!countryIso2) {
        countryIso2 = "US";
        countryIso3 = "USA";
      }
    }

    const data = {
      phone_number: `${phoneNumber}`,
      account: account_id,
      name: contacts?.[0]?.profile?.name ?? "",
      status: "New",
      active: true,
      tags: referral?.source_url
        ? referral.source_type == "ad"
          ? ["Meta Ad"]
          : ["Meta Ad", "Web"]
        : [],
      referral: referral,
      country: countryIso2 ?? countryIso3,
    };

    const record: ILead = (await pb
      .collection<ILead>("leads")
      .create(data)) as ILead;
    await addLeadToSmartList(account_id, record);
    return record;
  }
};

export const markMessageAsSentAndAddWAMID = async (
  message: IMessage,
  messageReponse: ISendMessageResponse
) => {
  try {
    const pb = await getPb();
    const [res, res2] = await Promise.all([
      pb.collection("messages").update(
        message.id,
        {
          delivery_status: "sent from wetarseel",
          wamid: messageReponse.messages[0].id,
        },
        { expand: "created_by" }
      ),
      pb.collection("message_logs").create({
        message_id: message.id,
        wamid: messageReponse.messages[0].id,
        message_body: message,
      }),
    ]);
    return res;
  } catch (error) {
    throw error;
  }
};

export const markMessageAsFailed = async (
  message: IMessage,
  error_message: string
) => {
  try {
    const pb = await getPb();
    const res = await pb
      .collection("messages")
      .update(
        message.id,
        { delivery_status: "failed", error: error_message },
        { expand: "created_by" }
      );
    return res;
  } catch (error) {
    throw error;
  }
};

export const resAddWAMI = async (
  message: IMessage,
  messageReponse: ISendMessageResponse
) => {
  try {
    const pb = await getPb();
    const res = await pb.collection("messages").update(
      message.id,
      {
        wamid: messageReponse.messages[0].id,
        delivery_status: "sent from wetarseel",
      },
      { expand: "created_by, user" }
    );
    return res;
  } catch (error) {
    throw error;
  }
};

export const increaseMessageLimitQuota = async (messageRecord: IMessage) => {
  try {
    const pb = await getPb();
    const userId = messageRecord.created_by;
    const accountId = messageRecord.account;
    const userLimit = await getMessageLimitsByUser(userId, accountId);
    if (userLimit) {
      const res = await pb
        .collection<IMessagingLimit>("messaging_limit")
        .update(userLimit.id, { "remaining_limit+": 1 });
      const res2 = pb
        .collection<Account>("accounts")
        .update(accountId, { "remaining_limit+": 1 });
      return res2;
    } else {
      throw new Error("User Limit not found");
    }
  } catch (error) {
    console.log(error);
    log.error("Error in increasing message limit quota", {
      error: JSON.stringify(error),
    });
  }
};

export const createList = async (
  listName: string,
  accountId: string,
  user: User
) => {
  const pb = await getPb();
  const list = await pb.collection("lists").create({
    name: listName,
    account: accountId,
    created_by: user.id,
    pending: true,
    type: "static",
  });
  return list;
};

//delete in our database
export const deleteTemplate = async (id: string) => {
  try {
    const pb = await getPb();
    return pb.collection("templates").update(id, {
      status: "DELETED",
    });
  } catch (error) {
    console.log(error);
    log.error("Error in marking template as deleted in database", {
      error: JSON.stringify(error),
    });
    throw error;
  }
};

export const createLog = async (
  accountId: string,
  logName: string,
  number_of_leads: number,
  log_data: ILead[],
  user: User
) => {
  const pb = await getPb();
  const log = await pb.collection("logs").create({
    log_name: logName,
    account: accountId,
    number_of_leads: number_of_leads,
    log_data: log_data,
    created_by: user.id,
    pending: true,
  });
  return log;
};

export const updateLeadWithCampaign = async (
  id: string,
  campaign_id: string
) => {
  // console.log("updating lead with campaign");
  const pb = await getPb();
  const res = await pb
    .collection("leads")
    .update(id, { "campaign_history+": campaign_id });
  // console.log("call");
  // const resultList = await fetch(
  //   `${process.env.NEXT_PUBLIC_BUN_SERVER_URL}/update-lead-with-campaign`,
  //   {
  //     method: "POST",
  //     headers: {
  //       "Content-Type": "application/json",
  //     },
  //     body: JSON.stringify({ id, campaign_id }),
  //   }
  // );
  // const pb = await getPb();
  // console.log(resultList);
  // const res = await pb
  //   .collection("leads")
  //   .update(id, { "campaign_history+": campaign_id });
};

export const generateInvoice = async (
  lastInvoiceId: string,
  fees: number,
  tax: number,
  charges: number,
  businessId: string,
  billingMonths: number,
  dueDateLimit: number,
  lastInvoiceRenewalDate: Date,
  from: string,
  isfirstTimeInvoice: boolean,
  accountId?: string
) => {
  // console.log("lastInvoice Renewal date is ")
  // console.log(dayjs(lastInvoiceRenewalDate))
  console.log("after adding date is");
  console.log(dayjs(lastInvoiceRenewalDate).add(billingMonths, "month"));
  try {
    const pb = await getPb();
    const wt_id = await getLatest_WT_ID();

    // Create the new invoice first
    const result = await pb.collection("invoice").create({
      amount_due: (fees * billingMonths + charges + tax).toFixed(2),
      unit_amount: fees.toFixed(2),
      tax: tax.toFixed(2),
      charges: charges.toFixed(2),
      payment_status: "due",
      billing_months: billingMonths,
      invoice_limit: dueDateLimit,
      account_id: businessId,
      invoice_valid_from: lastInvoiceRenewalDate
        ? dayjs(lastInvoiceRenewalDate).add(1, "day")
        : dayjs(),
      is_first_invoice: isfirstTimeInvoice,
      wt_id: wt_id + 1,
      //if it is first time invoice then renewal date will be null, as it will be updated after user pays the subscription amount but
      //if it is not the first invoice then renewal date will be current date plus billing duration
      renewal_date: isfirstTimeInvoice
        ? null
        : dayjs(lastInvoiceRenewalDate).add(billingMonths, "month"),
      // renewal_date: isfirstTimeInvoice
      //   ? null
      //   : dayjs().add(billingMonths, "month")
    });

    // Only mark the previous invoice as completed after successfully creating the new one
    if (lastInvoiceId) {
      try {
        await invoiceCompleted(lastInvoiceId);
      } catch (completionError) {
        console.error(
          `Failed to mark invoice ${lastInvoiceId} as completed:`,
          completionError
        );
        // Don't throw here to avoid rolling back the new invoice creation
        // The system will detect this on the next run
      }
    }

    // Update account subscription status if provided
    if (accountId) {
      try {
        await pb
          .collection("accounts")
          .update(accountId, { subscription_active: true });
      } catch (updateError) {
        console.error(
          `Failed to update subscription status for account ${accountId}:`,
          updateError
        );
        // Continue - this is not critical for invoice generation
      }
    }

    return result;
  } catch (err) {
    console.error("Error in generateInvoice:", err);
    throw err;
  }
};

export const changeInvoiceStatus = async (
  invoiceId: string,
  status: string
) => {
  try {
    const pb = await getPb();
    const result = await pb
      .collection("invoice")
      .update(invoiceId, { payment_status: status });
  } catch (err) {
    console.log(err);
  }
};

export const enableAutoRenewal = async (invoiceId: string) => {
  try {
    const pb = await getPb();
    const result = await pb
      .collection("invoice")
      .update(invoiceId, { auto_renewal: true });
  } catch (err) {
    console.log(err);
  }
};

export const disableAutoRenewal = async (invoiceId: string) => {
  try {
    const pb = await getPb();
    const result = await pb
      .collection("invoice")
      .update(invoiceId, { auto_renewal: false });
  } catch (err) {
    console.log(err);
  }
};

export const invoiceCompleted = async (invoiceId: string) => {
  try {
    const pb = await getPb();
    const result = await pb
      .collection("invoice")
      .update(invoiceId, { completed: true });
  } catch (err) {
    console.log(err);
    throw err;
  }
};

export const getInvoiceById = async (invoiceId: string) => {
  try {
    const pb = await getPb();
    const invoice = pb.collection<IInvoice>("invoice").getOne(invoiceId);
    return invoice;
  } catch (err) {
    throw err;
    console.log(err);
  }
};

export const getUserLimits = async (accountId: string) => {
  try {
    const pb = await getPb();
    const result = await pb
      .collection<IExpandedMessagingLimit>("messaging_limit")
      .getFullList({
        filter: `account = "${accountId}" && user.username != "${USERNAME}"`,
        expand: "user",
      });
    return result;
  } catch (error) {
    console.log("error");
    throw error;
  }
};

export const getMessageLimitsByUser = async (
  userId: string,
  accountId: string | undefined = undefined
) => {
  try {
    const pb = await getPb();
    let filter = `user = "${userId}"`;
    if (accountId) {
      filter = `user = "${userId}" && account = "${accountId}"`;
    }
    const res = await pb
      .collection<IMessagingLimit>("messaging_limit")
      .getFirstListItem<IMessagingLimit>(filter);
    return res;
  } catch (error) {
    return null;
  }
};

export const updateAccountRemainingLimit = async (
  accountId: string,
  totalContacts: number
) => {
  const pb = await getPb();
  // const accountLimit = await getAccountLimit(accountId)
  // const remaining_limit = accountLimit.remaining_limit - totalContacts
  const res = await pb
    .collection<Account>("accounts")
    .update(accountId, { "remaining_limit-": totalContacts });
};

export const updateUserRemainingLimit = async (
  userId: string,
  totalContacts: number,
  accountId: string | undefined = undefined
) => {
  try {
    const pb = await getPb();
    const userLimit: IMessagingLimit = (await getMessageLimitsByUser(
      userId,
      accountId
    )) as IMessagingLimit;
    if (userLimit) {
      // const remaining_limit = userLimit.remaining_limit - totalContacts;
      const res = await pb
        .collection<IMessagingLimit>("messaging_limit")
        .update(userLimit.id, { "remaining_limit-": totalContacts });
    } else {
      throw new Error("User limit not found");
    }
  } catch (error) {
    throw error;
  }
};

export const updateAccountWithPackageId = async (
  accountId: string,
  packageId: string,
  remaining_limit: number,
  period: string
) => {
  const pb = await getPb();
  let limit_start_date = dayjs();
  let limit_end_date = dayjs();
  if (period == "monthly") {
    limit_end_date = limit_start_date.add(1, "month");
  }
  const res = await pb.collection<Account>("accounts").update(accountId, {
    package_tier: packageId,
    remaining_limit,
    unutilized_limit: 0,
    limit_start_date,
    limit_end_date,
  });
  const allAdmins: IMessagingLimit[] = (await pb
    .collection<IMessagingLimit>("messaging_limit")
    .getFullList({
      filter: `account = "${accountId}" && user.type = "admin"`,
    })) as IMessagingLimit[];
  allAdmins.map(async (item) => {
    const res2 = await pb
      .collection<Account>("messaging_limit")
      .update(item.id, {
        remaining_limit: item.assigned_limit, //set this to remaining limit if resetting properly
        assigned_limit: item.assigned_limit, //set this to remaining_limit if resetting properly
        limit_start_date,
        limit_end_date,
        period,
      });
  });

  const allAgents: IMessagingLimit[] = (await pb
    .collection<IMessagingLimit>("messaging_limit")
    .getFullList({
      filter: `account = "${accountId}" && user.type = "agent"`,
    })) as IMessagingLimit[];
  allAgents.map(async (item) => {
    const res2 = await pb
      .collection<Account>("messaging_limit")
      .update(item.id, {
        remaining_limit: item.assigned_limit, //change this to 0 if resetting properly
        assigned_limit: item.assigned_limit, //change this to 0 if resetting properly
        limit_start_date,
        limit_end_date,
        period,
      });
  });
};

export const getCampaignById = async (campaignId: string) => {
  const pb = await getPb();
  const resultList = await pb
    .collection<IExpandedCampaign>("campaigns")
    .getOne<IExpandedCampaign>(campaignId, {
      expand: "template, leads_list, created_by, account",
    });
  return resultList;
};

export const addToAccountLimitHistory = async (
  accountId: string,
  message_limit: number,
  remaining_messages: number,
  agents_admin_data: IMessagingLimitPartial[]
) => {
  try {
    const pb = await getPb();
    await pb.collection<IAccountLimitHistory>("account_limit_history").create({
      date: dayjs(),
      account: accountId,
      message_limit,
      remaining_messages,
      agents_admin_data,
    });
  } catch (error) {
    throw error;
  }
};

export const updateCampaignRetryFailed = async (id: string, reason: string) => {
  try {
    const pb = await getPb();
    const res = await pb.collection("campaigns").update(id, {
      retry_status: "failed",
      retry_status_reason: reason,
    });
    return res;
  } catch (error) {
    throw error;
  }
};

export const updateCampaignRetryCount = async (
  id: string,
  next_retry_date: Date
) => {
  try {
    const pb = await getPb();
    const res = await pb.collection<IExpandedCampaign>("campaigns").update(
      id,
      {
        "retry_count+": 1,
        next_retry_date: dayjs(next_retry_date)
          .add(1, "day")
          .toDate()
          .toUTCString(),
      },
      {
        expand: "account",
      }
    );
    return res;
  } catch (error) {
    throw error;
  }
};

export const getAllInvoicesList = async () => {
  try {
    const pb = await getPb();
    const invoices = await pb
      .collection<IInvoice>("invoice")
      .getFullList({ expand: "account_id", sort: "-invoice_valid_from" });
    return invoices;
  } catch (err) {
    console.log(err);
  }
};

/**
 * Finds or creates a conversation record for the user
 * @param from the lead id of the person who is messaging us
 * @param accountId the account id of the business
 * @returns the conversation record
 */
export const upsertConversation = async (from: string, accountId: string) => {
  const pb = await getPb();
  try {
    const resultList = await pb
      .collection("conversations")
      .getFirstListItem(`from = "${from}" && account="${accountId}"`);
    return resultList;
  } catch (e) {
    const result = await pb.collection<IConversation>("conversations").create({
      from,
      unread_count: 0,
      account: accountId,
    });
    return result;
  }
};

export const addLeadToSmartListOnBulkImport = (
  listFilters: IExpandedList[],
  data: { country: string; tags: string[]; name: string; status: string }
) => {
  try {
    let listArrayToAdd: string[] = [];
    let listArrayToDelete: string[] = [];

    listFilters.forEach((listFilter) => {
      if (
        parseAndEvaluate(listFilter.expand.list_filters.parse_string, {
          country: data.country,
          tags: data.tags,
          name: data.name,
          status: data.status,
        })
      ) {
        listArrayToAdd.push(listFilter.id);
      } else {
        listArrayToDelete.push(listFilter.id);
      }
    });
    return { listArrayToDelete, listArrayToAdd };
  } catch (error) {
    throw error;
    console.log(error);
  }
};

export const getLatest_WT_ID = async () => {
  try {
    const pb = await getPb();
    const res = (await pb
      .collection<IInvoice>("invoice")
      .getFullList({ sort: "-created" })) as IInvoice[];
    let wt_id = 0;
    if (res.length > 0) {
      wt_id = res[0].wt_id;
    }
    return wt_id;
  } catch (error) {
    console.log(error);
    throw error;
  }
};

export const addLeadToSmartList = async (
  accountId: string,
  data: {
    id: string;
    country: string;
    tags: string[];
    name: string;
    status: string;
  }
) => {
  try {
    let listArrayToAdd: string[] = [];
    let listArrayToDelete: string[] = [];
    const pb = await getPb();
    const listFilters = (await pb
      .collection<IExpandedList>("lists")
      .getFullList({
        // filter: `account = "${accountId}" && created_by = "${userId}" && list_filters != null`,
        filter: `account = "${accountId}" && list_filters != null`,
        expand: "list_filters",
      })) as IExpandedList[];
    listFilters.forEach((listFilter) => {
      if (
        parseAndEvaluate(listFilter.expand.list_filters.parse_string, {
          country: data.country,
          tags: data.tags,
          name: data.name,
          status: data.status,
        })
      ) {
        listArrayToAdd.push(listFilter.id);
      } else {
        listArrayToDelete.push(listFilter.id);
      }
    });
    await pb
      .collection("leads")
      .update(data.id, { "list-": listArrayToDelete, "list+": listArrayToAdd });
  } catch (error) {
    console.log(error);
  }
};

export const createLeadForShopify = async ({
  phone_number,
  account_id,
  name,
  user_id,
  country_code,
}: {
  phone_number: string;
  account_id: string;
  name: string;
  user_id: string;
  country_code: string;
}) => {
  const pb = await getPb();
  try {
    const data = {
      phone_number: phone_number.replace("+", ""),
      account: account_id,
      name,
      status: "New",
      active: true,
      country: country_code,
      created_by: user_id,
    };

    const record = await pb.collection<ILead>("leads").create(data);
    // await addLeadToSmartList(account_id, record);
    return record.id;
  } catch (error) {
    console.error("Error in creating lead for Shopify:", error);
    return null;
  }
};
/**
 * Creates or updates payment records and transaction logs for invoices
 *
 * This function handles the entire payment processing workflow:
 * 1. Updates invoice status and renewal dates
 * 2. Creates or updates payment records
 * 3. Creates transaction logs for audit purposes
 *
 * @param paymentMethod - The method used for payment (e.g., 'on-system', 'off-system', 'automatic-stripe', 'saved-method')
 * @param businessId - The ID of the business/account making the payment
 * @param invoiceArray - Array of invoices being paid
 * @param status - Payment status ('success' or 'failed')
 * @param paymentError - Error message if payment failed (optional)
 * @param paymentDate - Date when payment was made (optional, defaults to current date)
 * @param accountId - Account ID for redirection after payment (optional)
 * @param payRef - Payment reference/ID from payment processor (optional)
 */
export const createPayment = async (
  paymentMethod: string,
  businessId: string,
  invoiceArray: IInvoice[],
  status: string,
  paymentError?: string,
  paymentDate?: Date,
  accountId?: string,
  payRef?: string
) => {
  try {
    const pb = await getPb();
    const currentDate = dayjs(); // Current date for transaction records

    // Handle successful payments
    if (status == "success") {
      // Process each invoice in the array
      for (let invoice of invoiceArray) {
        // Handle first-time invoices differently
        if (invoice.is_first_invoice) {
          if (paymentMethod == "off-system") {
            // For manual/off-system payments, use the provided payment date
            await pb.collection("invoice").update(invoice.id, {
              // Set renewal date based on provided payment date
              renewal_date: dayjs(paymentDate).add(
                invoice.billing_months,
                "month"
              ),
              payment_status: "paid",
              invoice_pdf: null,
            });
          } else {
            // For automatic/on-system payments, use current date
            await pb.collection("invoice").update(invoice.id, {
              // Set renewal date based on current date
              renewal_date: dayjs().add(invoice.billing_months, "month"),
              payment_status: "paid",
              invoice_pdf: null,
            });
          }
        } else {
          // For recurring invoices (not first-time)
          await pb.collection("invoice").update(invoice.id, {
            // Set renewal date based on invoice valid from date
            renewal_date: dayjs(invoice.invoice_valid_from).add(
              invoice.billing_months,
              "month"
            ),
            payment_status: "paid",
            invoice_pdf: null,
          });
        }

        // Check if a payment record already exists for this invoice
        const paymentExists = await pb
          .collection("payment")
          .getFullList({ filter: `invoice_id="${invoice.id}"` });

        if (paymentExists.length > 0) {
          // Update existing payment record
          const payment = await pb
            .collection("payment")
            .update(paymentExists[0].id, {
              payment_status: "paid",
              payment_ref: payRef,
            });

          // Create transaction log for audit purposes
          const transaction = await pb.collection("transaction_log").create({
            account_id: businessId,
            payment_amount: invoice.amount_due,
            payment_id: payment.id,
            transaction_date: paymentDate ?? currentDate, // Use provided date or current date
            error: null,
            payment_method: paymentMethod,
            payment_ref: payRef,
          });
        } else {
          // Create new payment record
          const payment = await pb.collection("payment").create({
            account_id: businessId,
            invoice_id: invoice.id,
            amount_paid: invoice.amount_due,
            payment_status: "paid",
            unit_amount: invoice.unit_amount,
            tax: invoice.tax,
            charges: invoice.charges,
            payment_ref: payRef,
          });

          // Create transaction log for audit purposes
          await pb.collection("transaction_log").create({
            account_id: businessId,
            payment_amount: invoice.amount_due,
            payment_id: payment.id,
            transaction_date: paymentDate ?? currentDate, // Use provided date or current date
            error: null,
            payment_method: paymentMethod,
            payment_ref: payRef,
          });
        }
      }
    } else {
      // Handle failed payments
      for (let invoice of invoiceArray) {
        // Check if a payment record already exists for this invoice
        const paymentExists = await pb
          .collection("payment")
          .getFullList({ invoice_id: invoice.id });

        if (paymentExists.length > 0) {
          // Update existing payment record to failed status
          const payment = await pb
            .collection("payment")
            .update(paymentExists[0].id, {
              payment_status: "failed",
            });

          // Create transaction log with error information
          await pb.collection("transaction_log").create({
            account_id: businessId,
            payment_amount: invoice.amount_due,
            payment_id: payment.id,
            transaction_date: paymentDate ?? currentDate, // Use provided date or current date
            error: paymentError, // Include error message
            payment_method: paymentMethod,
          });
        } else {
          // Create new payment record with failed status
          const payment = await pb.collection("payment").create({
            account_id: businessId,
            invoice_id: invoice.id,
            amount_paid: invoice.amount_due,
            payment_status: "failed",
            unit_amount: invoice.unit_amount,
            tax: invoice.tax,
            charges: invoice.charges,
            payment_ref: payRef,
          });

          // Create transaction log with error information
          await pb.collection("transaction_log").create({
            account_id: businessId,
            payment_amount: invoice.amount_due,
            payment_id: payment.id,
            transaction_date: paymentDate ?? currentDate, // Use provided date or current date
            error: paymentError, // Include error message
            payment_method: paymentMethod,
            payment_ref: payRef,
          });
        }
      }
    }
  } catch (error) {
    console.log(error); // Log the error
    throw error; // Re-throw to allow caller to handle
  }
};
