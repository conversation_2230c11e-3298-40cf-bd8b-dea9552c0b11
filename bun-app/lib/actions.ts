"use server";

import PocketBase from "pocketbase";
import {
  createList,
  createLog,
  getAccount,
  getPb,
  getUser,
  updateLogData,
  upsertNumberUpload,
} from "./pocket";

import parsePhoneNumber from "libphonenumber-js";
import type { IExpandedList, ILead, User } from "./types.ts";
import type { MockPocketBase } from "../tests/mocks/pocketbase-mock.ts";

export const prepareUploadLeads = async (
  accountId: string,
  transformedLeads: ILead[],
  fileName: string,
  rowCount: number,
  listId: string | undefined,
  user: User,
  pb: PocketBase
) => {
  let listFilters = null;
  try {
    listFilters = await pb.collection<IExpandedList>("lists").getFullList({
      // filter: `account = "${accountId}" && created_by = "${userId}" && list_filters != null`,
      filter: `account = "${accountId}" && list_filters != null`,
      expand: "list_filters",
    });
  } catch (error) {
    console.log(error, "error gettin list filters");
  }

  const logMessage = `${fileName}`;

  const log = await createLog(
    accountId,
    logMessage,
    rowCount,
    transformedLeads,
    user
  );
  let _listId;
  if (listId) {
    _listId = listId;
  } else {
    const list = await createList(logMessage, accountId, user);
    _listId = list.id;
  }

  let _transformedLeads = transformedLeads.filter(
    ({ phone_number }) => phone_number
  );
  const start = new Date().getTime();

  return { log, _listId, _transformedLeads, listFilters, start };
};

export const uploadOneLead = async ({
  pb,
  account,
  listFilters,
  _listId,
  chunk,
  user,
}: {
  pb: PocketBase;
  account: string;
  listFilters: IExpandedList[] | null;
  log: any;
  _listId: string | null;
  chunk: ILead[];
  user: User;
}) => {
  let result: any[] = [];
  try {
    // use measurement to measure time spent
    const start = new Date().getTime();
    const filterString = chunk
      .map((lead) => {
        const phone_num = lead.phone_number.replace(/\D/g, "").trim();
        return `(phone_number = "${phone_num}" && account = "${account}")`;
      })
      .join(" || ");
    const resultList = await pb.collection<ILead>("leads").getFullList({
      filter: filterString,
      skipTotal: true,
      fields: "id, phone_number, country, tags, name, status",
    });
    chunk.forEach(async (lead) => {
      const phone_num = lead.phone_number.replace(/\D/g, "").trim();
      const leadName = lead.name.length === 0 ? phone_num : lead.name;
      let phoneOutput = parsePhoneNumber(`+${phone_num}`, "US");
      if (!phoneOutput?.country) {
        result.push({
          status: "failure",
          lead,
          error: "The phone number is invalid",
        });
      } else if (!phoneOutput.isValid()) {
        result.push({
          status: "failure",
          lead,
          error: "The phone number is invalid",
        });
      } else {
        let tagsArray = lead.tags ? lead.tags : [];
        const { tags, country, name, status, phone_number, ...rest } = lead;
        let existingRecord =
          resultList.find(
            (result) => (result as ILead).phone_number === phone_num
          ) ?? null;
        upsertNumberUpload(
          existingRecord as ILead,
          _listId,
          account!,
          phone_num,
          user,
          leadName,
          tagsArray,
          lead.shopify_id,
          lead.status,
          phoneOutput.country,
          listFilters,
          rest as unknown as Record<string, string>
        );
        // time taken
        const end = new Date().getTime();
        result.push({ status: "success", lead });
      }
    });
  } catch (error: any) {
    console.log(error);
    // result = { status: 'failure', lead, error: error.message };
  }
  return result;
};

export const finishUploadingLeads = async ({
  start,
  _listId,
  results,
  log,
}: {
  start: any;
  _listId: string;
  results: any[];
  log: any;
}) => {
  const end = new Date().getTime();
  const pb = await getPb();
  console.log("Time taken in upload leads:", end - start);
  const _results = results.filter(
    (item: any, index: number, self: any) =>
      index ===
      self.findIndex((t: any) => t.lead.phone_number === item.lead.phone_number)
  );
  const logs = _results.map((item: any) => {
    return {
      name: item.lead.name,
      phone_number: item.lead.phone_number,
      tags: item.lead.tags,
      status: item.lead.status,
      upload: item.status == "failure" ? "failed" : "successful",
      rejection_reason: item.status == "failure" ? item.error : null,
    };
  });
  const failed = _results.filter((result: any) => result.status === "failure");
  const numberSuccessfull = _results.filter(
    (result: any) => result.status === "success"
  ).length;
  const numberFailed = failed.length;
  const failedLeads = failed.map((result: any) => ({
    phone_number: result.lead.phone_number,
    reason: result.error,
  }));

  // remove duplicates from logs

  await updateLogData(log.id, numberSuccessfull, logs);
  await pb.collection("lists").update(_listId, { pending: false });
  return { numberSuccessfull, numberFailed, failedLeads };
};

export const createShopifyCustomer = async ({
  shop,
  customer,
}: {
  shop: string;
  customer: ICreateShopifyCustomer;
}) => {
  const pb = await getPb();
  try {
    const rec = await pb
      .collection("shopify_accounts")
      .getFirstListItem(`store_details.myshopifyDomain = "${shop}"`);
    console.log(`Found Shopify account: ${rec.id}`);

    const user = await pb
      .collection<User>("users")
      .getFirstListItem(`email = "${rec.store_details.email}"`);
    console.log(`Found associated user: ${user.id}`);

    const newLead = {} as ILead;
    if (!customer.first_name) {
      newLead.name = customer.phone;
    } else {
      newLead.name = `${customer.first_name} ${customer.last_name}`;
    }
    newLead.phone_number = customer.phone;
    newLead.status = "New";
    newLead.shopify_id = customer.id.toString();

    const result = await uploadOneLead({
      pb,
      account: rec.account,
      listFilters: null,
      chunk: [newLead],
      _listId: null,
      log: null,
      user: user,
    });

    return {
      success: true,
      accountId: rec.account,
    };
  } catch (error) {
    console.error("Shopify sync error:", {
      shop,
      error,
      timestamp: new Date().toISOString(),
    });
    throw error;
  }
};

export interface ISyncShopifyCustomer {
  id: string;
  firstName: string;
  lastName: string;
  email: string;
  phone: string;
  defaultAddress: {
    address1: string;
    address2: string | null;
    city: string;
    province: string | null;
    country: string;
    zip: string;
    phone: string | null;
  };
  tags: string[];
  lifetimeDuration: string;
  createdAt: Date;
  updatedAt: Date;
}

export interface ISyncShopifyCustomerResponse {
  shop: string;
  customers: any[];
  isBatch: boolean;
  batchSize: number;
  totalProcessed: number;
}

export interface ICreateShopifyCustomer {
  id: number;
  email: string;
  created_at: string;
  updated_at: string;
  first_name: string;
  last_name: string;
  state: "enabled" | "disabled" | "invited" | "declined";
  note: string;
  verified_email: boolean;
  multipass_identifier: string | null;
  tax_exempt: boolean;
  currency: string;
  phone: string;
  addresses: ShopifyAddress[];
  tax_exemptions: string[];
  admin_graphql_api_id: string;
  default_address: ShopifyAddress;
}

export interface ShopifyAddress {
  id: number;
  customer_id: number;
  first_name: string;
  last_name: string;
  company: string;
  address1: string;
  address2: string;
  city: string;
  province: string;
  country: string;
  zip: string;
  phone: string;
  name: string;
  province_code: string | null;
  country_code: string;
  country_name: string;
  default: boolean;
}
