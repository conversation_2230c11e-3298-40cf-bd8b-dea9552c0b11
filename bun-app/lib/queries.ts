import { Database } from "bun:sqlite";

/**
 * Gets conversations with their last messages and related information
 * This query retrieves conversations with detailed information about the last message,
 * lead details, and assigned agent information
 *
 * @param db SQLite database instance
 * @param accountId Optional account ID to filter conversations by account
 * @returns Array of conversation objects with detailed information
 */
export function getConversationsWithLastMessage(
  db: Database,
  accountId: string
) {
  const query = `SELECT
    c.id AS convo_id,
    c.account,
    c."from",
    c.assigned_agent,
    c.chat_archive,
    c.unread_count,
    c.updated,
    lm.message AS last_message,
    lm.id AS last_message_id,
    lm.created AS last_message_created,
    lm.delivery_status AS last_message_delivery_status,
    lm."from" AS last_message_from,
    lm.type AS last_message_type,
    l.*,
    -- Explicit aliasing to avoid column conflicts
    aa.name AS agent_name,
    aa.team AS team_id,
    t.name as team_name
  FROM
    conversations c
  LEFT JOIN
    leads l ON c."from" = l.id  -- LEFT JOIN prevents duplication if no lead exists
  LEFT JOIN
    users aa ON c.assigned_agent = aa.id  -- LEFT JOIN prevents row multiplication
  INNER JOIN
    messages lm ON c.message = lm.id
  LEFT JOIN
    teams t on t.id = aa.team
  WHERE
    c.account = $accountId
    AND EXISTS (
      SELECT 1 FROM messages m
      WHERE m.convo_id = c.id AND m."from" = 'user'
    )
  ORDER BY
    c.unread_count DESC`;

  return db.query(query).all({ $accountId: accountId });
}
