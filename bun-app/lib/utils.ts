import jsep from "jsep";

export const USERNAME = "Super-User";

type Context = {
  [key: string]: string | string[]; // Context contains variables with single or multiple values
};

// Recursive function to evaluate the AST
function evaluateAST(ast: any, context: any): boolean {
  switch (ast.type) {
    case "Literal":
      return ast.value;
    case "Identifier":
      return context[ast.name] || false;
    case "BinaryExpression":
      const left = evaluateAST(ast.left, context);
      const right = evaluateAST(ast.right, context);
      switch (ast.operator) {
        case "&&":
          return left && right;
        case "||":
          return left || right;
        case "==":
          return left == right;
        case "===":
          return left === right;
        default:
          throw new Error(`Unsupported operator: ${ast.operator}`);
      }
    default:
      throw new Error(`Unsupported AST node type: ${ast.type}`);
  }
}

// Function to parse and evaluate logical expressions with variables from context
export const parseAndEvaluate = (
  expression: string,
  context: Context
): boolean => {
  // Replace variables in the expression
  const parsedExpression = expression.replace(
    /\b[a-zA-Z_][a-zA-Z0-9_ ]*\b/g,
    (match) => {
      if (context[match] !== undefined) {
        const value = context[match];
        if (Array.isArray(value)) {
          // If the variable is an array, generate a valid comparison expression
          if (value.length > 0) {
            return `${value.map((v) => `"${v.toLowerCase()}"`).join(" || ")}`;
          } else {
            return "''";
          }
        }
        return `"${value.toLowerCase()}"`; // Single value replacement
      }
      return `${match.toLowerCase()}`; // Default for undefined variables
    }
  );

  // Step 2: Evaluate the final logical expression
  try {
    const ast = jsep(parsedExpression); // Parse the expression into an AST
    return evaluateAST(ast, context);
  } catch (error) {
    return false;
  }
};

export const canadianAreaCodes = new Set([
  "204",
  "226",
  "236",
  "249",
  "250",
  "289",
  "306",
  "343",
  "365",
  "387",
  "403",
  "416",
  "418",
  "431",
  "437",
  "438",
  "450",
  "506",
  "514",
  "519",
  "579",
  "581",
  "587",
  "604",
  "613",
  "639",
  "647",
  "672",
  "705",
  "709",
  "742",
  "778",
  "780",
  "782",
  "807",
  "819",
  "825",
  "867",
  "873",
  "902",
  "905",
]);
