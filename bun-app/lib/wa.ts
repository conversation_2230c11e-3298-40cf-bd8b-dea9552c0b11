import type {
  ISendMessageErrorResponse,
  ISendMessageResponse,
} from "./types.ts";

export async function actually_send_message(
  data: Record<string, any>,
  phone_id: string
): Promise<ISendMessageResponse | ISendMessageErrorResponse> {
  const myHeaders = new Headers();
  myHeaders.append("Content-Type", "application/json");
  myHeaders.append(
    "Authorization",
    `Bearer ${process.env.CLOUD_API_ACCESS_TOKEN}`
  );
  myHeaders.append("Cache-Control", "no-cache");
  const raw = JSON.stringify(data);
  const requestOptions = {
    method: "POST",
    headers: myHeaders,
    body: raw,
  };

  const response = await fetch(
    `https://graph.facebook.com/v19.0/${phone_id}/messages`,
    requestOptions
  );
  const result: ISendMessageResponse = await response.json();
  return result;
}

export async function send_template({
  recipient_number,
  template,
  phoneId,
  components,
  templateLang,
}: Record<string, any>): Promise<
  ISendMessageResponse | ISendMessageErrorResponse
> {
  try {
    const data = {
      messaging_product: "whatsapp",
      to: `${recipient_number}`,
      type: "template",
      template: {
        name: template,
        language: {
          code: templateLang,
        },
        components: components,
      },
    };
    //No need to update template id in conversation as template id is already present.
    // update the template_id in the conversation
    // const conversation = await pb.collection('conversations').getFirstListItem(`from.phone_number = "${recipient_number}" && account.id = "${accountId}"`);
    // if (conversation) {
    //   await pb.collection('conversations').update(conversation.id, {
    //     template: template_id,
    //     message: message_id,
    //   });
    // }
    return await actually_send_message(data, phoneId);
  } catch (e) {
    console.log("error");

    console.log(e);
    throw e;
  }
}
