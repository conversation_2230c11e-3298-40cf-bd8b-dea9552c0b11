import { _spawn, _spawnBuffer } from './_base'
import { audioArgs } from './audio-args'
import type { FfmpegAudioOptions, FfmpegAudioOptionsWithStreamOut } from './types'

export async function audio(input: string, output: string, options?: FfmpegAudioOptions) {
  try {
    await _spawn({ args: ['ffmpeg', '-i', input, ...audioArgs(options), '-y', output] })
  }
  catch (_) {
    options?.onError?.(_)
  }
}

export async function audioWithStreamInput(input: ReadableStream<Uint8Array>, output: string, options?: FfmpegAudioOptions): Promise<void> {
  try {
    await _spawn({ args: ['ffmpeg', '-i', 'pipe:0', ...audioArgs(options), '-y', output], input })
  }
  catch (_) {
    options?.onError?.(_)
  }
}

export async function audioWithStreamOut(input: string, output: FfmpegAudioOptionsWithStreamOut, options?: FfmpegAudioOptions): Promise<void> {
  try {
    await _spawn({ args: ['ffmpeg', '-i', input, ...audioArgs(options), '-f', 'wav', 'pipe:1'], output })
  }
  catch (_) {
    options?.onError?.(_)
  }
}

export async function audioWithStreamInputAndOut(input: ReadableStream<Uint8Array>, output: FfmpegAudioOptionsWithStreamOut, options?: FfmpegAudioOptions): Promise<void> {
  try {
    await _spawn({ args: ['ffmpeg', '-i', 'pipe:0', ...audioArgs(options), '-f', 'wav', 'pipe:1'], input, output })
  }
  catch (_) {
    options?.onError?.(_)
  }
}

export async function audioWav(buffer: Uint8Array): Promise<Uint8Array> {
  const options = {
    codec: 'pcm_s16le',
    bitrate: '128k',
    channels: 1,
    sampleRate: 16000,
  }
  return await _spawnBuffer({
    args: ['ffmpeg', '-i', 'pipe:0', ...audioArgs(options), '-f', 'wav', 'pipe:1'],
    input: buffer,
  })
}

/**
 * Convert audio from a Blob/File to a specified output file.
 * @param inputBlob The input Blob or File (e.g., from form upload)
 * @param output The output file path
 * @param options FfmpegAudioOptions
 */
export async function audioFromBlob(inputBlob: Blob, output: string, options?: FfmpegAudioOptions) {
  try {
    const buffer = new Uint8Array(await inputBlob.arrayBuffer());
    // Use the stream input variant
    const stream = new ReadableStream<Uint8Array>({
      start(controller) {
        controller.enqueue(buffer);
        controller.close();
      }
    });
    await audioWithStreamInput(stream, output, options);
  } catch (_) {
    options?.onError?.(_);
  }
}

/**
 * Convert audio from a Blob/File and return the converted buffer.
 * @param inputBlob The input Blob or File (e.g., from form upload)
 * @param options FfmpegAudioOptions
 * @param format Output format, e.g. 'ogg', 'wav'
 * @returns Uint8Array of converted audio
 */
export async function audioFromBlobToBuffer(
  inputBlob: Blob,
  options?: FfmpegAudioOptions,
  format: string = "ogg"
): Promise<Uint8Array> {
  try {
    const buffer = new Uint8Array(await inputBlob.arrayBuffer());
    // Use _spawnBuffer to get the output as Uint8Array
    return await _spawnBuffer({
     args: [
  'ffmpeg',
  '-i', 'pipe:0',
  '-c:a', 'libopus',
  '-b:a', '64k',
  '-ac', '1',
  '-ar', '48000',
  '-f', 'ogg',
  'pipe:1'
],
      input: buffer,
    });
  } catch (_) {
    options?.onError?.(_);
    throw _;
  }
}
