export function setupWebSocketClient() {
  const ws = new WebSocket(`ws://${window.location.host}/ws`);
  
  ws.onopen = () => {
    console.log('WebSocket connection established');
  };

  ws.onmessage = (event) => {
    console.log('WebSocket message received:', event.data);
    const { type, data } = JSON.parse(event.data);
    
    // Dispatch custom event that components can listen to
    window.dispatchEvent(new CustomEvent('queue-update', {
      detail: { type, data }
    }));
  };

  ws.onerror = (error) => {
    console.error('WebSocket error:', error);
  };

  ws.onclose = () => {
    console.log('WebSocket connection closed');
    // Attempt to reconnect after 5 seconds
    setTimeout(setupWebSocketClient, 5000);
  };

  return ws;
}