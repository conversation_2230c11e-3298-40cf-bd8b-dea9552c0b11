import React from "react";

interface TemplateProps {
  queueStats: {
    waiting: any[];
    active: any[];
  };
}

export function Template({ queueStats }: TemplateProps) {
  return (
    <html lang="en">
      <head>
        <meta charSet="UTF-8" />
        <meta name="viewport" content="width=device-width, initial-scale=1.0" />
        <title>Queue Dashboard</title>
        <link rel="preconnect" href="https://fonts.googleapis.com" />
        <link rel="preconnect" href="https://fonts.gstatic.com" crossOrigin="" />
        <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet" />
        <style>{`
          body {
            font-family: 'Inter', sans-serif;
            margin: 0;
            padding: 20px;
            background: #f5f5f5;
          }
          .dashboard {
            max-width: 1200px;
          }
          .stats-container {
            display: grid;
            gap: 20px;
            margin-top: 20px;
          }
          .job-table {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
          }
          table { 
            border-collapse: collapse; 
            width: 100%;
          }
          th, td { 
            border: 1px solid #ddd; 
            padding: 12px; 
            text-align: left;
          }
          th { 
            background-color: #f4f4f4; 
            font-weight: 600;
          }
          h1 {
            font-weight: 700;
            color: #111;
            margin: 0;
          }
          h2 {
            font-weight: 600;
            color: #333;
            margin: 0 0 15px 0;
          }
        `}</style>
        <script type="module" dangerouslySetInnerHTML={{ __html: `
          import { setupWebSocketClient } from '/client/websocket.js';
          setupWebSocketClient();
        `}} />
      </head>
      <body>
        <script type="text/javascript" dangerouslySetInnerHTML={{ __html: `
          window.queueStats = ${JSON.stringify(queueStats)};
        `}} />
        <div id="root" />
        <script type="module" src="/out/client.js" />
      </body>
    </html>
  );
}