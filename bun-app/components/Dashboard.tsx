import toast, { Toaster } from 'react-hot-toast';
import React, { useEffect } from "react";
import { JobTable } from './JobTable';

declare global {
  interface Window {
    queueStats: DashboardProps;
  }
}

interface DashboardProps {
    waiting: any[];
    active: any[];
    delayed: any[];
    completed: any[];
}

export function Dashboard() {
  const [queueStats, setQueueStats] = React.useState<DashboardProps>();
  const [newQueue, setNewQueue] = React.useState<any>({});
  useEffect(() => {
    setQueueStats(window.queueStats)
    window.addEventListener('queue-update', ((event: Event) => {
      const customEvent = event as CustomEvent;
      const { type, data } = customEvent.detail;
      // WebSocket message received: {"type":"completed","data":{"jobId":"repeat:repeat-every-everyminute:1735762180029","returnvalue":null}}
      console.log(type, data);
      let jobId = data.jobId;
      if (newQueue?.[jobId]) {
        setNewQueue((newQueue: any) => ({ ...newQueue, [jobId]: { ...newQueue[jobId], ...data, type } }));
      } else {
        setNewQueue((newQueue: any) => ({ ...newQueue, [jobId]: {...data, type } }));
      }
      // Handle the update based on type (waiting, active, completed, etc.)
    }) as EventListener);
  }, []);
  return (
    <div className="dashboard">
      <h1>Queue Dashboard</h1>
      <div><Toaster/></div>

      <div>
        {/* Add a button to run a test job */}
        <button onClick={() => {
          fetch('/subscription-test').then((raw) => {
            if (raw.status === 200) {
              toast.success('Subscribtion task run successfully');
            } else {
              toast.error('Subscribtion task failed');
            }
          });
        }}>Run subscription test job</button>
      </div>
      <div className="stats-container">
        {queueStats ? (
          <>
            <JobTable jobs={queueStats.active} title="Active Jobs" />
            <JobTable jobs={queueStats.waiting} title="Pending Jobs" />
            <JobTable jobs={queueStats.delayed} title="Delayed Jobs" />
            <JobTable jobs={queueStats.completed} title="Completed Jobs" />
          </>
        ) : null}
        <table>
        {Object.entries(newQueue).map(([jobId, data]) => (
          <tr key={jobId}>
            <td>{jobId}</td>
            <td>{(data as any).type}</td>
          </tr>
        ))}
        </table>
      </div>
    </div>
  );
}