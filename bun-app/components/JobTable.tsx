import React from "react";
interface Job {
  id: string;
  name: string;
  timestamp: number;
  finishedOn?: number;
  processedOn?: number;
  data: any;
  progress?: number;
  delay?: number;
}

interface JobTableProps {
  jobs: Job[];
  title: string;
}

export function JobTable({ jobs, title }: JobTableProps) {
  return (
    <div className="job-table">
      <h2>{title}</h2>
      <table>
        <thead>
          <tr>
            <th>ID</th>
            <th>Name</th>
            <th>Timestamp</th>
            {title === 'Completed Jobs' && <th>Finished On</th>}
            {title === 'Delayed Jobs' && <th>Will Run On</th>}
            {title !== 'Delayed Jobs' && <th>Processed On</th>}
            
            <th>Data</th>
            {title === 'Active Jobs' && <th>Progress</th>}
          </tr>
        </thead>
        <tbody>
          {jobs.filter(job => job.name !== 'every-day').map(job => (
            <tr key={job.id}>
              <td>{job.id}</td>
              <td>{job.name}</td>
              <td>{new Date(job.timestamp).toLocaleString()}</td>
              {job.finishedOn ? <td>{new Date(job.finishedOn).toLocaleString()}</td> : null}
              {job.delay ? <td>{new Date(job.timestamp + job.delay).toLocaleString()}</td> : null}
              {job.processedOn ? <td>{new Date(job.processedOn).toLocaleString()}</td> : null}
              
              <td><pre>{JSON.stringify(job.data, null, 2)}</pre></td>
              {title === 'Active Jobs' && <td>{job.progress}%</td>}
            </tr>
          ))}
        </tbody>
      </table>
    </div>
  );
}