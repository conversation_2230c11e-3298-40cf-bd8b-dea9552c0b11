import { addToAccountLimitHistory, getAccountsWhereLimitEnds, getUserLimits, updateAccountWithPackageId } from './lib/pocket';
import { type IMessagingLimitPartial } from './lib/types';
import dayjs from 'dayjs';

//Sets assigned limit to 0 to all agents
//Sets assigned limit as package total limit to all admins
const resetLimits = async () => {
  const filter = `limit_end_date <= '${dayjs().format('YYYY-MM-DD 24:59:59[.000Z]')}' && limit_end_date != null`;
  const limitEndAccounts = await getAccountsWhereLimitEnds(filter);
  if (limitEndAccounts?.length > 0) {
    limitEndAccounts.map(async (account) => {
      const period = account.expand?.package_tier?.period;
      const packageId = account.expand?.package_tier?.id;
      const remaining_limit = account.expand?.package_tier?.total_limit; //package limit
      const res = await getUserLimits(account.id);
      const agents_admin_data = res.map((item) => {
        return {
          user: item.user,
          remaining_limit: item.remaining_limit,
          assigned_limit: item.assigned_limit,
          limit_start_date: item.limit_start_date,
          limit_end_date: item.limit_end_date,
          period: item.period,
        };
      }) as IMessagingLimitPartial[];
      await addToAccountLimitHistory(account.id, remaining_limit ?? 0, account.remaining_limit, agents_admin_data);
      await updateAccountWithPackageId(account.id, packageId ?? '', remaining_limit ?? 0, period ?? '');
    });
  }
};

export default resetLimits;
