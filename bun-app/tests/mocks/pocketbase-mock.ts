import type { BatchService, SubBatchService } from "pocketbase";

export class MockPocketBaseCollection {
  private records: Record<string, any>[] = [];
  private name: string;

  constructor(name: string, initialData: Record<string, any>[] = []) {
    this.name = name;
    this.records = [...initialData];
  }

  async create(data: Record<string, any>, options: any = {}) {
    const id = Math.random().toString(36).substring(2, 15);
    const record = { id, ...data, created: new Date(), updated: new Date() };
    this.records.push(record);
    
    if (options.expand) {
      return { ...record, expand: {} };
    }
    
    return record;
  }

  async update(id: string, data: Record<string, any>, options: any = {}) {
    const index = this.records.findIndex(record => record.id === id);
    if (index === -1) {
      throw new Error(`Record with id ${id} not found in collection ${this.name}`);
    }
    
    const updatedRecord: Record<string, any> = { 
      ...this.records[index], 
      ...data, 
      updated: new Date() 
    };
    
    // Special handling for increment/decrement operations
    Object.keys(data).forEach(key => {
      if (key.endsWith('+')) {
        const baseKey = key.slice(0, -1);
        const value = data[key];
        if (Array.isArray(this.records[index][baseKey]) && Array.isArray(value)) {
          updatedRecord[baseKey] = [...this.records[index][baseKey], ...value];
        } else if (typeof this.records[index][baseKey] === 'number') {
          updatedRecord[baseKey] = this.records[index][baseKey] + value;
        }
        delete updatedRecord[key];
      } else if (key.endsWith('-')) {
        const baseKey = key.slice(0, -1);
        const value = data[key];
        if (Array.isArray(this.records[index][baseKey]) && Array.isArray(value)) {
          updatedRecord[baseKey] = this.records[index][baseKey].filter(
            (item: any) => !value.includes(item)
          );
        } else if (typeof this.records[index][baseKey] === 'number') {
          updatedRecord[baseKey] = this.records[index][baseKey] - value;
        }
        delete updatedRecord[key];
      }
    });
    
    this.records[index] = updatedRecord;
    
    if (options.expand) {
      return { ...updatedRecord, expand: {} };
    }
    
    return updatedRecord;
  }

  async getFirstListItem<T>(filter: string, options: any = {}) {
    // This is a simple filter parser - for testing we'll keep it minimal
    const matches = filter.match(/([a-zA-Z0-9_.]+)\s*=\s*"([^"]+)"/g) || [];
    let result = [...this.records];
    
    for (const match of matches) {
      const [field, value] = match.split(/\s*=\s*/).map(s => s.replace(/"/g, ''));
      result = result.filter(record => record[field] === value);
    }
    
    if (result.length === 0) {
      throw new Error(`No records matching filter: ${filter}`);
    }
    
    if (options.expand) {
      return { ...result[0], expand: {} };
    }
    
    return result[0] as T;
  }

  async getList<T>(page: number, perPage: number, options: any = {}) {
    const start = (page - 1) * perPage;
    const end = start + perPage;
    const items = this.records.slice(start, end) as T[];
    
    if (options.expand) {
      return {
        items: items.map(item => ({ ...item, expand: {} })),
        page,
        perPage,
        totalItems: this.records.length,
        totalPages: Math.ceil(this.records.length / perPage)
      };
    }
    
    return {
      items,
      page,
      perPage,
      totalItems: this.records.length,
      totalPages: Math.ceil(this.records.length / perPage)
    };
  }
  
  async getFullList<T>(options: any = {}) {
    let result = [...this.records];
    
    // Handle filter option
    if (options.filter) {
      const matches = options.filter.match(/([a-zA-Z0-9_.]+)\s*=\s*"([^"]+)"/g) || [];
      for (const match of matches) {
        const [field, value] = match.split(/\s*=\s*/).map((s: string) => s.replace(/"/g, ''));
        result = result.filter(record => record[field] === value);
      }
      
      // Handle status filter with format: status = "PENDING DELETION"
      const statusMatches = options.filter.match(/([a-zA-Z0-9_.]+)\s*=\s*"([^"]+)"/g) || [];
      for (const match of statusMatches) {
        const [field, value] = match.split(/\s*=\s*/).map((s: string) => s.replace(/"/g, ''));
        result = result.filter(record => record[field] === value);
      }
    }
    
    // Handle sort option
    if (options.sort) {
      const sortField = options.sort.replace(/^-/, '');
      const sortDesc = options.sort.startsWith('-');
      
      result.sort((a, b) => {
        if (sortDesc) {
          return a[sortField] > b[sortField] ? -1 : 1;
        } else {
          return a[sortField] > b[sortField] ? 1 : -1;
        }
      });
    }
    
    if (options.expand) {
      return result.map(record => ({ ...record, expand: {} }));
    }
    
    return result as T[];
  }

  async delete(id: string) {
    const index = this.records.findIndex(record => record.id === id);
    if (index === -1) {
      throw new Error(`Record with id ${id} not found in collection ${this.name}`);
    }
    this.records.splice(index, 1);
  }

  async getOne<T>(id: string, options: any = {}) {
    const record = this.records.find(record => record.id === id) as T;
    if (!record) {
      throw new Error(`Record with id ${id} not found in collection ${this.name}`);
    }
    
    if (options.expand) {
      return { ...record, expand: {} };
    }
    
    return record as T;
  }

  // Get the current state of the records for assertions
  getRecords() {
    return [...this.records];
  }
}

export class MockPocketBase {
  private collections: Record<string, MockPocketBaseCollection> = {};
  private isAuthenticated: boolean = false;
  authStore = { isValid: true };

  constructor(initialData: Record<string, Record<string, any>[]> = {}) {
    // Initialize collections with provided data
    Object.entries(initialData).forEach(([name, data]) => {
      this.collections[name] = new MockPocketBaseCollection(name, data);
    });
  }

  collection<T = any>(name: string): MockPocketBaseCollection {
    if (!this.collections[name]) {
      this.collections[name] = new MockPocketBaseCollection(name);
    }
    return this.collections[name] as unknown as MockPocketBaseCollection;
  }

  async authWithPassword(email: string, password: string) {
    this.isAuthenticated = true;
    return { token: "mock-auth-token" };
  }

  autoCancellation(value: boolean) {
    // Mock implementation
    return this;
  }

  files = {
    getURL: (fileId: any, url: any) => {
      return "";
    }
  }

  // Method to inspect a collection's data (for testing)
  getCollectionData(name: string) {
    return this.collections[name]?.getRecords() || [];
  }
}