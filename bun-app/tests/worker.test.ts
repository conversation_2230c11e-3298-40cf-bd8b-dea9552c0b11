import { expect, test, mock, beforeEach } from "bun:test";
import { sendCampaign } from "../worker-functions/sendCampaign";
import { data, setData } from "../lib/pocket";

// Mock dependencies
const updateLeadWithCampaign = mock(
  pocketModule.updateLeadWithCampaign
).mockImplementation(() => Promise.resolve());

// const addMessage = mock(pocketModule.addMessage).mockImplementation(() =>
//   Promise.resolve({
//     id: "mock-message-id",
//     message: "Test message",
//     delivery_status: "pending",
//   } as IMessage)
// );

// const mockMessage: IExpandedMessage = {
//   id: "mock-message-id",
//   message: "Test message",
//   delivery_status: "pending",
// } as IExpandedMessage;
// const justSend = mock(helperModule.justSend).mockImplementation(() =>
//   Promise.resolve(mockMessage)
// );

test("sendCampaign should update lead and send a message", async () => {
  // Reset mocks before test
  // updateLeadWithCampaign.mockClear();
  // addMessage.mockClear();
  // justSend.mockClear();

  // Setup test data
  const campaignData = {
    lead_id: "o7tgcrxop9azd4n",
    campaign_id: "tro7akwge27uo91",
    template_name: "testaa",
    account_id: "mg7jso5gfhqiftc",
    template_id: "gr2r46glq1rn8jq",
    created_by: "0v90hlpe1v77iq1",
  };

  // Execute the function
  await sendCampaign(campaignData);

  // Verify updateLeadWithCampaign was called correctly
  expect(updateLeadWithCampaign).toHaveBeenCalledWith(
    campaignData.lead_id,
    campaignData.campaign_id
  );

  // Verify addMessage was called with correct parameters
  // expect(addMessage).toHaveBeenCalledWith(
  //   campaignData.lead_id,
  //   campaignData.template_name,
  //   "agent",
  //   campaignData.account_id,
  //   campaignData.campaign_id,
  //   "pending",
  //   campaignData.template_id,
  //   null,
  //   undefined,
  //   campaignData.created_by
  // );
});
