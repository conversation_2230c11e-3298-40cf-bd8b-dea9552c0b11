import { UserManagementHandler } from "./handlers/userManagement";
import type { ApiResponse } from "./types";

// Create instances of handlers
const userManagementHandler = new UserManagementHandler();

/**
 * Routes API requests to the appropriate handler
 * @param path The API path
 * @param method The HTTP method
 * @param body The request body
 * @returns ApiResponse or null if no handler is found
 */
export function routeApiRequest(path: string, method: string, body: any): ApiResponse | null {
  // Normalize path
  const normalizedPath = path.toLowerCase();
  
  // Route to the appropriate handler based on path and method
  if (method === "POST") {
    if (normalizedPath === "/api/usermanagement/verifycustomer") {
      return userManagementHandler.verifyCustomer(body);
    } else if (normalizedPath === "/api/usermanagement/generateotp") {
      return userManagementHandler.generateOtp(body);
    } else if (normalizedPath === "/api/usermanagement/verifyotp") {
      return userManagementHandler.verifyOtp(body);
    }
  }
  
  // No handler found
  return null;
}
