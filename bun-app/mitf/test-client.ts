/**
 * Test client for the MITF API
 * 
 * This script can be run to test the API endpoints
 * 
 * Usage:
 * bun run bun-app/mitf/test-client.ts
 */

async function testMitfApi() {
  const baseUrl = "http://localhost:3006";
  
  console.log("Testing MITF API...");
  
  // Test VerifyCustomer endpoint
  console.log("\n1. Testing /api/UserManagement/VerifyCustomer");
  
  // Test with valid number
  let response = await fetch(`${baseUrl}/api/UserManagement/VerifyCustomer`, {
    method: "POST",
    headers: {
      "Content-Type": "application/json"
    },
    body: JSON.stringify({
      WhatsAppNumber: "+966500000000"
    })
  });
  
  console.log("Valid number response:", await response.json());
  
  // Test with invalid number
  response = await fetch(`${baseUrl}/api/UserManagement/VerifyCustomer`, {
    method: "POST",
    headers: {
      "Content-Type": "application/json"
    },
    body: JSON.stringify({
      WhatsAppNumber: "+966599999999"
    })
  });
  
  console.log("Invalid number response:", await response.json());
  
  // Test GenerateOtp endpoint
  console.log("\n2. Testing /api/UserManagement/GenerateOtp");
  
  response = await fetch(`${baseUrl}/api/UserManagement/GenerateOtp`, {
    method: "POST",
    headers: {
      "Content-Type": "application/json"
    },
    body: JSON.stringify({
      WhatsAppNumber: "+966500000000"
    })
  });
  
  console.log("Generate OTP response:", await response.json());
  
  // Test VerifyOtp endpoint
  console.log("\n3. Testing /api/UserManagement/VerifyOtp");
  
  // This will fail because we don't know the actual OTP
  response = await fetch(`${baseUrl}/api/UserManagement/VerifyOtp`, {
    method: "POST",
    headers: {
      "Content-Type": "application/json"
    },
    body: JSON.stringify({
      WhatsAppNumber: "+966500000000",
      Otp: "123456" // This is likely to be wrong
    })
  });
  
  console.log("Verify OTP response (expected to fail):", await response.json());
  
  console.log("\nAPI testing complete!");
}

// Run the tests
testMitfApi().catch(console.error);
