{"openapi": "3.0.4", "info": {"title": "Mitf.WhatsApp.Api", "version": "1.0"}, "paths": {"/api/UserManagement/VerifyCustomer": {"post": {"tags": ["UserManagement"], "summary": "Verify if customer is registered to WhatsApp Banking Service", "operationId": "UserManagement", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CustomerVerificationDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/CustomerVerificationDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/CustomerVerificationDto"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ApiResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}}}}, "400": {"description": "Bad Request", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ApiResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}}}}}}}, "/api/UserManagement/GenerateOtp": {"post": {"tags": ["UserManagement"], "summary": "Generate OTP and send it to customer by SMS", "operationId": "GenerateOtp", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CustomerVerificationDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/CustomerVerificationDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/CustomerVerificationDto"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ApiResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}}}}, "400": {"description": "Bad Request", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ApiResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}}}}}}}, "/api/UserManagement/VerifyOtp": {"post": {"tags": ["UserManagement"], "summary": "Verify the OTP received via SMS for customer verification", "operationId": "VerifyOtp", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/OtpVerificationDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/OtpVerificationDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/OtpVerificationDto"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ApiResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}}}}, "400": {"description": "Bad Request", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ApiResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}}}}}}}}, "components": {"schemas": {"ApiErrorDetail": {"type": "object", "properties": {"Field": {"type": "string", "nullable": true}, "Message": {"type": "string", "nullable": true}}, "additionalProperties": false}, "ApiResponse": {"type": "object", "properties": {"Status": {"type": "string", "nullable": true}, "Code": {"type": "string", "nullable": true}, "Message": {"type": "string", "nullable": true}, "Details": {"type": "array", "items": {"$ref": "#/components/schemas/ApiErrorDetail"}, "nullable": true}, "Timestamp": {"type": "string", "format": "date-time"}}, "additionalProperties": false}, "CustomerVerificationDto": {"type": "object", "properties": {"WhatsAppNumber": {"type": "string", "nullable": true}}, "additionalProperties": false}, "OtpVerificationDto": {"type": "object", "properties": {"WhatsAppNumber": {"type": "string", "nullable": true}, "Otp": {"type": "string", "nullable": true}}, "additionalProperties": false}}}}