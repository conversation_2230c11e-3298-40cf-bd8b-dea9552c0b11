import { <PERSON><PERSON>and<PERSON> } from "./base";
import { ApiResponse, CustomerVerificationDto, OtpVerificationDto } from "../types";

export class UserManagementHandler extends BaseHandler {
  // Mock database for storing OTPs
  private otpStore: Map<string, string> = new Map();
  
  // Mock database for registered WhatsApp numbers
  private registeredNumbers: Set<string> = new Set([
    "+************",
    "+************",
    "+************"
  ]);

  /**
   * Verify if a customer is registered to WhatsApp Banking Service
   * @param request CustomerVerificationDto
   * @returns ApiResponse
   */
  public verifyCustomer(request: CustomerVerificationDto): ApiResponse {
    // Validate request
    const validation = this.validateRequest(request, ["WhatsAppNumber"]);
    if (!validation.isValid) {
      return this.createErrorResponse(
        "Invalid request", 
        "400", 
        validation.missingFields.map(field => ({ 
          Field: field, 
          Message: `${field} is required` 
        }))
      );
    }

    const whatsAppNumber = request.WhatsAppNumber!;
    
    // Check if the number is registered
    if (this.registeredNumbers.has(whatsAppNumber)) {
      return this.createSuccessResponse("Customer is registered to WhatsApp Banking Service");
    } else {
      return this.createErrorResponse(
        "Customer is not registered to WhatsApp Banking Service", 
        "404"
      );
    }
  }

  /**
   * Generate OTP and send it to customer by SMS
   * @param request CustomerVerificationDto
   * @returns ApiResponse
   */
  public generateOtp(request: CustomerVerificationDto): ApiResponse {
    // Validate request
    const validation = this.validateRequest(request, ["WhatsAppNumber"]);
    if (!validation.isValid) {
      return this.createErrorResponse(
        "Invalid request", 
        "400", 
        validation.missingFields.map(field => ({ 
          Field: field, 
          Message: `${field} is required` 
        }))
      );
    }

    const whatsAppNumber = request.WhatsAppNumber!;
    
    // Check if the number is registered
    if (!this.registeredNumbers.has(whatsAppNumber)) {
      return this.createErrorResponse(
        "Customer is not registered to WhatsApp Banking Service", 
        "404"
      );
    }

    // Generate a random 6-digit OTP
    const otp = Math.floor(100000 + Math.random() * 900000).toString();
    
    // Store the OTP
    this.otpStore.set(whatsAppNumber, otp);
    
    // In a real implementation, this would send an SMS
    console.log(`Sending OTP ${otp} to ${whatsAppNumber}`);
    
    return this.createSuccessResponse("OTP has been sent to the customer");
  }

  /**
   * Verify the OTP received via SMS for customer verification
   * @param request OtpVerificationDto
   * @returns ApiResponse
   */
  public verifyOtp(request: OtpVerificationDto): ApiResponse {
    // Validate request
    const validation = this.validateRequest(request, ["WhatsAppNumber", "Otp"]);
    if (!validation.isValid) {
      return this.createErrorResponse(
        "Invalid request", 
        "400", 
        validation.missingFields.map(field => ({ 
          Field: field, 
          Message: `${field} is required` 
        }))
      );
    }

    const whatsAppNumber = request.WhatsAppNumber!;
    const otp = request.Otp!;
    
    // Check if the number is registered
    if (!this.registeredNumbers.has(whatsAppNumber)) {
      return this.createErrorResponse(
        "Customer is not registered to WhatsApp Banking Service", 
        "404"
      );
    }

    // Check if an OTP exists for this number
    if (!this.otpStore.has(whatsAppNumber)) {
      return this.createErrorResponse(
        "No OTP has been generated for this customer", 
        "400"
      );
    }

    // Verify the OTP
    const storedOtp = this.otpStore.get(whatsAppNumber);
    if (storedOtp === otp) {
      // Clear the OTP after successful verification
      this.otpStore.delete(whatsAppNumber);
      return this.createSuccessResponse("OTP verification successful");
    } else {
      return this.createErrorResponse(
        "Invalid OTP", 
        "400"
      );
    }
  }
}
