import { ApiResponse } from "../types";

// Base class for API handlers
export abstract class BaseHandler {
  // Create a successful response
  protected createSuccessResponse(message: string = "Success"): ApiResponse {
    return {
      Status: "success",
      Code: "200",
      Message: message,
      Details: [],
      Timestamp: new Date().toISOString()
    };
  }

  // Create an error response
  protected createErrorResponse(
    message: string = "Bad Request", 
    code: string = "400", 
    details: { Field?: string; Message?: string }[] = []
  ): ApiResponse {
    return {
      Status: "error",
      Code: code,
      Message: message,
      Details: details,
      Timestamp: new Date().toISOString()
    };
  }

  // Validate that a request has the required fields
  protected validateRequest(request: any, requiredFields: string[]): { isValid: boolean; missingFields: string[] } {
    const missingFields = requiredFields.filter(field => !request[field]);
    return {
      isValid: missingFields.length === 0,
      missingFields
    };
  }
}
