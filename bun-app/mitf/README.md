# MITF API Implementation

This folder contains the implementation of the MITF WhatsApp API as defined in the `api_specs.json` file.

## API Endpoints

The following endpoints are implemented:

### 1. Verify Customer

Verifies if a customer is registered to WhatsApp Banking Service.

- **URL**: `/api/UserManagement/VerifyCustomer`
- **Method**: `POST`
- **Request Body**:
  ```json
  {
    "WhatsAppNumber": "+************"
  }
  ```
- **Success Response**:
  ```json
  {
    "Status": "success",
    "Code": "200",
    "Message": "Customer is registered to WhatsApp Banking Service",
    "Details": [],
    "Timestamp": "2023-04-14T12:34:56.789Z"
  }
  ```
- **Error Response**:
  ```json
  {
    "Status": "error",
    "Code": "404",
    "Message": "Customer is not registered to WhatsApp Banking Service",
    "Details": [],
    "Timestamp": "2023-04-14T12:34:56.789Z"
  }
  ```

### 2. Generate OTP

Generates an OTP and sends it to the customer by SMS.

- **URL**: `/api/UserManagement/GenerateOtp`
- **Method**: `POST`
- **Request Body**:
  ```json
  {
    "WhatsAppNumber": "+************"
  }
  ```
- **Success Response**:
  ```json
  {
    "Status": "success",
    "Code": "200",
    "Message": "OTP has been sent to the customer",
    "Details": [],
    "Timestamp": "2023-04-14T12:34:56.789Z"
  }
  ```
- **Error Response**:
  ```json
  {
    "Status": "error",
    "Code": "404",
    "Message": "Customer is not registered to WhatsApp Banking Service",
    "Details": [],
    "Timestamp": "2023-04-14T12:34:56.789Z"
  }
  ```

### 3. Verify OTP

Verifies the OTP received via SMS for customer verification.

- **URL**: `/api/UserManagement/VerifyOtp`
- **Method**: `POST`
- **Request Body**:
  ```json
  {
    "WhatsAppNumber": "+************",
    "Otp": "123456"
  }
  ```
- **Success Response**:
  ```json
  {
    "Status": "success",
    "Code": "200",
    "Message": "OTP verification successful",
    "Details": [],
    "Timestamp": "2023-04-14T12:34:56.789Z"
  }
  ```
- **Error Response**:
  ```json
  {
    "Status": "error",
    "Code": "400",
    "Message": "Invalid OTP",
    "Details": [],
    "Timestamp": "2023-04-14T12:34:56.789Z"
  }
  ```

## Testing

You can test the API using the provided test client:

```bash
bun run bun-app/mitf/test-client.ts
```

## Implementation Details

The API is implemented using a modular architecture:

- `types.ts` - Type definitions for request and response objects
- `api.ts` - Main API handler that processes HTTP requests
- `router.ts` - Routes requests to the appropriate handler
- `handlers/base.ts` - Base class for API handlers with common functionality
- `handlers/userManagement.ts` - Implementation of the UserManagement endpoints

The implementation currently uses mock data for demonstration purposes. In a production environment, these handlers would be connected to actual databases and services.
