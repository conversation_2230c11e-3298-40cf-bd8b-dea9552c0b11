import { routeApiRequest } from "./router";
import type { ApiResponse } from "./types";

/**
 * Main API handler function
 * @param req The HTTP request
 * @returns Response object
 */
export async function handleApiRequest(req: Request): Promise<Response> {
  try {
    // Get the request path
    const url = new URL(req.url);
    const path = url.pathname;
    
    // Get the request method
    const method = req.method;
    
    // Parse the request body if it exists
    let body = {};
    if (req.headers.get("content-type")?.includes("application/json")) {
      body = await req.json();
    }
    
    // Route the request to the appropriate handler
    const response = routeApiRequest(path, method, body);
    
    if (response) {
      // Return the response
      return new Response(JSON.stringify(response), {
        status: response.Code === "200" ? 200 : 400,
        headers: {
          "Content-Type": "application/json"
        }
      });
    } else {
      // No handler found
      const errorResponse: ApiResponse = {
        Status: "error",
        Code: "404",
        Message: "Not Found",
        Details: [],
        Timestamp: new Date().toISOString()
      };
      
      return new Response(JSON.stringify(errorResponse), {
        status: 404,
        headers: {
          "Content-Type": "application/json"
        }
      });
    }
  } catch (error) {
    // Handle any errors
    console.error("API Error:", error);
    
    const errorResponse: ApiResponse = {
      Status: "error",
      Code: "500",
      Message: "Internal Server Error",
      Details: [],
      Timestamp: new Date().toISOString()
    };
    
    return new Response(JSON.stringify(errorResponse), {
      status: 500,
      headers: {
        "Content-Type": "application/json"
      }
    });
  }
}
