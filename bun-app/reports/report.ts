import { s3, write, S3Client } from "bun";
import { Database } from "bun:sqlite";
import dayjs from "dayjs";
import type {
  IExpandedCampaign,
  IExpandedLead,
  IExpandedMessage,
  ILead,
  Meta,
} from "../lib/types";

export class Report {
  constructor(
    private db: Database,
    private account_id: string,
    private forex_rates: any
  ) {}
  private getQuery<T>(query: string, param: string): T {
    return this.db.query(query).get(param) as T;
  }
  private getBusinessCurrency() {
    const query = `SELECT currency FROM accounts WHERE id = ?`;
    const r = this.getQuery<{ currency: string }>(query, this.account_id);
    if (r?.currency) {
      return r.currency;
    }
    return "";
  }
  private getAccount() {
    // package_tier,pb_user_id
    const query = `SELECT * FROM accounts
     LEFT JOIN users ON accounts.pb_user_id = users.id
     LEFT JOIN packages_tier ON accounts.package_tier = packages_tier.id
    WHERE accounts.id = ?`;
    return this.db.query(query).get(this.account_id);
  }
  private getPricing() {
    const query = `SELECT * FROM pricing`;
    return this.db.query(query).all();
  }
  private getAllMetaKeys() {
    const query = `SELECT meta FROM leads WHERE account = $accountId`;
    const allLeads = this.db
      .query(query)
      .all({ $accountId: this.account_id }) as ILead[];
    const finalMetakeys = new Set<string>();
    const finalMetaTuple = new Set<string>();
    allLeads.forEach((lead: ILead) => {
      if (lead.meta) {
        const _meta: string = lead.meta as unknown as string;
        if (_meta === "null") {
          return;
        }
        Object.entries(JSON.parse(_meta)).forEach(([key, value]) => {
          if (key !== "") {
            finalMetakeys.add(key);
            finalMetaTuple.add(JSON.stringify({ key, value }));
          }
        });
      }
    });

    const finalAnswer: Meta[] = Array.from(finalMetakeys).map((key) => {
      return {
        key,
        values: Array.from(finalMetaTuple)
          .filter((tuple) => JSON.parse(tuple).key === key)
          .map((tuple) => JSON.parse(tuple).value.toString()),
      };
    });

    return finalAnswer;
  }
  private getMoreReport() {
    const query = `SELECT leads.* FROM leads LEFT JOIN users ON leads.created_by = users.id
      WHERE account = $accountId
      `;
    const _leadData = this.db
      .query(query)
      .all({ $accountId: this.account_id }) as IExpandedLead[];
    const leadData = _leadData.map((lead) => {
      return {
        ...lead,
        referral: lead?.referral
          ? JSON.parse(lead.referral as unknown as string)
          : null,
        tags: lead?.tags ? JSON.parse(lead.tags as unknown as string) : null,
      };
    });

    const campaignQuery = `
        SELECT  campaigns.*, campaigns.name as name, campaigns.type as type, users.name as created_by_name, campaigns.created_by as created_by, campaigns.created as created, users.type as created_by_type FROM campaigns 
        LEFT JOIN templates ON campaigns.template = templates.id
        LEFT JOIN users ON campaigns.created_by = users.id
        WHERE campaigns.account = $accountId
        ORDER BY campaigns.created DESC
      `;
    const campaigns = this.db
      .query(campaignQuery)
      .all({ $accountId: this.account_id }) as IExpandedCampaign[];

    const messagesQuery = `
      SELECT messages.*, 
        users.name as created_by_name,
        users.id as created_by,
        users.type as created_by_type,
        leads.country as user_country,
        templates.category as template_category,
        templates.template_name as template_name,
        campaigns.id as campaign_id
      FROM messages
      JOIN users ON messages.created_by = users.id
      JOIN templates ON messages.template = templates.id
      LEFT JOIN campaigns ON messages.campaign = campaigns.id
      LEFT JOIN leads ON messages.user = leads.id
      WHERE messages.'from' = 'agent'
      AND messages.account = $accountId
      AND messages.delivery_status != 'failed'
      AND messages.delivery_status != 'pending'
      AND messages.template IS NOT NULL
      AND messages.created_by IS NOT NULL
      ORDER BY messages.created DESC
  `;
    const messages_by_account = this.db
      .query(messagesQuery)
      .all({ $accountId: this.account_id }) as IExpandedMessage[];
    return { campaigns, leadData, messages_by_account };
  }
  private generate() {
    const currency = this.getBusinessCurrency();
    const account = this.getAccount();
    const pricingList = this.getPricing() as any[];
    const metaKeys = this.getAllMetaKeys();

    const convertTo = currency === "" ? "USD" : currency;
    const exchange_rate = this.forex_rates.rates[convertTo];
    const others_cost = pricingList?.filter(
      (pricing) => pricing.country_name == "other"
    );
    const { campaigns, leadData, messages_by_account } = this.getMoreReport();
    //filter those which are published
    const published_campaigns = campaigns.filter(
      (campaign) => campaign.status == "Completed"
    );
    //filter messages which are livechat and campaigns
    const messages_livechat = messages_by_account.filter(
      (message) => !message.campaign
    );
    //filter messages which are campaigns only
    const messages_campaigns = messages_by_account.filter(
      (message) => message.campaign
    );

    //get all messages which are sent by user and 'sent' status
    const messagesCampaignsArray = messages_campaigns.map((message: any) => {
      return {
        createdBy: message.created_by_name,
        createdById: message.created_by,
        createdAt: message.created,
        type: message.created_by_type,
      };
    });

    const messagesLiveChatArray = messages_livechat.map((message: any) => {
      return {
        createdBy: message.created_by_name,
        createdById: message.created_by,
        createdAt: message.created,
        type: message.created_by_type,
      };
    });

    let filteredMessages = messages_by_account as any[];
    //if user is agent and has view all campaigns then all messages will be fetched else only messages created by agent will be fetched
    filteredMessages = messages_by_account;

    let accountMessages: { date: string; cost: Number }[] = [];

    //This loop is to calculate cost of each message sent by user
    for (let message of filteredMessages) {
      let cost: number = 0;
      if (message.user_country) {
        const country = message.user_country;
        const countryCost = pricingList?.filter(
          (pricing) =>
            country.toUpperCase() == pricing.country_name.toUpperCase() ||
            pricing.iso_codes.includes(country.toUpperCase())
        );
        if (countryCost?.length) {
          if (message.template_category == "UTILITY") {
            cost = parseFloat(countryCost[0].utility);
          } else {
            cost = parseFloat(countryCost[0].marketing);
          }
        } else {
          if (message.template_category == "UTILITY") {
            cost = parseFloat(others_cost && others_cost[0].utility);
          } else {
            cost = parseFloat(others_cost && others_cost[0].marketing);
          }
        }
      } else {
        if (message?.template_category == "UTILITY") {
          cost = parseFloat(others_cost && others_cost[0].utility);
        } else {
          cost = parseFloat(others_cost && others_cost[0].marketing);
        }
      }
      cost = parseFloat((cost * exchange_rate).toFixed(2));
      accountMessages.push({
        date: dayjs(message.created).format("MMMM D, YYYY"),
        cost: cost,
      });
    }

    const getCampaignCost = (campaign_id: string) => {
      let totalCost: number = 0;
      // const campaignMessages = await getCampaignMessagesById(campaign_id);
      messages_campaigns
        .filter((item: any) => item.campaign_id == campaign_id)
        .forEach((message: any) => {
          const template_category = message.template_category;
          if (message.user_country) {
            const country = message.user_country;
            const countryCost = pricingList?.filter(
              (pricing) =>
                country.toUpperCase() == pricing.country_name.toUpperCase() ||
                pricing.iso_codes.includes(country.toUpperCase())
            );
            if (countryCost?.length) {
              if (template_category == "UTILITY") {
                totalCost = totalCost + countryCost[0].utility;
              } else {
                totalCost = totalCost + countryCost[0].marketing;
              }
            } else {
              if (template_category == "UTILITY") {
                totalCost = totalCost + (others_cost && others_cost[0].utility);
              } else {
                totalCost =
                  totalCost + (others_cost && others_cost[0].marketing);
              }
            }
          } else {
            if (template_category == "UTILITY") {
              totalCost = totalCost + (others_cost && others_cost[0].utility);
            } else {
              totalCost = totalCost + (others_cost && others_cost[0].marketing);
            }
          }
        });
      const convertedValue = totalCost * exchange_rate;
      return convertedValue;
    };

    let publishedCampaigns: (IExpandedCampaign & { cost?: number })[] = [];
    for (let campaign of published_campaigns) {
      const cost = getCampaignCost(campaign.id);
      publishedCampaigns.push({
        ...campaign,
        cost: parseFloat(cost.toFixed(2)),
      });
    }

    const campaignsArray = campaigns.map((campaign: any) => {
      return {
        name: campaign.name,
        createdBy: campaign.created_by_name,
        createdById: campaign.created_by,
        createdAt: campaign.created,
        type: campaign.created_by_type,
      };
    });

    const messagesCampaignsArrayAgent = messagesCampaignsArray;

    const messagesLiveChatArrayAgent = messagesLiveChatArray;

    const publishedCampaignsAgent = publishedCampaigns;

    return {
      accountMessages,
      convertTo,
      publishedCampaigns,
      messagesLiveChatArray,
      messagesCampaignsArrayAgent,
      messagesLiveChatArrayAgent,
      account,
      campaignsArray,
      messagesCampaignsArray,
      publishedCampaignsAgent,
      leadData,
      metaKeys,
    };
  }

  async getReportForToday() {
    // What is today?
    const today = `${new Date().getDate()}-${new Date().getMonth()}-${new Date().getFullYear()}`;
    const client = new S3Client();
    const reportFile = client.file(`${today}-${this.account_id}.json`);
    if (await reportFile.exists()) {
      const reportContent = await reportFile.json();
      return reportContent;
    }
    const reportContent = this.generate();
    await write(reportFile, JSON.stringify(reportContent));
    return reportContent;
  }
}
