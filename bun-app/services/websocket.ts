import { Server, ServerWebSocket } from 'bun';
import { queueEvents } from './queue';
import { QueueEventsListener } from 'bullmq';

export function setupWebSocket(ws: ServerWebSocket<unknown>) {
  // Forward queue events to connected clients
  const events: (keyof QueueEventsListener)[] = ['waiting', 'active', 'completed', 'failed', 'progress'];
  events.forEach(eventName => {
    queueEvents.on(eventName, (data) => {
      ws.send(JSON.stringify({ type: eventName, data }));
    });
  });
}