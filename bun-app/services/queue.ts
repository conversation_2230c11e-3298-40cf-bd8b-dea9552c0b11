import { Queue, QueueEvents } from 'bullmq';
import { redis } from './redis';
import { QNAME } from '../constant';

export const myQueue = new Queue(QNAME, { connection: redis });

export const queueEvents = new QueueEvents(QNAME, { connection: redis });

// Listen for job events
// queueEvents.on('waiting', ({ jobId }) => {
//   console.log(`Job ${jobId} is waiting`);
// });

// queueEvents.on('active', ({ jobId, prev }) => {
//   console.log(`Job ${jobId} is now active. Previous state: ${prev}`);
// });

// queueEvents.on('completed', ({ jobId, returnvalue }) => {
//   console.log(`Job ${jobId} completed with result:`, returnvalue);
// });

// queueEvents.on('failed', ({ jobId, failedReason }) => {
//   console.log(`Job ${jobId} failed with reason:`, failedReason);
// });

// queueEvents.on('progress', ({ jobId, data }) => {
//   console.log(`Job ${jobId} reported progress:`, data);
// });

export async function getQueueStats() {
  const [waiting, active, delayed, completed] = await Promise.all([
    myQueue.getWaiting(),
    myQueue.getActive(),
    myQueue.getDelayed(),
    myQueue.getCompleted(),
  ]);

  return {
    waiting: waiting.map(job => ({
      id: job.id,
      name: job.name,
      timestamp: job.timestamp,
      processedOn: job.processedOn,
      data: {},
    })),
    active: active.map(job => ({
      id: job.id,
      name: job.name,
      timestamp: job.timestamp,
      data: {},
      progress: job.progress,
      processedOn: job.processedOn,
    })),
    delayed: delayed.map(job => ({
      id: job.id,
      name: job.name,
      timestamp: job.timestamp,
      data: job.data,
      delay: job.delay,
      processedOn: job.processedOn,
    })),
    completed: completed.filter(j => j.name !== 'sendCampaign').filter(j => j.name !== 'send').map(job => ({
      id: job.id,
      name: job.name,
      timestamp: job.timestamp,
      data: job.name === 'uploadContactsDone' || job.name === 'uploadOneContact' ? {} : job.data,
      returnValue: job.returnvalue,
      processedOn: job.processedOn,
      finishedOn: job.finishedOn,
    })),
  };
}

export async function getJobCountsByName() {
  const [waiting, active, delayed, completed] = await Promise.all([
    myQueue.getWaiting(),
    myQueue.getActive(),
    myQueue.getDelayed(),
    myQueue.getCompleted(),
  ]);

  const allJobs = [...waiting, ...active, ...delayed, ...completed];
  const jobCounts = allJobs.reduce((acc, job) => {
    acc[job.name] = (acc[job.name] || 0) + 1;
    return acc;
  }, {} as Record<string, number>);

  return jobCounts;
}
