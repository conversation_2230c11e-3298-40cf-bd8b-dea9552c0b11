import { justSend } from "../helper";
import { addMessage, updateLeadWithCampaign } from "../lib/pocket";
import type { IExpandedMessage } from "../lib/types";

const sendCampaign = async (data: Record<string, any>) => {
  const {
    lead_id,
    campaign,
    template_name,
    account_id,
    template_id,
    created_by,
  } = data;

  // Update lead with campaign
  await updateLeadWithCampaign(lead_id, campaign.id);

  // Add message for the lead
  const message: IExpandedMessage = (await addMessage(
    lead_id,
    template_name,
    "agent",
    account_id,
    campaign.id,
    "pending",
    template_id,
    null,
    undefined,
    created_by
  )) as IExpandedMessage;

  await justSend({
    message,
  });
};

export { sendCampaign };
