import dayjs from "dayjs";
import {
  changeInvoiceStatus,
  enableAutoRenewal,
  generateInvoice,
  getAllInvoicesList,
  getInvoiceById,
} from "./lib/pocket";
import { processAutomaticPayment } from "./lib/stripePayment";

/**
 * Manages subscription renewals and invoice status updates
 * This function is likely called periodically to handle billing cycles
 */
const subscription = async () => {
  console.log("Starting subscription renewal process...");
  try {
    // Fetch all invoices from the database
    const invoices = await getAllInvoicesList();
    console.log(`Found ${invoices?.length || 0} invoices to process`);

    if (invoices) {
      for (const invoice of invoices) {
        try {
          const renewalDate = dayjs(invoice.renewal_date);
          const invoice_valid_from = dayjs(invoice.invoice_valid_from);
          const todayDate = dayjs();

          console.log(
            `Processing invoice ${invoice.id} - Status: ${invoice.payment_status}, Renewal: ${invoice.renewal_date}`
          );

          // Handle invoices with "due" payment status
          if (invoice.payment_status == "due") {
            // Check if the invoice is due for automatic payment (3 days after invoice creation)
            if (
              todayDate > invoice_valid_from.add(3, "day") &&
              todayDate <= invoice_valid_from.add(invoice.invoice_limit, "day")
            ) {
              // Try to process automatic payment
              console.log(
                `Attempting automatic payment for invoice ${invoice.id}`
              );
              const paymentResult = await processAutomaticPayment(invoice);

              if (paymentResult.success) {
                console.log(
                  `Automatic payment successful for invoice ${invoice.id}: ${paymentResult.message}`
                );
                // Invoice is already marked as completed in the processAutomaticPayment function
                // A new invoice will be generated in the next cycle
              } else {
                console.log(
                  `Automatic payment failed for invoice ${invoice.id}: ${paymentResult.message}`
                );
                // Payment failed, but we'll keep trying on subsequent runs until the invoice becomes overdue
              }
            }
            // If invoice is past due date + grace period, mark as overdue
            else if (
              todayDate > invoice_valid_from.add(invoice.invoice_limit, "day")
            ) {
              await changeInvoiceStatus(invoice.id, "over-due");
            }
          }
          // Handle paid or overdue invoices for renewal processing
          else if (
            invoice.payment_status == "paid" ||
            invoice.payment_status == "over-due"
          ) {
            // Special handling for first-time invoices
            if (invoice.is_first_invoice) {
              // Enable auto-renewal when approaching renewal date (within 5 days)
              if (
                renewalDate.diff(todayDate, "days") <= 5 &&
                !invoice.completed
              ) {
                await enableAutoRenewal(invoice.id);
              }

              // If renewal date has passed and invoice isn't marked complete
              if (
                renewalDate.diff(todayDate, "seconds") < 0 &&
                !invoice.completed
              ) {
                // Generate new invoice if auto-renewal is enabled and subscription is active
                if (
                  invoice.auto_renewal &&
                  invoice.expand?.account_id.subscription_active
                ) {
                  const invoiceData = await getInvoiceById(invoice.id);
                  console.log("the last invoice data is ");
                  console.log(invoiceData);
                  if (invoiceData) {
                    // Validate required fields before generating invoice
                    if (
                      !invoiceData.unit_amount ||
                      !invoiceData.account_id ||
                      !invoiceData.billing_months
                    ) {
                      console.error(
                        `Invalid invoice data for invoice ${invoice.id}: missing required fields`
                      );
                      continue;
                    }

                    // Create new invoice with same parameters as previous one
                    // Fixed: Use invoice.id as lastInvoiceId to mark current invoice as completed
                    await generateInvoice(
                      invoice.id, // Pass current invoice ID to mark it as completed
                      invoiceData.unit_amount,
                      invoiceData.tax || 0,
                      invoiceData.charges || 0,
                      invoiceData.account_id,
                      invoiceData.billing_months,
                      invoiceData.invoice_limit || 30,
                      invoiceData.renewal_date || new Date(), // Handle null renewal_date
                      "system", // Created by system, not user
                      false // Not first invoice
                    );
                    // Note: invoiceCompleted is now handled inside generateInvoice
                  }
                }
              }
            }
            // Handle recurring (non-first) invoices
            else {
              // Enable auto-renewal when approaching renewal date (within 5 days)
              if (
                renewalDate.diff(todayDate, "days") <= 5 &&
                !invoice.completed
              ) {
                await enableAutoRenewal(invoice.id);
              }

              // If renewal date has passed and invoice isn't marked complete
              if (
                dayjs(invoice.renewal_date).diff(todayDate, "seconds") < 0 &&
                !invoice.completed
              ) {
                // Generate new invoice if auto-renewal is enabled and subscription is active
                if (
                  invoice.auto_renewal &&
                  invoice.expand?.account_id.subscription_active
                ) {
                  const invoiceData = await getInvoiceById(invoice.id);
                  if (invoiceData) {
                    // Validate required fields before generating invoice
                    if (
                      !invoiceData.unit_amount ||
                      !invoiceData.account_id ||
                      !invoiceData.billing_months
                    ) {
                      console.error(
                        `Invalid invoice data for invoice ${invoice.id}: missing required fields`
                      );
                      continue;
                    }

                    // Create new invoice with same parameters as previous one
                    await generateInvoice(
                      invoice.id, // Pass current invoice ID to mark it as completed
                      invoiceData.unit_amount,
                      invoiceData.tax || 0,
                      invoiceData.charges || 0,
                      invoiceData.account_id,
                      invoiceData.billing_months,
                      invoiceData.invoice_limit || 30,
                      invoiceData.renewal_date || new Date(), // Handle null renewal_date
                      "system", // Created by system, not user
                      false // Not first invoice
                    );
                    // Note: invoiceCompleted is now handled inside generateInvoice
                  }
                }
              }
            }
          }
        } catch (invoiceError) {
          console.error(
            `Error processing invoice ${invoice.id}:`,
            invoiceError
          );
          // Continue processing other invoices even if one fails
        }
      }
    }
    console.log("Subscription renewal process completed successfully");
  } catch (error) {
    console.error("Error in subscription function:", error);
    throw error;
  }
};

export default subscription;
