# WhatsApp Hook

A comprehensive WhatsApp messaging platform that enables businesses to manage conversations, send automated messages, create campaigns, and build custom messaging flows.

## Project Overview

WhatsApp Hook is a full-featured WhatsApp Business API integration platform that provides:

- Automated messaging and follow-ups
- Campaign management
- Conversation tracking
- Contact management
- Message templates
- Automation flows
- Analytics and reporting

The system is built with a modern tech stack including Next.js, Bun, BullMQ, and PocketBase, providing a scalable and efficient solution for WhatsApp business messaging needs.

## Architecture

The project consists of two main components:

1. **Next.js Frontend** - User interface and server-side rendering
2. **Bun Backend** - Background job processing, webhooks, and database interactions

```mermaid
graph TD
    A[Client Browser] --> B[Next.js Frontend]
    B --> C[PocketBase]
    B --> D[Bun Backend]
    D --> C
    D --> E[Redis/BullMQ]
    F[WhatsApp API] --> D
    D --> F
    G[SQLite] --> D

    subgraph "Frontend Services"
    B
    end

    subgraph "Backend Services"
    C
    D
    E
    G
    end

    subgraph "External Services"
    F
    end
```
[![](https://mermaid.ink/img/pako:eNp9kVFPwjAQx79Kc89jOhgM9mDCGDMmakBMTNx4qNvJJlu7dJ2ChO9uoWPBYOzL9e7-v7t_0x3EPEFwYSVomZJnP2JEnXE4yTNkkniCf1UolqTTuSFe-IgbaX5UJBCcSWTJUsu9Y3sSzni8RunRCn81_NCrGfGoaraIr5HzZBo-YZJVV16d5w_zRheELymV1bgsyXh2p33451Sgk9twMb_PJLYKXa7qN_2yCE6eyQLFZxZjFUFjUgfV-oNqXF9AJ-c6TBsT_02abiQKRvOLUUFLgQEFioJmifqR3aEcgUyxwAhcdU2oWB-gvdLRWvLFlsXgSlGjAYLXqxTcd5pXKqvLhEr0M6qWF221pOyV8-KErMRhT4Or7SgmvGYSXMs5asHdwQbcgWVaXXvkdPsDy-45jjUwYAtuz7JNe9Rz-kPHse3haG_A93H4tTl0-vsf_wizrg?type=png)](https://mermaid.live/edit#pako:eNp9kVFPwjAQx79Kc89jOhgM9mDCGDMmakBMTNx4qNvJJlu7dJ2ChO9uoWPBYOzL9e7-v7t_0x3EPEFwYSVomZJnP2JEnXE4yTNkkniCf1UolqTTuSFe-IgbaX5UJBCcSWTJUsu9Y3sSzni8RunRCn81_NCrGfGoaraIr5HzZBo-YZJVV16d5w_zRheELymV1bgsyXh2p33451Sgk9twMb_PJLYKXa7qN_2yCE6eyQLFZxZjFUFjUgfV-oNqXF9AJ-c6TBsT_02abiQKRvOLUUFLgQEFioJmifqR3aEcgUyxwAhcdU2oWB-gvdLRWvLFlsXgSlGjAYLXqxTcd5pXKqvLhEr0M6qWF221pOyV8-KErMRhT4Or7SgmvGYSXMs5asHdwQbcgWVaXXvkdPsDy-45jjUwYAtuz7JNe9Rz-kPHse3haG_A93H4tTl0-vsf_wizrg)

### Key Components

- **Next.js Frontend** (`nextjs-docker/`): Handles user interface, authentication, and server-side rendering
- **Bun Backend** (`bun-app/`): Processes background jobs, handles webhooks, and manages database interactions
- **BullMQ**: Queue system for managing background jobs and scheduled tasks
- **PocketBase**: Database and authentication system
- **SQLite**: Local database for storing conversation data
- **WhatsApp Business API**: External API for sending and receiving WhatsApp messages

## Features

- **Live Chat**: Real-time conversation management with WhatsApp contacts
- **Campaigns**: Create and manage messaging campaigns to multiple contacts
- **Templates**: Create and manage WhatsApp message templates
- **Automation Flows**: Build custom messaging flows with visual editor
- **Contact Management**: Organize and manage WhatsApp contacts
- **Analytics**: Track message delivery, open rates, and engagement
- **Auto Follow-up**: Automatically send follow-up messages when users don't respond
- **Multi-user Support**: Team collaboration with role-based permissions

## Setup and Installation

### Prerequisites

- Docker and Docker Compose
- Node.js 18+ (for local development)
- Bun runtime (for local development)
- WhatsApp Business API credentials

### Environment Variables

Create a `.env` file in the root directory with the following variables:

```
# General
NODE_ENV=development
NEXT_PUBLIC_APP_URL=http://localhost:3001
NEXT_PUBLIC_BUN_SERVER_URL=http://localhost:3006

# WhatsApp API
WA_PHONE_NUMBER_ID=your_phone_number_id
CLOUD_API_ACCESS_TOKEN=your_cloud_api_token
CLOUD_API_VERSION=v18.0
USER_ACCESS_TOKEN=your_user_access_token
BUSINESS_ID=your_business_id

# Database
NEXT_PUBLIC_PB_URL=http://localhost:8090
SQLITE_DB_PATH=./main.db

# Queue
NEXT_PUBLIC_QNAME=whatsapp-hook

# Ports
HOST_PORT=3001
HOST_PORT_BUN=3006

# Other services
OPENAI_API_KEY=your_openai_api_key
NEXT_PUBLIC_AXIOM_TOKEN=your_axiom_token
NEXT_PUBLIC_AXIOM_DATASET=your_axiom_dataset
NEXT_PUBLIC_APP_ID=your_app_id
```

### Running with Docker

1. Clone the repository:
   ```bash
   git clone https://github.com/yourusername/whatsapp-hook.git
   cd whatsapp-hook
   ```

2. Build and start the containers:
   ```bash
   docker-compose up -d
   ```

3. Access the application:
   - Frontend: http://localhost:3001
   - Backend: http://localhost:3006

### Running for Development

#### Next.js Frontend

```bash
cd nextjs-docker
npm install
npm run dev
```

#### Bun Backend

```bash
cd bun-app
bun install
bun run dev
```

## Usage

### Sending Auto Follow-up Messages

The system automatically checks for conversations where the last message is from a user and no agent has replied for 20 hours. It then sends an automated "Are you interested?" message to re-engage the user.

This feature runs hourly and can be manually triggered through the BullMQ dashboard.

### Creating Campaigns

1. Navigate to the Campaigns section
2. Click "Create Campaign"
3. Select contacts or lists to target
4. Choose a message template
5. Schedule the campaign or send immediately
6. Monitor delivery and engagement metrics

### Managing Templates

1. Navigate to the Templates section
2. Create new templates or edit existing ones
3. Submit templates for WhatsApp approval
4. Use approved templates in campaigns and automated flows

## API Endpoints

### Bun Backend

- `/get-conversations` - Get all conversations with their last messages
- `/get-report` - Get analytics reports
- `/subscription-test` - Test subscription functionality

### Next.js Frontend

- `/api/whatsapp` - WhatsApp webhook endpoint
- `/api/v1/send-message` - Send a message to a contact

## External Resources

- Architecture Diagram: <https://www.tldraw.com/r/WpbIG0jIBfBbsWiKpHXkp?v=-107,-40,2056,1089&p=page>
- Issue Tracking: <https://clickchain.youtrack.cloud/issues>

## Contributing

1. Fork the repository
2. Create a feature branch: `git checkout -b feature/my-feature`
3. Commit your changes: `git commit -am 'Add new feature'`
4. Push to the branch: `git push origin feature/my-feature`
5. Submit a pull request

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Acknowledgements

- [WhatsApp Business API](https://developers.facebook.com/docs/whatsapp/api/reference)
- [Next.js](https://nextjs.org/)
- [Bun](https://bun.sh/)
- [BullMQ](https://docs.bullmq.io/)
- [PocketBase](https://pocketbase.io/)
